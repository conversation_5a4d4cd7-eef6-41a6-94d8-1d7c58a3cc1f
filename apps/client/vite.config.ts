import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import TanStackRouterVite from "@tanstack/router-plugin/vite";
import tailwindcss from "@tailwindcss/vite";
export default defineConfig({
  plugins: [
    react(),
    TanStackRouterVite({
      routesDirectory: "./src/app/routes",
      generatedRouteTree: "./src/app/routeTree.gen.ts",
    }),
    tailwindcss()
  ],
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },
  
});

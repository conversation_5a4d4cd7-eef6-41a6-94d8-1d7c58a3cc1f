# 🎯 PLANO: MODELOS DINÂMICOS COM TIPTAP

## 📋 VISÃO GERAL

Implementar sistema de modelos dinâmicos usando **TipTap Editor** como base, permitindo criação de formulários interativos que geram documentos profissionais automaticamente.

### 🎯 OBJETIVOS
- **Editor visual** para criação de modelos com TipTap
- **Campos dinâmicos** como nodes customizados
- **Wizard de aplicação** step-by-step
- **Geração automática** de documentos (PDF/DOCX)
- **Integração** com notas de sessão

---

## 🏗️ ARQUITETURA TÉCNICA

### **TipTap como Core Engine**
```typescript
// Estrutura base do editor
const editor = useEditor({
  extensions: [
    StarterKit,
    PlaceholderExtension,
    DynamicFieldExtension,
    ConditionalLogicExtension,
    ValidationExtension,
    PreviewExtension
  ],
  content: model.template.content,
  editable: true
});
```

### **Extensões Customizadas Necessárias**
1. **PlaceholderExtension** - Placeholders visuais
2. **DynamicFieldExtension** - Campos de formulário como nodes
3. **ConditionalLogicExtension** - Lógica condicional
4. **ValidationExtension** - Validação inline
5. **PreviewExtension** - Preview em tempo real

---

## 📊 ESTRUTURAS DE DADOS

### **DynamicModel Schema**
```typescript
interface DynamicModel {
  id: string;
  title: string;
  description: string;
  category: string;
  
  // Template TipTap
  template: {
    content: JSONContent; // TipTap JSON format
    placeholders: DynamicPlaceholder[];
  };
  
  // Formulário dinâmico
  form: {
    sections: FormSection[];
    validation: ValidationRules;
    conditional: ConditionalLogic[];
  };
  
  // Configurações de saída
  outputs: {
    note: boolean;
    pdf: boolean;
    docx: boolean;
  };
  
  // Metadados
  isFavorite: boolean;
  tags: string[];
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

interface FormSection {
  id: string;
  title: string;
  description?: string;
  fields: DynamicField[];
  conditional?: ConditionalRule;
}

interface DynamicField {
  id: string;
  type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'number';
  label: string;
  placeholder?: string;
  required: boolean;
  validation?: FieldValidation;
  options?: SelectOption[];
  conditional?: ConditionalRule;
}

interface DynamicPlaceholder {
  id: string;
  fieldId: string;
  label: string;
  transform?: 'uppercase' | 'lowercase' | 'capitalize' | 'date-format';
  fallback?: string;
}
```

---

## 🚀 FASES DE IMPLEMENTAÇÃO

### **FASE 1: FUNDAÇÃO TIPTAP (1-2 semanas)**

#### **1.1 Setup TipTap Base**
```bash
pnpm add @tiptap/react @tiptap/starter-kit @tiptap/extension-placeholder
pnpm add @tiptap/extension-text-style @tiptap/extension-color
```

#### **1.2 Extensão de Placeholders**
```typescript
// apps/client/src/features/models/extensions/PlaceholderExtension.ts
import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { PlaceholderNodeView } from '../components/PlaceholderNodeView';

export const PlaceholderExtension = Node.create({
  name: 'dynamicPlaceholder',
  
  addAttributes() {
    return {
      fieldId: { default: null },
      label: { default: '' },
      transform: { default: null },
      fallback: { default: '' }
    };
  },
  
  parseHTML() {
    return [{ tag: 'dynamic-placeholder' }];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['dynamic-placeholder', mergeAttributes(HTMLAttributes)];
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(PlaceholderNodeView);
  },
  
  addCommands() {
    return {
      insertPlaceholder: (attributes) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: attributes,
        });
      },
    };
  },
});
```

#### **1.3 Extensão de Campos Dinâmicos**
```typescript
// apps/client/src/features/models/extensions/DynamicFieldExtension.ts
export const DynamicFieldExtension = Node.create({
  name: 'dynamicField',
  
  addAttributes() {
    return {
      fieldId: { default: null },
      fieldType: { default: 'text' },
      label: { default: '' },
      required: { default: false },
      options: { default: [] },
      validation: { default: null }
    };
  },
  
  parseHTML() {
    return [{ tag: 'dynamic-field' }];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['dynamic-field', mergeAttributes(HTMLAttributes)];
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(DynamicFieldNodeView);
  }
});
```

### **FASE 2: EDITOR DE MODELOS (2-3 semanas)**

#### **2.1 Componente Principal do Editor**
```typescript
// apps/client/src/features/models/components/ModelEditor.tsx
export function ModelEditor({ model, onChange }: Props) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      PlaceholderExtension,
      DynamicFieldExtension,
      TextStyle,
      Color
    ],
    content: model.template.content,
    onUpdate: ({ editor }) => {
      onChange({
        ...model,
        template: {
          ...model.template,
          content: editor.getJSON()
        }
      });
    }
  });

  return (
    <div className="grid grid-cols-12 gap-6 h-screen">
      {/* Toolbar */}
      <div className="col-span-12">
        <ModelEditorToolbar editor={editor} />
      </div>
      
      {/* Sidebar com campos */}
      <div className="col-span-3">
        <FieldsSidebar onInsertField={insertField} />
      </div>
      
      {/* Editor principal */}
      <div className="col-span-6">
        <EditorContent editor={editor} className="prose max-w-none" />
      </div>
      
      {/* Preview */}
      <div className="col-span-3">
        <LivePreview editor={editor} model={model} />
      </div>
    </div>
  );
}
```

#### **2.2 Toolbar Customizada**
```typescript
// apps/client/src/features/models/components/ModelEditorToolbar.tsx
export function ModelEditorToolbar({ editor }: Props) {
  if (!editor) return null;

  return (
    <div className="border-b p-2 flex items-center gap-2">
      {/* Formatação básica */}
      <Button
        variant={editor.isActive('bold') ? 'default' : 'outline'}
        size="sm"
        onClick={() => editor.chain().focus().toggleBold().run()}
      >
        <Bold className="h-4 w-4" />
      </Button>
      
      <Button
        variant={editor.isActive('italic') ? 'default' : 'outline'}
        size="sm"
        onClick={() => editor.chain().focus().toggleItalic().run()}
      >
        <Italic className="h-4 w-4" />
      </Button>
      
      <Separator orientation="vertical" className="h-6" />
      
      {/* Inserir placeholder */}
      <PlaceholderInserter editor={editor} />
      
      {/* Inserir campo dinâmico */}
      <FieldInserter editor={editor} />
    </div>
  );
}
```

#### **2.3 Sidebar de Campos**
```typescript
// apps/client/src/features/models/components/FieldsSidebar.tsx
export function FieldsSidebar({ onInsertField }: Props) {
  const fieldTypes = [
    { type: 'text', icon: Type, label: 'Texto Simples' },
    { type: 'textarea', icon: AlignLeft, label: 'Texto Longo' },
    { type: 'select', icon: ChevronDown, label: 'Lista Suspensa' },
    { type: 'radio', icon: Circle, label: 'Múltipla Escolha' },
    { type: 'checkbox', icon: Square, label: 'Caixas de Seleção' },
    { type: 'date', icon: Calendar, label: 'Data' },
    { type: 'number', icon: Hash, label: 'Número' }
  ];

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-sm">Campos Disponíveis</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {fieldTypes.map(field => (
          <Button
            key={field.type}
            variant="outline"
            size="sm"
            className="w-full justify-start"
            onClick={() => onInsertField(field.type)}
          >
            <field.icon className="h-4 w-4 mr-2" />
            {field.label}
          </Button>
        ))}

        <Separator className="my-4" />

        {/* Placeholders do sistema */}
        <div className="space-y-2">
          <h4 className="text-xs font-medium text-muted-foreground">
            DADOS DO PACIENTE
          </h4>
          {PATIENT_PLACEHOLDERS.map(placeholder => (
            <PlaceholderButton
              key={placeholder.id}
              placeholder={placeholder}
              onInsert={onInsertField}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
```

### **FASE 3: WIZARD DE APLICAÇÃO (2 semanas)**

#### **3.1 Componente Principal do Wizard**
```typescript
// apps/client/src/features/models/components/ModelApplicationWizard.tsx
export function ModelApplicationWizard({ model, patient, appointment }: Props) {
  const [currentStep, setCurrentStep] = useState(0);
  const [responses, setResponses] = useState<FormResponse[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);

  const visibleSections = useMemo(() =>
    evaluateConditionals(model.form.sections, responses),
    [model.form.sections, responses]
  );

  return (
    <Dialog open onOpenChange={onClose} className="max-w-4xl">
      <DialogContent className="max-h-[90vh] overflow-hidden">
        {/* Header com progresso */}
        <DialogHeader>
          <DialogTitle>{model.title}</DialogTitle>
          <DialogDescription>{model.description}</DialogDescription>
          <Progress
            value={(currentStep + 1) / visibleSections.length * 100}
            className="mt-2"
          />
        </DialogHeader>

        {/* Conteúdo da seção atual */}
        <ScrollArea className="flex-1 px-6">
          <WizardSection
            section={visibleSections[currentStep]}
            responses={responses}
            errors={validationErrors}
            onChange={handleResponseChange}
          />
        </ScrollArea>

        {/* Footer com navegação */}
        <DialogFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={goToPreviousStep}
            disabled={currentStep === 0}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Anterior
          </Button>

          <div className="flex gap-2">
            <Button variant="outline" onClick={showPreview}>
              <Eye className="h-4 w-4 mr-2" />
              Visualizar
            </Button>

            {currentStep < visibleSections.length - 1 ? (
              <Button onClick={goToNextStep}>
                Próximo
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <FinishButton
                model={model}
                responses={responses}
                onFinish={handleFinish}
              />
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
```

#### **3.2 Componentes de Campo Dinâmicos**
```typescript
// apps/client/src/features/models/components/DynamicFieldRenderer.tsx
export function DynamicFieldRenderer({ field, value, error, onChange }: Props) {
  const commonProps = {
    value: value || '',
    onChange: (val: any) => onChange(field.id, val),
    className: error ? 'border-red-500' : ''
  };

  switch (field.type) {
    case 'text':
      return (
        <FormField>
          <FormLabel>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </FormLabel>
          <Input
            {...commonProps}
            placeholder={field.placeholder}
          />
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormField>
      );

    case 'textarea':
      return (
        <FormField>
          <FormLabel>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </FormLabel>
          <Textarea
            {...commonProps}
            placeholder={field.placeholder}
            rows={4}
          />
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormField>
      );

    case 'select':
      return (
        <FormField>
          <FormLabel>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </FormLabel>
          <Select
            value={value}
            onValueChange={(val) => onChange(field.id, val)}
          >
            <SelectTrigger className={error ? 'border-red-500' : ''}>
              <SelectValue placeholder={field.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormField>
      );

    case 'radio':
      return (
        <FormField>
          <FormLabel>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </FormLabel>
          <RadioGroup
            value={value}
            onValueChange={(val) => onChange(field.id, val)}
            className={error ? 'border border-red-500 rounded p-2' : ''}
          >
            {field.options?.map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} />
                <Label>{option.label}</Label>
              </div>
            ))}
          </RadioGroup>
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormField>
      );

    case 'checkbox':
      return (
        <FormField>
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={value === true}
              onCheckedChange={(checked) => onChange(field.id, checked)}
              className={error ? 'border-red-500' : ''}
            />
            <FormLabel>
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </FormLabel>
          </div>
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormField>
      );

    case 'date':
      return (
        <FormField>
          <FormLabel>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </FormLabel>
          <DatePicker
            value={value ? new Date(value) : undefined}
            onChange={(date) => onChange(field.id, date?.toISOString())}
            className={error ? 'border-red-500' : ''}
          />
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormField>
      );

    case 'number':
      return (
        <FormField>
          <FormLabel>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </FormLabel>
          <Input
            type="number"
            {...commonProps}
            placeholder={field.placeholder}
          />
          {error && <FormMessage>{error.message}</FormMessage>}
        </FormField>
      );

    default:
      return null;
  }
}
```

### **FASE 4: BACKEND APIS (1-2 semanas)**

#### **4.1 Atualizar Schema do Modelo**
```rust
// apps/server/src/db/models/model.rs
#[derive(Debug, Queryable, Selectable, Identifiable, AsChangeset, Serialize, Deserialize)]
#[diesel(table_name = models)]
pub struct Model {
    pub id: Uuid,
    pub user_id: Uuid,
    pub title: String,
    pub category: String,
    pub type_: String,
    pub description: Option<String>,
    pub content: String,

    // NOVOS CAMPOS
    pub template_content: Option<serde_json::Value>, // TipTap JSON
    pub form_schema: Option<serde_json::Value>,      // Formulário dinâmico
    pub output_types: Option<Vec<String>>,           // ['note', 'pdf', 'docx']

    pub is_favorite: Option<bool>,
    pub tags: Option<Vec<Option<String>>>,
    pub usage_count: Option<i32>,
    pub last_used: Option<NaiveDateTime>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}
```

#### **4.2 Novo Endpoint para Aplicar Modelo**
```rust
// apps/server/src/model/model_handler.rs
pub async fn apply_model_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(model_id): Path<Uuid>,
    Json(payload): Json<ApplyModelRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    let result = model_service::apply_model(
        user_id,
        model_id,
        payload,
        &mut conn
    ).await?;

    Ok(Json(result))
}

#[derive(Debug, Deserialize)]
pub struct ApplyModelRequest {
    pub patient_id: Uuid,
    pub appointment_id: Option<Uuid>,
    pub form_responses: Vec<FormResponse>,
    pub output_type: String, // 'note', 'pdf', 'docx'
}

#[derive(Debug, Deserialize)]
pub struct FormResponse {
    pub field_id: String,
    pub value: serde_json::Value,
}
```

#### **4.3 Serviço de Aplicação de Modelo**
```rust
// apps/server/src/model/model_service.rs
pub async fn apply_model(
    user_id: Uuid,
    model_id: Uuid,
    request: ApplyModelRequest,
    conn: &mut PgConnection,
) -> Result<ApplyModelResponse, String> {
    // 1. Buscar modelo
    let model = model_repository::find_model_by_id(model_id, user_id, conn)
        .map_err(|_| "Modelo não encontrado".to_string())?;

    // 2. Buscar dados do paciente
    let patient = patient_repository::find_patient_by_id(request.patient_id, user_id, conn)
        .map_err(|_| "Paciente não encontrado".to_string())?;

    // 3. Processar template com respostas
    let processed_content = process_template_with_responses(
        &model.template_content,
        &request.form_responses,
        &patient,
        request.appointment_id,
        conn
    )?;

    // 4. Gerar saída baseada no tipo
    match request.output_type.as_str() {
        "note" => create_session_note(user_id, request.patient_id, request.appointment_id, &processed_content, conn).await,
        "pdf" => generate_pdf_document(user_id, request.patient_id, Some(model_id), &processed_content, conn).await,
        "docx" => generate_docx_document(user_id, request.patient_id, Some(model_id), &processed_content, conn).await,
        _ => Err("Tipo de saída inválido".to_string())
    }
}
```

---

## 📅 CRONOGRAMA

| Fase | Duração | Tarefas Principais |
|------|---------|-------------------|
| **Fase 1** | 1-2 semanas | Setup TipTap + Extensões básicas |
| **Fase 2** | 2-3 semanas | Editor visual completo |
| **Fase 3** | 2 semanas | Wizard de aplicação |
| **Fase 4** | 1-2 semanas | APIs backend |

**Total: 6-9 semanas**

---

## 🎯 ENTREGÁVEIS

### **Frontend**
- [ ] Extensões TipTap customizadas
- [ ] Editor visual de modelos
- [ ] Wizard de aplicação step-by-step
- [ ] Componentes de campo dinâmicos
- [ ] Preview em tempo real
- [ ] Integração com notas de sessão

### **Backend**
- [ ] Schema atualizado para modelos dinâmicos
- [ ] APIs para aplicar modelos
- [ ] Processamento de templates
- [ ] Geração de documentos PDF/DOCX
- [ ] Validação de formulários

### **Integração**
- [ ] Modelos → Notas de sessão
- [ ] Modelos → Documentos PDF
- [ ] Modelos → Documentos DOCX
- [ ] Sistema de placeholders unificado

---

## 🚀 PRÓXIMOS PASSOS

1. **Instalar dependências** TipTap
2. **Criar extensões** básicas
3. **Implementar editor** visual
4. **Desenvolver wizard** de aplicação
5. **Integrar com backend**
6. **Testes e refinamentos**
```

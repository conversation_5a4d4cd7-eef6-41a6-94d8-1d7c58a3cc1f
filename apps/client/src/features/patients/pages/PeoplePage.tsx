import { useState } from "react";
import { Plus, Users, UserCheck } from "lucide-react";
import { useNavigate } from "@tanstack/react-router";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/shared/ui/tabs";
import { PageLayout, PageCard } from "@/shared/components/PageLayout";
import { usePatientQuery, PatientFilterOptions } from "@/features/patients/hooks/usePatientQuery";
import { usePatientMutations } from "@/features/patients/hooks/usePatientMutations";
import { useContactQuery } from "@/features/contacts/hooks/useContactQuery";
import { useContactMutations } from "@/features/contacts/hooks/useContactMutations";
import { usePatientTags } from "@/features/patients/hooks/usePatientTags";
import { usePatientContactMutations } from "@/features/patients/hooks/usePatientContacts";
import { PatientFilters } from "@/features/patients/components/PatientFilters";
import { PatientFormDialog } from "@/features/patients/components/PatientFormDialog";
import { ContactFormDialog } from "@/features/patients/components/ContactFormDialog";
import { ConfirmDeleteDialog } from "@/shared/components/ConfirmDeleteDialog";
import { ManageContactLinksDialog } from "@/features/patients/components/ManageContactLinksDialog";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/shared/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { Label } from "@/shared/ui/label";
import { Patient, CreatePatientData, UpdatePatientData } from "@/features/patients/types/patient.schema";
import { CreateContactData, Contact } from "@/shared/types/contact.schema";
import { ContactList } from "@/shared/components/ContactList";
import { PatientList } from "@/features/patients/components/PatientList";
import { PatientFilterOptions as PatientFilterOptionsType } from "@/features/patients/hooks/usePatientQuery";

export function PeoplePage() {
  const navigate = useNavigate();

  // Estados locais
  const [view, setView] = useState<"patients" | "contacts">("patients");
  const [, setSelectedPatient] = useState<Patient | null>(null);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [showPatientForm, setShowPatientForm] = useState(false);
  const [showContactForm, setShowContactForm] = useState(false);
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [contactToLink, setContactToLink] = useState<Contact | null>(null);
  const [selectedPatientToLink, setSelectedPatientToLink] = useState<string>("");
  const [filterOptions, setFilterOptions] = useState<PatientFilterOptionsType>({});
  const [contactToDelete, setContactToDelete] = useState<Contact | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showManageLinksDialog, setShowManageLinksDialog] = useState(false);

  // Hooks
  const { filteredPatients, isLoading: isLoadingPatients, setFilters } = usePatientQuery({ initialFilters: filterOptions });
  const { allPatients } = usePatientQuery();
  const { createPatient } = usePatientMutations();
  const { contacts } = useContactQuery();
  const { createContact, deleteContact, isDeleting } = useContactMutations();
  const { tags } = usePatientTags();
  const { linkContact, isLinking } = usePatientContactMutations();

  // Handlers
  const handleFilterChange = (newFilters: PatientFilterOptionsType) => {
    setFilterOptions(newFilters);
    setFilters(newFilters);
  };

  const handlePatientSelect = (patient: Patient) => navigate({ to: '/person/$patientId', params: { patientId: patient.id } });

  const handlePatientFormSubmit = (data: CreatePatientData | UpdatePatientData) => {
    createPatient(data as CreatePatientData, { onSuccess: () => { setShowPatientForm(false); } });
  };

  const handleContactFormSubmit = (data: CreateContactData) => {
    createContact(data, { onSuccess: () => { setShowContactForm(false); } });
  };

  const handleManageLinks = (contact: Contact) => {
    setSelectedContact(contact);
    setShowManageLinksDialog(true);
  };

  const handleLinkToPatient = (contact: Contact) => {
    setContactToLink(contact);
    setSelectedPatientToLink("");
    setShowLinkDialog(true);
  };

  return (
    <PageLayout
      title="Pessoas"
      description="Gerencie pacientes e contatos em um só lugar"
      breadcrumbs={[
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'Pessoas' }
      ]}
      actions={
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => { setSelectedContact(null); setShowContactForm(true); }}>
            <Plus className="h-4 w-4 mr-2" />
            Novo Contato
          </Button>
          <Button onClick={() => { setSelectedPatient(null); setShowPatientForm(true); }}>
            <Plus className="h-4 w-4 mr-2" />
            Novo Paciente
          </Button>
        </div>
      }
    >
      {/* Filtros */}
      <PatientFilters onFilterChange={handleFilterChange} availableTags={tags} initialFilters={filterOptions} />

      {/* Abas Pacientes/Contatos */}
      <Tabs defaultValue={view} onValueChange={(value) => { setView(value as "patients" | "contacts"); }}>
        <TabsList className="mb-6">
          <TabsTrigger value="patients" className="flex items-center">
            <UserCheck className="h-4 w-4 mr-2" />
            Pacientes
          </TabsTrigger>
          <TabsTrigger value="contacts" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            Contatos
          </TabsTrigger>
        </TabsList>

        <PageCard>
          {view === "patients" && (
            <PatientList
              patients={filteredPatients}
              isLoading={isLoadingPatients}
              onSelect={handlePatientSelect}
              onAddPatient={() => { setSelectedPatient(null); setShowPatientForm(true); }}
              tags={tags}
            />
          )}
          {view === "contacts" && (
            <ContactList
              contacts={contacts.filter(c => c.relationship_type !== 'guardian')}
              onSelect={(contact) => { setSelectedContact(contact); }}
              onEdit={(contact) => { setSelectedContact(contact); setShowContactForm(true); }}
              onDelete={(contact) => { setContactToDelete(contact); setShowDeleteConfirm(true); }}
              onLinkToPatient={handleLinkToPatient}
              onManageLinks={handleManageLinks}
              onAddContact={() => { setSelectedContact(null); setShowContactForm(true); }}
            />
          )}
        </PageCard>
      </Tabs>

      {/* Diálogos */}
      <PatientFormDialog
        open={showPatientForm}
        onOpenChange={setShowPatientForm}
        patient={undefined}
        onSubmit={handlePatientFormSubmit}
        onCancel={() => { setShowPatientForm(false); }}
        availableTags={tags}
      />
      <ContactFormDialog
        open={showContactForm}
        onOpenChange={setShowContactForm}
        onSubmit={handleContactFormSubmit}
        onCancel={() => { setShowContactForm(false); }}
        contact={selectedContact}
        patients={allPatients}
      />
      <Dialog open={showLinkDialog} onOpenChange={setShowLinkDialog}>
         <DialogContent>
           <DialogHeader>
             <DialogTitle>Vincular Contato a Paciente</DialogTitle>
             <DialogDescription>
               Selecione o paciente para vincular o contato{' '}
               <strong>{contactToLink?.name}</strong> (como contato secundário).
             </DialogDescription>
           </DialogHeader>
           <div className="grid gap-4 py-4">
             <div className="grid grid-cols-4 items-center gap-4">
               <Label htmlFor="patient-select-link" className="text-right"> Paciente </Label>
               <Select value={selectedPatientToLink} onValueChange={setSelectedPatientToLink}>
                 <SelectTrigger id="patient-select-link" className="col-span-3"> <SelectValue placeholder="Selecione um paciente..." /> </SelectTrigger>
                 <SelectContent> {allPatients.map((p) => ( <SelectItem key={p.id} value={p.id}> {p.full_name} </SelectItem> ))} </SelectContent>
               </Select>
             </div>
             {/* Remover seleção de Função (Role) */}
           </div>
           <DialogFooter>
             <Button variant="outline" onClick={() => { setShowLinkDialog(false); }}> Cancelar </Button>
             <Button
               onClick={() => {
                 if (contactToLink && selectedPatientToLink) {
                   linkContact({
                     contactId: contactToLink.id,
                     patientIdToLink: selectedPatientToLink,
                     role: 'other',
                   });
                   setShowLinkDialog(false);
                 }
               }}
               disabled={!selectedPatientToLink || isLinking}
             >
               {isLinking ? 'Vinculando...' : 'Vincular Contato'}
             </Button>
           </DialogFooter>
         </DialogContent>
      </Dialog>
      <ConfirmDeleteDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Excluir Contato"
        description="Tem certeza que deseja excluir permanentemente o contato"
        itemName={contactToDelete?.name}
        itemType="contato"
        isDeleting={isDeleting}
        onConfirm={() => {
          if (contactToDelete) {
            deleteContact(contactToDelete.id);
            setShowDeleteConfirm(false);
            setContactToDelete(null);
          }
        }}
        customWarning={<>Esta ação não pode ser desfeita. O contato será removido completamente do sistema.</>}
      />
      <ManageContactLinksDialog
        contact={selectedContact}
        open={showManageLinksDialog}
        onOpenChange={setShowManageLinksDialog}
      />
    </PageLayout>
  );
}

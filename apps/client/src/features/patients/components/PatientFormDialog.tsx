import { useEffect, useState, useCallback } from "react";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  <PERSON><PERSON>Footer,
} from "@/shared/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/ui/form";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Textarea } from "@/shared/ui/textarea";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/shared/ui/tabs";
import { PhoneInput } from "@/features/auth/components/forms/phoneInput";
import {
  Patient,
  CreatePatientData,
  UpdatePatientData,
  ContactLinkData,
  PatientStatus as PatientStatusType,
  TherapyType,
} from "@/features/patients/types/patient.schema";
import { Contact, createContactSchema } from "@/shared/types/contact.schema";
import apiClient from "@/shared/lib/api.client";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2, AlertCircle } from "lucide-react";
import { PatientTags, Tag } from "./PatientTags";
import { useContactQuery } from "@/features/contacts/hooks/useContactQuery";
import { useContactMutations } from "@/features/contacts/hooks/useContactMutations";
import { ConfirmDeleteDialog } from "@/shared/components/ConfirmDeleteDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { Switch } from "@/shared/ui/switch";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { ContactFormSection } from "./ContactFormSection";
import { cn } from "@/shared/lib/utils";

// Schema principal do formulário do paciente
const patientFormSchema = z.object({
  // --- Campos do Paciente ---
  full_name: z.string().min(3, { message: "Nome completo deve ter pelo menos 3 caracteres" }),
  date_of_birth: z.string().optional().nullable(),
  cpf: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  email: z.string().email({ message: "Email inválido" }).optional().or(z.literal("")).nullable(),
  address: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  status: z.string().optional(),
  therapy_type: z.string().optional().nullable(),
  has_guardian: z.boolean().default(false),
  school: z.string().optional().nullable(),

  // --- Campo para selecionar responsável existente ---
  existing_guardian_id: z.string().uuid().optional().nullable(),

  // --- Campos para NOVO responsável (vindos de NewContactFields com prefixo) ---
  contact_name: z.string().optional(),
  contact_relationship_type: z.string().optional(),
  contact_cpf: z.string().optional().nullable(),
  contact_rg: z.string().optional().nullable(),
  contact_phone: z.string().optional().nullable(),
  contact_email: z.string().email({ message: "Email inválido" }).optional().or(z.literal("")).nullable(),
  contact_address: z.string().optional().nullable(),
  contact_city: z.string().optional().nullable(),
  contact_state: z.string().optional().nullable(),
  contact_date_of_birth: z.string().optional().nullable(),
  contact_gender: z.string().optional().nullable(),
  contact_marital_status: z.string().optional().nullable(),
  contact_ethnicity: z.string().optional().nullable(),
  contact_nationality: z.string().optional().nullable(),
  contact_naturalness: z.string().optional().nullable(),
  contact_occupation: z.string().optional().nullable(),
});


// Tipos
type PatientFormValues = z.infer<typeof patientFormSchema>;
type NewContactFormValues = z.infer<typeof createContactSchema>;

interface PatientFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreatePatientData | UpdatePatientData) => void;
  onCancel: () => void;
  patient?: Patient;
  isProcessing?: boolean;
  availableTags?: Tag[];
  onAddTag?: (tag: Omit<Tag, "id">) => void;
  onRemoveTag?: (tagId: string) => void;
}

export function PatientFormDialog({
  open,
  onOpenChange,
  onSubmit,
  onCancel,
  patient,
  isProcessing = false,
  availableTags = [],
  onAddTag,
  onRemoveTag,
}: PatientFormDialogProps) {
  const [activeTab, setActiveTab] = useState("basic");
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [guardianSelectionMode, setGuardianSelectionMode] = useState<'new' | 'existing'>('new');
  const [selectedGuardianId, setSelectedGuardianId] = useState<string | null>(null);
  const [showGuardianDeleteConfirm, setShowGuardianDeleteConfirm] = useState(false);
  const [guardianToDelete, setGuardianToDelete] = useState<Contact | null>(null);
  const isEditing = !!patient;

  const { deleteContact, isDeleting: isDeletingContact } = useContactMutations();
  const { contacts: existingContacts, isLoading: isLoadingContacts, error: contactsError } = useContactQuery();
  const queryClient = useQueryClient();

  const form = useForm<PatientFormValues>({
    resolver: zodResolver(patientFormSchema),
    defaultValues: {
      full_name: "", date_of_birth: null, cpf: null, phone: null, email: null,
      address: null, city: null, state: null, notes: null, status: "active",
      therapy_type: null, has_guardian: false, school: null, existing_guardian_id: null,
      contact_name: '', contact_relationship_type: 'guardian', contact_cpf: null, contact_rg: null, contact_phone: null,
      contact_email: null, contact_address: null, contact_city: null, contact_state: null,
      contact_date_of_birth: null, contact_gender: null, contact_marital_status: null, contact_ethnicity: null,
      contact_nationality: null, contact_naturalness: null, contact_occupation: null,
    },
  });

  const hasGuardian = useWatch({ control: form.control, name: "has_guardian" });

  useEffect(() => {
    if (patient) {
      form.reset({
        full_name: patient.full_name,
        date_of_birth: patient.date_of_birth || null,
        cpf: patient.cpf || null,
        phone: patient.phone || null,
        email: patient.email || null,
        address: patient.address || null,
        city: patient.city || null,
        state: patient.state || null,
        notes: patient.notes || null,
        status: patient.status || "active",
        therapy_type: patient.therapy_type || null,
        has_guardian: !!patient.guardian,
        school: patient.school || null,
        existing_guardian_id: patient.guardian?.id || null,
        contact_name: '', contact_relationship_type: 'guardian', contact_cpf: null, contact_rg: null, contact_phone: null,
        contact_email: null, contact_address: null, contact_city: null, contact_state: null,
        contact_date_of_birth: null, contact_gender: null, contact_marital_status: null, contact_ethnicity: null,
        contact_nationality: null, contact_naturalness: null, contact_occupation: null,
      });
      if (patient.guardian) {
        setGuardianSelectionMode('existing');
        setSelectedGuardianId(patient.guardian.id);
      } else {
        setGuardianSelectionMode('new');
        setSelectedGuardianId(null);
      }
    } else {
      form.reset({
        full_name: "", date_of_birth: null, cpf: null, phone: null, email: null,
        address: null, city: null, state: null, notes: null, status: "active",
        therapy_type: null, has_guardian: false, school: null, existing_guardian_id: null,
        contact_name: '', contact_relationship_type: 'guardian', contact_cpf: null, contact_rg: null, contact_phone: null,
        contact_email: null, contact_address: null, contact_city: null, contact_state: null,
        contact_date_of_birth: null, contact_gender: null, contact_marital_status: null, contact_ethnicity: null,
        contact_nationality: null, contact_naturalness: null, contact_occupation: null,
      });
      setSelectedTags([]);
      setGuardianSelectionMode('new');
      setSelectedGuardianId(null);
    }
    setActiveTab("basic");
  }, [patient, form]);

  const finalizeSubmit = useCallback((payload: CreatePatientData | UpdatePatientData) => {
    onSubmit(payload);
    queryClient.invalidateQueries({ queryKey: ['patients'] });
    queryClient.invalidateQueries({ queryKey: ['contacts'] });
    if (patient?.id) {
      queryClient.invalidateQueries({ queryKey: ['patient', patient.id] });
    }
  }, [onSubmit, queryClient, patient?.id]);

  useEffect(() => {
    if (!hasGuardian) {
      form.setValue('contact_name', '');
      form.setValue('contact_relationship_type', 'guardian');
      form.setValue('contact_cpf', null);
      form.setValue('contact_rg', null);
      form.setValue('contact_phone', null);
      form.setValue('contact_email', null);
      form.setValue('contact_address', null);
      form.setValue('contact_city', null);
      form.setValue('contact_state', null);
      form.setValue('contact_date_of_birth', null);
      form.setValue('contact_gender', null);
      form.setValue('contact_marital_status', null);
      form.setValue('contact_ethnicity', null);
      form.setValue('contact_nationality', null);
      form.setValue('contact_naturalness', null);
      form.setValue('contact_occupation', null);
    }
  }, [hasGuardian, form]);

  const preparePatientPayload = (values: PatientFormValues): CreatePatientData | UpdatePatientData | null => {
    const contactLinks: ContactLinkData[] = [];
    let validationError = false;

    if (values.has_guardian) {
      if (guardianSelectionMode === 'existing') {
        const guardianIdToLink = selectedGuardianId;
        if (!guardianIdToLink) {
          form.setError("existing_guardian_id", { message: "Selecione um responsável existente." });
          setActiveTab("guardian");
          validationError = true;
        } else {
          contactLinks.push({ contact_id: guardianIdToLink, role: 'guardian', contact_data: undefined });
        }
      } else { // Modo 'new'
        const contactDataToValidate: NewContactFormValues = {
          name: values.contact_name || '',
          relationship_type: values.contact_relationship_type || 'guardian',
          cpf: values.contact_cpf || null,
          rg: values.contact_rg || null,
          phone: values.contact_phone || null,
          email: values.contact_email || null,
          address: values.contact_address || null,
          city: values.contact_city || null,
          state: values.contact_state || null,
          date_of_birth: values.contact_date_of_birth || null,
          gender: values.contact_gender || null,
          marital_status: values.contact_marital_status || null,
          ethnicity: values.contact_ethnicity || null,
          nationality: values.contact_nationality || null,
          naturalness: values.contact_naturalness || null,
          occupation: values.contact_occupation || null,
        };

        const validationResult = createContactSchema.safeParse(contactDataToValidate);
        if (!validationResult.success) {
          validationResult.error.errors.forEach(err => {
            const baseFieldName = err.path[0] as keyof NewContactFormValues;
            const formFieldName = `contact_${baseFieldName}` as keyof PatientFormValues;
            if (formFieldName in form.getValues()) {
               form.setError(formFieldName, { type: 'manual', message: err.message });
            } else {
               const baseFieldNameAsPatientField = baseFieldName as keyof PatientFormValues;
               if (baseFieldNameAsPatientField in form.getValues()) {
                   form.setError(baseFieldNameAsPatientField, { type: 'manual', message: err.message });
               } else {
                   if ('contact_name' in form.getValues()) {
                       form.setError('contact_name', { type: 'manual', message: `Erro no campo ${baseFieldName}: ${err.message}` });
                   }
               }
            }
          });
          setActiveTab("guardian");
          validationError = true;
        } else {
          contactLinks.push({ contact_id: undefined, contact_data: validationResult.data, role: 'guardian' });
        }
      }
    }

    if (validationError) return null;

    const patientPayload: CreatePatientData | UpdatePatientData = {
      full_name: values.full_name,
      date_of_birth: values.date_of_birth || null,
      cpf: values.cpf || null,
      phone: values.phone || null,
      email: values.email || null,
      address: values.address || null,
      city: values.city || null,
      state: values.state || null,
      notes: values.notes || null,
      school: values.school || null,
      status: (values.status as PatientStatusType) || 'active',
      therapy_type: (values.therapy_type as TherapyType) || null,
      has_financial_issues: false,
      contacts: contactLinks.length > 0 ? contactLinks.map(link => ({
          ...link,
          contact_id: link.contact_id || undefined, 
      })) : undefined,
    };

    return patientPayload;
  };

  const handleSubmit = async (values: PatientFormValues) => {
    if (isEditing && patient?.guardian && !values.has_guardian) {
      setGuardianToDelete(patient.guardian);
      setShowGuardianDeleteConfirm(true);
      return;
    }

    form.clearErrors();
    const patientPayload = preparePatientPayload(values);
    if (patientPayload) {
      finalizeSubmit(patientPayload);
    }
  };

  const handleGuardianDeleteConfirm = async () => {
    if (!guardianToDelete) return;
    try {
      await deleteContact(guardianToDelete.id);
      const currentFormValues = form.getValues();
      const payloadValues = { ...currentFormValues, has_guardian: false };
      form.clearErrors();
      const patientPayload = preparePatientPayload(payloadValues);
      if (patientPayload) {
        finalizeSubmit(patientPayload);
      } else {
        // Se a preparação falhar após a exclusão, logar o erro é importante
        console.error("Falha ao preparar payload do paciente após exclusão do responsável.");
        alert("Erro: Falha ao preparar os dados do paciente após excluir o responsável.");
      }
    } catch (error) {
      console.error("Erro ao excluir responsável ou atualizar paciente:", error);
      alert(`Erro ao processar a desvinculação: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setShowGuardianDeleteConfirm(false);
      setGuardianToDelete(null);
    }
  };

  const handleTagSelect = (tag: Tag) => {
    setSelectedTags((prev) => {
      if (prev.some((t) => t.id === tag.id)) {
        return prev.filter((t) => t.id !== tag.id);
      } else {
        return [...prev, tag];
      }
    });
  };

  const isLoading = isProcessing || isDeletingContact;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Editar Paciente" : "Novo Paciente"}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-1">
              <TabsList className={cn("grid w-full mb-4", hasGuardian ? "grid-cols-4" : "grid-cols-3")}>
                <TabsTrigger value="basic">Dados Básicos</TabsTrigger>
                <TabsTrigger value="clinical">Dados Clínicos</TabsTrigger>
                {hasGuardian && <TabsTrigger value="guardian">Responsável</TabsTrigger>}
                <TabsTrigger value="additional">Informações Adicionais</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4 mt-0">
                 <FormField control={form.control} name="full_name" render={({ field }) => (<FormItem><FormLabel>Nome Completo</FormLabel><FormControl><Input placeholder="Ex: João Silva" {...field} /></FormControl><FormMessage /></FormItem>)} />
                 <div className="grid grid-cols-2 gap-4">
                   <FormField control={form.control} name="date_of_birth" render={({ field }) => (<FormItem><FormLabel>Data de Nascimento</FormLabel><FormControl><Input type="date" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                   <FormField control={form.control} name="cpf" render={({ field }) => (<FormItem><FormLabel>CPF</FormLabel><FormControl><Input placeholder="000.000.000-00" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                 </div>
                 <div className="grid grid-cols-2 gap-4">
                   <FormField control={form.control} name="phone" render={({ field }) => (<FormItem><FormLabel>Telefone</FormLabel><FormControl><PhoneInput value={field.value ?? ""} onValueChange={field.onChange} /></FormControl><FormMessage /></FormItem>)} />
                   <FormField control={form.control} name="email" render={({ field }) => (<FormItem><FormLabel>Email</FormLabel><FormControl><Input type="email" placeholder="<EMAIL>" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                 </div>
                 <FormField control={form.control} name="address" render={({ field }) => (<FormItem><FormLabel>Endereço</FormLabel><FormControl><Input placeholder="Rua, número, complemento" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                 <div className="grid grid-cols-2 gap-4">
                   <FormField control={form.control} name="city" render={({ field }) => (<FormItem><FormLabel>Cidade</FormLabel><FormControl><Input placeholder="Cidade" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                   <FormField control={form.control} name="state" render={({ field }) => (<FormItem><FormLabel>Estado</FormLabel><FormControl><Input placeholder="UF" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                 </div>
              </TabsContent>

              <TabsContent value="clinical" className="space-y-4 mt-0">
                 <FormField control={form.control} name="status" render={({ field }) => (<FormItem><FormLabel>Status do Paciente</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value}><FormControl><SelectTrigger><SelectValue placeholder="Selecione o status" /></SelectTrigger></FormControl><SelectContent><SelectItem value="active">Ativo</SelectItem><SelectItem value="evaluation">Em Avaliação</SelectItem><SelectItem value="paused">Pausado</SelectItem><SelectItem value="waiting">Lista de Espera</SelectItem><SelectItem value="discharged">Em Alta</SelectItem></SelectContent></Select><FormMessage /></FormItem>)} />
                 <FormField control={form.control} name="therapy_type" render={({ field }) => (<FormItem><FormLabel>Tipo de Terapia</FormLabel><Select onValueChange={field.onChange} defaultValue={field.value ?? undefined}><FormControl><SelectTrigger><SelectValue placeholder="Selecione o tipo" /></SelectTrigger></FormControl><SelectContent><SelectItem value="language">Linguagem</SelectItem><SelectItem value="speech">Fala</SelectItem><SelectItem value="voice">Voz</SelectItem><SelectItem value="dysphagia">Disfagia</SelectItem><SelectItem value="fluency">Fluência</SelectItem><SelectItem value="orofacial">Motricidade Orofacial</SelectItem><SelectItem value="aac">Comunicação Alternativa</SelectItem><SelectItem value="other">Outro</SelectItem></SelectContent></Select><FormMessage /></FormItem>)} />
                 <FormField control={form.control} name="school" render={({ field }) => (<FormItem><FormLabel>Escola</FormLabel><FormControl><Input placeholder="Nome da escola (se aplicável)" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                 <FormField control={form.control} name="has_guardian" render={({ field }) => (<FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"><div className="space-y-0.5"><FormLabel>Possui responsável</FormLabel><FormDescription className="text-xs text-muted-foreground">Marque se o paciente possui um responsável legal</FormDescription></div><FormControl><Switch checked={field.value} onCheckedChange={field.onChange} /></FormControl></FormItem>)} />
                 <div className="space-y-2 pt-4">
                   <FormLabel>Etiquetas</FormLabel>
                   <div className="border rounded-lg p-3">
                     <PatientTags tags={selectedTags} editable={true} onAddTag={onAddTag} onRemoveTag={(tagId) => { setSelectedTags(prev => prev.filter(t => t.id !== tagId)); onRemoveTag?.(tagId); }} />
                     {availableTags.length > 0 && (
                       <div className="mt-3 pt-3 border-t">
                         <p className="text-xs text-muted-foreground mb-2">Selecione das etiquetas existentes:</p>
                         <div className="flex flex-wrap gap-1">
                           {availableTags.filter(tag => !selectedTags.some(t => t.id === tag.id)).map(tag => (<Button key={tag.id} variant="outline" size="sm" className="h-6 text-xs" onClick={() => handleTagSelect(tag)}>{tag.name}</Button>))}
                         </div>
                       </div>
                     )}
                   </div>
                 </div>
              </TabsContent>

              <TabsContent value="additional" className="space-y-4 mt-0">
                 <FormField control={form.control} name="notes" render={({ field }) => (<FormItem><FormLabel>Observações</FormLabel><FormControl><Textarea placeholder="Observações relevantes sobre o paciente..." className="min-h-32 resize-none" {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>)} />
                 <Alert> <AlertCircle className="h-4 w-4" /> <AlertDescription>Informações adicionais podem ser adicionadas no prontuário.</AlertDescription> </Alert>
              </TabsContent>

              {hasGuardian && (
                <TabsContent value="guardian" className="mt-0">
                  <ContactFormSection
                    control={form.control}
                    form={form}
                    guardianSelectionMode={guardianSelectionMode}
                    setGuardianSelectionMode={setGuardianSelectionMode}
                    selectedGuardianId={selectedGuardianId}
                    setSelectedGuardianId={setSelectedGuardianId}
                    existingContacts={existingContacts}
                    isLoadingContacts={isLoadingContacts}
                    contactsError={contactsError}
                  />
                </TabsContent>
              )}
            </Tabs>
            <DialogFooter className="mt-6 gap-2 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>Cancelar</Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (<><Loader2 className="mr-2 h-4 w-4 animate-spin" />Salvando...</>) : isEditing ? ("Salvar Alterações") : ("Cadastrar Paciente")}
              </Button>
            </DialogFooter>
          </form>
        </Form>

        <ConfirmDeleteDialog
          open={showGuardianDeleteConfirm}
          onOpenChange={setShowGuardianDeleteConfirm}
          title="Confirmar Desvinculação e Exclusão"
          description={`Tem certeza que deseja desvincular ${guardianToDelete?.name || 'o responsável'} como responsável principal?`}
          itemName={guardianToDelete?.name || ''}
          itemType="responsável"
          isDeleting={isDeletingContact}
          onConfirm={handleGuardianDeleteConfirm}
          customWarning={
            <>
              Esta ação também irá{' '}
              <strong>excluir permanentemente</strong> o contato{' '}
              <strong>{guardianToDelete?.name}</strong> do sistema. Esta ação não pode ser desfeita.
            </>
          }
        />
      </DialogContent>
    </Dialog>
  );
}

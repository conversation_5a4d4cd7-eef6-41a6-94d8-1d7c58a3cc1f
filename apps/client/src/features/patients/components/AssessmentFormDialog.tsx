import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from '@/shared/ui/dialog';
import { But<PERSON> } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Textarea } from '@/shared/ui/textarea';
import { Label } from '@/shared/ui/label';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/shared/ui/form';
import { CreateAssessmentData, createAssessmentSchema } from '@/features/assessment/types/assessment.schema';
import { useAssessmentMutations } from '@/features/assessment/hooks/useAssessmentMutations';

interface AssessmentFormDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  patientId: string;
}

export function AssessmentFormDialog({ isOpen, onOpenChange, patientId }: AssessmentFormDialogProps) {
  const form = useForm<CreateAssessmentData>({
    resolver: zodResolver(createAssessmentSchema),
    defaultValues: {
      title: '',
      content: '',
    },
  });

  const { createAssessment, isCreating } = useAssessmentMutations(patientId);

  const onSubmit = (data: CreateAssessmentData) => {
    createAssessment(data, {
      onSuccess: () => {
        onOpenChange(false);
        form.reset();
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Nova Avaliação</DialogTitle>
          <DialogDescription>Registre uma nova avaliação para o paciente.</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título da Avaliação</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Avaliação de Linguagem" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Conteúdo da Avaliação</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Descreva os resultados e observações da avaliação..."
                      rows={10}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
              <Button type="submit" disabled={isCreating}>
                {isCreating ? 'Salvando...' : 'Salvar Avaliação'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
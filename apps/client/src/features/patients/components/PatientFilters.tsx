import { useState } from "react";
import { Search, SlidersHorizontal, Calendar, X } from "lucide-react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { PatientStatusBadge, PatientStatus } from "./PatientStatusBadge";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/ui/popover";
import { Label } from "@/shared/ui/label";
import { Checkbox } from "@/shared/ui/checkbox";
import { Badge } from "@/shared/ui/badge";
import { cn } from "@/shared/lib/utils";
import { Tag } from "./PatientTags";
import { PatientFilterOptions as PatientFilterOptionsType, TherapyType } from "@/features/patients/hooks/usePatientQuery";

const defaultFilters: PatientFilterOptionsType = {
  search: "",
  status: [],
  ageGroups: [],
  therapyTypes: [],
  tags: [],
  hasPendingPayments: false,
};

interface PatientFiltersProps {
  onFilterChange: (filters: PatientFilterOptionsType) => void;
  availableTags?: Tag[];
  className?: string;
  initialFilters?: Partial<PatientFilterOptionsType>;
}

export function PatientFilters({
  onFilterChange,
  availableTags = [],
  className,
  initialFilters = {}
}: PatientFiltersProps) {
  const [filters, setFilters] = useState<PatientFilterOptionsType>({
    ...defaultFilters,
    ...initialFilters
  });
  const [advancedOpen, setAdvancedOpen] = useState(false);
  const [searchInput, setSearchInput] = useState(filters.search);

  // Lista de status possíveis para pacientes
  const patientStatusOptions: { value: PatientStatus; label: string }[] = [
    { value: "active", label: "Ativo" },
    { value: "evaluation", label: "Em Avaliação" },
    { value: "discharged", label: "Em Alta" },
    { value: "paused", label: "Pausado" },
    { value: "waiting", label: "Lista de Espera" },
  ];

  // Lista de faixas etárias
  const ageGroupOptions = [
    { value: "first-infancy", label: "Primeira infância (0-3 anos)" },
    { value: "children", label: "Infantil (4-11 anos)" },
    { value: "teen", label: "Adolescente (12-17 anos)" },
    { value: "adult", label: "Adulto (18-59 anos)" },
    { value: "elder", label: "Idoso (60+ anos)" },
  ];

  // Lista de tipos de terapia
  const therapyTypeOptions = [
    { value: "language", label: "Linguagem" },
    { value: "speech", label: "Fala" },
    { value: "voice", label: "Voz" },
    { value: "dysphagia", label: "Disfagia" },
    { value: "fluency", label: "Fluência" },
    { value: "orofacial", label: "Motricidade Orofacial" },
    { value: "aac", label: "Comunicação Alternativa" },
  ];

  // Atualiza filtros e propaga mudanças
  const updateFilters = (updatedFilters: Partial<PatientFilterOptionsType>) => {
    const newFilters = { ...filters, ...updatedFilters };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Lidar com mudanças no campo de busca
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  // Aplicar mudança de busca quando pressionar Enter ou perder o foco
  const applySearch = () => {
    updateFilters({ search: searchInput });
  };

  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      applySearch();
    }
  };

  // Lidar com mudanças de status
  const handleStatusChange = (status: PatientStatus, checked: boolean) => {
    const currentStatus = filters.status || [];
    const newStatus = checked
      ? [...currentStatus, status]
      : currentStatus.filter(s => s !== status);
    updateFilters({ status: newStatus });
  };

  // Lidar com mudanças de faixa etária
  const handleAgeGroupChange = (ageGroup: string, checked: boolean) => {
    const currentAgeGroups = filters.ageGroups || [];
    const newAgeGroups = checked
      ? [...currentAgeGroups, ageGroup]
      : currentAgeGroups.filter(a => a !== ageGroup);
    updateFilters({ ageGroups: newAgeGroups });
  };

  // Lidar com mudanças de tipo de terapia
  const handleTherapyTypeChange = (therapyType: string, checked: boolean) => {
    const currentTherapyTypes = filters.therapyTypes || [];
    const newTherapyTypes = checked
      ? [...currentTherapyTypes, therapyType as TherapyType]
      : currentTherapyTypes.filter(t => t !== therapyType);
    updateFilters({ therapyTypes: newTherapyTypes });
  };

  // Lidar com mudanças de etiquetas
  const handleTagChange = (tagId: string, checked: boolean) => {
    const currentTags = filters.tags || [];
    const newTags = checked
      ? [...currentTags, tagId]
      : currentTags.filter(t => t !== tagId);
    updateFilters({ tags: newTags });
  };

  const handlePendingPaymentsChange = (checked: boolean) => {
    updateFilters({ hasPendingPayments: checked });
  };

  // Limpar todos os filtros
  const clearAllFilters = () => {
    setFilters(defaultFilters);
    setSearchInput("");
    onFilterChange(defaultFilters);
  };

  // Remover um filtro específico
  const removeFilter = (type: keyof PatientFilterOptionsType, value?: string) => {
    if (type === "search") {
      updateFilters({ search: "" });
      setSearchInput("");
    } else if (type === "status" && value) {
      updateFilters({
        status: (filters.status || []).filter(s => s !== value)
      });
    } else if (type === "ageGroups" && value) {
      updateFilters({
        ageGroups: (filters.ageGroups || []).filter(a => a !== value)
      });
    } else if (type === "therapyTypes" && value) {
      updateFilters({
        therapyTypes: (filters.therapyTypes || []).filter(t => t !== value)
      });
    } else if (type === "tags" && value) {
      updateFilters({
        tags: (filters.tags || []).filter(t => t !== value)
      });
    } else if (type === "hasPendingPayments") {
      updateFilters({ hasPendingPayments: false });
    }
  };

  // Verificar se existem filtros ativos
  const hasActiveFilters = () => {
    return (
      !!filters.search ||
      (filters.status && filters.status.length > 0) ||
      (filters.ageGroups && filters.ageGroups.length > 0) ||
      (filters.therapyTypes && filters.therapyTypes.length > 0) ||
      (filters.tags && filters.tags.length > 0) ||
      !!filters.hasPendingPayments
    );
  };

  // Obter rótulo para filtro de faixa etária
  const getAgeGroupLabel = (value: string) => {
    return ageGroupOptions.find(o => o.value === value)?.label || value;
  };

  // Obter rótulo para tipo de terapia
  const getTherapyTypeLabel = (value: string) => {
    return therapyTypeOptions.find(o => o.value === value)?.label || value;
  };

  // Obter rótulo para etiquetas
  const getTagLabel = (value: string) => {
    return availableTags.find(t => t.id === value)?.name || value;
  };

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            className="pl-9 pr-4"
            placeholder="Buscar por nome, telefone, email..."
            value={searchInput}
            onChange={handleSearchChange}
            onKeyDown={handleSearchKeyDown}
            onBlur={applySearch}
          />
        </div>

        <Popover open={advancedOpen} onOpenChange={setAdvancedOpen}>
          <PopoverTrigger asChild>
            <Button
              variant={hasActiveFilters() ? "default" : "outline"}
              size="sm"
              className="h-10"
            >
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Filtros
              {hasActiveFilters() && (
                <Badge className="ml-2 h-5 px-1.5 bg-primary-foreground text-primary">
                  {Object.values(filters).filter(v =>
                    Array.isArray(v) ? v.length > 0 : v === true || (typeof v === 'string' && v !== "")
                  ).length}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-4" align="end">
            <div className="space-y-4">
              <h4 className="font-medium mb-2">Filtros</h4>

              {/* Status do paciente */}
              <div className="space-y-2">
                <Label className="text-xs">Status</Label>
                <div className="grid grid-cols-2 gap-2">
                  {patientStatusOptions.map((status) => (
                    <div key={status.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status.value}`}
                        checked={(filters.status || []).includes(status.value)}
                        onCheckedChange={(checked) =>
                          handleStatusChange(status.value, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`status-${status.value}`}
                        className="text-xs cursor-pointer"
                      >
                        {status.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Faixa etária */}
              <div className="space-y-2">
                <Label className="text-xs">Faixa etária</Label>
                <div className="grid grid-cols-2 gap-2">
                  {ageGroupOptions.map((age) => (
                    <div key={age.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`age-${age.value}`}
                        checked={(filters.ageGroups || []).includes(age.value)}
                        onCheckedChange={(checked) =>
                          handleAgeGroupChange(age.value, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`age-${age.value}`}
                        className="text-xs cursor-pointer"
                      >
                        {age.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Tipo de terapia */}
              <div className="space-y-2">
                <Label className="text-xs">Tipo de terapia</Label>
                <div className="grid grid-cols-2 gap-2">
                  {therapyTypeOptions.map((therapy) => (
                    <div key={therapy.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`therapy-${therapy.value}`}
                        checked={(filters.therapyTypes || []).includes(therapy.value as TherapyType)}
                        onCheckedChange={(checked) =>
                          handleTherapyTypeChange(therapy.value, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`therapy-${therapy.value}`}
                        className="text-xs cursor-pointer"
                      >
                        {therapy.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Etiquetas */}
              {availableTags.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-xs">Etiquetas</Label>
                  <div className="max-h-28 overflow-y-auto p-1 -mx-1">
                    <div className="grid grid-cols-2 gap-2">
                      {availableTags.map((tag) => (
                        <div key={tag.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`tag-${tag.id}`}
                            checked={(filters.tags || []).includes(tag.id)}
                            onCheckedChange={(checked) =>
                              handleTagChange(tag.id, checked as boolean)
                            }
                          />
                          <Label
                            htmlFor={`tag-${tag.id}`}
                            className="text-xs cursor-pointer"
                          >
                            {tag.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Outras opções */}
              <div className="space-y-2">
                <Label className="text-xs">Outras opções</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="has-pending-payments"
                      checked={filters.hasPendingPayments}
                      onCheckedChange={(checked) =>
                        handlePendingPaymentsChange(checked as boolean)
                      }
                    />
                    <Label
                      htmlFor="has-pending-payments"
                      className="text-xs cursor-pointer"
                    >
                      Com pendências financeiras
                    </Label>
                  </div>
                </div>
              </div>

              {/* Rodapé */}
              <div className="flex justify-between pt-2 border-t">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  disabled={!hasActiveFilters()}
                >
                  Limpar filtros
                </Button>
                <Button
                  size="sm"
                  onClick={() => setAdvancedOpen(false)}
                >
                  Aplicar
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        <Button
          variant="outline"
          size="sm"
          className="h-10 flex items-center gap-1"
        >
          <Calendar className="h-4 w-4 mr-1" />
          <span className="hidden sm:inline">Agenda</span> Hoje
        </Button>
      </div>

      {/* Mostrar filtros ativos */}
      {hasActiveFilters() && (
        <div className="flex flex-wrap gap-1.5 pt-2">
          {filters.search && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <span>Busca: {filters.search}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 hover:bg-secondary/80"
                onClick={() => removeFilter("search")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {(filters.status || []).map(status => (
            <Badge key={status} variant="secondary" className="flex items-center gap-1">
              <PatientStatusBadge status={status} showIcon={false} className="bg-transparent border-none p-0" />
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 hover:bg-secondary/80"
                onClick={() => removeFilter("status", status)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          {(filters.ageGroups || []).map(ageGroup => (
            <Badge key={ageGroup} variant="secondary" className="flex items-center gap-1">
              <span>Idade: {getAgeGroupLabel(ageGroup)}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 hover:bg-secondary/80"
                onClick={() => removeFilter("ageGroups", ageGroup)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          {(filters.therapyTypes || []).map(therapyType => (
            <Badge key={therapyType} variant="secondary" className="flex items-center gap-1">
              <span>Terapia: {getTherapyTypeLabel(therapyType)}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 hover:bg-secondary/80"
                onClick={() => removeFilter("therapyTypes", therapyType)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          {(filters.tags || []).map(tag => (
            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
              <span>Tag: {getTagLabel(tag)}</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 hover:bg-secondary/80"
                onClick={() => removeFilter("tags", tag)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          {filters.hasPendingPayments && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <span>Com pendências financeiras</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 hover:bg-secondary/80"
                onClick={() => removeFilter("hasPendingPayments")}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
import { MaskedInput } from '@/shared/components/MaskedInput';
import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/shared/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Trash2 } from "lucide-react";
import { Contact, UpdateContactData } from '@/shared/types/contact.schema';
import { useContactMutations } from '@/features/contacts/hooks/useContactMutations';
import { ConfirmDeleteDialog } from "@/shared/components/ConfirmDeleteDialog";

interface EditContactDialogProps {
  contact: Contact;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  patientId: string;
  onContactUpdate?: () => void;
}

type EditableContactData = Partial<UpdateContactData>;

export function EditContactDialog({
  contact,
  open,
  onOpenChange,
  patientId,
  onContactUpdate
}: EditContactDialogProps) {
  const { updateContact, deleteContact, isUpdating, isDeleting } = useContactMutations();
  const [editedData, setEditedData] = useState<EditableContactData>({});
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (contact && open) {
      setEditedData({
        name: contact.name || '',
        relationship_type: contact.relationship_type || '',
        cpf: contact.cpf || '',
        rg: contact.rg || '',
        phone: contact.phone || '',
        email: contact.email || '',
        address: contact.address || '',
        city: contact.city || '',
        state: contact.state || '',
        date_of_birth: contact.date_of_birth ? contact.date_of_birth.split('T')[0] : '',
        gender: contact.gender || '',
        marital_status: contact.marital_status || '',
        ethnicity: contact.ethnicity || '',
        nationality: contact.nationality || '',
        naturalness: contact.naturalness || '',
        occupation: contact.occupation || '',
        is_active: contact.is_active ?? true,
        notes: contact.notes || '',
      });
    } else if (!open) {
        setEditedData({});
    }
  }, [contact, open]);

  const handleInputChange = (field: keyof EditableContactData, value: string | boolean | null) => {
     const finalValue = typeof value === 'string' && value.trim() === '' && field !== 'name' && field !== 'relationship_type' ? null : value;
    setEditedData((prev) => ({ ...prev, [field]: finalValue }));
  };

  const handleSaveChanges = () => {
    const payload: UpdateContactData = {
        ...editedData,
        is_active: typeof editedData.is_active === 'boolean' ? editedData.is_active : true,
        date_of_birth: editedData.date_of_birth || null,
    };
    // Chamar updateContact (que chama mutate) com id e payload.
    // As callbacks onSuccess/onError são tratadas dentro do hook useContactMutations.
    updateContact({ id: contact.id, data: payload });
    // Fechar o diálogo otimisticamente. Se a mutação falhar, o usuário verá um toast de erro.
    onOpenChange(false);
    // Chamar o callback onContactUpdate imediatamente também.
    // A invalidação da query ocorrerá no onSuccess do hook.
    onContactUpdate?.();
  };

  const handleDeleteContact = () => {
    deleteContact(contact.id);
    onOpenChange(false);
    onContactUpdate?.();
  };

  const isLoading = isUpdating || isDeleting;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md md:max-w-lg lg:max-w-xl">
        <DialogHeader>
          <DialogTitle>Editar Contato</DialogTitle>
          <DialogDescription>
            Modifique os dados do contato abaixo. Clique em salvar para aplicar as alterações.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto pr-6 pl-2">
          {/* Campos do formulário */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-name" className="text-right">
              Nome*
            </Label>
            <Input
              id="contact-name"
              value={editedData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="col-span-3"
              disabled={isLoading}
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-relationship_type" className="text-right">
              Relação/Tipo*
            </Label>
            <Input
              id="contact-relationship_type"
              value={editedData.relationship_type || ''}
              onChange={(e) => handleInputChange('relationship_type', e.target.value)}
              className="col-span-3"
              disabled={isLoading}
               required
            />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-phone" className="text-right">
              Telefone
            </Label>
            <Input
              id="contact-phone"
              value={editedData.phone || ''}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="col-span-3"
              disabled={isLoading}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-email" className="text-right">
              Email
            </Label>
            <Input
              id="contact-email"
              type="email"
              value={editedData.email || ''}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="col-span-3"
              disabled={isLoading}
            />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-cpf" className="text-right">
              CPF
            </Label>
            <MaskedInput
              id="contact-cpf"
              mask="999.999.999-99"
              value={editedData.cpf || ''}
              onChange={(value) => handleInputChange('cpf', value)}
              onChangeRaw={(rawValue) => handleInputChange('cpf', rawValue)}
              className="col-span-3"
              disabled={isLoading}
            />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-rg" className="text-right">
              RG
            </Label>
            <Input
              id="contact-rg"
              value={editedData.rg || ''}
              onChange={(e) => handleInputChange('rg', e.target.value)}
              className="col-span-3"
              disabled={isLoading}
            />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-dob" className="text-right">
              Data Nasc.
            </Label>
            <Input
              id="contact-dob"
              type="date"
              value={editedData.date_of_birth || ''}
              onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
              className="col-span-3"
              disabled={isLoading}
            />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-address" className="text-right">
              Endereço
            </Label>
            <Input
              id="contact-address"
              value={editedData.address || ''}
              onChange={(e) => handleInputChange('address', e.target.value)}
              className="col-span-3"
              disabled={isLoading}
            />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-city" className="text-right">
              Cidade
            </Label>
            <Input
              id="contact-city"
              value={editedData.city || ''}
              onChange={(e) => handleInputChange('city', e.target.value)}
              className="col-span-3"
              disabled={isLoading}
            />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="contact-state" className="text-right">
              Estado
            </Label>
            <Input
              id="contact-state"
              value={editedData.state || ''}
              onChange={(e) => handleInputChange('state', e.target.value)}
              className="col-span-3"
              disabled={isLoading}
            />
          </div>
           <div className="grid grid-cols-4 items-center gap-4">
             <Label htmlFor="contact-occupation" className="text-right">
               Ocupação
             </Label>
             <Input
               id="contact-occupation"
               value={editedData.occupation || ''}
               onChange={(e) => handleInputChange('occupation', e.target.value)}
               className="col-span-3"
               disabled={isLoading}
             />
           </div>
            <div className="grid grid-cols-4 items-center gap-4">
             <Label htmlFor="contact-notes" className="text-right">
               Notas
             </Label>
             <Input
               id="contact-notes"
               value={editedData.notes || ''}
               onChange={(e) => handleInputChange('notes', e.target.value)}
               className="col-span-3"
               disabled={isLoading}
             />
           </div>
           {/* Adicionar outros campos como gender, marital_status, etc. se necessário */}

        </div>
        <DialogFooter className="flex justify-between">
          <Button
            variant="destructive"
            onClick={() => setShowDeleteConfirm(true)}
            disabled={isLoading}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Excluir contato
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
              Cancelar
            </Button>
            <Button type="submit" onClick={handleSaveChanges} disabled={isLoading}>
              {isUpdating ? 'Salvando...' : 'Salvar Alterações'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>

      {/* Diálogo de confirmação para excluir contato */}
      <ConfirmDeleteDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Excluir Contato"
        description="Tem certeza que deseja excluir permanentemente o contato"
        itemName={contact.name}
        itemType="contato"
        isDeleting={isDeleting}
        onConfirm={handleDeleteContact}
        customWarning={
          <>Esta ação não pode ser desfeita. O contato será removido completamente do sistema.</>
        }
      />
    </Dialog>
  );
}

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/shared/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/shared/ui/form";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Textarea } from "@/shared/ui/textarea";
import { PhoneInput } from "@/features/auth/components/forms/phoneInput";
import { Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { Patient } from "@/features/patients/types/patient.schema";
import { Contact } from "@/shared/types/contact.schema"; // Adicionar import

// Schema para o formulário de contato
const contactFormSchema = z.object({
  name: z.string().min(3, { message: "Nome deve ter pelo menos 3 caracteres" }),
  relationship_type: z.string().min(1, { message: "Tipo de relação é obrigatório" }),
  phone: z.string().optional(),
  email: z.string().email({ message: "Email inválido" }).optional().or(z.literal("")),
  address: z.string().optional(),
  notes: z.string().optional(),
  patient_id: z.string().uuid().optional(),
});

export type ContactFormValues = z.infer<typeof contactFormSchema>;

// Interface para o componente
interface ContactFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ContactFormValues) => void;
  onCancel: () => void;
  contact?: Contact | null; // Usar o tipo Contact importado
  isProcessing?: boolean;
  patients?: Patient[];
  defaultPatientId?: string;
}

export function ContactFormDialog({
  open,
  onOpenChange,
  onSubmit,
  onCancel,
  contact,
  isProcessing = false,
  patients = [],
  defaultPatientId,
}: ContactFormDialogProps) {
  const isEditing = !!contact;

  // Configurar o formulário
  const form = useForm<ContactFormValues>({
    resolver: zodResolver(contactFormSchema),
    defaultValues: {
      name: "",
      relationship_type: "",
      phone: "",
      email: "",
      address: "",
      notes: "",
      patient_id: defaultPatientId,
    },
  });

  // Preencher o formulário ao editar
  useEffect(() => {
    if (contact) {
      form.reset({
        name: contact.name,
        relationship_type: contact.relationship_type,
        phone: contact.phone || "",
        email: contact.email || "",
        address: contact.address || "",
        notes: contact.notes || "",
        patient_id: contact.patient_id ?? undefined, // Converter null para undefined
      });
    } else {
      form.reset({
        name: "",
        relationship_type: "",
        phone: "",
        email: "",
        address: "",
        notes: "",
        patient_id: defaultPatientId, // Usar defaultPatientId apenas na criação
      });
    }
  }, [contact, form, defaultPatientId]);

  // Tipos de relacionamento (filtrar 'guardian' neste contexto)
  const relationshipTypes = [
    { value: "parent", label: "Mãe/Pai" },
    // { value: "guardian", label: "Responsável Legal" }, // Removido deste formulário
    { value: "relative", label: "Outro Familiar" },
    { value: "school", label: "Escola/Professor" },
    { value: "professional", label: "Profissional de Saúde" },
    { value: "other", label: "Outro" },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Editar Contato" : "Novo Contato"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Nome do Contato */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome</FormLabel>
                  <FormControl>
                    <Input placeholder="Nome completo" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tipo de Relacionamento */}
            <FormField
              control={form.control}
              name="relationship_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo de Relacionamento</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {relationshipTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Paciente Associado - Mostrar apenas na criação */}
            {!isEditing && patients.length > 0 && (
              <FormField
                control={form.control}
                name="patient_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Vincular a Paciente (Opcional)</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(value || undefined)}
                      defaultValue={field.value ?? undefined}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione um paciente" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {patients.map((patient) => (
                          <SelectItem key={patient.id} value={patient.id}>
                            {patient.full_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Vincule este novo contato a um paciente existente.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Telefone */}
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Telefone</FormLabel>
                  <FormControl>
                    <PhoneInput
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Email */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...field}
                      value={field.value ?? ''} // Lidar com null
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Endereço */}
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Endereço</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Endereço completo"
                      {...field}
                      value={field.value ?? ''} // Lidar com null
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Observações */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Observações</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Informações adicionais sobre este contato..."
                      className="resize-none h-20"
                      {...field}
                      value={field.value ?? ''} // Lidar com null
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isProcessing}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isProcessing}>
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : isEditing ? (
                  "Salvar Alterações"
                ) : (
                  "Salvar Contato"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

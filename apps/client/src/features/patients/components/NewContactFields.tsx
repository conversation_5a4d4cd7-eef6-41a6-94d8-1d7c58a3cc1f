import { Control } from "react-hook-form";
import {
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/ui/form";
import { Input } from "@/shared/ui/input";
import { PhoneInput } from "@/features/auth/components/forms/phoneInput";
import { CreateContactData } from "@/shared/types/contact.schema"; // Usar o tipo do schema

interface NewContactFieldsProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>; // Receber o controle do formulário pai
  fieldNamePrefix?: string; // Tornar o prefixo opcional
}

// Usar CreateContactData para tipar os nomes dos campos base
type BaseFieldName = keyof CreateContactData;

export function NewContactFields({ control, fieldNamePrefix = "" }: NewContactFieldsProps) {
  // Helper para gerar o nome completo do campo com prefixo
  const getFieldName = (baseName: BaseFieldName): string => {
    return `${fieldNamePrefix}${baseName}`;
  };

  return (
    <div className="space-y-4">
      {/* Usar getFieldName para construir o nome do campo */}
      <FormField
        control={control}
        name={getFieldName('name')}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Nome Completo*</FormLabel>
            <FormControl>
              <Input placeholder="Nome do contato" {...field} required />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name={getFieldName('relationship_type')}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Relação/Tipo*</FormLabel>
            <FormControl>
              {/* Considerar usar um Select aqui se os tipos forem fixos */}
              <Input placeholder="Ex: Pai, Mãe, Escola, Profissional" {...field} required />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={control}
          name={getFieldName('cpf')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>CPF</FormLabel>
              <FormControl>
                <Input placeholder="000.000.000-00" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name={getFieldName('rg')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>RG</FormLabel>
              <FormControl>
                <Input placeholder="Número do RG" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={control}
          name={getFieldName('phone')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Telefone</FormLabel>
              <FormControl>
                <PhoneInput
                  value={field.value || ""}
                  onValueChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name={getFieldName('email')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <FormField
        control={control}
        name={getFieldName('address')}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Endereço</FormLabel>
            <FormControl>
              <Input placeholder="Rua, número, complemento" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={control}
          name={getFieldName('city')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cidade</FormLabel>
              <FormControl>
                <Input placeholder="Cidade" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name={getFieldName('state')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Estado</FormLabel>
              <FormControl>
                <Input placeholder="UF" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <FormField
        control={control}
        name={getFieldName('date_of_birth')}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Data Nasc.</FormLabel>
            <FormControl>
              <Input type="date" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={control}
          name={getFieldName('gender')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Gênero</FormLabel>
              <FormControl>
                <Input placeholder="Gênero" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name={getFieldName('marital_status')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Estado Civil</FormLabel>
              <FormControl>
                <Input placeholder="Estado Civil" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <FormField
          control={control}
          name={getFieldName('ethnicity')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Etnia</FormLabel>
              <FormControl>
                <Input placeholder="Etnia" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name={getFieldName('nationality')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nacionalidade</FormLabel>
              <FormControl>
                <Input placeholder="Nacionalidade" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name={getFieldName('naturalness')}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Naturalidade</FormLabel>
              <FormControl>
                <Input placeholder="Naturalidade" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <FormField
        control={control}
        name={getFieldName('occupation')}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Ocupação</FormLabel>
            <FormControl>
              <Input placeholder="Profissão" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { <PERSON><PERSON> } from "@/shared/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { ScrollArea } from "@/shared/ui/scroll-area";
import { Badge } from "@/shared/ui/badge";
import { Search, Loader2, PlusCircle, ArrowLeft } from "lucide-react";
import { createContactSchema, Contact } from "@/shared/types/contact.schema";
import { useContactQuery } from "@/features/contacts/hooks/useContactQuery";
import { CONTACT_QUERY_KEYS } from "@/features/contacts/hooks/constants";
import { usePatientContactMutations } from '@/features/patients/hooks/usePatientContacts';
import { NewContactFields } from './NewContactFields';
import { Form } from '@/shared/ui/form';
import { Separator } from '@/shared/ui/separator';
import { axiosInstance } from '@/shared/lib/api.client';
import { useToast } from '@/shared/hooks/use-toast';

interface AddGuardianDialogProps {
  patientId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onGuardianAdded?: () => void;
}

type NewGuardianFormValues = z.infer<typeof createContactSchema>;

function getRelationshipTypeLabel(type?: string | null): string {
    if (!type) return 'Não especificado';
    const types: Record<string, string> = {
        'parent': 'Mãe/Pai',
        'guardian': 'Responsável Legal',
        'relative': 'Familiar',
        'school': 'Escola',
        'professional': 'Profissional',
        'other': 'Outro'
    };
    return types[type] || type;
}

export function AddGuardianDialog({
  patientId,
  open,
  onOpenChange,
  onGuardianAdded
}: AddGuardianDialogProps) {
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedExistingGuardianId, setSelectedExistingGuardianId] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { allContacts, isLoading: isLoadingContacts } = useContactQuery();
  const { linkContact, isLinking } = usePatientContactMutations();

  const createContactMutation = useMutation({
    mutationFn: (data: NewGuardianFormValues): Promise<Contact> => 
      axiosInstance.post('/protected/contacts', data).then(res => res.data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.all });
      toast({ title: "Contato criado com sucesso!" });
    },
    onError: (error: any) => {
      toast({ title: "Erro ao criar contato", description: error.response?.data?.message || error.message, variant: "destructive" });
    }
  });

  const form = useForm<NewGuardianFormValues>({
    resolver: zodResolver(createContactSchema),
    defaultValues: {
      name: '', relationship_type: 'guardian', phone: '', email: '', cpf: '', rg: '',
      date_of_birth: '', gender: '', marital_status: '', ethnicity: '', nationality: '',
      naturalness: '', occupation: '', address: '', city: '', state: '',
    },
  });

  useEffect(() => {
    if (open) {
      setSearchTerm('');
      setSelectedExistingGuardianId(null);
      setIsCreatingNew(false);
      form.reset();
    }
  }, [open, form]);

  const filteredContacts = (allContacts || []).filter(contact => {
    const isGuardianForAnyPatient = contact.role === 'guardian' && contact.patient_id !== null;
    const matchesSearch =
      contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (contact.email && contact.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (contact.phone && contact.phone.includes(searchTerm));
    return !isGuardianForAnyPatient && matchesSearch;
  });

  const handleLinkExistingContact = () => {
    if (!selectedExistingGuardianId) return;
    linkContact({
      contactId: selectedExistingGuardianId,
      role: 'guardian',
      patientIdToLink: patientId
    }, {
      onSuccess: () => {
        onOpenChange(false);
        onGuardianAdded?.();
      }
    });
  };

  const onSubmitNewContact = async (data: NewGuardianFormValues) => {
    const payload = { ...data, relationship_type: data.relationship_type || 'guardian' };
    try {
      const newContact = await createContactMutation.mutateAsync(payload);
      linkContact({
        contactId: newContact.id,
        role: 'guardian',
        patientIdToLink: patientId
      }, {
        onSuccess: () => {
          onOpenChange(false);
          onGuardianAdded?.();
        }
      });
    } catch (error) {
      // O erro já é tratado no onError da mutação
    }
  };

  const isLoading = isLoadingContacts || isLinking || createContactMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-xl md:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Adicionar Responsável Principal</DialogTitle>
          <DialogDescription>
            {isCreatingNew
              ? "Preencha os dados do novo responsável."
              : "Selecione um contato existente ou cadastre um novo."}
          </DialogDescription>
        </DialogHeader>

        {!isCreatingNew ? (
          <div className="space-y-4 py-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Buscar contatos..."
                className="pl-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={isLoadingContacts}
              />
            </div>
            <ScrollArea className="h-64 rounded-md border p-2">
              <div className="space-y-2 pr-2">
                {isLoadingContacts ? (
                  <p className="text-center py-4 text-muted-foreground text-sm">Carregando...</p>
                ) : filteredContacts.length > 0 ? (
                  filteredContacts.map(contact => (
                    <div
                      key={contact.id}
                      className={`flex items-center justify-between p-2 rounded-md border cursor-pointer ${selectedExistingGuardianId === contact.id ? 'bg-muted ring-2 ring-primary' : 'hover:bg-muted/30'}`}
                      onClick={() => setSelectedExistingGuardianId(contact.id)}
                    >
                      <Label htmlFor={`contact-guardian-${contact.id}`} className="cursor-pointer flex-1">
                        <p className="font-medium">{contact.name}</p>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          {contact.relationship_type && ( <Badge variant="outline" className="text-xs">{getRelationshipTypeLabel(contact.relationship_type)}</Badge> )}
                          {contact.phone && <span>{contact.phone}</span>}
                        </div>
                      </Label>
                      {selectedExistingGuardianId === contact.id && <div className="text-primary">✓</div>}
                    </div>
                  ))
                ) : (
                  <p className="text-center py-4 text-muted-foreground text-sm">
                    {searchTerm ? "Nenhum contato encontrado." : "Nenhum contato disponível."}
                  </p>
                )}
              </div>
            </ScrollArea>
            <Separator />
            <Button
              variant="link"
              className="p-0 h-auto text-sm"
              onClick={() => setIsCreatingNew(true)}
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Ou Cadastrar Novo Responsável
            </Button>
          </div>
        ) : (
          <div className="py-4 max-h-[60vh] overflow-y-auto pr-2">
             <Button
                variant="ghost"
                size="sm"
                className="mb-4 text-muted-foreground"
                onClick={() => setIsCreatingNew(false)}
             >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Voltar para seleção
             </Button>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmitNewContact)} className="space-y-4">
                 <NewContactFields control={form.control} />
              </form>
            </Form>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            Cancelar
          </Button>
          {!isCreatingNew ? (
            <Button onClick={handleLinkExistingContact} disabled={isLoading || !selectedExistingGuardianId}>
              {isLinking ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Vinculando...</> : 'Vincular Selecionado'}
            </Button>
          ) : (
            <Button type="button" onClick={form.handleSubmit(onSubmitNewContact)} disabled={isLoading}>
              {createContactMutation.isPending ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Criando...</> : 'Criar e Vincular'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

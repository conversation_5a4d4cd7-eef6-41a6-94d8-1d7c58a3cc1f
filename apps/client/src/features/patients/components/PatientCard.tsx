import { calculateAge } from "@/shared/lib/date-utils";
import { Card, CardContent } from "@/shared/ui/card";
import { PatientStatusBadge, PatientStatus } from "./PatientStatusBadge";
import { PatientTags, Tag } from "./PatientTags";
import { PatientQuickActions } from "./PatientQuickActions";
import { Patient } from "@/features/patients/types/patient.schema";
import { cn } from "@/shared/lib/utils";
import { Calendar, Mail, Phone, School, AlertCircle } from "lucide-react";
import { Badge } from "@/shared/ui/badge";

interface PatientCardProps {
  patient: Patient;
  onClick?: (patient: Patient) => void;
  onEdit?: (patient: Patient) => void;
  onDelete?: (patient: Patient) => void;
  onSchedule?: (patient: Patient) => void;
  onViewRecords?: (patient: Patient) => void;
  selected?: boolean;
  className?: string;
  tags?: Tag[];
}

export function PatientCard({
  patient,
  onClick,
  onEdit,
  onDelete,
  onSchedule,
  onViewRecords,
  selected = false,
  className,
  tags = []
}: PatientCardProps) {
  const age = patient.date_of_birth
    ? calculateAge(new Date(patient.date_of_birth))
    : patient.age || null;

  const getAgeCategory = () => {
    if (!age) return null;

    if (age < 3) return { label: "Primeira infância", color: "pink" };
    if (age < 12) return { label: "Infantil", color: "blue" };
    if (age < 18) return { label: "Adolescente", color: "purple" };
    if (age < 60) return { label: "Adulto", color: "green" };
    return { label: "Idoso", color: "amber" };
  };

  const ageCategory = getAgeCategory();

  // Mapear status do paciente (adaptando do modelo existente)
  const mapStatus = (): PatientStatus => {
    // No futuro, isso viria diretamente da API
    // Por enquanto, vamos criar um mapeamento básico
    if (patient.notes?.includes("avaliação")) return "evaluation";
    if (patient.notes?.includes("alta")) return "discharged";
    if (patient.notes?.includes("pausa")) return "paused";
    if (patient.notes?.includes("espera")) return "waiting";
    return "active";
  };

  const status = mapStatus();

  // Indicador de pendências financeiras
  const hasFinancialIssues = patient.notes?.includes("pendência financeira") || false;

  return (
    <Card
      className={cn(
        "transition-all hover:shadow-md cursor-pointer",
        selected && "ring-2 ring-primary",
        hasFinancialIssues && "border-l-2 border-l-amber-500",
        className
      )}
      onClick={() => onClick?.(patient)}
    >
      <CardContent className="p-4">
        {/* Cabeçalho com nome e status */}
        <div className="flex justify-between items-start mb-2">
          <div>
            <h3 className="font-medium flex items-center">
              {patient.full_name}
              {hasFinancialIssues && (
                <Badge
                  variant="outline"
                  className="ml-2 bg-amber-100 text-amber-800 dark:bg-amber-900/30 border-amber-200"
                >
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Pendência
                </Badge>
              )}
            </h3>

            {/* Idade e categoria etária */}
            <div className="flex items-center mt-1 text-sm text-muted-foreground">
              {age && (
                <div className="flex items-center">
                  <Calendar className="h-3.5 w-3.5 mr-1" />
                  <span>{age} anos</span>
                </div>
              )}

              {ageCategory && (
                <Badge
                  variant="outline"
                  className={cn(
                    "ml-2 text-xs py-0 px-1.5",
                    `bg-${ageCategory.color}-100 text-${ageCategory.color}-800 dark:bg-${ageCategory.color}-900/30 dark:text-${ageCategory.color}-300 border-${ageCategory.color}-200`
                  )}
                >
                  {ageCategory.label}
                </Badge>
              )}
            </div>
          </div>

          <PatientStatusBadge status={status} />
        </div>

        {/* Informações de contato */}
        <div className="mb-2 space-y-1 text-sm">
          {patient.phone && (
            <div className="flex items-center text-muted-foreground">
              <Phone className="h-3.5 w-3.5 mr-1.5" />
              <span>{patient.phone}</span>
            </div>
          )}

          {patient.email && (
            <div className="flex items-center text-muted-foreground">
              <Mail className="h-3.5 w-3.5 mr-1.5" />
              <span className="truncate">{patient.email}</span>
            </div>
          )}

          {patient.notes && patient.notes.includes("escola:") && (
            <div className="flex items-center text-muted-foreground">
              <School className="h-3.5 w-3.5 mr-1.5" />
              <span className="truncate">
                {patient.notes.split("escola:")[1].split(",")[0].trim()}
              </span>
            </div>
          )}
        </div>

        {/* Etiquetas */}
        <PatientTags tags={tags} maxVisible={2} className="my-2" />

        {/* Ações rápidas */}
        <div className="flex justify-end mt-2 pt-2 border-t">
          <PatientQuickActions
            patient={patient}
            onEdit={onEdit}
            onDelete={onDelete}
            onSchedule={onSchedule}
            onViewRecords={onViewRecords}
            variant="card"
          />
        </div>
      </CardContent>
    </Card>
  );
}
import { Badge } from "@/shared/ui/badge";
import { cn } from "@/shared/lib/utils";
import { CheckCircle, AlertCircle, Clock, XCircle } from "lucide-react";

export type PatientStatus =
  | "active"
  | "evaluation"
  | "discharged"
  | "paused"
  | "waiting";

interface PatientStatusBadgeProps {
  status: PatientStatus;
  className?: string;
  showIcon?: boolean;
}

export function PatientStatusBadge({
  status,
  className,
  showIcon = true
}: PatientStatusBadgeProps) {
  const getStatusConfig = (status: PatientStatus) => {
    switch (status) {
      case "active":
        return {
          label: "Ativo",
          icon: CheckCircle,
          variant: "outline",
          className: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-800"
        };
      case "evaluation":
        return {
          label: "Em Avaliação",
          icon: Clock,
          variant: "outline",
          className: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800"
        };
      case "discharged":
        return {
          label: "Em Alta",
          icon: XCircle,
          variant: "outline",
          className: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 border-purple-200 dark:border-purple-800"
        };
      case "paused":
        return {
          label: "Pausado",
          icon: AlertCircle,
          variant: "outline",
          className: "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 border-amber-200 dark:border-amber-800"
        };
      case "waiting":
        return {
          label: "Lista de Espera",
          icon: Clock,
          variant: "outline",
          className: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-800"
        };
      default:
        return {
          label: "Desconhecido",
          icon: AlertCircle,
          variant: "outline",
          className: "bg-muted text-muted-foreground"
        };
    }
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <Badge
      variant="outline"
      className={cn(config.className, className)}
    >
      {showIcon && <Icon className="h-3 w-3 mr-1" />}
      {config.label}
    </Badge>
  );
}
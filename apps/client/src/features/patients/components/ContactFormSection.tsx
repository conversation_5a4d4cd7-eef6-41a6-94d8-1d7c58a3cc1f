import { UseFormReturn, Control } from "react-hook-form";
import {
  // Remover FormField e FormMessage não utilizados
  FormControl,
  FormItem,
  FormLabel,
  // FormMessage,
} from "@/shared/ui/form";
// Remover imports não utilizados de Input e PhoneInput se NewContactFields os contiver
import { Input } from "@/shared/ui/input";
import { MaskedInput } from '@/shared/components/MaskedInput';
// import { PhoneInput } from "@/features/auth/components/forms/phoneInput.tsx";
import { Contact } from "@/shared/types/contact.schema";
import { Loader2, AlertCircle } from "lucide-react";
import { NewContactFields } from './NewContactFields'; // Importar o componente reutilizável
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { RadioGroup, RadioGroupItem } from "@/shared/ui/radio-group";
import { <PERSON><PERSON>, AlertDescription } from "@/shared/ui/alert";
import { Separator } from "@/shared/ui/separator";

// Definir props com os nomes atualizados
interface ContactFormSectionProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form: UseFormReturn<any>;
  guardianSelectionMode: 'new' | 'existing'; // Manter nome por consistência com PatientFormDialog
  setGuardianSelectionMode: (mode: 'new' | 'existing') => void;
  selectedGuardianId: string | null; // Representa contact_id
  setSelectedGuardianId: (id: string | null) => void;
  existingContacts: Contact[]; // Usar Contact[]
  isLoadingContacts: boolean;
  contactsError: Error | null;
}

// Renomear componente
export function ContactFormSection({
  control,
  form,
  guardianSelectionMode,
  setGuardianSelectionMode,
  selectedGuardianId,
  setSelectedGuardianId,
  existingContacts,
  isLoadingContacts,
  contactsError,
}: ContactFormSectionProps) { // Usar o tipo de props atualizado

  return (
    <div className="space-y-4 rounded-lg border bg-muted/30 p-4">
      <h3 className="text-sm font-medium mb-2">Dados do Responsável</h3>
      <RadioGroup
        value={guardianSelectionMode}
        onValueChange={(value: 'new' | 'existing') => {
          setGuardianSelectionMode(value);
          setSelectedGuardianId(null); // Limpar seleção ao mudar modo
          form.clearErrors(); // Limpar erros relacionados ao responsável
        }}
        className="flex space-x-4"
      >
        <FormItem className="flex items-center space-x-2 space-y-0">
          <FormControl>
            <RadioGroupItem value="new" />
          </FormControl>
          <FormLabel className="font-normal">Cadastrar Novo</FormLabel>
        </FormItem>
        <FormItem className="flex items-center space-x-2 space-y-0">
          <FormControl>
            <RadioGroupItem value="existing" />
          </FormControl>
          <FormLabel className="font-normal">Selecionar Existente</FormLabel>
        </FormItem>
      </RadioGroup>

      <Separator />

      {guardianSelectionMode === 'existing' && (
        <FormItem>
          <FormLabel>Contato Existente</FormLabel>
          {isLoadingContacts ? (
            <div className="flex items-center text-sm text-muted-foreground">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Carregando...
            </div>
          ) : contactsError ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Erro ao carregar contatos: {contactsError.message}
              </AlertDescription>
            </Alert>
          ) : existingContacts.length === 0 ? (
             <Alert variant="default">
               <AlertCircle className="h-4 w-4" />
               <AlertDescription>
                 Nenhum contato cadastrado ainda. Cadastre um novo.
               </AlertDescription>
             </Alert>
          ) : (
            <Select
              // Usar selectedGuardianId que representa o contact_id
              value={selectedGuardianId ?? ""}
              onValueChange={(value) => setSelectedGuardianId(value || null)}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um contato" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {existingContacts.map((contact) => (
                  <SelectItem key={contact.id} value={contact.id.toString()}>
                    {contact.name} {contact.cpf ? `(${contact.cpf})` : ''}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
           {/* Remover FormMessage daqui, a validação principal é feita no form pai */}
           {/* <FormMessage>{form.formState.errors.existing_guardian_id?.message}</FormMessage> */}
        </FormItem>
      )}

      {guardianSelectionMode === 'new' && (
            // Usar o componente NewContactFields
            // Usar NewContactFields e passar o prefixo 'contact_'
            <NewContactFields control={control} fieldNamePrefix="contact_" />
            /* Remover campos duplicados que agora estão em NewContactFields:
              <FormField
                control={control}
                name="guardian_full_name" // Mapear para 'name' no submit
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome Completo (Contato)</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome do contato" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
               <FormField
                control={control}
                name="guardian_relationship"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Relação/Tipo</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: Pai, Mãe, Escola, Profissional" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                 <FormField
                  control={control}
                  name="guardian_cpf"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CPF (Contato)</FormLabel>
                      <FormControl>
                        <MaskedInput 
                          mask="999.999.999-99"
                          placeholder="000.000.000-00" 
                          value={field.value || ''}
                          onChange={field.onChange}
                          onChangeRaw={(rawValue) => field.onChange(rawValue)} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                 <FormField
                  control={control}
                  name="guardian_rg"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>RG (Contato)</FormLabel>
                      <FormControl>
                        <Input placeholder="Número do RG" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                 <FormField
                  control={control}
                  name="guardian_phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Telefone (Contato)</FormLabel>
                      <FormControl>
                         <PhoneInput
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                 <FormField
                  control={control}
                  name="guardian_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email (Contato)</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
               <FormField
                  control={control}
                  name="guardian_address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Endereço (Contato)</FormLabel>
                      <FormControl>
                        <Input placeholder="Rua, número, complemento" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="grid grid-cols-2 gap-4">
                   <FormField
                    control={control}
                    name="guardian_city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cidade (Contato)</FormLabel>
                        <FormControl>
                          <Input placeholder="Cidade" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                  )}
                />
                 <FormField
                  control={control}
                    name="guardian_state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estado (Contato)</FormLabel>
                        <FormControl>
                          <Input placeholder="UF" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                 <FormField
                  control={control}
                  name="guardian_date_of_birth"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Data Nasc. (Contato)</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                      <div className="grid grid-cols-2 gap-4">
                         <FormField
                          control={control}
                          name="guardian_gender"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Gênero</FormLabel>
                              <FormControl>
                                <Input placeholder="Gênero" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                         <FormField
                          control={control}
                          name="guardian_marital_status"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Estado Civil</FormLabel>
                              <FormControl>
                                <Input placeholder="Estado Civil" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                         <FormField
                          control={control}
                          name="guardian_ethnicity"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Etnia</FormLabel>
                              <FormControl>
                                <Input placeholder="Etnia" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                         <FormField
                          control={control}
                          name="guardian_nationality"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Nacionalidade</FormLabel>
                              <FormControl>
                                <Input placeholder="Nacionalidade" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                         <FormField
                          control={control}
                          name="guardian_naturalness"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Naturalidade</FormLabel>
                              <FormControl>
                                <Input placeholder="Naturalidade" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                       <FormField
                          control={control}
                          name="guardian_occupation"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Ocupação</FormLabel>
                              <FormControl>
                                <Input placeholder="Profissão" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
            */
      )}
    </div>
  );
}

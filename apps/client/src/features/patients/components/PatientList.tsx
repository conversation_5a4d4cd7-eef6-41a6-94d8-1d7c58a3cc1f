import { useState } from "react";
import { Patient } from "@/features/patients/types/patient.schema";
import { PatientCard } from "@/features/patients/components/PatientCard";
import { PatientQuickActions } from "@/features/patients/components/PatientQuickActions";
import { ViewToggle } from "@/features/patients/components/ViewToggle";
import { usePatientPagination } from "@/features/patients/hooks/usePatientPagination";
import { useUserPreferences } from "@/shared/hooks/useUserPreferences";
import { EmptyState } from "@/shared/ui/empty-state";
import { Users, Plus } from "lucide-react";
import { Button } from "@/shared/ui/button";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/shared/ui/pagination";
import { Tag } from "@/features/patients/components/PatientTags";

interface PatientListProps {
  patients: Patient[];
  isLoading?: boolean;
  onSelect?: (patient: Patient) => void;
  onEdit?: (patient: Patient) => void;
  onDelete?: (patient: Patient) => void;
  onSchedule?: (patient: Patient) => void;
  onAddPatient?: () => void;
  tags?: Tag[];
  showActions?: boolean;
}

export function PatientList({
  patients,
  isLoading = false,
  onSelect,
  onEdit,
  onDelete,
  onSchedule,
  onAddPatient,
  tags = [],
  showActions = true
}: PatientListProps) {
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(null);
  const [view, setView] = useUserPreferences<'grid' | 'list'>('patientView', 'grid');

  const {
    paginatedPatients,
    currentPage,
    totalPages,
    nextPage,
    prevPage,
    hasNextPage,
    hasPrevPage
  } = usePatientPagination({
    patients,
    pageSize: view === 'grid' ? 12 : 10
  });


  const getPatientTags = (patientId: string) => {
    return tags.filter(tag => {
      // Aqui usamos uma lógica simplificada para demonstração
      // Na implementação real, usaríamos tags realmente associadas ao paciente
      const hash = patientId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
      return hash % (tag.id.length || 1) === 0;
    });
  };

  // Renderizar estado vazio
  if (!isLoading && patients.length === 0) {
    return (
      <EmptyState
        icon={<Users />}
        title="Nenhum paciente encontrado"
        description="Adicione pacientes para começar a gerenciar sua clínica."
        action={
          <Button onClick={onAddPatient}>
            <Plus className="mr-2 h-4 w-4" />
            Adicionar Paciente
          </Button>
        }
      />
    );
  }

  // Renderizar lista de pacientes
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <span className="text-sm text-muted-foreground">
          {patients.length} {patients.length === 1 ? 'paciente' : 'pacientes'}
        </span>

        <ViewToggle view={view} onViewChange={setView} />
      </div>

      {view === 'grid' ? (
        // Visualização em grade
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {paginatedPatients.map(patient => (
            <PatientCard
              key={patient.id}
              patient={patient}
              selected={patient.id === selectedPatientId}
              onClick={(p) => {
                setSelectedPatientId(p.id);
                onSelect?.(p);
              }}
              onEdit={onEdit}
              onDelete={onDelete}
              onSchedule={onSchedule}
              tags={getPatientTags(patient.id)}
            />
          ))}
        </div>
      ) : (
        // Visualização em lista
        <div className="space-y-2">
          {paginatedPatients.map(patient => (
            <div
              key={patient.id}
              className={`flex items-center justify-between p-4 rounded-lg border hover:bg-accent/30 transition-colors ${
                patient.id === selectedPatientId ? "bg-accent/30" : ""
              }`}
              onClick={() => {
                setSelectedPatientId(patient.id);
                onSelect?.(patient);
              }}
            >
              <div className="flex-1">
                <h3 className="font-medium">{patient.full_name}</h3>
                <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                  {patient.phone && (
                    <span>{patient.phone}</span>
                  )}
                  {patient.email && (
                    <span>{patient.email}</span>
                  )}
                </div>
              </div>

              {showActions && (
                <PatientQuickActions
                  patient={patient}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onSchedule={onSchedule}
                  variant="compact"
                />
              )}
            </div>
          ))}
        </div>
      )}

      {/* Paginação */}
      {totalPages > 1 && (
        <Pagination>
          <PaginationContent>
            <PaginationItem>
              <Button variant="ghost" onClick={prevPage} disabled={!hasPrevPage}>
                Anterior
              </Button>
            </PaginationItem>
            <PaginationItem>
              <span className="px-4 py-2 text-sm font-medium">
                Página {currentPage} de {totalPages}
              </span>
            </PaginationItem>
            <PaginationItem>
              <Button variant="ghost" onClick={nextPage} disabled={!hasNextPage}>
                Próxima
              </Button>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
  DialogDescription,
} from '@/shared/ui/dialog';
import { Button } from '@/shared/ui/button';
import { Textarea } from '@/shared/ui/textarea';
import { Label } from '@/shared/ui/label';
import { Input } from '@/shared/ui/input';
import { useState } from 'react';
import { useSessionNotesMutations } from '@/features/notes/hooks/useSessionNotesMutations';
import { toast } from 'sonner';

interface ClinicalNoteFormDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  patientId: string;
  appointmentId?: string | null;
}

export function ClinicalNoteFormDialog({ 
  isOpen, 
  onOpenChange, 
  patientId,
  appointmentId = null
}: ClinicalNoteFormDialogProps) {
  const [title, setTitle] = useState('Observação Clínica');
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { createSessionNote } = useSessionNotesMutations(patientId);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim()) {
      toast.error('O conteúdo da observação é obrigatório');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await createSessionNote({
        title: title.trim() || 'Observação Clínica',
        content: content.trim(),
        patient_id: patientId,
        appointment_id: appointmentId
      }, {
        onSuccess: () => {
          toast.success('Observação clínica registrada com sucesso');
          resetAndClose();
        },
        onError: (error) => {
          console.error('Erro ao salvar observação clínica:', error);
          toast.error('Erro ao salvar a observação clínica. Tente novamente.');
        }
      });
    } catch (error) {
      console.error('Erro ao salvar observação clínica:', error);
      toast.error('Erro ao salvar a observação clínica. Tente novamente.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const resetAndClose = () => {
    setTitle('Observação Clínica');
    setContent('');
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) resetAndClose();
      else onOpenChange(open);
    }}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Nova Observação Clínica</DialogTitle>
          <DialogDescription>Registre uma nova observação sobre o paciente.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4 py-2">
          <div className="space-y-2">
            <Label htmlFor="clinical-note-title">Título</Label>
            <Input 
              id="clinical-note-title" 
              placeholder="Título da observação..."
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="clinical-note-content">Conteúdo</Label>
            <Textarea 
              id="clinical-note-content" 
              placeholder="Descreva a observação clínica..." 
              rows={10}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              required
            />
          </div>
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={resetAndClose}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button 
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Salvando...' : 'Salvar Observação'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 
import { useEffect } from 'react'; // Remover useState
import { useParams } from '@tanstack/react-router';
import { useQueryClient } from '@tanstack/react-query';
import { usePatientQuery, PATIENT_QUERY_KEYS } from '@/features/patients/hooks/usePatientQuery';
// import { usePatientContacts } from '@/features/patients/hooks/usePatientContacts'; // Remover import
import { Skeleton } from '@/shared/ui/skeleton';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/shared/ui/alert';
import { Button } from '@/shared/ui/button';
import { toast } from '@/shared/hooks/use-toast';
import { Contact } from '@/shared/types/contact.schema';

// Remover importações não mais necessárias
// import { EditedPatientData } from './types';
// import { getTherapyTypeFromNotes, getSchoolFromNotes } from './utils'; // Se não forem mais usadas aqui
import { PatientDetailHeader } from './PatientDetailHeader';
import { PatientTabs } from './PatientTabs';
import { OverviewTab } from './OverviewTab';
import { PersonalDataTab } from './PersonalDataTab';
import { HistoryTab } from './HistoryTab';
import { SessionsTab } from './SessionsTab';
import { DocumentsTab } from './DocumentsTab';
import { FinancialTab } from './FinancialTab';

export function PatientDetailPage() {
  const { patientId } = useParams({ from: '/_authenticated/person/$patientId' });
  const queryClient = useQueryClient();

  // Remover states de edição global
  // const [isEditing, setIsEditing] = useState(false);
  // const [editedPatient, setEditedPatient] = useState<EditedPatientData | null>(null);
  // const [editedContactIds, setEditedContactIds] = useState<number[]>([]);
  // const [isSaving, setIsSaving] = useState(false);

  // Data fetching hooks - remover updatePatient se não for mais usado aqui
  const {
    // patients, // Não precisamos mais da lista completa aqui se currentPatient funciona
    isLoading, // Usar isLoading combinado do hook
    isError, // Usar isError combinado do hook
    setCurrentPatientId,
    currentPatient,
    // refetch, // Manter se necessário para outras partes
    refetchDetails, // Manter para passar como onDataUpdate
    // updatePatient,
  } = usePatientQuery({ initialPatientId: patientId });

  // Remover uso de usePatientContacts
  // const { patientContacts, isLoading: isLoadingContacts } = usePatientContacts(numericPatientId);

  // Effect to set the current patient ID, invalidate cache and refetch data
  useEffect(() => {
    if (patientId) {
      setCurrentPatientId(patientId);
      queryClient.invalidateQueries({ queryKey: PATIENT_QUERY_KEYS.detail(patientId) });
      refetchDetails();
    }
    return () => setCurrentPatientId(null);
  }, [patientId, setCurrentPatientId, refetchDetails, queryClient]);

  // Determine the patient data to display
  const patient = currentPatient;

  // Remover useEffect de inicialização de edição global
  // useEffect(() => { ... });

  // Remover handlers de edição global
  // const handleStartEditing = () => { ... };
  // const handleCancelEditing = () => { ... };
  // const handleSaveChanges = async () => { ... };
  // const handleInputChange = (field: keyof EditedPatientData, value: string | boolean) => { ... };

  // Manter handleNewSession
  const handleNewSession = () => {
    // TODO: Implement navigation or dialog for creating a new session
    toast({ title: "Ação Pendente", description: "Funcionalidade 'Nova Sessão' ainda não implementada." });
  };
  // --- Render Logic ---

  // Usar isLoading combinado do hook
  if (isLoading && !currentPatient) { // Mostrar loading se estiver carregando E não houver dados ainda
    return (
      <div className="container max-w-full p-6 space-y-4">
        <Skeleton className="h-8 w-1/4" />
        {/* Ajustar Skeletons se necessário */}
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Usar isError combinado do hook.
  if (isError || (!isLoading && !patient)) {
    return (
      <div className="container max-w-full p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erro</AlertTitle>
          <AlertDescription>
            {isError ? "Não foi possível carregar os dados do paciente." : "Paciente não encontrado."}
          </AlertDescription>
          {isError && <Button variant="outline" size="sm" className="mt-4" onClick={() => refetchDetails()}>Tentar novamente</Button>}
        </Alert>
      </div>
    );
  }

  // Patient loaded successfully
  return (
    <div className="container max-w-full p-6 space-y-6">
      {/* Remover props de edição do Header */}
      <PatientDetailHeader
        patient={patient!}
        onNewSession={handleNewSession}
      />

      <PatientTabs
        overviewContent={<OverviewTab patient={patient!} />}
        personalDataContent={
          // Passar apenas patient e onDataUpdate para PersonalDataTab
          // isLoadingContacts e patientContacts não são mais necessários aqui
          <PersonalDataTab
            patient={patient!}
            // isLoadingContacts={isLoadingContacts} // Remover
            // patientContacts={patientContacts} // Remover
            onDataUpdate={refetchDetails}
          />
        }
        historyContent={
          // Remover props de edição global
          <HistoryTab
            patient={patient!}
          />
        }
        sessionsContent={<SessionsTab patientId={patientId} />}
        documentsContent={<DocumentsTab />}
        financialContent={<FinancialTab patientId={patientId} />}
      />
    </div>
  );
}

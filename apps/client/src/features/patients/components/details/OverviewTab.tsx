import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/shared/ui/card";
import { Badge } from "@/shared/ui/badge";
import { Button } from "@/shared/ui/button";
import { Patient } from "@/features/patients/types/patient.schema";
import { ContactList } from "@/shared/components/ContactList";
import { useAppointmentQuery } from "@/features/calendar/hooks/useAppointmentQuery";
import { useSessionNotesQuery } from "@/features/notes/hooks/useSessionNotesQuery";
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  Users,
  Calendar,
  DollarSign,
  FileText,
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Activity,
  Plus,
  Stethoscope,
  Edit3
} from "lucide-react";

interface OverviewTabProps {
  patient: Patient;
}

export function OverviewTab({ patient }: OverviewTabProps) {
  // Buscar dados de consultas e notas
  const { appointments, isLoading: isLoadingAppointments } = useAppointmentQuery({
    viewMode: 'month',
    patientId: patient.id
  });
  const { sessionNotes, isLoading: isLoadingNotes } = useSessionNotesQuery(patient.id);

  // Calcular estatísticas
  const totalSessions = appointments?.filter(apt => apt.status === 'completed').length || 0;
  const totalNotes = sessionNotes?.length || 0;
  const nextAppointment = appointments?.find(apt =>
    apt.status === 'scheduled' && new Date(apt.start_time) > new Date()
  );

  // Criar timeline de atividades
  const createTimeline = () => {
    const activities: Array<{
      id: string;
      type: string;
      title: string;
      date: string;
      status?: string;
      description?: string;
      icon?: any;
    }> = [];

    // Adicionar consultas
    if (appointments) {
      appointments.forEach(apt => {
        activities.push({
          id: `apt-${apt.id}`,
          type: 'appointment',
          date: apt.start_time,
          title: apt.status === 'completed' ? 'Consulta realizada' : 'Consulta agendada',
          description: apt.title || 'Consulta',
          status: apt.status,
          icon: Calendar
        });
      });
    }

    // Adicionar notas
    if (sessionNotes) {
      sessionNotes.forEach(note => {
        activities.push({
          id: `note-${note.id}`,
          type: 'note',
          date: note.created_at,
          title: 'Nota de sessão criada',
          description: note.title || 'Evolução da sessão',
          status: 'completed',
          icon: Edit3
        });
      });
    }

    // Ordenar por data (mais recente primeiro)
    return activities.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  const timeline = createTimeline();

  return (
    <div className="space-y-6">
      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Próxima Sessão</p>
                <p className="text-lg font-semibold">
                  {nextAppointment
                    ? format(parseISO(nextAppointment.start_time), 'dd/MM', { locale: ptBR })
                    : 'Não agendada'
                  }
                </p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Saldo</p>
                <p className="text-lg font-semibold text-green-600">R$ 0,00</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sessões</p>
                <p className="text-lg font-semibold">{totalSessions}</p>
              </div>
              <Activity className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Notas Criadas</p>
                <p className="text-lg font-semibold">{totalNotes}</p>
              </div>
              <FileText className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Ações Rápidas
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="outline" className="w-full justify-start">
              <Calendar className="h-4 w-4 mr-2" />
              Agendar Nova Sessão
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <FileText className="h-4 w-4 mr-2" />
              Adicionar Documento
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <DollarSign className="h-4 w-4 mr-2" />
              Registrar Pagamento
            </Button>
          </CardContent>
        </Card>

        {/* Timeline de Atividades */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Timeline de Atividades
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingAppointments || isLoadingNotes ? (
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-muted rounded-full animate-pulse"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded animate-pulse"></div>
                      <div className="h-3 bg-muted rounded w-1/2 animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : timeline.length > 0 ? (
              <div className="space-y-4">
                {timeline.slice(0, 5).map((activity, index) => (
                  <div key={activity.id} className="flex items-start gap-3">
                    <div className={`
                      w-8 h-8 rounded-full flex items-center justify-center
                      ${activity.type === 'appointment'
                        ? activity.status === 'completed'
                          ? 'bg-green-100 text-green-600'
                          : 'bg-blue-100 text-blue-600'
                        : 'bg-purple-100 text-purple-600'
                      }
                    `}>
                      <activity.icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-foreground">
                          {activity.title}
                        </p>
                        <span className="text-xs text-muted-foreground">
                          {format(parseISO(activity.date), 'dd/MM/yyyy', { locale: ptBR })}
                        </span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {activity.description}
                      </p>
                    </div>
                  </div>
                ))}
                {timeline.length > 5 && (
                  <div className="text-center pt-4">
                    <Button variant="outline" size="sm">
                      Ver mais atividades
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Nenhuma atividade registrada</p>
                <p className="text-xs mt-1">As consultas e notas aparecerão aqui</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Patient Status & Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Status do Tratamento</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Cadastro */}
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="font-medium">Cadastro Completo</p>
                    <p className="text-sm text-muted-foreground">Todas as informações básicas foram preenchidas</p>
                  </div>
                </div>
                <Badge variant="secondary">Concluído</Badge>
              </div>

              {/* Primeira Sessão */}
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  {totalSessions > 0 ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-amber-500" />
                  )}
                  <div>
                    <p className="font-medium">
                      {totalSessions > 0 ? 'Primeira Sessão Realizada' : 'Primeira Sessão'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {totalSessions > 0
                        ? `${totalSessions} sessão${totalSessions > 1 ? 'ões' : ''} realizada${totalSessions > 1 ? 's' : ''}`
                        : 'Agende a primeira sessão com o paciente'
                      }
                    </p>
                  </div>
                </div>
                <Badge variant={totalSessions > 0 ? "secondary" : "outline"}>
                  {totalSessions > 0 ? 'Concluído' : 'Pendente'}
                </Badge>
              </div>

              {/* Documentação */}
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  {totalNotes > 0 ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-amber-500" />
                  )}
                  <div>
                    <p className="font-medium">Documentação</p>
                    <p className="text-sm text-muted-foreground">
                      {totalNotes > 0
                        ? `${totalNotes} nota${totalNotes > 1 ? 's' : ''} de evolução criada${totalNotes > 1 ? 's' : ''}`
                        : 'Crie notas de evolução das sessões'
                      }
                    </p>
                  </div>
                </div>
                <Badge variant={totalNotes > 0 ? "secondary" : "outline"}>
                  {totalNotes > 0 ? 'Em dia' : 'Pendente'}
                </Badge>
              </div>

              {/* Próxima Consulta */}
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  {nextAppointment ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-amber-500" />
                  )}
                  <div>
                    <p className="font-medium">Continuidade</p>
                    <p className="text-sm text-muted-foreground">
                      {nextAppointment
                        ? `Próxima consulta em ${format(parseISO(nextAppointment.start_time), 'dd/MM/yyyy', { locale: ptBR })}`
                        : 'Agende a próxima consulta para dar continuidade'
                      }
                    </p>
                  </div>
                </div>
                <Badge variant={nextAppointment ? "secondary" : "outline"}>
                  {nextAppointment ? 'Agendado' : 'Pendente'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Contatos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">Nenhum contato vinculado</p>
              <Button variant="outline" size="sm" className="mt-2">
                <Plus className="h-4 w-4 mr-2" />
                Adicionar Contato
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

import { useState, useEffect, useRef } from 'react';
import { useRouterState, useSearch } from '@tanstack/react-router';
import { RichTextEditor } from '@/features/models/components/RichTextEditor';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Label } from '@/shared/ui/label';
import { Card, CardContent } from '@/shared/ui/card';
// Removida a importação de PlaceholderContext e processPlaceholders pois agora usamos o ApplyModelContextDialog
import { useModelsQuery } from '@/features/models/hooks/useModelsQuery';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery';
import { useAppointmentQuery } from '@/features/calendar/hooks/useAppointmentQuery';
import { useAuthQuery } from '@/features/auth/hooks';
import { useToast } from '@/shared/hooks/use-toast';
import { Model } from '@/features/models/types/model.schema';
import { Patient } from '@/features/patients/types/patient.schema';
import { Appointment } from '@/features/calendar/types/appointment.schema';
import { ModelSelectorDialog } from '@/features/models/components/ModelSelectorDialog';
import { ApplyModelContextDialog } from '@/features/models/components/ApplyModelContextDialog';
import { format, parseISO } from 'date-fns';

interface SessionNoteEditorProps {
  patientId: string; // ID do paciente para contexto
  appointmentId?: string; // ID do agendamento/sessão para contexto (opcional)
  initialContent?: string;
  onSave: (content: string, title: string, appointmentId?: string) => void;
  onCancel?: () => void;
  isSaving?: boolean;
}

export function SessionNoteEditor({ patientId, appointmentId: propAppointmentId, initialContent = '', onSave, onCancel, isSaving = false }: SessionNoteEditorProps) {
  // Obter o estado da rota para verificar se há conteúdo inicial
  const routerState = useRouterState();
  const search = useSearch({ from: '/_authenticated/person/$patientId/session-notes/new' });
  const routeState = routerState.location.state as { initialContent?: string; initialTitle?: string } | null;
  
  const appointmentId = search.appointmentId || propAppointmentId;

  // Usar o conteúdo inicial da rota, se disponível, ou o passado via props
  const [content, setContent] = useState(routeState?.initialContent || initialContent);
  const [title, setTitle] = useState(routeState?.initialTitle || '');
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState(false);
  const [isContextDialogOpen, setIsContextDialogOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);

  // Referência para o editor Tiptap
  const editorRef = useRef<any>(null);

  // Hooks para obter dados do contexto
  const { allPatients } = usePatientQuery();
  const patient = allPatients.find((p: Patient) => p.id === patientId);
  const { user } = useAuthQuery();
  const { toast } = useToast();

  // Se appointmentId for fornecido, buscar dados do agendamento
  const { appointments } = useAppointmentQuery({
    patientId: patientId,
  });

  const appointment = appointmentId
    ? appointments.find((a: Appointment) => a.id === appointmentId)
    : undefined;

  // Buscar modelos para o seletor
  useModelsQuery();

  // Efeito para definir um título padrão baseado na data atual ou no agendamento
  useEffect(() => {
    if (!title) {
      if (appointment && appointment.start_time) {
        setTitle(`Evolução - ${format(parseISO(appointment.start_time), 'dd/MM/yyyy')}`);
      } else {
        setTitle(`Evolução - ${format(new Date(), 'dd/MM/yyyy')}`);
      }
    }
  }, [appointment, title]);

  // Função para abrir o seletor de modelos
  const handleOpenModelSelector = () => {
    setIsModelSelectorOpen(true);
  };

  // Função chamada quando um modelo é selecionado
  const handleModelSelect = (model: Model) => {
    setSelectedModel(model);
    setIsModelSelectorOpen(false);
    setIsContextDialogOpen(true);
  };

  // Função para receber o conteúdo processado do modelo
  const handleApplyModelContent = (
    processedContent: string,
    _context: any,
    modelTitle: string,
    shouldAppend: boolean
  ) => {
    if (shouldAppend && editorRef.current?.editor) {
      // Inserir no final do conteúdo existente
      const currentLength = editorRef.current.editor.storage.characterCount || 0;
      editorRef.current.editor.commands.insertContentAt(currentLength, processedContent);
      setContent(editorRef.current.editor.getHTML());
    } else {
      // Sobrescrever conteúdo
      setContent(processedContent);
      if (editorRef.current?.editor) {
        editorRef.current.editor.commands.setContent(processedContent, true);
      }
    }

    // Se o título estiver vazio, usar o título do modelo
    if (!title) {
      setTitle(modelTitle);
    }

    toast({
      title: "Modelo aplicado",
      description: `O modelo "${modelTitle}" foi aplicado com sucesso.`
    });
  };

  // A função applyModelWithContext foi removida pois agora usamos o ApplyModelContextDialog

  // Função auxiliar para calcular idade
  const calculateAge = (birthDate: Date): number => {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Função para salvar as notas
  const handleSave = () => {
    onSave(content, title, appointmentId);
  };

  // Função para cancelar
  const handleCancel = () => {
    if (onCancel) onCancel();
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <div className="flex-1 mr-4">
          <Label htmlFor="note-title" className="mb-2 block">Título da Nota</Label>
          <Input
            id="note-title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Título da nota de sessão"
            className="w-full"
          />
        </div>
        <div className="flex gap-2">
          {onCancel && (
            <Button variant="outline" onClick={handleCancel}>
              Cancelar
            </Button>
          )}
          <Button variant="outline" onClick={handleOpenModelSelector}>
            Usar Modelo
          </Button>
          <Button onClick={handleSave} disabled={!content.trim() || !title.trim() || isSaving}>
            {isSaving ? 'Salvando...' : 'Salvar Nota'}
          </Button>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <RichTextEditor
            value={content}
            onChange={setContent}
            placeholder="Digite as notas da sessão aqui..."
            onEditorReady={(editor) => {
              editorRef.current = editor;
            }}
          />
        </CardContent>
      </Card>

      {/* Seletor de Modelos */}
      <ModelSelectorDialog
        isOpen={isModelSelectorOpen}
        onClose={() => setIsModelSelectorOpen(false)}
        onSelect={handleModelSelect}
        filter={{ category: 'session_note' }} // Filtrar apenas modelos de notas de sessão
      />

      {/* Diálogo de aplicação de modelo com contexto */}
      {selectedModel && (
        <ApplyModelContextDialog
          isOpen={isContextDialogOpen}
          onClose={() => setIsContextDialogOpen(false)}
          model={selectedModel}
          initialContext={{
            patientId,
            appointmentId
          }}
          onApply={handleApplyModelContent}
        />
      )}
    </div>
  );
}

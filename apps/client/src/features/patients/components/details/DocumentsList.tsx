import { useState } from 'react';
import { Button } from '@/shared/ui/button';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/ui/dialog';
import { useDocumentsQuery } from '@/features/documents/hooks/useDocumentsQuery';
import { useDocumentMutations } from '@/features/documents/hooks/useDocumentMutations';
import { Document } from '@/features/documents/types/document.schema';
import { DocumentEditor } from './DocumentEditor';
import { formatDate } from './utils';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/shared/ui/alert-dialog';
import { useToast } from '@/shared/hooks/use-toast';
import { ScrollArea } from '@/shared/ui/scroll-area';

interface DocumentsListProps {
  patientId: string;
}

export function DocumentsList({ patientId }: DocumentsListProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const { toast } = useToast();

  const {
    documents,
    isLoading,
    isError,
  } = useDocumentsQuery({ patient_id: patientId });

  const {
    uploadDocument,
    updateDocument,
    updateDocumentContent,
    deleteDocument,
    isUploading,
    isUpdatingMetadata,
    isUpdatingContent,
    isDeleting,
  } = useDocumentMutations();

  // Função para abrir o diálogo de criação
  const handleOpenCreateDialog = () => {
    setIsCreateDialogOpen(true);
  };

  // Função para abrir o diálogo de edição
  const handleOpenEditDialog = (document: Document) => {
    setSelectedDocument(document);
    setIsEditDialogOpen(true);
  };

  // Função para abrir o diálogo de visualização
  const handleOpenViewDialog = (document: Document) => {
    setSelectedDocument(document);
    setIsViewDialogOpen(true);
  };

  // Função para abrir o diálogo de exclusão
  const handleOpenDeleteDialog = (document: Document) => {
    setSelectedDocument(document);
    setIsDeleteDialogOpen(true);
  };

  // Função para criar um novo documento
  const handleCreateDocument = (content: string, title: string) => {
    const formData = new FormData();
    // O backend espera um arquivo, então criamos um blob do conteúdo.
    const file = new File([content], `${title || "documento"}.html`, { type: "text/html" });
    formData.append("file", file);
    formData.append("metadata", JSON.stringify({ title, patient_id: patientId }));

    uploadDocument(formData, {
        onSuccess: () => {
          setIsCreateDialogOpen(false);
          toast({
            title: 'Documento criado',
            description: 'O documento foi criado com sucesso.',
          });
        },
        onError: (error) => {
          toast({
            title: 'Erro ao criar documento',
            description: `Ocorreu um erro ao criar o documento: ${error.message}`,
            variant: 'destructive',
          });
        },
      });
  };

  // Função para atualizar um documento
  const handleUpdateDocument = (content: string, title: string) => {
    if (!selectedDocument) return;

    updateDocumentContent({ id: selectedDocument.id, data: { content } }, {
      onSuccess: () => {
        updateDocument({ id: selectedDocument.id, data: { title } }, {
          onSuccess: () => {
            setIsEditDialogOpen(false);
            toast({
              title: 'Documento atualizado',
              description: 'O conteúdo e o título do documento foram atualizados com sucesso.',
            });
          },
          onError: (error) => {
            toast({
              title: 'Erro ao atualizar título',
              description: `O conteúdo foi salvo, mas houve um erro ao atualizar o título: ${error.message}`,
              variant: 'destructive',
            });
          }
        })
      },
      onError: (error) => {
        toast({
          title: 'Erro ao atualizar conteúdo',
          description: `Ocorreu um erro ao salvar o conteúdo do documento: ${error.message}`,
          variant: 'destructive',
        });
      }
    });
  };

  // Função para excluir um documento
  const handleDeleteDocument = () => {
    if (!selectedDocument) return;

    deleteDocument(selectedDocument.id, {
      onSuccess: () => {
        setIsDeleteDialogOpen(false);
        toast({
          title: 'Documento excluído',
          description: 'O documento foi excluído com sucesso.',
        });
      },
      onError: (error) => {
        toast({
          title: 'Erro ao excluir documento',
          description: `Ocorreu um erro ao excluir o documento: ${error.message}`,
          variant: 'destructive',
        });
      },
    });
  };

  if (isLoading) {
    return <div>Carregando documentos...</div>;
  }

  if (isError) {
    return <div>Erro ao carregar documentos.</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Documentos</h2>
        <Button onClick={handleOpenCreateDialog}>Novo Documento</Button>
      </div>

      {documents.length === 0 ? (
        <Card>
          <CardContent className="py-6 text-center">
            <p className="text-muted-foreground">Nenhum documento encontrado para este paciente.</p>
            <Button className="mt-4" onClick={handleOpenCreateDialog}>
              Criar Documento
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {documents.map((document: Document) => (
            <Card key={document.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg truncate" title={String(document.title)}>
                  {document.title}
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Atualizado em {formatDate(new Date(document.updated_at))}
                </p>
              </CardHeader>
              <CardContent className="pb-2">
                <div
                  className="text-sm line-clamp-3 h-16 overflow-hidden"
                  dangerouslySetInnerHTML={{ __html: String(document.content) }}
                />
              </CardContent>
              <div className="flex justify-end p-4 pt-0 space-x-2">
                <Button variant="outline" size="sm" onClick={() => handleOpenViewDialog(document)}>
                  Visualizar
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleOpenEditDialog(document)}>
                  Editar
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive hover:text-destructive"
                  onClick={() => handleOpenDeleteDialog(document)}
                >
                  Excluir
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Diálogo de Criação */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-4xl h-[80vh]">
          <DialogHeader>
            <DialogTitle>Novo Documento</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-full pr-4">
            <DocumentEditor
              patientId={patientId}
              onSave={handleCreateDocument}
              onCancel={() => setIsCreateDialogOpen(false)}
            />
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Diálogo de Edição */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl h-[80vh]">
          <DialogHeader>
            <DialogTitle>Editar Documento</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-full pr-4">
            {selectedDocument && (
              <DocumentEditor
                patientId={patientId}
                documentId={selectedDocument.id}
                initialContent={String(selectedDocument.content ?? '')}
                initialTitle={selectedDocument.title}
                onSave={handleUpdateDocument}
                onCancel={() => setIsEditDialogOpen(false)}
              />
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Diálogo de Visualização */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-4xl h-[80vh]">
          <DialogHeader>
            <DialogTitle>{String(selectedDocument?.title)}</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-full pr-4">
            {selectedDocument && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-muted-foreground">
                      Criado em {formatDate(new Date(selectedDocument.created_at))}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Atualizado em {formatDate(new Date(selectedDocument.updated_at))}
                    </p>
                  </div>
                  <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                    Fechar
                  </Button>
                </div>
                <div
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: String(selectedDocument.content) }}
                />
              </div>
            )}
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Diálogo de Exclusão */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Documento</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir o documento "{String(selectedDocument?.title)}"? Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteDocument} disabled={isDeleting}>
              {isDeleting ? 'Excluindo...' : 'Excluir'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

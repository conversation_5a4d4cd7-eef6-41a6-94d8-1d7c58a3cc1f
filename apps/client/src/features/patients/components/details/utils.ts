import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export const formatDate = (dateInput?: string | Date | null): string => {
  if (!dateInput) {
    return 'N/A';
  }
  try {
    const date = typeof dateInput === 'string' ? parseISO(dateInput) : dateInput;
    return format(date, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  } catch (error) {
    console.error("Error formatting date:", error);
    return 'Data inválida';
  }
};

export const getRelationshipTypeLabel = (type?: string | null): string => {
    if (!type) return 'Não especificado';
    const labels: { [key: string]: string } = {
        parent: 'Pai/Mãe',
        guardian: 'Responsável Legal',
        relative: 'Parente',
        school: 'Escola',
        professional: 'Profissional',
        other: 'Outro',
    };
    return labels[type] || type;
} 
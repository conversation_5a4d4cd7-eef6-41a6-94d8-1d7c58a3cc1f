import { useEffect } from 'react';
import { useParams } from '@tanstack/react-router';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery';
import { Loader2, ArrowLeft } from 'lucide-react';
import { Button } from '@/shared/ui/button';
import { Link } from '@tanstack/react-router';
import { PatientTabs } from './PatientTabs';
import { PatientDetailHeader } from './PatientDetailHeader';
import { PersonalDataTab } from './PersonalDataTab';
import { DocumentsTab } from './DocumentsTab';
import { SessionsTab } from './SessionsTab';
import { FinancialTab } from './FinancialTab';
import { HistoryTab } from './HistoryTab';
import { OverviewTab } from './OverviewTab';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/shared/ui/tabs';

export function PatientDetailsPage() {
  const { patientId } = useParams({ from: '/_authenticated/person/$patientId' });
  const {
    currentPatient,
    isLoadingDetails,
    isError,
  } = usePatientQuery({ initialPatientId: patientId });

  if (isLoadingDetails) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="animate-spin h-8 w-8 text-primary" />
      </div>
    );
  }

  if (isError || !currentPatient) {
    return (
        <div className="container max-w-full p-6 space-y-4 text-center">
            <h2 className="text-xl font-semibold text-destructive">Erro ao carregar paciente</h2>
            <p className="text-muted-foreground">Não foi possível encontrar os dados do paciente solicitado.</p>
            <Link to="/people">
                <Button variant="outline">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Voltar para a lista
                </Button>
            </Link>
        </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb Header */}
      <div className="border-b bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/50">
        <div className="container max-w-full px-6 py-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Link to="/people" className="hover:text-foreground transition-colors">
              Pacientes
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">{currentPatient.full_name}</span>
          </div>
        </div>
      </div>

      {/* Patient Header - Sticky */}
      <div className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container max-w-full px-6">
          <PatientDetailHeader patient={currentPatient} onNewSession={() => { console.log('new session'); }} />
        </div>
      </div>

      {/* Main Content */}
      <div className="container max-w-full px-6 py-6">
        <Tabs defaultValue="overview" className="space-y-6">
          {/* Enhanced Tab Navigation */}
          <div className="flex items-center justify-between">
            <TabsList className="grid grid-cols-6 w-fit bg-muted/50">
              <TabsTrigger value="overview" className="data-[state=active]:bg-background">
                Visão Geral
              </TabsTrigger>
              <TabsTrigger value="personalData" className="data-[state=active]:bg-background">
                Dados Pessoais
              </TabsTrigger>
              <TabsTrigger value="sessions" className="data-[state=active]:bg-background">
                Sessões
              </TabsTrigger>
              <TabsTrigger value="documents" className="data-[state=active]:bg-background">
                Documentos
              </TabsTrigger>
              <TabsTrigger value="financial" className="data-[state=active]:bg-background">
                Financeiro
              </TabsTrigger>
              <TabsTrigger value="history" className="data-[state=active]:bg-background">
                Histórico
              </TabsTrigger>
            </TabsList>

            {/* Quick Actions */}
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Button>
            </div>
          </div>

          {/* Tab Contents */}
          <TabsContent value="overview" className="space-y-6">
            <OverviewTab patient={currentPatient} />
          </TabsContent>
          <TabsContent value="personalData" className="space-y-6">
            <PersonalDataTab patient={currentPatient} />
          </TabsContent>
          <TabsContent value="sessions" className="space-y-6">
            <SessionsTab patientId={currentPatient.id} />
          </TabsContent>
          <TabsContent value="documents" className="space-y-6">
            <DocumentsTab />
          </TabsContent>
          <TabsContent value="financial" className="space-y-6">
            <FinancialTab patientId={currentPatient.id} />
          </TabsContent>
          <TabsContent value="history" className="space-y-6">
            <HistoryTab patient={currentPatient} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

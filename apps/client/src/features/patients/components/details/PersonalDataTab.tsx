import { useState, useEffect, useMemo } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription } from '@/shared/ui/card';
import { Input } from '@/shared/ui/input';
import { MaskedInput } from '@/shared/components/MaskedInput';
import { Label } from '@/shared/ui/label';
import { Pencil, UserPlus, UserMinus, AlertTriangle, Loader2 } from 'lucide-react'; // Icons
import { Skeleton } from '@/shared/ui/skeleton';
import { Button } from '@/shared/ui/button';
import { useToast } from '@/shared/hooks/use-toast';
import { Patient, UpdatePatientData } from '@/features/patients/types/patient.schema'; // Patient schema
import { EditContactDialog } from '@/features/patients/components/EditContactDialog';
import { AddGuardianDialog } from '@/features/patients/components/AddGuardianDialog';
import { formatDate, getRelationshipTypeLabel } from './utils';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery'; // Hook for patient data
import { usePatientMutations } from '@/features/patients/hooks/usePatientMutations'; // Hook for patient mutations
import { useContactMutations } from '@/features/contacts/hooks/useContactMutations'; // Hook for contact mutations
import { usePatientContactMutations } from '@/features/patients/hooks/usePatientContacts'; // Importar para unlinkContact
import { ConfirmDeleteDialog } from '@/shared/components/ConfirmDeleteDialog'; // Confirmation dialog

// Tipos para os dados editáveis inline
type EditablePersonalData = {
  full_name: string;
  date_of_birth: string;
  cpf: string;
  school: string;
};
type EditableContactData = {
  phone: string;
  email: string;
  address: string;
  city: string;
  state: string;
};

// Props do componente
interface PersonalDataTabProps {
  patient: Patient; // Receber o paciente inicial (pode estar desatualizado)
  onDataUpdate?: () => void; // Callback para atualizar dados (refetch)
}

export function PersonalDataTab({
  patient: initialPatientData,
  onDataUpdate,
}: PersonalDataTabProps) {
  const { toast } = useToast();

  // --- Hooks ---
  const {
    currentPatient: patient,
    isLoading,
    refetchDetails,
  } = usePatientQuery({ initialPatientId: initialPatientData.id });
  
  const { updatePatient: updatePatientMutation, isUpdating: isSavingPatient } = usePatientMutations();
  const { deleteContact, isDeleting: isDeletingContact } = useContactMutations();
  const { unlinkContact, isUnlinking } = usePatientContactMutations();

  // --- Estados Locais ---
  const [editContactDialogOpen, setEditContactDialogOpen] = useState(false);
  const [addGuardianDialogOpen, setAddGuardianDialogOpen] = useState(false);
  const [showGuardianUnlinkConfirm, setShowGuardianUnlinkConfirm] = useState(false);
  const [guardianToUnlink, setGuardianToUnlink] = useState<Patient['guardian']>(null);
  const [isEditingPersonal, setIsEditingPersonal] = useState(false);
  const [isEditingContact, setIsEditingContact] = useState(false);
  const [editedPersonalData, setEditedPersonalData] = useState<EditablePersonalData | null>(null);
  const [editedContactData, setEditedContactData] = useState<EditableContactData | null>(null);

  // --- Efeitos ---
  useEffect(() => {
    if (isEditingPersonal && patient) {
      setEditedPersonalData({
        full_name: patient.full_name || '',
        date_of_birth: patient.date_of_birth ? patient.date_of_birth.split('T')[0] : '',
        cpf: patient.cpf || '',
        school: patient.school || '',
      });
    } else {
      setEditedPersonalData(null);
    }
  }, [isEditingPersonal, patient]);

  useEffect(() => {
    if (isEditingContact && patient) {
      setEditedContactData({
        phone: patient.phone || '',
        email: patient.email || '',
        address: patient.address || '',
        city: patient.city || '',
        state: patient.state || '',
      });
    } else {
      setEditedContactData(null);
    }
  }, [isEditingContact, patient]);

  // --- Cálculos ---
  const isMinor = useMemo(() => {
    if (!patient || !patient.date_of_birth) return false;
    const birthDate = new Date(patient.date_of_birth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age < 18;
  }, [patient]);

  // --- Handlers ---
  const handlePersonalInputChange = (field: keyof EditablePersonalData, value: string) => {
    setEditedPersonalData(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handleContactInputChange = (field: keyof EditableContactData, value: string) => {
    setEditedContactData(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handleSavePersonalData = () => {
    if (!editedPersonalData || !patient) return;
    const payload: Partial<UpdatePatientData> = {
        full_name: editedPersonalData.full_name,
        date_of_birth: editedPersonalData.date_of_birth || null,
        cpf: editedPersonalData.cpf || null,
        school: editedPersonalData.school || null,
    };
    updatePatientMutation({ id: patient.id, data: payload }, {
      onSuccess: () => {
        setIsEditingPersonal(false);
        (onDataUpdate || refetchDetails)();
        toast({ title: "Sucesso", description: "Dados pessoais atualizados." });
      },
      onError: (error) => {
        toast({ title: "Erro", description: "Não foi possível salvar os dados pessoais.", variant: "destructive" });
      }
    });
  };

  const handleSaveContactData = () => {
     if (!editedContactData || !patient) return;
     const payload: Partial<UpdatePatientData> = {
        phone: editedContactData.phone || null,
        email: editedContactData.email || null,
        address: editedContactData.address || null,
        city: editedContactData.city || null,
        state: editedContactData.state || null,
     };
     updatePatientMutation({ id: patient.id, data: payload }, {
       onSuccess: () => {
         setIsEditingContact(false);
         (onDataUpdate || refetchDetails)();
         toast({ title: "Sucesso", description: "Dados de contato atualizados." });
       },
       onError: (error) => {
         toast({ title: "Erro", description: "Não foi possível salvar os dados de contato.", variant: "destructive" });
       }
     });
  };

  const handleRemoveGuardianClick = () => {
    if (!patient?.guardian) return;
    if (isMinor) {
      toast({
        title: "Operação não permitida",
        description: "Não é possível remover o responsável principal de um paciente menor de idade.",
        variant: "destructive"
      });
      return;
    }
    setGuardianToUnlink(patient.guardian);
    setShowGuardianUnlinkConfirm(true);
  };

  const handleGuardianUnlinkConfirm = () => {
    if (!guardianToUnlink || !patient) return;

    const guardianContactId = guardianToUnlink.id;
    const currentPatientId = patient.id;

    unlinkContact({ contactId: guardianContactId, patientId: currentPatientId }, {
        onSuccess: () => {
            deleteContact(guardianContactId, {
                onSuccess: () => {
                    toast({ title: "Sucesso", description: "Responsável desvinculado e excluído." });
                    (onDataUpdate || refetchDetails)();
                    setShowGuardianUnlinkConfirm(false);
                    setGuardianToUnlink(null);
                },
                onError: (error: any) => {
                    toast({ title: "Erro na Exclusão", description: `O responsável foi desvinculado, mas houve um erro ao excluir o contato: ${error?.message || 'Erro desconhecido'}`, variant: "destructive" });
                    (onDataUpdate || refetchDetails)();
                    setShowGuardianUnlinkConfirm(false);
                    setGuardianToUnlink(null);
                }
            });
        },
        onError: (error: any) => {
            toast({ title: "Erro ao Desvincular", description: `Não foi possível desvincular o responsável: ${error?.message || 'Erro desconhecido'}`, variant: "destructive" });
            setShowGuardianUnlinkConfirm(false);
            setGuardianToUnlink(null);
        }
    });
  };

  // --- Renderização ---
  if (isLoading && !patient) {
    return (
      <div className="grid gap-6 md:grid-cols-2">
        <Card><CardHeader><Skeleton className="h-8 w-3/4" /></CardHeader><CardContent><Skeleton className="h-40 w-full" /></CardContent></Card>
        <div className="space-y-6">
          <Card><CardHeader><Skeleton className="h-8 w-3/4" /></CardHeader><CardContent><Skeleton className="h-40 w-full" /></CardContent></Card>
          <Card><CardHeader><Skeleton className="h-8 w-3/4" /></CardHeader><CardContent><Skeleton className="h-20 w-full" /></CardContent></Card>
        </div>
      </div>
    );
  }

  if (!patient) {
     return <p>Erro ao carregar dados do paciente ou paciente não encontrado.</p>;
  }

  const formattedBirthDate = patient.date_of_birth ? formatDate(patient.date_of_birth) : 'Não informado';

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Card Informações Pessoais */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Informações Pessoais</CardTitle>
            <CardDescription>Dados de identificação do paciente.</CardDescription>
          </div>
          <Button variant="outline" size="icon" onClick={() => setIsEditingPersonal(!isEditingPersonal)} aria-label={isEditingPersonal ? "Cancelar Edição" : "Editar Informações Pessoais"}>
            <Pencil className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
           <div className="grid gap-2">
             <Label htmlFor="full_name_personal">Nome Completo</Label>
             {isEditingPersonal && editedPersonalData ? ( <Input id="full_name_personal" value={editedPersonalData.full_name} onChange={(e) => handlePersonalInputChange('full_name', e.target.value)} /> ) : ( <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">{patient.full_name || '-'}</p> )}
           </div>
           <div className="grid sm:grid-cols-2 gap-4">
             <div className="grid gap-2">
               <Label htmlFor="date_of_birth_personal">Data de Nascimento</Label>
               {isEditingPersonal && editedPersonalData ? ( <Input id="date_of_birth_personal" type="date" value={editedPersonalData.date_of_birth} onChange={(e) => handlePersonalInputChange('date_of_birth', e.target.value)} /> ) : ( <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">{formattedBirthDate}</p> )}
             </div>
             <div className="grid gap-2">
               <Label htmlFor="cpf_personal">CPF</Label>
               {isEditingPersonal && editedPersonalData ? ( 
                 <MaskedInput 
                   id="cpf_personal" 
                   mask="999.999.999-99"
                   value={editedPersonalData.cpf} 
                   onChange={(value) => handlePersonalInputChange('cpf', value)}
                   onChangeRaw={(rawValue) => handlePersonalInputChange('cpf', rawValue)}
                 /> 
               ) : ( 
                 <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">
                   {patient.cpf || 'Não informado'}
                 </p> 
               )}
             </div>
           </div>
           <div className="grid gap-2">
             <Label htmlFor="school_personal">Escola / Instituição</Label>
              {isEditingPersonal && editedPersonalData ? ( <Input id="school_personal" value={editedPersonalData.school} onChange={(e) => handlePersonalInputChange('school', e.target.value)} /> ) : ( <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">{patient.school || 'Não informado'}</p> )}
           </div>
          {isEditingPersonal && (
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setIsEditingPersonal(false)} disabled={isSavingPatient}>Cancelar</Button>
              <Button onClick={handleSavePersonalData} disabled={isSavingPatient}> {isSavingPatient ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" />Salvando...</> : 'Salvar'} </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Card Contato */}
      <div className="space-y-6">
        <Card>
           <CardHeader className="flex flex-row items-center justify-between">
             <div>
               <CardTitle>Contato</CardTitle>
               <CardDescription>Informações de contato do paciente.</CardDescription>
             </div>
             <Button variant="outline" size="icon" onClick={() => setIsEditingContact(!isEditingContact)} aria-label={isEditingContact ? "Cancelar Edição" : "Editar Contato"}>
               <Pencil className="h-4 w-4" />
             </Button>
           </CardHeader>
          <CardContent className="space-y-4">
             <div className="grid sm:grid-cols-2 gap-4">
               <div className="grid gap-2">
                 <Label htmlFor="phone_contact">Telefone</Label>
                  {isEditingContact && editedContactData ? ( <Input id="phone_contact" value={editedContactData.phone} onChange={(e) => handleContactInputChange('phone', e.target.value)} /> ) : ( <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">{patient.phone || 'Não informado'}</p> )}
               </div>
               <div className="grid gap-2">
                 <Label htmlFor="email_contact">Email</Label>
                  {isEditingContact && editedContactData ? ( <Input id="email_contact" type="email" value={editedContactData.email} onChange={(e) => handleContactInputChange('email', e.target.value)} /> ) : ( <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">{patient.email || 'Não informado'}</p> )}
               </div>
             </div>
             <div className="grid gap-2">
               <Label htmlFor="address_contact">Endereço</Label>
                {isEditingContact && editedContactData ? ( <Input id="address_contact" value={editedContactData.address} onChange={(e) => handleContactInputChange('address', e.target.value)} /> ) : ( <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">{patient.address || 'Não informado'}</p> )}
             </div>
             <div className="grid sm:grid-cols-2 gap-4">
               <div className="grid gap-2">
                 <Label htmlFor="city_contact">Cidade</Label>
                  {isEditingContact && editedContactData ? ( <Input id="city_contact" value={editedContactData.city} onChange={(e) => handleContactInputChange('city', e.target.value)} /> ) : ( <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">{patient.city || 'Não informado'}</p> )}
               </div>
               <div className="grid gap-2">
                 <Label htmlFor="state_contact">Estado</Label>
                  {isEditingContact && editedContactData ? ( <Input id="state_contact" value={editedContactData.state} onChange={(e) => handleContactInputChange('state', e.target.value)} /> ) : ( <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">{patient.state || 'Não informado'}</p> )}
               </div>
             </div>
            {isEditingContact && (
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setIsEditingContact(false)} disabled={isSavingPatient}>Cancelar</Button>
                <Button onClick={handleSaveContactData} disabled={isSavingPatient}> {isSavingPatient ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" />Salvando...</> : 'Salvar'} </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Card Responsável Principal */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
             <div> <CardTitle>Responsável Principal</CardTitle> </div>
             {patient.guardian && (
                <div className="flex gap-2">
                  <Button variant="outline" size="icon" onClick={() => setEditContactDialogOpen(true)} aria-label="Editar Responsável Principal"> <Pencil className="h-4 w-4" /> </Button>
                  {!isMinor && (
                    <Button variant="outline" size="icon" onClick={handleRemoveGuardianClick} aria-label="Remover Responsável Principal" disabled={isUnlinking || isDeletingContact}>
                      {(isUnlinking || isDeletingContact) ? <Loader2 className="h-4 w-4 animate-spin" /> : <UserMinus className="h-4 w-4" />}
                    </Button>
                  )}
                </div>
              )}
          </CardHeader>
          <CardContent>
             {patient.guardian ? (
                 <div className="space-y-2">
                   <p className="font-medium">{patient.guardian.name}</p>
                   <div className="text-sm text-muted-foreground space-y-1">
                     {patient.guardian.relationship_type && ( <p>Relação: {getRelationshipTypeLabel(patient.guardian.relationship_type)}</p> )}
                     {patient.guardian.phone && ( <p>Telefone: {patient.guardian.phone}</p> )}
                     {patient.guardian.email && ( <p>Email: {patient.guardian.email}</p> )}
                     {patient.guardian.cpf && ( <p>CPF: {patient.guardian.cpf}</p> )}
                   </div>
                 </div>
               ) : (
                 <div className="space-y-4">
                   <p className="text-sm text-muted-foreground">Nenhum responsável principal associado.</p>
                   <Button variant="outline" size="sm" className="w-full" onClick={() => setAddGuardianDialogOpen(true)}> <UserPlus className="h-4 w-4 mr-2" /> Adicionar Responsável </Button>
                 </div>
               )}
            </CardContent>
          </Card>
      </div>

      {/* Diálogos */}
      {patient?.guardian && (
        <EditContactDialog
          contact={patient.guardian}
          open={editContactDialogOpen}
          onOpenChange={setEditContactDialogOpen}
          patientId={patient.id}
          onContactUpdate={onDataUpdate || refetchDetails}
        />
      )}
      <AddGuardianDialog
        patientId={patient.id}
        open={addGuardianDialogOpen}
        onOpenChange={setAddGuardianDialogOpen}
        onGuardianAdded={onDataUpdate || refetchDetails}
      />
      <ConfirmDeleteDialog
        open={showGuardianUnlinkConfirm}
        onOpenChange={setShowGuardianUnlinkConfirm}
        title="Confirmar Desvinculação e Exclusão"
        description={`Tem certeza que deseja desvincular ${guardianToUnlink?.name || 'o responsável'} como responsável principal?`}
        itemName={guardianToUnlink?.name || ''}
        itemType="responsável e seu registro de contato"
        isDeleting={isUnlinking || isDeletingContact}
        onConfirm={handleGuardianUnlinkConfirm}
        customWarning={
          <>
            <AlertTriangle className="h-4 w-4 text-destructive inline-block mr-1" />{' '}
            Esta ação também irá <strong>excluir permanentemente</strong> o contato{' '}
            <strong>{guardianToUnlink?.name}</strong> do sistema. Esta ação não pode ser desfeita.
          </>
        }
      />
    </div>
  );
}

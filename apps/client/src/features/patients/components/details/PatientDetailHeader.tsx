import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/shared/ui/avatar";
import { Badge } from "@/shared/ui/badge";
import { Patient } from "@/features/patients/types/patient.schema";
import { useSessionNotes } from "@/features/session-notes";
import { ExternalLink, Calendar, Phone, Mail, MapPin, Clock, User } from "lucide-react";

// Remover props de edição global
interface PatientDetailHeaderProps {
  patient: Patient;
  onNewSession: () => void;
}

export function PatientDetailHeader({
  patient,
  onNewSession,
}: PatientDetailHeaderProps) {
  const { openQuickNote } = useSessionNotes();

  const handleFloatingNote = () => {
    const patientData = {
      id: patient.id,
      name: patient.full_name,
      avatar: undefined // Patient type doesn't have avatar_url
    };

    openQuickNote(patientData, 1);
  };

  const getPatientInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  };

  return (
    <div className="py-6">
      <div className="flex items-start gap-6">
        {/* Avatar */}
        <Avatar className="h-20 w-20 border-4 border-background shadow-lg">
          <AvatarImage src={undefined} alt={patient.full_name} />
          <AvatarFallback className="text-lg font-semibold bg-primary/10 text-primary">
            {getPatientInitials(patient.full_name)}
          </AvatarFallback>
        </Avatar>

        {/* Patient Info */}
        <div className="flex-1 space-y-3">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold text-foreground">{patient.full_name}</h1>
            <Badge variant="secondary" className="text-xs">
              <User className="h-3 w-3 mr-1" />
              Paciente
            </Badge>
          </div>

          {/* Quick Info Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            {patient.date_of_birth && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>{calculateAge(patient.date_of_birth)} anos</span>
              </div>
            )}

            {patient.phone && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span>{patient.phone}</span>
              </div>
            )}

            {patient.email && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span className="truncate">{patient.email}</span>
              </div>
            )}

            <div className="flex items-center gap-2 text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>Última consulta: --</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col gap-2">
          <Button onClick={onNewSession} size="sm" className="min-w-[120px]">
            <Calendar className="h-4 w-4 mr-2" />
            Nova Sessão
          </Button>
          <Button
            onClick={handleFloatingNote}
            size="sm"
            variant="outline"
            className="min-w-[120px]"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Nota Rápida
          </Button>
        </div>
      </div>
    </div>
  );
}

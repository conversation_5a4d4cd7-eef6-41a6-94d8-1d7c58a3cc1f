import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from '@/shared/ui/card';
import { Label } from '@/shared/ui/label';
import { Textarea } from '@/shared/ui/textarea';
import { Button } from '@/shared/ui/button';
import { Plus } from 'lucide-react';
import { Patient } from '@/features/patients/types/patient.schema';
import { useState } from 'react';
import { AssessmentFormDialog } from '../AssessmentFormDialog';
import { ClinicalNoteFormDialog } from '../ClinicalNoteFormDialog';
import { useAssessmentsQuery } from '@/features/assessment/hooks/useAssessmentsQuery';
import { useSessionNotesQuery } from '@/features/notes/hooks/useSessionNotesQuery';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/shared/ui/accordion';
import { Skeleton } from '@/shared/ui/skeleton';
import { formatDate } from '@/shared/lib/date-utils';

interface HistoryTabProps {
  patient: Patient;
}

export function HistoryTab({
  patient,
}: HistoryTabProps) {
  const [isAssessmentModalOpen, setIsAssessmentModalOpen] = useState(false);
  const [isNoteModalOpen, setIsNoteModalOpen] = useState(false);
  const { data: assessments, isLoading, isError } = useAssessmentsQuery(patient.id);
  const { 
    sessionNotes, 
    isLoading: isLoadingNotes, 
    isError: isErrorNotes 
  } = useSessionNotesQuery(patient.id);

  const currentTherapyType = patient.notes?.match(/terapia:\s*([^,\n]+)/i)?.[1]?.trim() || 'Não definido';
  const currentMainComplaint = patient.notes?.replace(/terapia:\s*([^,\n]+)/i, '').replace(/escola:\s*([^,\n]+)/i, '').replace(/pendência financeira/i, '').trim() || 'Não informado';

  return (
    <>
      <AssessmentFormDialog
        isOpen={isAssessmentModalOpen}
        onOpenChange={setIsAssessmentModalOpen}
        patientId={patient.id}
      />
      <ClinicalNoteFormDialog
        isOpen={isNoteModalOpen}
        onOpenChange={setIsNoteModalOpen}
        patientId={patient.id}
      />
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Anamnese & Histórico</h2>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Anamnese</CardTitle>
              <CardDescription>Histórico clínico e informações relevantes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="therapy_type">Tipo de Terapia (Detectado)</Label>
                   <p className="text-sm text-muted-foreground border p-2 rounded-md min-h-[40px]">
                     {currentTherapyType}
                   </p>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="main_complaint">Queixa Principal / Observações</Label>
                   <Textarea
                     id="main_complaint"
                     value={currentMainComplaint}
                     readOnly
                     className="min-h-[100px] text-muted-foreground"
                   />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Histórico de Avaliações</CardTitle>
              <CardDescription>Avaliações realizadas com o paciente</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading && (
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              )}
              {isError && (
                <div className="text-center py-8 text-destructive">
                  <p>Erro ao carregar as avaliações.</p>
                </div>
              )}
              {!isLoading && !isError && assessments && assessments.length > 0 && (
                <Accordion type="single" collapsible className="w-full">
                  {assessments.map((assessment) => (
                    <AccordionItem value={assessment.id} key={assessment.id}>
                      <AccordionTrigger>
                        <div className="flex justify-between w-full pr-4">
                          <span>{assessment.title}</span>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(new Date(assessment.created_at))}
                          </span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="whitespace-pre-wrap text-muted-foreground">
                        {assessment.content || 'Nenhum conteúdo detalhado fornecido.'}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              )}
              {!isLoading && !isError && (!assessments || assessments.length === 0) && (
                <div className="text-center py-8 text-muted-foreground">
                  <p>Nenhuma avaliação registrada.</p>
                  <Button variant="link" size="sm" onClick={() => setIsAssessmentModalOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Adicionar Avaliação
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <CardHeader className="flex flex-row justify-between items-start space-y-0">
              <div>
                <CardTitle>Observações Clínicas Adicionais</CardTitle>
                <CardDescription>Registros e observações importantes ao longo do tempo</CardDescription>
              </div>
              <Button onClick={() => setIsNoteModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Nova Observação
              </Button>
            </CardHeader>
            <CardContent>
              {isLoadingNotes && (
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              )}
              {isErrorNotes && (
                <div className="text-center py-8 text-destructive">
                  <p>Erro ao carregar as observações clínicas.</p>
                </div>
              )}
              {!isLoadingNotes && !isErrorNotes && sessionNotes && sessionNotes.length > 0 && (
                <Accordion type="single" collapsible className="w-full">
                  {sessionNotes.map((note) => (
                    <AccordionItem value={note.id} key={note.id}>
                      <AccordionTrigger>
                        <div className="flex justify-between w-full pr-4">
                          <span>{note.title || "Observação Clínica"}</span>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(new Date(note.created_at))}
                          </span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="whitespace-pre-wrap text-muted-foreground">
                        {note.content || 'Nenhum conteúdo detalhado fornecido.'}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              )}
              {!isLoadingNotes && !isErrorNotes && (!sessionNotes || sessionNotes.length === 0) && (
                <div className="text-center py-8 text-muted-foreground">
                  <p>Nenhuma observação clínica registrada.</p>
                  <Button variant="link" size="sm" onClick={() => setIsNoteModalOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Adicionar Observação
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}

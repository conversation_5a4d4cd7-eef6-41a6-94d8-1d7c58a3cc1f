import React, { useState } from 'react';
import { useParams } from '@tanstack/react-router';
import { DocumentEditor } from './DocumentEditor';
import { DocumentUploadDialog } from '@/features/documents/components/DocumentUploadDialog';
import { DocumentTypeIcon } from '@/features/documents/components/DocumentTypeIcon';
import { EditDocumentMetadataDialog } from '@/features/documents/components/EditDocumentMetadataDialog';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Badge } from '@/shared/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/shared/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Separator } from '@/shared/ui/separator';
import { useUserPreferences } from '@/shared/hooks/useUserPreferences';
import { useDocumentsQuery } from '@/features/documents/hooks/useDocumentsQuery';
import { useToast } from '@/shared/hooks/use-toast';
import { formatDate } from './utils';
import { formatFileSize } from '@/shared/lib/utils';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/shared/ui/alert-dialog';
import {
  Search,
  LayoutGrid,
  List,
  Plus,
  Upload,
  Download,
  Trash2,
  MoreHorizontal,
  FileText,
  Star,
  X,
  Filter,
  ChevronLeft,
  Loader2,
  AlertCircle,
  Eye,
  FileEdit,
  ImageIcon,
  Music,
  Video,
  File as FileIcon
} from 'lucide-react';
import { useDocumentMutations } from '@/features/documents/hooks/useDocumentMutations';

import { Document } from "@/features/documents/types/document.schema";

// Categorias para filtragem
const documentCategories = [
  { id: 'all', label: 'Todos' },
  { id: 'relatórios', label: 'Relatórios' },
  { id: 'avaliações', label: 'Avaliações' },
  { id: 'encaminhamentos', label: 'Encaminhamentos' },
  { id: 'médicos', label: 'Documentos Médicos' },
  { id: 'gravações', label: 'Gravações' },
  { id: 'imagens', label: 'Imagens' },
  { id: 'exercícios', label: 'Exercícios' },
  { id: 'escola', label: 'Escola' }
];

// Função para obter o ícone baseado no tipo de arquivo
const getFileIcon = (type: string, size: 'sm' | 'md' | 'lg' = 'md') => {
  const sizeMap = {
    sm: 3.5,
    md: 5,
    lg: 8
  };
  const iconSize = sizeMap[size];

  switch (type) {
    case 'pdf':
      return <FileText className={`h-${iconSize} w-${iconSize} text-red-500`} />;
    case 'image':
      return <ImageIcon className={`h-${iconSize} w-${iconSize} text-blue-500`} />;
    case 'audio':
      return <Music className={`h-${iconSize} w-${iconSize} text-purple-500`} />;
    case 'video':
      return <Video className={`h-${iconSize} w-${iconSize} text-pink-500`} />;
    case 'docx':
      return <FileText className={`h-${iconSize} w-${iconSize} text-blue-400`} />;
    case 'xlsx':
      return <FileText className={`h-${iconSize} w-${iconSize} text-green-500`} />;
    default:
      return <FileIcon className={`h-${iconSize} w-${iconSize} text-gray-500`} />;
  }
};

// Componente principal
export function DocumentsTab() {
  const { patientId } = useParams({ from: '/_authenticated/person/$patientId' });
  const { toast } = useToast();

  // Estados
  const [isCreatingDocument, setIsCreatingDocument] = useState(false);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [activeTab, setActiveTab] = useState('all');
  const [view, setView] = useUserPreferences<'grid' | 'list'>('documentsView', 'grid');
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [appliedFilters, setAppliedFilters] = useState<string[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null);
  const [documentToEdit, setDocumentToEdit] = useState<Document | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [documentToEditContent, setDocumentToEditContent] = useState<Document | null>(null);
  const [editingContent, setEditingContent] = useState('');

  const buildFilters = () => {
    return {
      patient_id: patientId,
      // search: searchTerm || undefined, // Backend search by title to be implemented
      is_favorite: activeTab === 'starred' ? true : undefined,
    };
  };

  const {
    documents,
    isLoading,
    isError,
    error,
    refetch,
    getDownloadUrl,
    getDocumentContent,
  } = useDocumentsQuery(buildFilters());

  const {
    deleteDocument,
    updateDocument,
    createDocumentFromHtml,
    updateDocumentContent,
    isCreatingFromHtml,
    isUpdatingMetadata,
    isUpdatingContent,
    isDeleting,
  } = useDocumentMutations();

  // Efeito para refetch quando os filtros mudarem
  React.useEffect(() => {
    refetch();
  }, [searchTerm, activeTab, refetch]);

  const filteredDocuments = documents.filter(doc => {
    const categoryMatch = activeCategory === 'all' ? true : doc.tags?.includes(activeCategory);
    const searchMatch = searchTerm ? doc.title.toLowerCase().includes(searchTerm.toLowerCase()) : true;
    return categoryMatch && searchMatch;
  });

  const handleDocumentClick = (doc: Document) => {
    setSelectedDocument(doc);
  };

  const handleCloseDocument = () => {
    setSelectedDocument(null);
  };

  const handleToggleStar = async (doc: Document, e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    updateDocument({ id: doc.id, data: { is_favorite: !doc.is_favorite }}, {
        onSuccess: () => {
             toast({ title: doc.is_favorite ? 'Removido dos favoritos' : 'Adicionado aos favoritos' });
        },
        onError: () => toast({ title: 'Erro', description: 'Não foi possível atualizar o status de favorito.', variant: 'destructive' })
    });
  };

  const handleView = async (doc: Document) => {
    try {
      const { url } = await getDownloadUrl(doc.id);
      window.open(url, '_blank');
    } catch (error) {
      toast({ title: 'Erro ao visualizar', description: 'Não foi possível obter a URL do documento.', variant: 'destructive' });
    }
  };

  const handleDownload = async (doc: Document) => {
    try {
      const { url, filename } = await getDownloadUrl(doc.id);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    } catch (error) {
      toast({ title: 'Erro ao baixar', description: 'Não foi possível obter a URL do documento.', variant: 'destructive' });
    }
  };

  const handleEdit = async (doc: Document) => {
    if (doc.mime_type === 'text/html') {
      try {
        const content = await getDocumentContent(doc.id);
        setEditingContent(content);
        setDocumentToEditContent(doc);
      } catch (error) {
        toast({ title: 'Erro ao carregar conteúdo', description: 'Não foi possível buscar o conteúdo para edição.', variant: 'destructive'});
      }
    } else {
      setDocumentToEdit(doc);
      setIsEditDialogOpen(true);
    }
  };

  const handleDelete = (doc: Document, e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    setDocumentToDelete(doc);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (!documentToDelete) return;
    deleteDocument(documentToDelete.id, {
      onSuccess: () => {
        toast({ title: 'Documento excluído' });
        if (selectedDocument && selectedDocument.id === documentToDelete.id) setSelectedDocument(null);
        setIsDeleteDialogOpen(false);
        setDocumentToDelete(null);
      },
      onError: (e) => toast({ title: 'Erro ao excluir', description: e.message, variant: 'destructive' })
    });
  };

  const handleSaveDocument = (content: string, title: string) => {
    if (!patientId) return;
    createDocumentFromHtml({ title, content, patient_id: patientId }, {
        onSuccess: () => {
            toast({ title: 'Documento Salvo!' });
            setIsCreatingDocument(false);
            refetch();
        }
    });
  };

  const handleUpdateDocument = (content: string, title: string) => {
    if (!documentToEditContent) return;
    const promises = [];
    if (title !== documentToEditContent.title) {
        promises.push(updateDocument({ id: documentToEditContent.id, data: { title } }));
    }
    promises.push(updateDocumentContent({ id: documentToEditContent.id, data: { content } }));
    Promise.all(promises).then(() => {
        toast({ title: 'Documento Atualizado!' });
        setDocumentToEditContent(null);
        setEditingContent('');
        refetch();
    }).catch((e) => toast({ title: 'Erro ao atualizar', description: e.message, variant: 'destructive' }));
  };

  const renderDocumentDetail = () => {
    if (!selectedDocument) return null;
    return (
      <Card className="h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Button variant="ghost" size="sm" onClick={handleCloseDocument} className="mr-2 h-7 w-7 p-0">
                <ChevronLeft className="h-3 w-3" />
              </Button>
              <CardTitle className="text-sm">{selectedDocument.title}</CardTitle>
            </div>
          </div>
        </CardHeader>
        <Separator />
        <CardContent className="pt-4">
          <div className="flex flex-col items-center justify-center border rounded-md bg-muted/20 h-[250px] mb-4 text-center p-4">
              <DocumentTypeIcon mimeType={selectedDocument.mime_type} filename={selectedDocument.filename} className="h-16 w-16" />
              <p className="mt-2 text-xs text-muted-foreground">
                Visualização não disponível aqui. <br/> Abra o arquivo para ver seu conteúdo.
              </p>
          </div>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <h3 className="text-xs font-medium mb-2">Ações</h3>
              <div className="space-y-1">
                <Button variant="outline" className="w-full justify-start text-xs h-7" size="sm" onClick={() => handleView(selectedDocument)}><Eye className="h-3 w-3 mr-2" />Visualizar</Button>
                <Button variant="outline" className="w-full justify-start text-xs h-7" size="sm" onClick={() => handleDownload(selectedDocument)}><Download className="h-3 w-3 mr-2" />Baixar</Button>
                <Button variant="outline" className="w-full justify-start text-xs h-7" size="sm" onClick={() => handleEdit(selectedDocument)}><FileEdit className="h-3 w-3 mr-2" />Editar</Button>
                <Button variant="outline" className="w-full justify-start text-xs h-7" size="sm" onClick={(e) => handleToggleStar(selectedDocument, e)}><Star className={`h-3 w-3 mr-2 ${selectedDocument.is_favorite ? 'fill-yellow-500 text-yellow-500' : ''}`} />{selectedDocument.is_favorite ? 'Remover Favorito' : 'Adicionar Favorito'}</Button>
                <Button variant="outline" className="w-full justify-start text-xs text-destructive h-7" size="sm" onClick={() => handleDelete(selectedDocument)} disabled={isDeleting}>
                  {isDeleting ? <><Loader2 className="h-3 w-3 mr-2 animate-spin" />Excluindo...</> : <><Trash2 className="h-3 w-3 mr-2" />Excluir</>}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const DocumentCard = ({ doc }: { doc: Document }) => (
    <div key={doc.id} className="border rounded-md hover:bg-accent/30 transition-colors p-2 cursor-pointer flex flex-col items-center text-center" onClick={() => handleDocumentClick(doc)}>
      <div className="relative mb-1">
        <div className="h-10 w-10 flex items-center justify-center">
          <DocumentTypeIcon mimeType={doc.mime_type} filename={doc.filename} className="h-10 w-10" />
        </div>
        <Button variant="ghost" size="icon" className={`absolute -top-1 -right-1 h-5 w-5 p-0 ${doc.is_favorite ? 'text-yellow-500' : 'text-muted-foreground hover:text-foreground'}`} onClick={(e) => handleToggleStar(doc, e)}>
          <Star className={`h-3 w-3 ${doc.is_favorite ? 'fill-yellow-500' : ''}`} />
        </Button>
      </div>
      <p className="font-medium text-xs truncate w-full">{doc.title}</p>
      <div className="mt-1 flex gap-1 justify-center">
        <DropdownMenu>
          <DropdownMenuTrigger asChild><Button variant="ghost" size="icon" className="h-6 w-6 p-0" onClick={(e) => e.stopPropagation()}><MoreHorizontal className="h-3 w-3" /></Button></DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="text-xs">
            <DropdownMenuItem onClick={() => handleView(doc)} className="text-xs py-1 h-7"><Eye className="h-3 w-3 mr-1" />Visualizar</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownload(doc)} className="text-xs py-1 h-7"><Download className="h-3 w-3 mr-1" />Baixar</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleEdit(doc)} className="text-xs py-1 h-7"><FileEdit className="h-3 w-3 mr-1" />Editar</DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => handleToggleStar(doc, e as unknown as React.MouseEvent)} className="text-xs py-1 h-7"><Star className={`h-3 w-3 mr-1 ${doc.is_favorite ? 'fill-yellow-500 text-yellow-500' : ''}`} />{doc.is_favorite ? 'Remover Favorito' : 'Adicionar Favorito'}</DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => handleDelete(doc, e)} className="text-destructive text-xs py-1 h-7" disabled={isDeleting}>{isDeleting ? <><Loader2 className="h-3 w-3 mr-1 animate-spin" />Excluindo...</> : <><Trash2 className="h-3 w-3 mr-1" />Excluir</> }</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );

  const DocumentRow = ({ doc }: { doc: Document }) => (
     <div key={doc.id} className="flex items-center p-2 border rounded-md hover:bg-accent/30 transition-colors cursor-pointer" onClick={() => handleDocumentClick(doc)}>
      <div className="flex-shrink-0 mr-2"><div className="h-8 w-8 flex items-center justify-center"><DocumentTypeIcon mimeType={doc.mime_type} filename={doc.filename} className="h-8 w-8" /></div></div>
      <div className="flex-1 min-w-0"><p className="font-medium text-xs truncate">{doc.title}</p></div>
      <div className="flex items-center gap-1 ml-2">
         <DropdownMenu>
          <DropdownMenuTrigger asChild><Button variant="ghost" size="icon" className="h-6 w-6 p-0" onClick={(e) => e.stopPropagation()}><MoreHorizontal className="h-3 w-3" /></Button></DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="text-xs">
             <DropdownMenuItem onClick={() => handleView(doc)} className="text-xs py-1 h-7"><Eye className="h-3 w-3 mr-1" />Visualizar</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownload(doc)} className="text-xs py-1 h-7"><Download className="h-3 w-3 mr-1" />Baixar</DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleEdit(doc)} className="text-xs py-1 h-7"><FileEdit className="h-3 w-3 mr-1" />Editar</DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => handleToggleStar(doc, e as unknown as React.MouseEvent)} className="text-xs py-1 h-7"><Star className={`h-3 w-3 mr-1 ${doc.is_favorite ? 'fill-yellow-500 text-yellow-500' : ''}`} />{doc.is_favorite ? 'Remover Favorito' : 'Adicionar Favorito'}</DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => handleDelete(doc, e)} className="text-destructive text-xs py-1 h-7" disabled={isDeleting}>{isDeleting ? <><Loader2 className="h-3 w-3 mr-1 animate-spin" />Excluindo...</> : <><Trash2 className="h-3 w-3 mr-1" />Excluir</> }</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );

  const renderContent = () => {
    if (isLoading) return <div className="text-center py-6"><Loader2 className="h-8 w-8 mx-auto text-primary animate-spin" /><p className="text-muted-foreground text-xs mt-2">Carregando...</p></div>;
    if (isError) return <div className="text-center py-6"><AlertCircle className="h-8 w-8 mx-auto text-destructive" /><p className="text-muted-foreground text-xs mt-2">{error?.message || "Erro ao carregar."}</p><Button variant="outline" size="sm" className="text-xs h-7 mt-2" onClick={() => refetch()}>Tentar novamente</Button></div>;
    if (filteredDocuments.length === 0) return <div className="text-center py-6"><FileText className="h-8 w-8 mx-auto text-muted-foreground" /><p className="text-muted-foreground text-xs mt-2">Nenhum documento.</p></div>;

    if (view === 'grid') return <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">{filteredDocuments.map(doc => <DocumentCard key={doc.id} doc={doc} />)}</div>;
    return <div className="space-y-0.5">{filteredDocuments.map(doc => <DocumentRow key={doc.id} doc={doc} />)}</div>;
  }

  if (documentToEditContent) {
    return <DocumentEditor patientId={patientId} documentId={documentToEditContent.id} initialTitle={documentToEditContent.title} initialContent={editingContent} onCancel={() => { setDocumentToEditContent(null); setEditingContent(''); }} onSave={handleUpdateDocument} />;
  }
  if (isCreatingDocument) {
    return <DocumentEditor patientId={patientId} onCancel={() => setIsCreatingDocument(false)} onSave={handleSaveDocument} />;
  }

  return (
    <div className="space-y-3">
      <DocumentUploadDialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen} patientId={patientId} onSuccess={() => refetch()} />
      <EditDocumentMetadataDialog document={documentToEdit} open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} onSuccess={() => refetch()} />
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader><AlertDialogTitle>Excluir Documento</AlertDialogTitle><AlertDialogDescription>Tem certeza que deseja excluir o documento "{documentToDelete?.title}"? Esta ação não pode ser desfeita.</AlertDialogDescription></AlertDialogHeader>
          <AlertDialogFooter><AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel><AlertDialogAction onClick={confirmDelete} disabled={isDeleting} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">{isDeleting ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" />Excluindo...</> : 'Excluir'}</AlertDialogAction></AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <div className="flex justify-between items-center">
        <h2 className="text-base font-semibold">Documentos</h2>
        <div className="flex items-center gap-1">
          <Tabs value={view} onValueChange={(v) => setView(v as 'grid' | 'list')} className="mr-1"><TabsList className="grid w-14 grid-cols-2 h-7"><TabsTrigger value="grid" className="px-1 py-0"><LayoutGrid className="h-3 w-3" /></TabsTrigger><TabsTrigger value="list" className="px-1 py-0"><List className="h-3 w-3" /></TabsTrigger></TabsList></Tabs>
          <Button variant="outline" size="sm" className="h-7 text-xs" onClick={() => setIsUploadDialogOpen(true)}><Upload className="h-3 w-3 mr-1" />Enviar</Button>
          <Button size="sm" className="h-7 text-xs" onClick={() => setIsCreatingDocument(true)}><Plus className="h-3 w-3 mr-1" />Novo</Button>
        </div>
      </div>

      {selectedDocument ? renderDocumentDetail() : (
        <Card>
          <CardHeader className="pb-2">
            <div className="flex flex-col gap-2">
              <div className="flex gap-1">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2 text-muted-foreground" />
                  <Input className="pl-7 pr-2 h-7 text-xs" placeholder="Buscar por título..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild><Button variant="outline" size="sm" className="h-7 text-xs"><Filter className="h-3 w-3 mr-1" />Filtrar</Button></DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="text-xs">
                    {documentCategories.map(cat => ( <DropdownMenuItem key={cat.id} onClick={() => setActiveCategory(cat.id)} className={`${activeCategory === cat.id ? "bg-accent" : ""} text-xs py-1 h-7`}>{cat.label}</DropdownMenuItem>))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <Tabs value={activeTab} onValueChange={setActiveTab}><TabsList className="grid grid-cols-3 h-7"><TabsTrigger value="all" className="text-xs">Todos</TabsTrigger><TabsTrigger value="recent" className="text-xs">Recentes</TabsTrigger><TabsTrigger value="starred" className="text-xs">Favoritos</TabsTrigger></TabsList></Tabs>
            </div>
          </CardHeader>
          <CardContent className="pt-0">{renderContent()}</CardContent>
        </Card>
      )}
    </div>
  );
}
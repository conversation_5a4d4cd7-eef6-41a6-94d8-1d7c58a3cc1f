import { useState } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/shared/ui/card';
import { Button } from '@/shared/ui/button';
import { Badge } from '@/shared/ui/badge';
import { Plus, Calendar, Clock, CheckCircle2, XCircle, FileEdit, PlusCircle } from 'lucide-react';
import { formatDate } from './utils';
import { useToast } from '@/shared/hooks/use-toast';
import { useSessionNotesQuery } from '@/features/notes/hooks/useSessionNotesQuery';
import { SessionNote } from '@/features/notes/types/session-note.schema';

interface SessionsTabProps {
  patientId: string;
}

export function SessionsTab({ patientId }: SessionsTabProps) {
  const { toast } = useToast();
  const navigate = useNavigate();

  const {
    sessionNotes,
    isLoading,
    error: queryError,
  } = useSessionNotesQuery(patientId);

  const handleNewNote = () => {
    navigate({ 
      to: '/person/$patientId/session-notes/new', 
      params: { patientId } 
    });
  };

  const handleEditNote = (note: SessionNote) => {
    navigate({ 
      to: '/person/$patientId/session-notes/$noteId/edit', 
      params: { patientId, noteId: note.id } 
    });
  };

  if (isLoading) return <div>Carregando sessões...</div>;
  if (queryError) return <div>Erro ao carregar sessões: {queryError.message}</div>;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Sessões & Evolução</h2>
        <Button onClick={handleNewNote}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Nova Nota de Sessão
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Histórico de Sessões</CardTitle>
          <CardDescription>Registro de todas as sessões realizadas e agendadas</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {isLoading ? (
              <div className="text-center py-8 text-muted-foreground">
                <p>Carregando notas de sessão...</p>
              </div>
            ) : sessionNotes.length > 0 ? (
              <div className="space-y-4">
                {sessionNotes.map(note => (
                  <div key={note.id} className="border rounded-md p-4 space-y-2">
                    <div className="flex justify-between items-start">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">
                          {formatDate(new Date(note.created_at))}
                        </span>
                        {note.appointment_id && (
                          <Badge variant="outline" className="ml-2">
                            <span className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              Agendamento #{note.appointment_id}
                            </span>
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="mt-1 font-medium">
                      {note.title}
                    </div>

                    <div className="mt-2 text-sm border-t pt-2">
                      <div dangerouslySetInnerHTML={{ __html: note.content }} />
                    </div>

                    <div className="flex justify-end gap-2 mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditNote(note)}
                      >
                        <FileEdit className="h-3 w-3 mr-1" />
                        Editar Nota
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p>Nenhuma sessão registrada.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Evolução do Tratamento</CardTitle>
          <CardDescription>Acompanhamento do progresso terapêutico</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <p>Nenhum registro de evolução disponível.</p>
            <p className="text-sm mt-1">
              Os dados de evolução serão exibidos após o registro de sessões.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

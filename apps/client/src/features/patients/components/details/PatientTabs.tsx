import { Tabs, Ta<PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/shared/ui/tabs";
import React from "react";

interface PatientTabsProps {
  overviewContent: React.ReactNode;
  personalDataContent: React.ReactNode;
  historyContent: React.ReactNode;
  sessionsContent: React.ReactNode;
  documentsContent: React.ReactNode;
  financialContent: React.ReactNode;
}

export function PatientTabs({
  overviewContent,
  personalDataContent,
  historyContent,
  sessionsContent,
  documentsContent,
  financialContent,
}: PatientTabsProps) {
  return (
    <Tabs defaultValue="personal-data" className="w-full">
      <TabsList className="grid w-full grid-cols-6">
        <TabsTrigger value="overview">Visão Geral</TabsTrigger>
        <TabsTrigger value="personal-data">Dados Cadastrais</TabsTrigger>
        <TabsTrigger value="history">Anamnese & Histórico</TabsTrigger>
        <TabsTrigger value="sessions">Sessões & Evolução</TabsTrigger>
        <TabsTrigger value="documents">Documentos</TabsTrigger>
        <TabsTrigger value="financial">Financeiro</TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="mt-4">
        {overviewContent}
      </TabsContent>
      <TabsContent value="personal-data" className="mt-4">
        {personalDataContent}
      </TabsContent>
      <TabsContent value="history" className="mt-4">
        {historyContent}
      </TabsContent>
      <TabsContent value="sessions" className="mt-4">
        {sessionsContent}
      </TabsContent>
      <TabsContent value="documents" className="mt-4">
        {documentsContent}
      </TabsContent>
      <TabsContent value="financial" className="mt-4">
        {financialContent}
      </TabsContent>
    </Tabs>
  );
}

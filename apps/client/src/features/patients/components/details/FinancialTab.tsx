import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/shared/ui/card';
import { Button } from '@/shared/ui/button';
import { Badge } from '@/shared/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/shared/ui/dropdown-menu";
import {
  Receipt,
  DollarSign,
  MoreVertical,
  Loader2,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { useGetTransactions } from '@/features/financial/hooks/useFinancialQuery';
import { useState, useMemo } from 'react';
import { format, parseISO, isValid } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Transaction } from '@/features/financial/types/transaction.schema';
import { TransactionFormDialog } from '@/features/financial/components/TransactionFormDialog';

interface FinancialTabProps {
  patientId: string;
}

const formatCurrency = (value: number | string) => {
  const numericValue = typeof value === 'string' ? parseFloat(value) : value;
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(numericValue);
};

const renderStatusBadge = (status: Transaction['status']) => {
  switch (status) {
    case "paid":
      return <Badge variant="default">Pago</Badge>;
    case "pending":
      return <Badge variant="secondary">Pendente</Badge>;
    case "overdue":
      return <Badge variant="destructive">Atrasado</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
};

export function FinancialTab({ patientId }: FinancialTabProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [defaultType, setDefaultType] = useState<'income' | 'expense'>('income');
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  
  const { data: transactions = [], isLoading } = useGetTransactions({ patient_id: patientId });

  const financialSummary = useMemo(() => {
    const summary = {
      balance: 0,
      totalPaid: 0,
      totalPending: 0,
    };
    transactions.forEach(t => {
      const amount = parseFloat(t.amount);
      if (t.type === 'income') {
        if (t.status === 'paid') {
          summary.balance += amount;
          summary.totalPaid += amount;
        } else if (t.status === 'pending' || t.status === 'overdue') {
          summary.balance -= amount; // Pending income is a debt
          summary.totalPending += amount;
        }
      } else if (t.type === 'expense') {
        // Assuming expenses are always 'paid' for this calculation, or handled differently
        summary.balance -= amount;
      }
    });
    return summary;
  }, [transactions]);

  const openDialog = (transaction?: Partial<Transaction>) => {
    if (transaction) {
      // Se for passada uma transação parcial (como ao adicionar nova receita/despesa)
      const transactionWithPatient = {
        ...transaction,
        patient_id: patientId // Pré-selecionar o paciente atual
      } as Transaction;
      setSelectedTransaction(transactionWithPatient);
      setDefaultType(transactionWithPatient.type || 'income');
    } else {
      setSelectedTransaction(null);
    }
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    setSelectedTransaction(null);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2 text-muted-foreground">Carregando histórico financeiro...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Financeiro</h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => { openDialog({ type: 'expense', status: 'pending' }); }}>
            <TrendingDown className="h-4 w-4 mr-2" />
            Adicionar Despesa
          </Button>
          <Button size="sm" onClick={() => { openDialog({ type: 'income', status: 'pending' }); }}>
            <TrendingUp className="h-4 w-4 mr-2" />
            Adicionar Receita
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo Devedor</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">
              {formatCurrency(financialSummary.totalPending)}
            </div>
            <p className="text-xs text-muted-foreground">
              {transactions.filter(t => t.status === 'pending' || t.status === 'overdue').length} transação(ões) pendente(s)
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Recebido</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(financialSummary.totalPaid)}
            </div>
            <p className="text-xs text-muted-foreground">
              Total de pagamentos recebidos
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Histórico de Transações</CardTitle>
          <CardDescription>Todas as receitas e despesas associadas ao paciente.</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Descrição</TableHead>
                <TableHead>Data</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead className="text-right">Valor</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.map(transaction => (
                <TableRow key={transaction.id}>
                  <TableCell className="font-medium">{transaction.description}</TableCell>
                  <TableCell>{(() => {
                    try {
                      if (transaction.transaction_date && typeof transaction.transaction_date === 'string') {
                        const date = parseISO(transaction.transaction_date);
                        return isValid(date) ? format(date, "dd/MM/yyyy", { locale: ptBR }) : 'Data inválida';
                      }
                      return 'Data inválida';
                    } catch (error) {
                      console.error('Error parsing transaction date:', error);
                      return 'Data inválida';
                    }
                  })()}</TableCell>
                  <TableCell>
                    <span className={`flex items-center gap-1 ${transaction.type === 'income' ? 'text-green-600' : 'text-destructive'}`}>
                      {transaction.type === 'income' ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                      {transaction.type === 'income' ? 'Receita' : 'Despesa'}
                    </span>
                  </TableCell>
                  <TableCell className="text-right font-medium">{formatCurrency(transaction.amount)}</TableCell>
                  <TableCell>
                    {renderStatusBadge(transaction.status)}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => { openDialog(transaction); }}>Ver Detalhes</DropdownMenuItem>
                        {(transaction.status === 'pending' || transaction.status === 'overdue') && <DropdownMenuItem>Registrar Pagamento</DropdownMenuItem>}
                        <DropdownMenuItem className="text-destructive">Excluir</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {transactions.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <p>Nenhuma transação encontrada para este paciente.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {isDialogOpen && (
        <TransactionFormDialog
          isOpen={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          transactionToEdit={selectedTransaction}
          defaultType={defaultType}
        />
      )}
    </div>
  );
}

import { useEffect } from 'react';
import { <PERSON><PERSON> } from "@/shared/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import { ScrollArea } from "@/shared/ui/scroll-area";
import { useLinkedPatients } from '@/features/contacts/hooks/useContactQuery';
// Remover import de usePatientContactMutations
// import { usePatientContactMutations } from '@/features/patients/hooks/usePatientContacts';
import { Contact } from '@/shared/types/contact.schema';
import { Loader2, UserMinus, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/shared/ui/alert';
import { toast } from '@/shared/hooks/use-toast';
import { Link } from '@tanstack/react-router';
import { useMutation, useQueryClient } from '@tanstack/react-query'; // Importar useMutation e useQueryClient
import { axiosInstance } from '@/shared/lib/api.client'; // Importar axiosInstance
import { CONTACT_QUERY_KEYS } from '@/features/contacts/hooks/constants'; // Importar chaves de query
import { PATIENT_QUERY_KEYS } from '@/features/patients/hooks/usePatientQuery'; // Importar chaves de query de paciente

interface ManageContactLinksDialogProps {
  contact: Contact | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ManageContactLinksDialog({
  contact,
  open,
  onOpenChange,
}: ManageContactLinksDialogProps) {
  const queryClient = useQueryClient(); // Obter queryClient

  const {
    linkedPatients,
    isLoading: isLoadingLinks,
    isError: isErrorLinks,
    refetch: refetchLinks,
  } = useLinkedPatients(contact?.id);

  // Criar mutação específica para desvincular aqui
  const unlinkMutation = useMutation({
    mutationFn: async ({ patientId, contactId }: { patientId: string; contactId: string }) => {
      // Chamar diretamente a API de desvinculação
      await axiosInstance.delete(`/protected/patients/${patientId}/contacts/${contactId}`);
    },
    onSuccess: (_, variables) => { // Usar variables para obter IDs
      toast({ title: "Sucesso", description: "Vínculo removido." });
      // Invalidar queries relevantes
      queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.linkedPatients(variables.contactId) });
      queryClient.invalidateQueries({ queryKey: PATIENT_QUERY_KEYS.detail(variables.patientId) }); // Invalidar detalhes do paciente afetado
      queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.all }); // Invalidar lista geral de contatos (para atualizar contagem)
      refetchLinks(); // Refetch local para atualizar a lista no diálogo imediatamente
    },
    onError: (error: any) => {
      toast({
          title: "Erro",
          description: `Não foi possível remover o vínculo: ${error?.response?.data?.message || error?.message || 'Erro desconhecido'}`,
          variant: "destructive",
      });
    }
  });


  const handleUnlink = (patientId: string) => {
    if (!contact) return;
    // Chamar a mutação específica passando as variáveis
    unlinkMutation.mutate({ contactId: contact.id, patientId: patientId });
  };

  useEffect(() => {
    if (open && contact?.id) {
      refetchLinks();
    }
  }, [open, contact?.id, refetchLinks]);


  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Gerenciar Vínculos de {contact?.name || 'Contato'}</DialogTitle>
          <DialogDescription>
            Veja e remova os pacientes aos quais este contato está vinculado (exceto como responsável principal).
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {isLoadingLinks ? (
            <div className="flex justify-center items-center h-32">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : isErrorLinks ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Erro ao carregar os vínculos deste contato. Tente novamente mais tarde.
              </AlertDescription>
            </Alert>
          ) : linkedPatients.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center">
              Este contato não está vinculado a nenhum paciente como contato secundário.
            </p>
          ) : (
            <ScrollArea className="h-64 rounded-md border">
              <div className="p-4 space-y-3">
                {linkedPatients.map((patient) => (
                  <div key={patient.id} className="flex items-center justify-between p-2 rounded-md border bg-muted/10">
                    <Link
                      to="/person/$patientId"
                      params={{ patientId: patient.id }}
                      className="text-sm font-medium hover:underline"
                      onClick={() => onOpenChange(false)}
                    >
                      {patient.full_name}
                    </Link>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleUnlink(patient.id)}
                      disabled={unlinkMutation.isPending} // Usar estado da nova mutação
                      className="text-xs text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      {unlinkMutation.isPending ? <Loader2 className="h-3 w-3 animate-spin mr-1" /> : <UserMinus className="h-3 w-3 mr-1" />}
                      Desvincular
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

import { useState, useMemo } from 'react';
import { Patient } from '@/features/patients/types/patient.schema';
import { Contact } from '@/shared/types/contact.schema';
import { useContactQuery } from '@/features/contacts/hooks/useContactQuery';
import { useContactMutations } from '@/features/contacts/hooks/useContactMutations';
import { usePatientContactMutations } from '@/features/patients/hooks/usePatientContacts';
import { Button } from '@/shared/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/shared/ui/dialog';
import { Label } from '@/shared/ui/label';
import { UserPlus, UserMinus, Search, Trash2, AlertCircle } from 'lucide-react';
import { Input } from '@/shared/ui/input';
import { Badge } from '@/shared/ui/badge';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { ConfirmDeleteDialog } from "@/shared/components/ConfirmDeleteDialog";

interface PatientContactsManagerProps {
  patient: Patient;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onContactsUpdate?: () => void;
}

function getRelationshipTypeLabel(type?: string | null): string {
  if (!type) return 'Não especificado';
  const types: Record<string, string> = {
    'parent': 'Mãe/Pai',
    'guardian': 'Responsável Legal',
    'relative': 'Familiar',
    'school': 'Escola',
    'professional': 'Profissional',
    'other': 'Outro'
  };
  return types[type] || type;
}

export function PatientContactsManager({
  patient,
  open,
  onOpenChange,
  onContactsUpdate,
}: PatientContactsManagerProps) {
  const [view, setView] = useState<"list" | "add">("list");
  const [contactToEdit, setContactToEdit] = useState<Contact | null>(null);
  const { allContacts, isLoading: isLoadingContacts } = useContactQuery();
  const { deleteContact, isDeleting } = useContactMutations();
  const { linkContact, unlinkContact, isLinking, isUnlinking } = usePatientContactMutations();

  const [searchTerm, setSearchTerm] = useState('');
  const [contactToDelete, setContactToDelete] = useState<Contact | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const patientContactIds = useMemo(() => patient.contacts?.map(c => c.id) || [], [patient.contacts]);

  const linkedContacts = useMemo(() => {
    return allContacts.filter(c => patientContactIds.includes(c.id) && c.role !== 'guardian');
  }, [allContacts, patientContactIds]);

  const availableContacts = useMemo(() => {
    return allContacts.filter(contact => {
      const isLinked = patientContactIds.includes(contact.id);
      const matchesSearch =
        searchTerm === '' ||
        contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (contact.email && contact.email.toLowerCase().includes(searchTerm.toLowerCase()));
      return !isLinked && matchesSearch;
    });
  }, [allContacts, patientContactIds, searchTerm]);

  const handleLinkContact = (contactId: string) => {
    linkContact({ contactId, patientIdToLink: patient.id, role: 'other' }, {
        onSuccess: onContactsUpdate
    });
  };

  const handleUnlinkContact = (contactId: string) => {
    unlinkContact({ contactId, patientId: patient.id }, {
        onSuccess: onContactsUpdate
    });
  };

  const handleDeleteClick = (contact: Contact) => {
    setContactToDelete(contact);
    setShowDeleteConfirm(true);
  };
  
  const handleDeleteContact = (contactId: string) => {
    deleteContact(contactId, {
      onSuccess: () => {
        setContactToDelete(null);
        setShowDeleteConfirm(false);
        onContactsUpdate?.();
      }
    });
  };

  const handleConfirmDelete = () => {
    if (contactToDelete) {
      handleDeleteContact(contactToDelete.id);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Gerenciar Contatos Vinculados a {patient?.full_name}</DialogTitle>
           <DialogDescription>
             Gerencie os contatos vinculados ao paciente. O Responsável Principal é gerenciado separadamente na aba de dados pessoais.
             <div className="mt-2 p-2 border rounded-md bg-blue-50 flex items-start gap-2">
               <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
               <div className="text-sm text-blue-800">
                 Aqui são exibidos apenas os contatos secundários. Você pode vincular novos contatos, desvincular ou excluir permanentemente.
               </div>
             </div>
           </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Buscar contatos disponíveis..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {view === "list" && (
            <>
              {linkedContacts.length > 0 && (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Contatos Vinculados</h3>
                  <ScrollArea className="h-48 rounded-md border p-2">
                    {linkedContacts.map(contact => (
                      <div key={contact.id} className="flex items-center justify-between p-2">
                        <div className="flex-1">
                          <p className="font-medium">{contact.name}</p>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            {contact.relationship_type && (
                              <Badge variant="outline" className="text-xs">
                                {getRelationshipTypeLabel(contact.relationship_type)}
                              </Badge>
                            )}
                            {contact.phone && <span>{contact.phone}</span>}
                          </div>
                        </div>
                        <div className="flex gap-1">
                          <Button size="sm" variant="ghost" onClick={() => handleUnlinkContact(contact.id)} disabled={isUnlinking}>Desvincular</Button>
                          <Button size="sm" variant="ghost" className="text-destructive" onClick={() => handleDeleteClick(contact)} disabled={isDeleting}>Excluir</Button>
                        </div>
                      </div>
                    ))}
                  </ScrollArea>
                </div>
              )}

              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground">Contatos Disponíveis</h3>
                <ScrollArea className="h-64 rounded-md border p-2">
                  {isLoadingContacts ? <p>Carregando...</p> : availableContacts.map(contact => (
                    <div key={contact.id} className="flex items-center justify-between p-2">
                      <Label htmlFor={`contact-link-${contact.id}`} className="cursor-pointer flex-1">
                        <p className="font-medium">{contact.name}</p>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          {contact.relationship_type && (
                            <Badge variant="outline" className="text-xs">
                              {getRelationshipTypeLabel(contact.relationship_type)}
                            </Badge>
                          )}
                          {contact.phone && <span>{contact.phone}</span>}
                        </div>
                      </Label>
                      <Button
                        variant="ghost"
                        size="sm"
                        id={`contact-link-${contact.id}`}
                        onClick={() => handleLinkContact(contact.id)}
                        disabled={isLinking}
                      >
                        <UserPlus className="h-4 w-4 mr-1" />
                        Vincular
                      </Button>
                    </div>
                  ))}

                  {searchTerm && availableContacts.length === 0 && (
                    <p className="text-center py-4 text-muted-foreground text-sm">
                      Nenhum contato encontrado.
                    </p>
                  )}
                </ScrollArea>
              </div>
              <Button variant="link" onClick={() => setView('add')}>Adicionar Novo Contato</Button>
            </>
          )}

          {view === "add" && (
            <div>
              {/* Formulário de criação/edição de contato aqui */}
              <Button variant="ghost" onClick={() => setView('list')}>Voltar</Button>
            </div>
          )}
        </div>
      </DialogContent>

      <ConfirmDeleteDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        title="Excluir Contato"
        description={`Tem certeza que deseja excluir permanentemente o contato ${contactToDelete?.name}?`}
        onConfirm={handleConfirmDelete}
        isDeleting={isDeleting}
      />
    </Dialog>
  );
}

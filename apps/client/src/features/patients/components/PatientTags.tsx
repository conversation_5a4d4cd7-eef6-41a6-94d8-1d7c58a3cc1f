import { Badge } from "@/shared/ui/badge";
import { cn } from "@/shared/lib/utils";
import { Tag as TagIcon, Plus, X } from "lucide-react";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider
} from "@/shared/ui/tooltip";
import { Input } from "@/shared/ui/input";
import { useState } from "react";
import {Button} from "@/shared/ui/button";

export interface Tag {
  id: string;
  name: string;
  color: string;
}

interface PatientTagsProps {
  tags: Tag[];
  className?: string;
  onAddTag?: (tag: Omit<Tag, "id">) => void;
  onRemoveTag?: (tagId: string) => void;
  editable?: boolean;
  maxVisible?: number;
}

export function PatientTags({
  tags,
  className,
  onAddTag,
  onRemoveTag,
  editable = false,
  maxVisible = 3
}: PatientTagsProps) {
  const [newTagName, setNewTagName] = useState("");
  const [isAdding, setIsAdding] = useState(false);

  // Predefined colors for tags
  const tagColors = [
    "blue", "green", "purple", "amber", "pink",
    "orange", "indigo", "teal", "red", "gray"
  ];

  const getRandomColor = () => {
    return tagColors[Math.floor(Math.random() * tagColors.length)];
  };

  const handleAddTag = () => {
    if (newTagName.trim() && onAddTag) {
      onAddTag({
        name: newTagName.trim(),
        color: getRandomColor()
      });
      setNewTagName("");
      setIsAdding(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleAddTag();
    } else if (e.key === "Escape") {
      setIsAdding(false);
      setNewTagName("");
    }
  };

  const visibleTags = tags.slice(0, maxVisible);
  const remainingCount = tags.length - maxVisible;

  return (
    <div className={cn("flex flex-wrap gap-1.5 items-center", className)}>
      {tags.length === 0 && !isAdding && (
        <span className="text-xs text-muted-foreground">Sem etiquetas</span>
      )}

      {visibleTags.map((tag) => (
        <TooltipProvider key={tag.id}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                variant="outline"
                className={cn(
                  "flex items-center gap-1 text-xs py-0 px-2 h-5",
                  `bg-${tag.color}-100 text-${tag.color}-800 dark:bg-${tag.color}-900/30 dark:text-${tag.color}-300 border-${tag.color}-200 dark:border-${tag.color}-800`
                )}
              >
                <TagIcon className="h-2.5 w-2.5" />
                <span className="truncate max-w-20">{tag.name}</span>
                {editable && onRemoveTag && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemoveTag(tag.id);
                    }}
                    className="ml-1 rounded-full hover:bg-muted/20 p-0.5"
                  >
                    <X className="h-2.5 w-2.5" />
                  </button>
                )}
              </Badge>
            </TooltipTrigger>
            <TooltipContent side="top">{tag.name}</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}

      {remainingCount > 0 && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                variant="outline"
                className="bg-muted text-muted-foreground hover:bg-muted/80"
              >
                +{remainingCount}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              {tags.slice(maxVisible).map(tag => tag.name).join(", ")}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {editable && onAddTag && (
        <>
          {isAdding ? (
            <div className="flex items-center gap-1">
              <Input
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Nova etiqueta..."
                className="h-6 text-xs min-w-[120px] max-w-[150px]"
                autoFocus
                onBlur={() => {
                  if (newTagName.trim()) {
                    handleAddTag();
                  } else {
                    setIsAdding(false);
                  }
                }}
              />
            </div>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              className="h-5 px-1 text-xs"
              onClick={() => setIsAdding(true)}
            >
              <Plus className="h-3 w-3 mr-1" />
              Adicionar
            </Button>
          )}
        </>
      )}
    </div>
  );
}
// Remover imports não utilizados do react-hook-form
import { Checkbox } from "@/shared/ui/checkbox";
import {
  // FormControl, // Removido
  FormItem,
  FormLabel,
  FormDescription,
  FormMessage,
} from "@/shared/ui/form";
import { useContactQuery } from "@/features/contacts/hooks/useContactQuery";
import { Contact } from "@/shared/types/contact.schema"; // Importar o tipo Contact
import { Loader2, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { ScrollArea } from "@/shared/ui/scroll-area";

interface PatientContactsSectionProps {
  // Props para gerenciar seleção externamente
  selectedContactIds: string[];
  onSelectedContactIdsChange: (ids: string[]) => void;
  isEditing: boolean; // Para habilitar/desabilitar checkboxes
}

export function PatientContactsSection({
  selectedContactIds,
  onSelectedContactIdsChange,
  isEditing
}: PatientContactsSectionProps) {
  // Chamar o hook e pegar os dados/estado diretamente
  // O hook retorna um objeto com a query, não o erro diretamente
  const {
    contacts: availableContacts,
    isLoading,
    // contactsQuery, // Descomentar se precisar de mais detalhes da query
  } = useContactQuery(); // Chamar o hook diretamente

  // Acessar o erro da query específica (assumindo que useContactQuery retorna contactsQuery internamente ou que isLoading cobre o estado de erro inicial)
  // Para simplificar, vamos confiar no isLoading por enquanto e no tratamento de erro dentro do hook que usa toast.
  // Se um tratamento de erro visual for estritamente necessário aqui, precisaríamos expor o estado de erro da query interna.

  // Remover useController
  // const { field } = useController({ ... });

  const handleCheckboxChange = (contactId: string, checked: boolean) => {
    // Usar as props para gerenciar o estado
    const currentSelectedIds: string[] = selectedContactIds || [];
    let newSelectedIds: string[];

    if (checked) {
      newSelectedIds = [...currentSelectedIds, contactId];
    } else {
      newSelectedIds = currentSelectedIds.filter((id) => id !== contactId);
    }
    // Chamar a função passada via props para atualizar o estado no componente pai
    onSelectedContactIdsChange(newSelectedIds);
  };

  if (isLoading) {
    return (
      <div className="flex items-center text-sm text-muted-foreground p-4">
        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Carregando contatos...
      </div>
    );
  }

  // Removendo a verificação explícita de 'error' aqui, pois o hook já mostra um toast em caso de erro.
  // Se a query falhar, isLoading pode permanecer true ou availableContacts será vazio/undefined.
  // A verificação abaixo já cobre o caso de não haver contatos.

  // if (error) { // Remover esta seção
  //   return (
  //     <Alert variant="destructive" className="mt-4">
  //       <AlertCircle className="h-4 w-4" />
  //       <AlertDescription>
  //         Erro ao carregar contatos: {error.message}
  //       </AlertDescription>
  //     </Alert>
  //   );
  // }

  if (!availableContacts || availableContacts.length === 0) {
     return (
       <Alert variant="default" className="mt-4">
         <AlertCircle className="h-4 w-4" />
         <AlertDescription>
           Nenhum contato geral cadastrado ainda. Você pode cadastrá-los na seção 'Pessoas'.
         </AlertDescription>
       </Alert>
     );
  }

  return (
    <FormItem className="space-y-4 rounded-lg border bg-muted/30 p-4 mt-4">
       <div className="mb-4">
        <FormLabel className="text-base">Contatos Associados</FormLabel>
        <FormDescription>
          Selecione outros contatos (profissionais, familiares secundários, etc.) relacionados a este paciente.
        </FormDescription>
      </div>
      <ScrollArea className="h-48"> {/* Adjust height as needed */}
        <div className="space-y-2 pr-4">
          {/* Adicionar tipo explícito para 'contact' */}
          {availableContacts.map((contact: Contact) => (
            <FormItem
              key={contact.id}
              className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-3 shadow-sm"
            >
              {/* Remover FormControl desnecessário */}
              <Checkbox
                checked={selectedContactIds?.includes(contact.id)} // Usar prop
                onCheckedChange={(checked) => handleCheckboxChange(contact.id, !!checked)}
                disabled={!isEditing} // Usar prop para desabilitar
                aria-label={`Selecionar ${contact.name}`}
              />
              <div className="space-y-1 leading-none">
                {/* Adicionar clique na label para marcar/desmarcar checkbox */}
                <FormLabel
                  className={`font-normal ${isEditing ? 'cursor-pointer' : 'cursor-default'}`}
                  onClick={() => {
                    if (isEditing) {
                      handleCheckboxChange(contact.id, !selectedContactIds?.includes(contact.id));
                    }
                  }}
                >
                  {contact.name}
                </FormLabel>
                {contact.relationship_type && (
                   <p className="text-xs text-muted-foreground">
                     {contact.relationship_type}
                   </p>
                )}
                 {contact.phone && (
                   <p className="text-xs text-muted-foreground">
                     {contact.phone}
                   </p>
                )}
              </div>
            </FormItem>
          ))}
        </div>
      </ScrollArea>
      <FormMessage />
    </FormItem>
  );
}

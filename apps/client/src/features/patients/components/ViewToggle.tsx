// ViewToggle.tsx
import { But<PERSON> } from '@/shared/ui/button';
import { Grid2X2, List } from 'lucide-react';

interface ViewToggleProps {
  view: 'grid' | 'list';
  onViewChange: (view: 'grid' | 'list') => void;
}

export function ViewToggle({ view, onViewChange }: ViewToggleProps) {
  return (
    <div className="border rounded-md flex">
      <Button
        variant={view === 'grid' ? 'secondary' : 'ghost'}
        size="sm"
        className="rounded-r-none px-3 h-8"
        onClick={() => onViewChange('grid')}
        aria-label="Visualização em grade"
      >
        <Grid2X2 className="h-4 w-4" />
      </Button>
      <Button
        variant={view === 'list' ? 'secondary' : 'ghost'}
        size="sm"
        className="rounded-l-none px-3 h-8"
        onClick={() => onViewChange('list')}
        aria-label="Visualização em lista"
      >
        <List className="h-4 w-4" />
      </Button>
    </div>
  );
}
import { useNavigate } from "@tanstack/react-router";
import {
  Calendar,
  FileText,
  Edit,
  Trash2,
  MoreHorizontal,
  ClipboardList,
  Pencil
} from "lucide-react";
import { Button } from "@/shared/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/shared/ui/dropdown-menu";
import { Patient } from "@/features/patients/types/patient.schema";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider
} from "@/shared/ui/tooltip";

interface PatientQuickActionsProps {
  patient: Patient;
  onEdit?: (patient: Patient) => void;
  onDelete?: (patient: Patient) => void;
  onSchedule?: (patient: Patient) => void;
  onViewRecords?: (patient: Patient) => void;
  variant?: "row" | "compact" | "card";
  className?: string;
}

export function PatientQuickActions({
  patient,
  onEdit,
  onDelete,
  onSchedule,
  onViewRecords,
  variant = "row",
  className
}: PatientQuickActionsProps) {
  const navigate = useNavigate();


  const handleSchedule = () => {
    if (onSchedule) {
      onSchedule(patient);
    } else {
      navigate({ to: "/calendar", search: { patient_id: patient.id.toString() } });
    }
  };


  const handleViewRecords = () => {
    if (onViewRecords) {
      onViewRecords(patient);
    } else {
      // Implementação futura - navegar para o prontuário
      console.log("Ver prontuário do paciente:", patient.id);
    }
  };


  const handleEdit = () => {
    if (onEdit) {
      onEdit(patient);
    }
  };
  const handleDelete = () => {
    if (onDelete) {
      onDelete(patient);
    }
  };

  if (variant === "compact") {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={handleSchedule}>
            <Calendar className="h-4 w-4 mr-2" />
            Agendar
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleViewRecords}>
            <FileText className="h-4 w-4 mr-2" />
            Prontuário
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Editar
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleDelete}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Excluir
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  if (variant === "card") {
    return (
      <div className={`flex items-center gap-1 ${className}`}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleSchedule}
              >
                <Calendar className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Agendar consulta</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleViewRecords}
              >
                <FileText className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Ver prontuário</TooltipContent>
          </Tooltip>
        </TooltipProvider>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Editar
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleViewRecords}>
              <ClipboardList className="h-4 w-4 mr-2" />
              Evolução
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={handleDelete}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Excluir
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }


  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Button variant="outline" size="sm" onClick={handleSchedule}>
        <Calendar className="h-4 w-4 mr-2" />
        Agendar
      </Button>
      <Button variant="outline" size="sm" onClick={handleViewRecords}>
        <FileText className="h-4 w-4 mr-2" />
        Prontuário
      </Button>
      <Button variant="ghost" size="icon" onClick={handleEdit}>
        <Pencil className="h-4 w-4" />
      </Button>
    </div>
  );
}
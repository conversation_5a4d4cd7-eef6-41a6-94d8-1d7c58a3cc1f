import { z } from 'zod';
import { contactSchema, createContactSchema } from '@/shared/types/contact.schema'; // Importar schemas de contato

// --- Remover <PERSON>hemas Guardian ---
// Os schemas relacionados a Guardian não são mais necessários, pois foram fundidos em Contact.

// --- Patient Schemas ---

export const patientStatusValues = [
  'active',
  'evaluation',
  'discharged',
  'paused',
  'waiting'
] as const;

export type PatientStatus = typeof patientStatusValues[number];


export const therapyTypeValues = [
  'language',    // Linguagem
  'speech',      // Fala
  'voice',       // Voz
  'dysphagia',   // Disfagia
  'fluency',     // Fluência
  'orofacial',   // Motricidade Orofacial
  'aac',         // Comunicação alternativa
  'other'
] as const;

export type TherapyType = typeof therapyTypeValues[number];


export const patientSchema = z.object({
  id: z.string().uuid(),
  full_name: z.string(),
  date_of_birth: z.string().optional().nullable(), // Permitir null
  age: z.number().optional(),
  phone: z.string().optional().nullable(), // Permitir null
  email: z.string().email({ message: "Email inválido" }).optional().nullable().or(z.literal("")), // Corrigir validação
  address: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  // has_guardian: z.boolean().default(false), // Removido
  notes: z.string().optional().nullable(),
  cpf: z.string().optional().nullable(),

  // Campos específicos da aplicação (usados em filtros, etc.)
  status: z.enum(patientStatusValues).optional().nullable().default('active'), // Adicionado
  therapy_type: z.enum(therapyTypeValues).optional().nullable(), // Adicionado
  has_financial_issues: z.boolean().optional().default(false), // Adicionado
  school: z.string().optional().nullable(), // Adicionado
  tags: z.array(z.string()).optional(), // Adicionado

  // Campos adicionais que podem existir (verificar se estão no backend)
  gender: z.string().optional().nullable(),
  marital_status: z.string().optional().nullable(),
  rg: z.string().optional().nullable(),
  ethnicity: z.string().optional().nullable(),
  nationality: z.string().optional().nullable(),
  naturalness: z.string().optional().nullable(),
  occupation: z.string().optional().nullable(),
  is_active: z.boolean().optional().nullable(),
  created_at: z.string().optional().nullable(),
  updated_at: z.string().optional().nullable(),
  // Fim campos adicionais

  // Campos de relacionamento (preenchidos por queries/serviços)
  guardian: contactSchema.optional().nullable(), // Responsável agora é um Contato
  contacts: z.array(contactSchema).optional(), // Lista completa de contatos associados (descomentado)
});

// Schema para o link entre Paciente e Contato (usado em CreatePatient)
const contactLinkSchema = z.object({
  contact_id: z.string().uuid().optional(), // ID se já existir
  contact_data: createContactSchema.optional(), // Dados se for novo
  role: z.enum(['guardian', 'other']), // Definir roles permitidos
}).refine(data => data.contact_id || data.contact_data, {
  message: "É necessário fornecer contact_id ou contact_data",
  path: ["contact_data"], // Ou path: ["contact_id"]
});

export const createPatientSchema = patientSchema.omit({
  id: true,
  age: true,
  guardian: true, // Omitir campo preenchido por query
  contacts: true, // Omitir campo preenchido por query
  created_at: true,
  updated_at: true,
  // Omitir outros campos que não devem ser enviados na criação
  gender: true,
  marital_status: true,
  rg: true,
  ethnicity: true,
  nationality: true,
  naturalness: true,
  occupation: true,
  is_active: true,
}).extend({
  // Adicionar o array de contatos a serem criados/vinculados
  contacts: z.array(contactLinkSchema).optional(),
});

// Schema para atualizar dados básicos do paciente (vínculos gerenciados separadamente)
export const updatePatientSchema = patientSchema.pick({
  full_name: true,
  date_of_birth: true,
  cpf: true,
  phone: true,
  email: true,
  address: true,
  city: true,
  state: true,
  notes: true,
  is_active: true,
  school: true, // Adicionar school
  // Adicionar outros campos editáveis aqui
  gender: true,
  marital_status: true,
  rg: true,
  ethnicity: true,
  nationality: true,
  naturalness: true,
  occupation: true,
}).partial(); // Tornar todos os campos opcionais para atualização

export type Patient = z.infer<typeof patientSchema>;
export type CreatePatientData = z.infer<typeof createPatientSchema>;
export type UpdatePatientData = z.infer<typeof updatePatientSchema>;
export type ContactLinkData = z.infer<typeof contactLinkSchema>;

// Schema e tipo para SimplePatient (usado em listas vinculadas)
export const simplePatientSchema = z.object({
  id: z.string().uuid(),
  full_name: z.string(),
});
export type SimplePatient = z.infer<typeof simplePatientSchema>;

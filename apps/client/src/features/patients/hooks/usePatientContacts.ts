import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/shared/hooks/use-toast";
import axios from "axios";
import { axiosInstance } from "@/shared/lib/api.client";
import { PATIENT_QUERY_KEYS } from "./usePatientQuery";
import { CONTACT_QUERY_KEYS } from "@/features/contacts/hooks/constants";

// Este hook agora lida apenas com as *mutações* de vínculo/desvínculo
export function usePatientContactMutations() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Vincular contato a paciente
  const linkContactMutation = useMutation({
    mutationFn: async (data: { contactId: string; role: string; patientIdToLink: string }) => {
      return axiosInstance.post(`/protected/patients/${data.patientIdToLink}/contacts`, {
        contact_id: data.contactId,
        role: data.role,
      });
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: PATIENT_QUERY_KEYS.detail(variables.patientIdToLink),
      });
      queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.all });
      // Também invalidar a lista de pacientes vinculados ao contato, se aplicável
      queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.linkedPatients(variables.contactId) });

      toast({
        title: "Contato vinculado",
        description: "O contato foi vinculado ao paciente com sucesso",
      });
    },
    onError: (error: unknown) => {
      let errorMessage = "Não foi possível vincular o contato";
      if (axios.isAxiosError(error)) {
        errorMessage = error.response?.data?.message ?? error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      toast({
        title: "Erro ao vincular contato",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  // Desvincular contato de paciente
  const unlinkContactMutation = useMutation({
    mutationFn: async ({
      patientId,
      contactId,
    }: {
      patientId: string;
      contactId: string;
    }) => {
      const response = await axiosInstance.delete(
        `/protected/patients/${patientId}/contacts/${contactId}`
      );
      return response.data;
    },
    onSuccess: (_, { patientId }) => {
      queryClient.invalidateQueries({
        queryKey: PATIENT_QUERY_KEYS.detail(patientId),
      });
      queryClient.invalidateQueries({
        queryKey: CONTACT_QUERY_KEYS.all,
      }); // Invalida contatos não vinculados
    },
  });

  return {
    linkContact: linkContactMutation.mutateAsync,
    unlinkContact: unlinkContactMutation.mutateAsync,
    isLinking: linkContactMutation.isPending,
    isUnlinking: unlinkContactMutation.isPending,
  };
}

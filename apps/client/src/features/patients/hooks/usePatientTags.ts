import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '@/shared/lib/api.client';
import { useToast } from '@/shared/hooks/use-toast';
import { Tag } from '@/features/patients/components/PatientTags';

export const TAG_QUERY_KEYS = {
  all: ['tags'],
  byPatient: (patientId: string) => ['tags', 'patient', patientId]
};

export function usePatientTags() {
  const queryClient = useQueryClient();
  const { toast } = useToast();


  const tagsQuery = useQuery({
    queryKey: TAG_QUERY_KEYS.all,
    queryFn: async (): Promise<Tag[]> => {
      try {
        const response = await axiosInstance.get('/protected/tags');
        return response.data;
      } catch (error) {
        // Simulação de dados caso a API não exista ainda
        return [
          { id: "tag1", name: "Prioridade", color: "red" },
          { id: "tag2", name: "Escola X", color: "blue" },
          { id: "tag3", name: "Plano de Saúde", color: "green" },
          { id: "tag4", name: "TEA", color: "purple" },
          { id: "tag5", name: "Particular", color: "amber" }
        ];
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  // Consulta para obter tags de um paciente específico
  const getPatientTags = (patientId: string) => {
    const query = useQuery({
      queryKey: TAG_QUERY_KEYS.byPatient(patientId),
      queryFn: async (): Promise<Tag[]> => {
        try {
          const response = await axiosInstance.get(`/protected/patients/${patientId}/tags`);
          return response.data;
        } catch (error) {
          // Simulação para desenvolvimento
          const allTags = tagsQuery.data || [];
          return allTags.filter((_, index) => index % 2 === 0);
        }
      },
      enabled: !!patientId && tagsQuery.isSuccess,
    });

    return {
      tags: query.data || [],
      isLoading: query.isLoading
    };
  };

  const createTagMutation = useMutation({
  mutationFn: async (tag: Omit<Tag, "id">) => {
    const response = await axiosInstance.post('/protected/tags', tag);
    return response.data;
  },
  onSuccess: (data) => {
    queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.all });
    toast({
      title: "Etiqueta criada",
      description: `A etiqueta ${data.name} foi criada com sucesso!`
    });
  },
  onError: (error: any) => {
    toast({
      title: "Erro ao criar etiqueta",
      description: error?.response?.data?.message || "Ocorreu um erro ao criar a etiqueta",
      variant: "destructive"
    });
  }
});

const deleteTagMutation = useMutation({
  mutationFn: async (tagId: string) => {
    await axiosInstance.delete(`/protected/tags/${tagId}`);
    return tagId;
  },
  onSuccess: (tagId) => {
    queryClient.invalidateQueries({ queryKey: TAG_QUERY_KEYS.all });
    toast({
      title: "Etiqueta removida",
      description: "A etiqueta foi removida com sucesso!"
    });
  },
  onError: (error: any) => {
    toast({
      title: "Erro ao remover etiqueta",
      description: error?.response?.data?.message || "Ocorreu um erro ao remover a etiqueta",
      variant: "destructive"
    });
  }
});

const addTagToPatientMutation = useMutation({
  mutationFn: async ({ patientId, tagId }: { patientId: string, tagId: string }) => {
    const response = await axiosInstance.post(`/protected/patients/${patientId}/tags`, { tag_id: tagId });
    return response.data;
  },
  onSuccess: (_, variables) => {
    queryClient.invalidateQueries({
      queryKey: TAG_QUERY_KEYS.byPatient(variables.patientId)
    });
    toast({
      title: "Etiqueta adicionada",
      description: "A etiqueta foi adicionada ao paciente com sucesso!"
    });
  },
  onError: (error: any) => {
    toast({
      title: "Erro ao adicionar etiqueta",
      description: error?.response?.data?.message || "Ocorreu um erro ao adicionar a etiqueta ao paciente",
      variant: "destructive"
    });
  }
});

const removeTagFromPatientMutation = useMutation({
  mutationFn: async ({ patientId, tagId }: { patientId: string, tagId: string }) => {
    await axiosInstance.delete(`/protected/patients/${patientId}/tags/${tagId}`);
    return { patientId, tagId };
  },
  onSuccess: (_, variables) => {
    queryClient.invalidateQueries({
      queryKey: TAG_QUERY_KEYS.byPatient(variables.patientId)
    });
    toast({
      title: "Etiqueta removida",
      description: "A etiqueta foi removida do paciente com sucesso!"
    });
  },
  onError: (error: any) => {
    toast({
      title: "Erro ao remover etiqueta",
      description: error?.response?.data?.message || "Ocorreu um erro ao remover a etiqueta do paciente",
      variant: "destructive"
    });
  }
});

  return {
    // Dados
    tags: tagsQuery.data || [],
    isLoading: tagsQuery.isLoading,

    // Funções
    getPatientTags,

    // Mutações
    createTag: (tag: Omit<Tag, "id">) => createTagMutation.mutate(tag),
    deleteTag: (tagId: string) => deleteTagMutation.mutate(tagId),
    addTagToPatient: (patientId: string, tagId: string) =>
      addTagToPatientMutation.mutate({ patientId, tagId }),
    removeTagFromPatient: (patientId: string, tagId: string) =>
      removeTagFromPatientMutation.mutate({ patientId, tagId }),
  };
}
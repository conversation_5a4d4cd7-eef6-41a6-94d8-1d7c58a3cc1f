import { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { axiosInstance } from '@/shared/lib/api.client';
import { Patient, patientSchema, PatientStatus, TherapyType } from '@/features/patients/types/patient.schema';
import { calculateAge } from '@/shared/lib/date-utils';

export const PATIENT_QUERY_KEYS = {
  all: ['patients'] as const,
  lists: () => [...PATIENT_QUERY_KEYS.all, 'list'] as const,
  detail: (id: string) => [...PATIENT_QUERY_KEYS.all, 'detail', id] as const,
  filtered: (filters: PatientFilterOptions) => [...PATIENT_QUERY_KEYS.all, 'filtered', filters] as const,
};

export type { TherapyType };

export interface PatientFilterOptions {
  search?: string;
  status?: PatientStatus[];
  therapyTypes?: TherapyType[];
  ageGroups?: string[];
  tags?: string[];
  hasPendingPayments?: boolean;
}

const filterPatients = (patients: Patient[], filters: PatientFilterOptions): Patient[] => {
    return patients.filter((patient) => {
      if (
        filters.search &&
        !patient.full_name.toLowerCase().includes(filters.search.toLowerCase()) &&
        !(patient.email && patient.email.toLowerCase().includes(filters.search.toLowerCase())) &&
        !(patient.phone && patient.phone.includes(filters.search))
      ) {
        return false;
      }

      if (filters.status?.length && !filters.status.includes(patient.status || 'active')) {
        return false;
      }
      
      if (filters.therapyTypes?.length && (!patient.therapy_type || !filters.therapyTypes.includes(patient.therapy_type))) {
        return false;
      }

      if (filters.ageGroups?.length) {
        const age = patient.age ?? (patient.date_of_birth ? calculateAge(new Date(patient.date_of_birth)) : null);
        if (age === null) return false;

        const getAgeGroup = (age: number): string | undefined => {
            if (age < 3) return "first-infancy";
            if (age < 12) return "children";
            if (age < 18) return "teen";
            if (age < 60) return "adult";
            return "elder";
        }
        const ageGroup = getAgeGroup(age);
        if (!ageGroup || !filters.ageGroups.includes(ageGroup)) {
          return false;
        }
      }

      if (filters.tags?.length) {
         const patientTags = patient.tags ?? [];
         const hasMatchingTag = filters.tags.some(tag => patientTags?.includes(tag));
         if (!hasMatchingTag) {
           return false;
         }
      }

      if (filters.hasPendingPayments && !patient.has_financial_issues) {
        return false;
      }

      return true;
    });
}


export function usePatientQuery(options?: {
  initialPatientId?: string | null;
  initialFilters?: PatientFilterOptions;
}) {
  const [currentPatientId, setCurrentPatientId] = useState<string | null>(
    options?.initialPatientId || null
  );
  const [filters, setFilters] = useState<PatientFilterOptions>(
    options?.initialFilters || {}
  );

  const allPatientsQuery = useQuery({
    queryKey: PATIENT_QUERY_KEYS.lists(),
    queryFn: async (): Promise<Patient[]> => {
        const response = await axiosInstance.get('/protected/patients');
        const parsedData = z.array(patientSchema).safeParse(response.data);
        if (!parsedData.success) {
            console.error("Zod validation error (allPatientsQuery):", parsedData.error);
            throw new Error("Invalid data from API for patient list.");
        }
        return parsedData.data;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const patientDetailsQuery = useQuery({
    queryKey: PATIENT_QUERY_KEYS.detail(currentPatientId || ''),
    queryFn: async (): Promise<Patient | null> => {
      if (!currentPatientId) return null;
      const response = await axiosInstance.get(`/protected/patients/${currentPatientId}`);
      const parsedData = patientSchema.safeParse(response.data);
      if (!parsedData.success) {
        console.error("Zod validation error (patientDetailsQuery):", parsedData.error);
        throw new Error("Invalid data from API for patient details.");
      }
      return parsedData.data;
    },
    enabled: !!currentPatientId,
    staleTime: 1000 * 60 * 2, // 2 minutes
  });

  const filteredPatients = useMemo(() => {
    if (!allPatientsQuery.data) return [];
    if (Object.keys(filters).length === 0) return allPatientsQuery.data;
    return filterPatients(allPatientsQuery.data, filters);
  }, [allPatientsQuery.data, filters]);

  const isLoading = allPatientsQuery.isLoading || patientDetailsQuery.isLoading;

  return {
    // Data
    allPatients: allPatientsQuery.data || [],
    filteredPatients,
    currentPatient: patientDetailsQuery.data,
    
    // State
    filters,
    currentPatientId,

    // Loaders
    isLoading,
    isLoadingList: allPatientsQuery.isLoading,
    isLoadingDetails: patientDetailsQuery.isLoading,
    isError: allPatientsQuery.isError || patientDetailsQuery.isError,

    // Actions
    setCurrentPatientId,
    setFilters,
    refetchList: allPatientsQuery.refetch,
    refetchDetails: patientDetailsQuery.refetch,
  };
} 
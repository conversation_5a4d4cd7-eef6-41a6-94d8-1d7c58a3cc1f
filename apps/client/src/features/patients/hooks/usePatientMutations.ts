import { useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '@/shared/lib/api.client';
import { useToast } from '@/shared/hooks/use-toast';
import { Patient, CreatePatientData, UpdatePatientData, patientSchema } from '@/features/patients/types/patient.schema';
import { PATIENT_QUERY_KEYS } from './usePatientQuery';

export function usePatientMutations() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const createPatientMutation = useMutation({
    mutationFn: async (data: CreatePatientData): Promise<Patient> => {
      const response = await axiosInstance.post('/protected/patients', data);
      const parsedData = patientSchema.safeParse(response.data);
      if (!parsedData.success) {
        throw new Error("Invalid data from API on patient creation.");
      }
      return parsedData.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: PATIENT_QUERY_KEYS.lists() });
      toast({
        title: "Paciente cadastrado",
        description: `${data.full_name} foi cadastrado com sucesso!`
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao cadastrar paciente",
        description: error?.response?.data?.message || error.message,
        variant: "destructive"
      });
    }
  });

  const updatePatientMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string, data: UpdatePatientData }): Promise<Patient> => {
      const response = await axiosInstance.put(`/protected/patients/${id}`, data);
      const parsedData = patientSchema.safeParse(response.data);
      if (!parsedData.success) {
        throw new Error("Invalid data from API on patient update.");
      }
      return parsedData.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: PATIENT_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: PATIENT_QUERY_KEYS.detail(data.id) });
      toast({
        title: "Paciente atualizado",
        description: `${data.full_name} foi atualizado com sucesso!`
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao atualizar paciente",
        description: error?.response?.data?.message || error.message,
        variant: "destructive"
      });
    }
  });

  const deletePatientMutation = useMutation({
    mutationFn: async (id: string): Promise<string> => {
      await axiosInstance.delete(`/protected/patients/${id}`);
      return id;
    },
    onSuccess: (id) => {
      queryClient.invalidateQueries({ queryKey: PATIENT_QUERY_KEYS.lists() });
      queryClient.removeQueries({ queryKey: PATIENT_QUERY_KEYS.detail(id) });
      toast({
        title: "Paciente removido",
        description: "O paciente foi removido com sucesso!"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao remover paciente",
        description: error?.response?.data?.message || error.message,
        variant: "destructive"
      });
    }
  });

  return {
    createPatient: createPatientMutation.mutate,
    updatePatient: updatePatientMutation.mutate,
    deletePatient: deletePatientMutation.mutate,
    isCreating: createPatientMutation.isPending,
    isUpdating: updatePatientMutation.isPending,
    isDeleting: deletePatientMutation.isPending,
  };
} 
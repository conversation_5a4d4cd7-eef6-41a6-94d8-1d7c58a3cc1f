import { useState } from "react";
import { User, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Key, Camera } from "lucide-react";
import { useAuthQuery } from "@/features/auth/hooks";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/shared/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Switch } from "@/shared/ui/switch";
import { Separator } from "@/shared/ui/separator";
import { useToast } from "@/shared/hooks/use-toast";
import { useThemeStore } from "@/features/theme/stores/useThemeStore";
import { Loader2 } from "lucide-react";
import { UpdateUserData } from "@/features/auth/types/user.schema";

export function SettingsPage() {
  const { user, isLoading, updateProfile, changePassword, uploadProfilePicture } = useAuthQuery();
  const { toast } = useToast();
  const { theme, setTheme } = useThemeStore();

  const [isUpdatingProfile, setIsUpdatingProfile] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [profileData, setProfileData] = useState<UpdateUserData>({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    date_of_birth: "",
    cpf: "",
    address: "",
    city: "",
    state: ""
  });
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });

  // Inicializar dados do perfil quando o usuário é carregado
  useState(() => {
    if (user) {
      setProfileData({
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email,
        phone: user.phone || "",
        date_of_birth: user.date_of_birth || "",
        cpf: user.cpf || "",
        address: user.address || "",
        city: user.city || "",
        state: user.state || ""
      });
    }
  });

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdatingProfile(true);

    try {
      await updateProfile(profileData);
      toast({
        title: "Perfil atualizado",
        description: "Suas informações foram atualizadas com sucesso"
      });
    } catch {
      toast({
        title: "Erro ao atualizar perfil",
        description: "Não foi possível atualizar seus dados",
        variant: "destructive"
      });
    } finally {
      setIsUpdatingProfile(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast({
        title: "Senhas não coincidem",
        description: "A nova senha e a confirmação devem ser idênticas",
        variant: "destructive"
      });
      return;
    }

    setIsChangingPassword(true);

    try {
      await changePassword(passwordData.currentPassword, passwordData.newPassword);
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      });
      toast({
        title: "Senha alterada",
        description: "Sua senha foi alterada com sucesso"
      });
    } catch {
      toast({
        title: "Erro ao alterar senha",
        description: "Verifique se a senha atual está correta",
        variant: "destructive"
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleProfilePictureChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Formato não suportado",
        description: "Por favor, utilize imagens em formato JPG, PNG ou GIF",
        variant: "destructive"
      });
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "Arquivo muito grande",
        description: "O tamanho máximo permitido é 2MB",
        variant: "destructive"
      });
      return;
    }

    uploadProfilePicture(file);
  };

  if (isLoading) {
    return (
      <div className="container flex items-center justify-center h-full p-6">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Carregando configurações...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-full p-6">

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList>
          <TabsTrigger value="profile" className="flex items-center gap-1">
            <User className="h-4 w-4" />
            Perfil
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-1">
            <Shield className="h-4 w-4" />
            Segurança
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-1">
            <Bell className="h-4 w-4" />
            Notificações
          </TabsTrigger>
          <TabsTrigger value="appearance" className="flex items-center gap-1">
            {theme === 'dark' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
            Aparência
          </TabsTrigger>
        </TabsList>

        {/* Perfil */}
        <TabsContent value="profile">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Informações Pessoais</CardTitle>
                <CardDescription>
                  Atualize suas informações básicas de perfil
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleProfileUpdate} className="space-y-4">
                  <div className="grid gap-2">
                    <Label htmlFor="first_name">Nome</Label>
                    <Input
                      id="first_name"
                      value={profileData.first_name}
                      onChange={(e) => setProfileData({...profileData, first_name: e.target.value})}
                      required
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="last_name">Sobrenome</Label>
                    <Input
                      id="last_name"
                      value={profileData.last_name}
                      onChange={(e) => setProfileData({...profileData, last_name: e.target.value})}
                      required
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profileData.email}
                      onChange={(e) => setProfileData({...profileData, email: e.target.value})}
                      disabled
                    />
                    <p className="text-xs text-muted-foreground">
                      O email não pode ser alterado
                    </p>
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={profileData.phone}
                      onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="date_of_birth">Data de Nascimento</Label>
                    <Input
                      id="date_of_birth"
                      type="date"
                      value={profileData.date_of_birth?.split('T')[0] || ""}
                      onChange={(e) => setProfileData({...profileData, date_of_birth: e.target.value})}
                    />
                  </div>

                  <Separator className="my-4" />

                  <div className="grid gap-2">
                    <Label htmlFor="cpf">CPF</Label>
                    <Input
                      id="cpf"
                      value={profileData.cpf}
                      onChange={(e) => setProfileData({...profileData, cpf: e.target.value})}
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="address">Endereço</Label>
                    <Input
                      id="address"
                      value={profileData.address}
                      onChange={(e) => setProfileData({...profileData, address: e.target.value})}
                    />
                     <p className="text-xs text-muted-foreground">
                      O CPF e o endereço são necessários para a emissão de notas fiscais.
                    </p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="city">Cidade</Label>
                      <Input
                        id="city"
                        value={profileData.city}
                        onChange={(e) => setProfileData({...profileData, city: e.target.value})}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="state">Estado</Label>
                      <Input
                        id="state"
                        value={profileData.state}
                        onChange={(e) => setProfileData({...profileData, state: e.target.value})}
                        maxLength={2}
                      />
                    </div>
                  </div>

                  <Button type="submit" disabled={isUpdatingProfile}>
                    {isUpdatingProfile ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Salvando
                      </>
                    ) : "Salvar alterações"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Foto de Perfil</CardTitle>
                <CardDescription>
                  Atualize sua foto de perfil
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center">
                <div className="mb-6 relative">
                  <div className="h-32 w-32 rounded-full bg-secondary flex items-center justify-center overflow-hidden">
                    {user?.profile_picture ? (
                      <img
                        src={user.profile_picture}
                        alt={user.first_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-5xl font-medium text-secondary-foreground">
                        {user?.first_name?.[0] || "U"}
                      </span>
                    )}
                  </div>
                  <div className="absolute bottom-0 right-0">
                    <label htmlFor="profile-picture" className="cursor-pointer p-2 bg-primary text-primary-foreground rounded-full hover:bg-primary/90 transition-colors">
                      <Camera className="h-5 w-5" />
                      <input
                        type="file"
                        id="profile-picture"
                        className="hidden"
                        accept="image/png, image/jpeg, image/gif"
                        onChange={handleProfilePictureChange}
                      />
                    </label>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground text-center max-w-xs">
                  Clique no ícone da câmera para alterar sua foto. Imagens JPG, PNG ou GIF com até 2MB.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Segurança */}
        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Alterar Senha</CardTitle>
              <CardDescription>
                Mantenha sua conta segura com uma senha forte
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordChange} className="max-w-md space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="current_password">Senha Atual</Label>
                  <div className="relative">
                    <Key className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      id="current_password"
                      type="password"
                      className="pl-10"
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData({...passwordData, currentPassword: e.target.value})}
                      required
                    />
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="new_password">Nova Senha</Label>
                  <Input
                    id="new_password"
                    type="password"
                    value={passwordData.newPassword}
                    onChange={(e) => setPasswordData({...passwordData, newPassword: e.target.value})}
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="confirm_password">Confirmar Nova Senha</Label>
                  <Input
                    id="confirm_password"
                    type="password"
                    value={passwordData.confirmPassword}
                    onChange={(e) => setPasswordData({...passwordData, confirmPassword: e.target.value})}
                    required
                  />
                </div>

                <Button type="submit" disabled={isChangingPassword}>
                  {isChangingPassword ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Alterando
                    </>
                  ) : "Alterar Senha"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notificações */}
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Preferências de Notificação</CardTitle>
              <CardDescription>
                Escolha como deseja receber notificações da plataforma
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Sistema</h3>
                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Notificações no navegador</p>
                    <p className="text-xs text-muted-foreground">Receba alertas no navegador quando estiver usando a plataforma</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Sons de notificação</p>
                    <p className="text-xs text-muted-foreground">Reproduzir sons ao receber novas notificações</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Email</h3>
                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Lembretes de consulta</p>
                    <p className="text-xs text-muted-foreground">Receba emails sobre suas próximas consultas e compromissos</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Atualizações do sistema</p>
                    <p className="text-xs text-muted-foreground">Receba informações sobre novidades e atualizações da plataforma</p>
                  </div>
                  <Switch defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Marketing e promoções</p>
                    <p className="text-xs text-muted-foreground">Receba ofertas especiais e conteúdos exclusivos</p>
                  </div>
                  <Switch />
                </div>
              </div>

              <Button>Salvar preferências</Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Aparência */}
        <TabsContent value="appearance">
          <Card>
            <CardHeader>
              <CardTitle>Tema e Aparência</CardTitle>
              <CardDescription>
                Personalize a aparência da plataforma conforme sua preferência
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Tema</h3>
                <Separator />

                <div className="grid grid-cols-3 gap-4">
                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${theme === 'light' ? 'border-primary bg-accent' : 'hover:border-primary'}`}
                    onClick={() => setTheme('light')}
                  >
                    <div className="flex items-center justify-center mb-4">
                      <div className="h-10 w-10 rounded-full bg-white border flex items-center justify-center text-black">
                        <Sun className="h-6 w-6" />
                      </div>
                    </div>
                    <p className="text-center font-medium">Claro</p>
                  </div>

                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${theme === 'dark' ? 'border-primary bg-accent' : 'hover:border-primary'}`}
                    onClick={() => setTheme('dark')}
                  >
                    <div className="flex items-center justify-center mb-4">
                      <div className="h-10 w-10 rounded-full bg-slate-900 border flex items-center justify-center text-white">
                        <Moon className="h-6 w-6" />
                      </div>
                    </div>
                    <p className="text-center font-medium">Escuro</p>
                  </div>

                  <div
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${theme === 'system' ? 'border-primary bg-accent' : 'hover:border-primary'}`}
                    onClick={() => setTheme('system')}
                  >
                    <div className="flex items-center justify-center mb-4">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-br from-white to-slate-800 border flex items-center justify-center">
                        <div className="flex">
                          <Sun className="h-3 w-3 text-black" />
                          <Moon className="h-3 w-3 text-white" />
                        </div>
                      </div>
                    </div>
                    <p className="text-center font-medium">Sistema</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default SettingsPage;
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '@/shared/lib/api.client';
import { useToast } from '@/shared/hooks/use-toast';
import { Contact, CreateContactData, UpdateContactData } from "@/shared/types/contact.schema";
import { PATIENT_QUERY_KEYS } from "@/features/patients/hooks/usePatientQuery";
import { CONTACT_QUERY_KEYS } from './constants';
import { z } from 'zod';
import { createContactSchema, updateContactSchema } from '@/shared/types/contact.schema';

export function useContactMutations() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    const invalidateContacts = () => {
        queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.all });
    };

    const createContactMutation = useMutation({
        mutationFn: (data: CreateContactData): Promise<Contact> => {
            return axiosInstance.post("/protected/contacts", data).then(res => res.data);
        },
        onSuccess: () => {
            invalidateContacts();
            toast({ title: "Contato criado", description: "O novo contato foi criado com sucesso." });
        },
        onError: (error: any) => {
            toast({ title: "Erro ao criar contato", description: error.response?.data?.message || error.message, variant: "destructive" });
        },
    });

    const updateContactMutation = useMutation({
        mutationFn: ({ id, data }: { id: string; data: UpdateContactData }): Promise<Contact> => {
            return axiosInstance.put(`/protected/contacts/${id}`, data).then(res => res.data);
        },
        onSuccess: (data) => {
            invalidateContacts();
            queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.detail(data.id) });
            queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.linkedPatients(data.id) });
            queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.linksSummary(data.id) });
            queryClient.invalidateQueries({ queryKey: PATIENT_QUERY_KEYS.all });
            toast({ title: "Contato atualizado", description: "O contato foi atualizado com sucesso." });
        },
        onError: (error: any) => {
            toast({ title: "Erro ao atualizar contato", description: error.response?.data?.message || error.message, variant: "destructive" });
        },
    });

    const deleteContactMutation = useMutation({
        mutationFn: (id: string): Promise<void> => {
            return axiosInstance.delete(`/protected/contacts/${id}`).then(res => res.data);
        },
        onSuccess: (_, id) => {
            invalidateContacts();
            queryClient.removeQueries({ queryKey: CONTACT_QUERY_KEYS.detail(id) });
            queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.linkedPatients(id) });
            queryClient.invalidateQueries({ queryKey: CONTACT_QUERY_KEYS.linksSummary(id) });
            queryClient.invalidateQueries({ queryKey: PATIENT_QUERY_KEYS.all });
            toast({ title: "Contato excluído", description: "O contato foi excluído com sucesso." });
        },
        onError: (error: any) => {
            toast({ title: "Erro ao excluir contato", description: error.response?.data?.message || error.message, variant: "destructive" });
        },
    });

    return {
        createContact: createContactMutation.mutateAsync,
        updateContact: updateContactMutation.mutate,
        deleteContact: deleteContactMutation.mutate,
        isCreating: createContactMutation.isPending,
        isUpdating: updateContactMutation.isPending,
        isDeleting: deleteContactMutation.isPending,
    }
} 
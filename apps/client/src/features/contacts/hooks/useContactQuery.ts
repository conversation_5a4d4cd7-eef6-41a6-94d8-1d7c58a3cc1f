import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { z } from 'zod';
import { axiosInstance } from '@/shared/lib/api.client';
import { Contact, contactSchema } from "@/shared/types/contact.schema";
import { CONTACT_QUERY_KEYS } from './constants';
import { SimplePatient, simplePatientSchema } from "@/features/patients/types/patient.schema";


export function useContactQuery(options?: {
  initialSearchTerm?: string;
}) {
  const [searchTerm, setSearchTerm] = useState<string>(
    options?.initialSearchTerm || ''
  );

  const contactsQuery = useQuery({
    queryKey: CONTACT_QUERY_KEYS.all,
    queryFn: async (): Promise<Contact[]> => {
      try {
        const response = await axiosInstance.get('/protected/contacts');
        const parsedData = z.array(contactSchema).safeParse(response.data);
        if (!parsedData.success) {
            console.error("Zod validation error (contactsQuery):", parsedData.error);
            throw new Error("Invalid data received from API (contact list).");
        }
        return parsedData.data;
      } catch (error: unknown) {
        const apiError = error as any;
        console.error("Error loading contacts:", apiError?.response?.data?.message ?? (error instanceof Error ? error.message : "Unknown error"));
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5,
  });

  const filteredContacts = (contactsQuery.data || []).filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (contact.email && contact.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (contact.phone && contact.phone.includes(searchTerm))
  );

  const getContactById = (id: string) => {
    return contactsQuery.data?.find(c => c.id === id);
  }

  return {
    contacts: filteredContacts,
    allContacts: contactsQuery.data || [],
    isLoading: contactsQuery.isLoading,
    isError: contactsQuery.isError,
    error: contactsQuery.error,
    setSearchTerm,
    refetchContacts: contactsQuery.refetch,
    getContactById,
  };
}

const linkedPatientsSchema = z.array(simplePatientSchema);

export function useLinkedPatients(contactId: string | null | undefined) {
    const query = useQuery({
        queryKey: CONTACT_QUERY_KEYS.linkedPatients(contactId!),
        queryFn: async (): Promise<SimplePatient[]> => {
            if (!contactId) return [];
            try {
                const response = await axiosInstance.get(`/protected/contacts/${contactId}/linked-patients`);
                const parsedData = linkedPatientsSchema.safeParse(response.data);
                if (!parsedData.success) {
                    console.error("Zod validation error (useLinkedPatients):", parsedData.error);
                    throw new Error("Invalid data received from API (linked patients).");
                }
                return parsedData.data;
            } catch (error: unknown) {
                const apiError = error as any;
                console.error("Error fetching linked patients:", apiError?.response?.data?.message ?? (error instanceof Error ? error.message : "Unknown error"));
                throw error;
            }
        },
        enabled: !!contactId,
        staleTime: 1000 * 60 * 1,
    });

    return {
        linkedPatients: query.data ?? [],
        isLoading: query.isLoading,
        isError: query.isError,
        error: query.error,
        refetch: query.refetch,
    };
}

const contactLinksSummarySchema = z.object({
    guardian_link_count: z.number().int().nonnegative(),
    other_link_count: z.number().int().nonnegative(),
});
type ContactLinksSummary = z.infer<typeof contactLinksSummarySchema>;

export function useContactLinksSummary(contactId: string | null | undefined) {
    const query = useQuery({
        queryKey: CONTACT_QUERY_KEYS.linksSummary(contactId!),
        queryFn: async (): Promise<ContactLinksSummary> => {
            if (!contactId) return { guardian_link_count: 0, other_link_count: 0 };
            try {
                const response = await axiosInstance.get(`/protected/contacts/${contactId}/links-summary`);
                const parsedData = contactLinksSummarySchema.safeParse(response.data);
                if (!parsedData.success) {
                    console.error("Zod validation error (useContactLinksSummary):", parsedData.error);
                    throw new Error("Invalid data received from API (links summary).");
                }
                return parsedData.data;
            } catch (error: unknown) {
                 const apiError = error as any;
                console.error("Error fetching links summary:", apiError?.response?.data?.message ?? (error instanceof Error ? error.message : "Unknown error"));
                throw error;
            }
        },
        enabled: !!contactId,
        staleTime: 1000 * 60 * 2,
    });

     return {
        summary: query.data,
        isLoading: query.isLoading,
        isError: query.isError,
        error: query.error,
        refetch: query.refetch,
    };
}

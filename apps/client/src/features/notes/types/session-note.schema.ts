import { z } from 'zod';

// Schema para validação de notas de sessão
export const sessionNoteSchema = z.object({
  id: z.string().uuid(),
  patient_id: z.string().uuid(),
  appointment_id: z.string().uuid().nullable(),
  title: z.string().min(1, 'O título é obrigatório'),
  content: z.string().min(1, 'O conteúdo é obrigatório'),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  patient_name: z.string().optional(),
  appointment_date: z.string().datetime().optional(),
});

// Schema para criação de notas de sessão
export const createSessionNoteSchema = z.object({
  patient_id: z.string().uuid(),
  appointment_id: z.string().uuid().nullable(),
  title: z.string().min(1, 'O título é obrigatório'),
  content: z.string().min(1, 'O conteúdo é obrigatório'),
});

// Schema para atualização de notas de sessão
export const updateSessionNoteSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1, 'O título é obrigatório').optional(),
  content: z.string().min(1, 'O conteúdo é obrigatório').optional(),
  appointment_id: z.string().uuid().nullable().optional(),
});

// Tipos derivados dos schemas
export type SessionNote = z.infer<typeof sessionNoteSchema>;
export type CreateSessionNotePayload = z.infer<typeof createSessionNoteSchema>;
export type UpdateSessionNotePayload = z.infer<typeof updateSessionNoteSchema>;

// src/schemas/notes.schema.ts
import { z } from 'zod';

// Schema Zod para validação (opcional, mas bom para consistência)
export const noteSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  content: z.string(),
  patient_id: z.string().uuid().nullable(),
  appointment_id: z.string().uuid().nullable(),
  created_at: z.string().datetime(), // Espera string ISO 8601
  updated_at: z.string().datetime(), // Espera string ISO 8601
});

// Tipo TypeScript inferido do schema Zod
export type Note = z.infer<typeof noteSchema>;

// Schema e Tipo para criação (espelha note_dto::CreateNoteRequest)
export const createNoteDataSchema = z.object({
  content: z.string().min(1, "O conteúdo não pode estar vazio"), // Adiciona validação básica
  patient_id: z.string().uuid().nullish(), // Permite string (uuid), null, ou undefined
  appointment_id: z.string().uuid().nullish(), // Permite string (uuid), null, ou undefined
});

export type CreateNotePayload = z.infer<typeof createNoteDataSchema>;

// Schema e Tipo para atualização (espelha note_dto::UpdateNoteRequest)
export const updateNoteDataSchema = z.object({
  content: z.string().min(1).optional(), // Opcional, mas não vazio se presente
  patient_id: z.string().uuid().nullable().optional(), // Opcional, permite null para desvincular
  appointment_id: z.string().uuid().nullable().optional(), // Opcional, permite null para desvincular
});

export type UpdateNotePayload = z.infer<typeof updateNoteDataSchema>;

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { sessionNotesApi, SESSION_NOTES_QUERY_KEYS } from '@/features/notes/api/session-notes.api';
import { SessionNote, CreateSessionNotePayload, UpdateSessionNotePayload } from '@/features/notes/types/session-note.schema';

export function useSessionNotesMutations(patientId: string) {
  const queryClient = useQueryClient();

  // Mutation para criar nota de sessão
  const createSessionNoteMutation = useMutation<
    SessionNote,
    Error,
    CreateSessionNotePayload,
    { onSuccess?: (createdNote: SessionNote) => void; onError?: (error: Error) => void }
  >({
    mutationFn: (payload) => sessionNotesApi.create(payload),
    onSuccess: (newNote, _variables, context) => {
      // Invalidar a query para recarregar os dados
      void queryClient.invalidateQueries({ queryKey: SESSION_NOTES_QUERY_KEYS.byPatient(patientId) });

      // Chamar callback de sucesso se fornecido, passando a nota criada
      if (context.onSuccess) {
        context.onSuccess(newNote);
      }
    },
    onError: (error, _variables, context) => {
      // Chamar callback de erro se fornecido
      if (context?.onError) {
        context.onError(error);
      }
    },
  });

  // Mutation para atualizar nota de sessão
  const updateSessionNoteMutation = useMutation<
    SessionNote, 
    Error, 
    UpdateSessionNotePayload,
    { onSuccess?: () => void; onError?: (error: Error) => void }
  >({
    mutationFn: (payload) => sessionNotesApi.update(payload),
    onSuccess: (_updatedNote, _variables, context) => {
      // Invalidar a query para recarregar os dados
      void queryClient.invalidateQueries({ queryKey: SESSION_NOTES_QUERY_KEYS.byPatient(patientId) });
      
      // Chamar callback de sucesso se fornecido
      if (context.onSuccess) {
        context.onSuccess();
      }
    },
    onError: (error, _variables, context) => {
      // Chamar callback de erro se fornecido
      if (context?.onError) {
        context.onError(error);
      }
    },
  });

  // Mutation para excluir nota de sessão
  const deleteSessionNoteMutation = useMutation<
    void, 
    Error, 
    string,
    { onSuccess?: () => void; onError?: (error: Error) => void }
  >({
    mutationFn: (noteId) => sessionNotesApi.delete(noteId),
    onSuccess: (_void, _noteId, context) => {
      // Invalidar a query para recarregar os dados
      queryClient.invalidateQueries({ queryKey: SESSION_NOTES_QUERY_KEYS.byPatient(patientId) });
      
      // Chamar callback de sucesso se fornecido
      if (context?.onSuccess) {
        context.onSuccess();
      }
    },
    onError: (error, _noteId, context) => {
      // Chamar callback de erro se fornecido
      if (context?.onError) {
        context.onError(error);
      }
    },
  });

  return {
    // Funções para criar, atualizar e excluir notas de sessão
    createSessionNote: (
      payload: CreateSessionNotePayload,
      options?: { onSuccess?: (createdNote: SessionNote) => void; onError?: (error: Error) => void }
    ) => { createSessionNoteMutation.mutate(payload, options); },
    
    updateSessionNote: (
      payload: UpdateSessionNotePayload, 
      options?: { onSuccess?: () => void; onError?: (error: Error) => void }
    ) => { updateSessionNoteMutation.mutate(payload, options); },
    
    deleteSessionNote: (
      noteId: string, 
      options?: { onSuccess?: () => void; onError?: (error: Error) => void }
    ) => { deleteSessionNoteMutation.mutate(noteId, options); },
    
    // Estados das mutations
    isCreating: createSessionNoteMutation.isPending,
    isUpdating: updateSessionNoteMutation.isPending,
    isDeleting: deleteSessionNoteMutation.isPending,
  };
} 
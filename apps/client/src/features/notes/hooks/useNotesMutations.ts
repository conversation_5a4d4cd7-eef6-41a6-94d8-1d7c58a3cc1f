import { useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '@/shared/lib/api.client';
import { useToast } from '@/shared/hooks/use-toast';
import {
  Note,
  CreateNotePayload,
  UpdateNotePayload,
} from '@/features/notes/types/notes.schema';
import { NOTES_QUERY_KEYS } from './constants';

export function useNotesMutations() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const createNoteMutation = useMutation({
    mutationFn: (data: CreateNotePayload): Promise<Note> => {
      return axiosInstance.post('/protected/notes', data).then((res) => res.data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: NOTES_QUERY_KEYS.all() });
      toast({
        title: 'Nota criada',
        description: 'Sua nota foi criada com sucesso.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erro ao criar nota',
        description: error?.response?.data?.message || error.message,
        variant: 'destructive',
      });
    },
  });

  const updateNoteMutation = useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: UpdateNotePayload;
    }): Promise<Note> => {
      return axiosInstance
        .put(`/protected/notes/${id}`, data)
        .then((res) => res.data);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: NOTES_QUERY_KEYS.all() });
      queryClient.invalidateQueries({
        queryKey: NOTES_QUERY_KEYS.detail(data.id),
      });
      toast({
        title: 'Nota atualizada',
        description: 'Sua nota foi atualizada com sucesso.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erro ao atualizar nota',
        description: error?.response?.data?.message || error.message,
        variant: 'destructive',
      });
    },
  });

  const deleteNoteMutation = useMutation({
    mutationFn: (id: string): Promise<void> => {
      return axiosInstance
        .delete(`/protected/notes/${id}`)
        .then((res) => res.data);
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: NOTES_QUERY_KEYS.all() });
      queryClient.removeQueries({ queryKey: NOTES_QUERY_KEYS.detail(id) });
      toast({
        title: 'Nota removida',
        description: 'A nota foi removida com sucesso.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Erro ao remover nota',
        description: error?.response?.data?.message || error.message,
        variant: 'destructive',
      });
    },
  });

  return {
    createNote: createNoteMutation.mutateAsync,
    updateNote: updateNoteMutation.mutate,
    deleteNote: deleteNoteMutation.mutate,
    isCreating: createNoteMutation.isPending,
    isUpdating: updateNoteMutation.isPending,
    isDeleting: deleteNoteMutation.isPending,
  };
} 
import { useQuery } from '@tanstack/react-query';
import { sessionNotesApi, SESSION_NOTES_QUERY_KEYS } from '@/features/notes/api/session-notes.api';
import { SessionNote } from '@/features/notes/types/session-note.schema';

export function useSessionNotesQuery(patientId: string) {
  // Query para buscar notas de sessão por paciente
  const {
    data: sessionNotes,
    isLoading,
    error,
    refetch,
  } = useQuery<SessionNote[], Error>({
    queryKey: SESSION_NOTES_QUERY_KEYS.byPatient(patientId),
    queryFn: () => sessionNotesApi.getByPatient(patientId),
    staleTime: 1000 * 60 * 2, // 2 minutos
  });

  return {
    sessionNotes: sessionNotes || [], // Retorna array vazio se undefined
    isLoading,
    isError: !!error,
    error,
    refetchSessionNotes: refetch,
  };
}

import { useQuery } from '@tanstack/react-query';
import { notesApi } from '@/features/notes/api/notes.api';
import { Note } from '@/features/notes/types/notes.schema';
import { NOTES_QUERY_KEYS } from './constants';

export function useNotesQuery(limit: number = 10) {
  const { data: notes, isLoading, isError, error, refetch } = useQuery<Note[], Error>({
    queryKey: NOTES_QUERY_KEYS.filtered({ limit }),
    queryFn: () => notesApi.getRecent(limit),
  });

  return {
    notes: notes || [],
    isLoading,
    isError,
    error,
    refetch,
  };
}

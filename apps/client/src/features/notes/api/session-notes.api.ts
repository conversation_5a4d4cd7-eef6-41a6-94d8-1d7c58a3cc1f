import { axiosInstance } from '@/shared/lib/api.client';
import { SessionNote, CreateSessionNotePayload, UpdateSessionNotePayload } from '@/features/notes/types/session-note.schema';

// Chaves de query para React Query
export const SESSION_NOTES_QUERY_KEYS = {
  all: ['session-notes'] as const,
  byPatient: (patientId: string) => [...SESSION_NOTES_QUERY_KEYS.all, 'patient', patientId] as const,
  byAppointment: (appointmentId: string) => [...SESSION_NOTES_QUERY_KEYS.all, 'appointment', appointmentId] as const,
  detail: (noteId: string) => [...SESSION_NOTES_QUERY_KEYS.all, noteId] as const,
};

// API para notas de sessão
export const sessionNotesApi = {
  // Buscar notas de sessão por paciente
  async getByPatient(patientId: string): Promise<SessionNote[]> {
    const response = await axiosInstance.get(`/protected/session-notes?patient_id=${patientId}`);
    return response.data;
  },

  // Buscar notas de sessão por agendamento
  async getByAppointment(appointmentId: string): Promise<SessionNote[]> {
    const response = await axiosInstance.get(`/protected/session-notes?appointment_id=${appointmentId}`);
    return response.data;
  },

  // Buscar uma nota de sessão por ID
  async getById(noteId: string): Promise<SessionNote> {
    const response = await axiosInstance.get(`/protected/session-notes/${noteId}`);
    return response.data;
  },

  // Criar uma nova nota de sessão
  async create(data: CreateSessionNotePayload): Promise<SessionNote> {
    const response = await axiosInstance.post('/protected/session-notes', data);
    return response.data;
  },

  // Atualizar uma nota de sessão existente
  async update(data: UpdateSessionNotePayload): Promise<SessionNote> {
    const response = await axiosInstance.put(`/protected/session-notes/${data.id}`, {
      title: data.title,
      content: data.content,
      appointment_id: data.appointment_id,
    });
    return response.data;
  },

  // Excluir uma nota de sessão
  async delete(noteId: string): Promise<void> {
    await axiosInstance.delete(`/protected/session-notes/${noteId}`);
  },
};

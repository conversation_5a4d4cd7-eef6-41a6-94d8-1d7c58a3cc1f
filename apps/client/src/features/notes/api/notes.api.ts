
import { axiosInstance } from '@/shared/lib/api.client';

import { Note, CreateNotePayload, UpdateNotePayload } from '@/features/notes/types/notes.schema';


export const NOTES_QUERY_KEYS = {
  all: ['notes'],
  list: (params?: { limit?: number }) => [...NOTES_QUERY_KEYS.all, 'list', params],
  detail: (id: string) => [...NOTES_QUERY_KEYS.all, 'detail', id],
};


export const notesApi = {
  
  getRecent: async (limit: number = 10): Promise<Note[]> => {
    try {
      const response = await axiosInstance.get('/protected/notes', { params: { limit } });
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar notas:', error);
      throw error;
    }
  },

  
  create: async (payload: CreateNotePayload): Promise<Note> => { // Usar tipo do frontend
    try {
      const response = await axiosInstance.post('/protected/notes', payload);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar nota:', error);
      throw error;
    }
  },

  
  update: async (noteId: string, payload: UpdateNotePayload): Promise<Note> => { // Usar tipo do frontend
    try {
      const response = await axiosInstance.put(`/protected/notes/${noteId}`, payload);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar nota ${noteId}:`, error);
      throw error;
    }
  },

  
  delete: async (noteId: string): Promise<void> => {
    try {
      await axiosInstance.delete(`/protected/notes/${noteId}`);
    } catch (error) {
      console.error(`Erro ao deletar nota ${noteId}:`, error);
      throw error;
    }
  },
};


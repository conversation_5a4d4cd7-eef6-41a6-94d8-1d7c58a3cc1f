import { useState, useEffect, useCallback } from 'react';
import { useLocation } from '@tanstack/react-router';

interface AIContext {
  currentPage: string;
  pageTitle: string;
  availableActions: string[];
  userPreferences: Record<string, unknown>;
  recentActivity: ActivityItem[];
  suggestions: Suggestion[];
}

interface ActivityItem {
  id: string;
  type: 'appointment' | 'note' | 'patient' | 'document';
  title: string;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

interface Suggestion {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  category: 'automation' | 'insight' | 'action';
  action: string;
  parameters?: Record<string, unknown>;
}

export function useAIContext() {
  const [context, setContext] = useState<AIContext>({
    currentPage: '',
    pageTitle: '',
    availableActions: [],
    userPreferences: {},
    recentActivity: [],
    suggestions: []
  });
  
  const location = useLocation();

  // Mapear rotas para contextos
  const getPageContext = useCallback((pathname: string) => {
    const pageMap: Record<string, { title: string; actions: string[] }> = {
      '/dashboard': {
        title: 'Dashboard',
        actions: ['create_widget', 'rearrange_layout', 'view_insights', 'export_data']
      },
      '/people': {
        title: 'Gerenciamento de Pessoas',
        actions: ['create_patient', 'create_contact', 'view_patient_details', 'schedule_appointment']
      },
      '/calendar': {
        title: 'Calendário',
        actions: ['create_appointment', 'reschedule_appointment', 'view_availability', 'block_time']
      },
      '/documents': {
        title: 'Documentos',
        actions: ['create_document', 'upload_file', 'generate_report', 'search_documents']
      },
      '/settings': {
        title: 'Configurações',
        actions: ['update_profile', 'manage_preferences', 'configure_integrations', 'export_settings']
      }
    };

    // Buscar correspondência exata ou parcial
    const exactMatch = pageMap[pathname];
    if (exactMatch) return exactMatch;

    // Buscar correspondência parcial
    const partialMatch = Object.entries(pageMap).find(([path]) => 
      pathname.startsWith(path)
    );
    
    if (partialMatch) return partialMatch[1];

    return { title: 'Evolua Care', actions: ['navigate', 'search', 'help'] };
  }, []);

  // Gerar sugestões baseadas no contexto
  const generateSuggestions = useCallback((pageContext: ReturnType<typeof getPageContext>) => {
    const suggestions: Suggestion[] = [];

    // Sugestões baseadas na página atual
    if (pageContext.title === 'Dashboard') {
      suggestions.push({
        id: 'optimize-dashboard',
        title: 'Otimizar Dashboard',
        description: 'Reorganizar widgets baseado no seu uso',
        priority: 'medium',
        category: 'automation',
        action: 'optimize_dashboard_layout'
      });

      suggestions.push({
        id: 'daily-summary',
        title: 'Resumo do Dia',
        description: 'Ver estatísticas e atividades de hoje',
        priority: 'high',
        category: 'insight',
        action: 'generate_daily_summary'
      });
    }

    if (pageContext.title === 'Gerenciamento de Pessoas') {
      suggestions.push({
        id: 'patient-followup',
        title: 'Follow-up de Pacientes',
        description: 'Identificar pacientes que precisam de acompanhamento',
        priority: 'high',
        category: 'insight',
        action: 'analyze_patient_followup'
      });

      suggestions.push({
        id: 'bulk-schedule',
        title: 'Agendamento em Lote',
        description: 'Agendar consultas para múltiplos pacientes',
        priority: 'medium',
        category: 'automation',
        action: 'bulk_schedule_appointments'
      });
    }

    if (pageContext.title === 'Calendário') {
      suggestions.push({
        id: 'schedule-gaps',
        title: 'Otimizar Horários',
        description: 'Identificar e preencher lacunas na agenda',
        priority: 'medium',
        category: 'automation',
        action: 'optimize_schedule_gaps'
      });

      suggestions.push({
        id: 'appointment-reminders',
        title: 'Lembretes Automáticos',
        description: 'Configurar lembretes para pacientes',
        priority: 'high',
        category: 'automation',
        action: 'setup_appointment_reminders'
      });
    }

    // Sugestões baseadas no horário
    const currentHour = new Date().getHours();
    if (currentHour >= 8 && currentHour <= 10) {
      suggestions.push({
        id: 'morning-prep',
        title: 'Preparação Matinal',
        description: 'Revisar agenda do dia e preparar materiais',
        priority: 'high',
        category: 'action',
        action: 'morning_preparation'
      });
    }

    if (currentHour >= 17 && currentHour <= 19) {
      suggestions.push({
        id: 'end-day-summary',
        title: 'Resumo do Dia',
        description: 'Finalizar notas e revisar atividades do dia',
        priority: 'high',
        category: 'action',
        action: 'end_day_summary'
      });
    }

    return suggestions.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }, []);

  // Simular atividade recente (em produção viria de uma API)
  const getRecentActivity = useCallback((): ActivityItem[] => {
    return [
      {
        id: '1',
        type: 'appointment',
        title: 'Consulta com Maria Silva',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 horas atrás
        metadata: { patientId: '123', duration: 50 }
      },
      {
        id: '2',
        type: 'note',
        title: 'Nota de evolução - João Santos',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 horas atrás
        metadata: { patientId: '456', sessionNumber: 5 }
      },
      {
        id: '3',
        type: 'patient',
        title: 'Novo paciente cadastrado - Ana Costa',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 dia atrás
        metadata: { patientId: '789' }
      }
    ];
  }, []);

  // Atualizar contexto quando a rota mudar
  useEffect(() => {
    const pageContext = getPageContext(location.pathname);
    const suggestions = generateSuggestions(pageContext);
    const recentActivity = getRecentActivity();

    setContext({
      currentPage: location.pathname,
      pageTitle: pageContext.title,
      availableActions: pageContext.actions,
      userPreferences: {}, // Em produção, viria do localStorage ou API
      recentActivity,
      suggestions
    });
  }, [location.pathname, getPageContext, generateSuggestions, getRecentActivity]);

  // Função para executar ações sugeridas
  const executeAction = useCallback((actionId: string, parameters?: Record<string, unknown>) => {
    console.log(`Executando ação: ${actionId}`, parameters);
    
    // Em produção, aqui seria feita a integração real com as funcionalidades
    switch (actionId) {
      case 'optimize_dashboard_layout':
        // Lógica para otimizar dashboard
        break;
      case 'generate_daily_summary':
        // Lógica para gerar resumo
        break;
      case 'analyze_patient_followup':
        // Lógica para analisar follow-up
        break;
      // ... outras ações
      default:
        console.warn(`Ação não implementada: ${actionId}`);
    }
  }, []);

  // Função para adicionar atividade recente
  const addActivity = useCallback((activity: Omit<ActivityItem, 'id' | 'timestamp'>) => {
    const newActivity: ActivityItem = {
      ...activity,
      id: Date.now().toString(),
      timestamp: new Date().toISOString()
    };

    setContext(prev => ({
      ...prev,
      recentActivity: [newActivity, ...prev.recentActivity.slice(0, 9)] // Manter apenas 10 itens
    }));
  }, []);

  return {
    context,
    executeAction,
    addActivity,
    refreshContext: () => {
      const pageContext = getPageContext(location.pathname);
      const suggestions = generateSuggestions(pageContext);
      setContext(prev => ({ ...prev, suggestions }));
    }
  };
}

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/shared/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Textarea } from '@/shared/ui/textarea';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { 
  Brain, 
  MessageCircle, 
  X, 
  Send, 
  Sparkles, 
  Lightbulb,
  Calendar,
  Users,
  FileText,
  TrendingUp,
  Zap
} from 'lucide-react';
import { cn } from '@/shared/lib/utils';
import { useLocation } from '@tanstack/react-router';

interface AIAgentButtonProps {
  className?: string;
}

interface Suggestion {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  action: () => void;
  category: 'quick' | 'contextual' | 'automation';
}

export function AIAgentButton({ className }: AIAgentButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const location = useLocation();

  // Gerar sugestões baseadas no contexto atual
  useEffect(() => {
    const contextualSuggestions = getContextualSuggestions(location.pathname);
    setSuggestions(contextualSuggestions);
  }, [location.pathname]);

  const getContextualSuggestions = (pathname: string): Suggestion[] => {
    const baseSuggestions: Suggestion[] = [
      {
        id: 'schedule-appointment',
        title: 'Agendar consulta',
        description: 'Criar um novo agendamento rapidamente',
        icon: Calendar,
        action: () => { console.log('Agendar consulta'); },
        category: 'quick'
      },
      {
        id: 'create-note',
        title: 'Criar nota de sessão',
        description: 'Iniciar uma nova nota de evolução',
        icon: FileText,
        action: () => { console.log('Criar nota'); },
        category: 'quick'
      }
    ];

    // Sugestões específicas por página
    if (pathname.includes('/dashboard')) {
      baseSuggestions.push({
        id: 'optimize-dashboard',
        title: 'Otimizar dashboard',
        description: 'Sugerir melhor organização dos widgets',
        icon: TrendingUp,
        action: () => { console.log('Otimizar dashboard'); },
        category: 'contextual'
      });
    }

    if (pathname.includes('/people')) {
      baseSuggestions.push({
        id: 'patient-insights',
        title: 'Insights de pacientes',
        description: 'Analisar padrões nos seus pacientes',
        icon: Users,
        action: () => { console.log('Insights pacientes'); },
        category: 'contextual'
      });
    }

    if (pathname.includes('/calendar')) {
      baseSuggestions.push({
        id: 'schedule-optimization',
        title: 'Otimizar agenda',
        description: 'Sugerir melhor distribuição de horários',
        icon: Zap,
        action: () => { console.log('Otimizar agenda'); },
        category: 'automation'
      });
    }

    return baseSuggestions;
  };

  const handleSendMessage = () => {
    if (!message.trim()) return;
    
    setIsTyping(true);
    // Simular resposta da IA
    setTimeout(() => {
      setIsTyping(false);
      setMessage('');
    }, 2000);
  };

  const handleSuggestionClick = (suggestion: Suggestion) => {
    suggestion.action();
    setIsOpen(false);
  };

  return (
    <>
      {/* Botão Flutuante */}
      <div className={cn(
        'fixed bottom-6 right-6 z-50 transition-all duration-300',
        isOpen && 'bottom-[420px]',
        className
      )}>
        <Button
          onClick={() => { setIsOpen(!isOpen); }}
          className={cn(
            'h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300',
            'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700',
            'border-2 border-white/20',
            isOpen && 'rotate-180'
          )}
        >
          {isOpen ? (
            <X className="h-6 w-6 text-white" />
          ) : (
            <Brain className="h-6 w-6 text-white animate-pulse" />
          )}
        </Button>
        
        {/* Indicador de atividade */}
        {!isOpen && (
          <div className="absolute -top-1 -right-1">
            <div className="h-4 w-4 bg-green-500 rounded-full border-2 border-white animate-pulse">
              <div className="h-full w-full bg-green-400 rounded-full animate-ping"></div>
            </div>
          </div>
        )}
      </div>

      {/* Interface do Chat */}
      {isOpen && (
        <div className="fixed bottom-6 right-6 z-40 w-96 h-96 transition-all duration-300">
          <Card className="h-full shadow-2xl border-2 border-purple-200/50 bg-gradient-to-b from-white to-purple-50/30">
            <CardHeader className="pb-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-t-lg">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Brain className="h-5 w-5" />
                IA Assistant
                <Badge variant="secondary" className="ml-auto bg-white/20 text-white">
                  Beta
                </Badge>
              </CardTitle>
            </CardHeader>
            
            <CardContent className="p-0 flex flex-col h-full">
              {/* Sugestões Contextuais */}
              <div className="p-4 border-b bg-gradient-to-r from-purple-50 to-blue-50">
                <div className="flex items-center gap-2 mb-3">
                  <Lightbulb className="h-4 w-4 text-amber-500" />
                  <span className="text-sm font-medium text-gray-700">Sugestões para você</span>
                </div>
                <div className="space-y-2">
                  {suggestions.slice(0, 2).map((suggestion) => (
                    <button
                      key={suggestion.id}
                      onClick={() => { handleSuggestionClick(suggestion); }}
                      className="w-full text-left p-2 rounded-lg bg-white hover:bg-purple-50 border border-purple-100 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <suggestion.icon className="h-4 w-4 text-purple-600" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {suggestion.title}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {suggestion.description}
                          </p>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Área de Chat */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {/* Mensagem de boas-vindas */}
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center">
                      <Brain className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1 bg-gray-100 rounded-lg p-3">
                      <p className="text-sm text-gray-800">
                        Olá! Sou seu assistente inteligente. Como posso ajudar você hoje?
                      </p>
                    </div>
                  </div>

                  {/* Indicador de digitação */}
                  {isTyping && (
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-center">
                        <Brain className="h-4 w-4 text-white" />
                      </div>
                      <div className="flex-1 bg-gray-100 rounded-lg p-3">
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>

              {/* Input de Mensagem */}
              <div className="p-4 border-t bg-white">
                <div className="flex items-end gap-2">
                  <Textarea
                    value={message}
                    onChange={(e) => { setMessage(e.target.value); }}
                    placeholder="Digite sua pergunta ou comando..."
                    className="flex-1 min-h-[40px] max-h-[80px] resize-none"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!message.trim() || isTyping}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                  <Sparkles className="h-3 w-3" />
                  Pressione Enter para enviar, Shift+Enter para nova linha
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  );
}

import { axiosInstance } from '@/shared/lib/api.client';
import {
  Document,
  DocumentListResponse,
  DownloadUrlResponse,
  UpdateDocumentPayload,
  documentSchema,
  documentListResponseSchema,
  downloadUrlResponseSchema
} from '@/features/documents/types/document.schema';

// Parâmetros para filtrar documentos
export interface DocumentFilterParams {
  patient_id?: string;
  title?: string;
  search?: string;
  page?: number;
  page_size?: number;
  file_type?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  is_favorite?: boolean;
}

// API para documentos
export const documentsApi = {
  // Buscar documentos com filtros opcionais
  async getDocuments(filters: DocumentFilterParams = {}): Promise<DocumentListResponse> {
    try {
      const response = await axiosInstance.get('/protected/documents', { params: filters });

      // Validação Zod
      const parsedData = documentListResponseSchema.safeParse(response.data);

      if (!parsedData.success) {
        throw new Error(`Zod validation failed: ${parsedData.error.message}`);
      }

      return parsedData.data;
    } catch (error) {
      throw error;
    }
  },

  // Buscar documentos por paciente (mantido para compatibilidade)
  async getByPatient(patientId: string): Promise<Document[]> {
    const response = await this.getDocuments({ patient_id: patientId });
    return response.documents;
  },

  // Buscar todos os documentos (mantido para compatibilidade)
  async getAll(): Promise<Document[]> {
    const response = await this.getDocuments();
    return response.documents;
  },

  // Obter URL de download para um documento
  async getDownloadUrl(documentId: string): Promise<DownloadUrlResponse> {
    const response = await axiosInstance.get(`/protected/documents/${documentId}/download`);
    return downloadUrlResponseSchema.parse(response.data);
  },

  // Upload de documento
  async uploadDocument(formData: FormData): Promise<Document> {
    const response = await axiosInstance.post('/protected/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return documentSchema.parse(response.data);
  },

  // Atualizar metadados de documento
  async updateDocumentMetadata(documentId: string, data: UpdateDocumentPayload): Promise<Document> {
    const response = await axiosInstance.put(`/protected/documents/${documentId}`, data);
    return documentSchema.parse(response.data);
  },

  // Excluir documento
  async deleteDocument(documentId: string): Promise<void> {
    await axiosInstance.delete(`/protected/documents/${documentId}`);
  },

  // Métodos legados para compatibilidade
  async create(formData: FormData): Promise<Document> {
    return this.uploadDocument(formData);
  },

  async update(data: { id: string } & UpdateDocumentPayload): Promise<Document> {
    return this.updateDocumentMetadata(data.id, data);
  },

  async delete(documentId: string): Promise<void> {
    return this.deleteDocument(documentId);
  }
};



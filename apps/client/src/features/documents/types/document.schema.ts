import { z } from 'zod';

// Schema para validação de documentos (baseado na nova estrutura com MinIO)
export const documentSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid().optional(), // Tornando opcional para maior flexibilidade
  title: z.string().min(1, 'O título é obrigatório'),
  filename: z.string(),
  mime_type: z.string().nullable(),
  size_bytes: z.number().nullable(),
  patient_id: z.string().uuid().nullable(),
  model_id: z.string().uuid().nullable(),
  created_at: z.string(), // Removida validação .datetime() para aceitar qualquer formato de string de data
  updated_at: z.string(), // Removida validação .datetime() para aceitar qualquer formato de string de data
  created_by: z.string().uuid().optional(), // Tornando opcional para maior flexibilidade
  updated_by: z.string().uuid().optional(), // Tornando opcional para maior flexibilidade
  // Campo para armazenamento no S3
  s3_key: z.string().optional(), // Chave do objeto no S3

  // Campos adicionais para exibição
  patient_name: z.string().nullable().optional(),
  model_name: z.string().nullable().optional(),
  is_favorite: z.boolean().optional(), // Indicador se o documento é favorito
  tags: z.array(z.string()).optional(), // Suporte para tags
}).passthrough(); // Permite campos adicionais no documento

// Schema para resposta de lista de documentos com paginação
export const documentListResponseSchema = z.object({
  documents: z.array(documentSchema),
  total_count: z.number()
}).passthrough(); // Permite campos adicionais na resposta

// Schema para resposta de URL de download
export const downloadUrlResponseSchema = z.object({
  download_url: z.string(),
  filename: z.string(),
  mime_type: z.string().nullable(),
});

// Schema para metadados de upload de documento
export const uploadDocumentMetadataSchema = z.object({
  title: z.string().min(1, 'O título é obrigatório'),
  patient_id: z.string().uuid().nullable().optional(),
  model_id: z.string().uuid().nullable().optional(),
});

// Schema para atualização de metadados de documento
export const updateDocumentSchema = z.object({
  title: z.string().min(1, 'O título é obrigatório').optional(),
  patient_id: z.string().uuid().nullable().optional(),
  model_id: z.string().uuid().nullable().optional(),
  is_favorite: z.boolean().optional(),
});

export const updateDocumentContentSchema = z.object({
  content: z.string(),
});

export const createDocumentFromHtmlSchema = z.object({
  title: z.string(),
  content: z.string(),
  patient_id: z.string().uuid().optional(),
  model_id: z.string().uuid().optional(),
});

export const documentContentSchema = z.object({
  content: z.string(),
});

// Tipos derivados dos schemas
export type Document = z.infer<typeof documentSchema>;
export type DocumentListResponse = z.infer<typeof documentListResponseSchema>;
export type DownloadUrlResponse = z.infer<typeof downloadUrlResponseSchema>;
export type UploadDocumentMetadata = z.infer<typeof uploadDocumentMetadataSchema>;
export type UpdateDocumentPayload = z.infer<typeof updateDocumentSchema>;
export type UpdateDocumentContentPayload = z.infer<typeof updateDocumentContentSchema>;
export type CreateDocumentFromHtmlPayload = z.infer<typeof createDocumentFromHtmlSchema>;
export type DocumentContent = z.infer<typeof documentContentSchema>;

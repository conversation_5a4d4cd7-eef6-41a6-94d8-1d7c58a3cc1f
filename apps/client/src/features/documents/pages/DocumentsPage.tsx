import React, { useState } from "react";
import { DocumentUploadDialog } from "@/features/documents/components/DocumentUploadDialog";
import { DocumentCard } from "@/features/documents/components/DocumentCard";
import { DocumentTypeIcon } from "@/features/documents/components/DocumentTypeIcon";
import { EditDocumentMetadataDialog } from "@/features/documents/components/EditDocumentMetadataDialog";
import {
  Search,
  Plus,
  Upload,
  Star,
  Clock,
  Grid as GridIcon,
  List as ListIcon,
  Loader2,
  SlidersHorizontal,
  Trash2,
  FolderOpen,
  Users,
  Calendar,
  FileText,
  AlertCircle,
  FileEdit,
} from "lucide-react";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Badge } from "@/shared/ui/badge";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/shared/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/shared/ui/tabs";
import { ScrollArea } from "@/shared/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/shared/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { cn } from "@/shared/lib/utils";
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useToast } from "@/shared/hooks/use-toast";
import { useDocumentsQuery } from "@/features/documents/hooks/useDocumentsQuery";
import { useDocumentMutations } from "@/features/documents/hooks/useDocumentMutations";
import { usePatientQuery } from "@/features/patients/hooks/usePatientQuery";
import { Document } from "@/features/documents/types/document.schema";
import { DocumentFilterParams } from "@/features/documents/api/documents.api";

// Categorias para filtros
const documentCategories = [
  { id: "all", name: "Todos", icon: FileText },
  { id: "patient", name: "Pacientes", icon: Users },
  { id: "session", name: "Sessões", icon: Calendar },
  { id: "contact", name: "Contatos", icon: Users },
  { id: "unlinked", name: "Não vinculados", icon: FileText },
];

export function DocumentContent() {
  // Estados para filtros e UI
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("recent");
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [fileType, setFileType] = useState<string>("all");
  const [selectedPatientId, setSelectedPatientId] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(0); // Página inicial é 0 para compatibilidade com o backend
  const [pageSize] = useState(20);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [documentToEdit, setDocumentToEdit] = useState<Document | null>(null);
  const { toast } = useToast();

  // Obter lista de pacientes para o filtro
  const { allPatients: patients, isLoadingList: isPatientsLoading } = usePatientQuery();

  // Construir filtros para a API
  const buildFilters = (): DocumentFilterParams => {
    // Filtros básicos suportados pelo backend
    const filters: DocumentFilterParams = {
      page: currentPage,
      page_size: pageSize,
    };

    // Adicionar termo de busca (usando title no backend)
    if (searchTerm) {
      filters.title = searchTerm; // O backend usa 'title' para busca
    }

    // Adicionar filtro de paciente apenas se não estivermos no filtro "unlinked"
    if (selectedPatientId && activeFilter !== "unlinked") {
      filters.patient_id = selectedPatientId;
    }

    // Não enviar filtros específicos para "unlinked" ou "patient" para o backend
    // Esses filtros são aplicados localmente no useMemo abaixo

    // Adicionar filtro de favoritos se estiver na aba de favoritos
    if (activeFilter === "favorites") {
      filters.is_favorite = true;
    }

    // Ordenação
    if (sortBy === "recent") {
      filters.sort_by = "created_at";
      filters.sort_order = "desc";
    } else if (sortBy === "name") {
      filters.sort_by = "title";
      filters.sort_order = "asc";
    } else if (sortBy === "size") {
      filters.sort_by = "size_bytes";
      filters.sort_order = "desc";
    }

    return filters;
  };

  // Obter documentos com os filtros aplicados
  const {
    documents,
    totalCount,
    isLoading,
    isError,
    error,
    refetch,
    getDownloadUrl,
  } = useDocumentsQuery(buildFilters());

  const { deleteDocument, isDeleting } = useDocumentMutations();

  // Filtrar documentos localmente para os filtros "patient" e "unlinked"
  // Isso é necessário porque o backend não suporta diretamente esses filtros
  const documentsLocal = React.useMemo(() => {
    // Verificar se documents é um array válido
    if (!Array.isArray(documents)) {
      return [];
    }

    if (activeFilter === "unlinked") {
      // Mostrar apenas documentos sem paciente vinculado
      return documents.filter(doc => doc && doc.patient_id === null);
    } else if (activeFilter === "patient" && !selectedPatientId) {
      // Mostrar apenas documentos com paciente vinculado (qualquer paciente)
      return documents.filter(doc => doc && doc.patient_id !== null);
    }
    return documents;
  }, [documents, activeFilter, selectedPatientId]);

  // Calcular total de páginas
  const totalPages = Math.ceil(totalCount / pageSize);

  // Função para lidar com a pesquisa
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(0); // Resetar para a primeira página (índice 0) ao pesquisar
  };

  // Função para alternar favorito
  const toggleFavorite = async (document: Document, isFavorite: boolean) => {
    // TODO: Implementar a mutação de `is_favorite` corretamente.
    // A rota PATCH de documentos atualmente não suporta este campo.
    // É necessário criar um endpoint específico ou adicionar suporte no endpoint existente.
    console.log(`Simulando toggleFavorite para: ${document.id}, isFavorite: ${isFavorite}`);
    toast({
        title: "Funcionalidade em desenvolvimento",
        description: "A ação de favoritar ainda não foi implementada no backend.",
        variant: "default",
    });
    // try {
    //   const data: UpdateDocumentPayload & { is_favorite: boolean } = { is_favorite: isFavorite };
    //   await updateDocument({
    //     id: document.id,
    //     data: data as any, 
    //   });
    // } catch (err) {
    //   console.error("Erro ao atualizar favorito:", err);
    //   toast({
    //     title: "Erro",
    //     description: "Não foi possível atualizar o status de favorito.",
    //     variant: "destructive",
    //   });
    // }
  };

  // Função para baixar documento
  const handleDownload = async (document: Document) => {
    try {
      const { url } = await getDownloadUrl(document.id);

      const a = window.document.createElement('a');
      a.href = url;
      a.download = document.filename || document.title;
      window.document.body.appendChild(a);
      a.click();
      window.document.body.removeChild(a);

      toast({
        title: "Download iniciado",
        description: `O download de "${document.title}" foi iniciado.`,
      });
    } catch (error) {
      toast({
        title: "Erro ao baixar",
        description: "Não foi possível baixar o documento.",
        variant: "destructive",
      });
    }
  };

  // Função para visualizar documento
  const handleView = async (document: Document) => {
    try {
      const { url } = await getDownloadUrl(document.id);
      window.open(url, '_blank');
    } catch (error) {
      toast({
        title: "Erro ao visualizar",
        description: "Não foi possível visualizar o documento.",
        variant: "destructive",
      });
    }
  };

  // Função para excluir documento
  const handleDelete = async (document: Document) => {
    try {
      await deleteDocument(document.id);
      toast({
        title: "Documento excluído",
        description: "O documento foi removido com sucesso.",
      });
    } catch (error) {
      // O hook de mutação já mostra um toast de erro.
      // Poderíamos adicionar um log aqui se necessário.
      console.error("Erro ao excluir documento:", error);
    }
  };

  // Função para editar metadados do documento
  const handleEdit = (document: Document) => {
    setDocumentToEdit(document);
    setIsEditDialogOpen(true);
  };

  // Função para mudar de página
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Efeito para refetch quando os filtros mudarem
  React.useEffect(() => {
    refetch();
  }, [
    searchTerm,
    activeFilter,
    fileType,
    selectedPatientId,
    sortBy,
    currentPage,
    pageSize,
    refetch
  ]);

  // Renderizar card de documento
  const renderDocumentCard = (document: Document) => {
    // Formatar a data de criação
    const formattedDate = document.created_at
      ? format(new Date(document.created_at), "dd/MM/yyyy", { locale: ptBR })
      : "";

    return (
      <Card
        key={document.id}
        className={cn(
          "cursor-pointer hover:border-primary/50 transition-colors flex flex-col",
          selectedDocumentId === document.id && "border-primary"
        )}
        onClick={() => { setSelectedDocumentId(document.id); }}
      >
        <CardHeader className="p-4 pb-2 flex flex-row items-start justify-between">
          <div className="flex items-center gap-2">
            <DocumentTypeIcon
              mimeType={document.mime_type}
              filename={document.filename}
              className="h-8 w-8"
            />
            <div>
              <CardTitle className="text-base line-clamp-1">{document.title}</CardTitle>
              <p className="text-xs text-muted-foreground">
                {document.patient_name ? `Paciente: ${document.patient_name}` : "Não vinculado"}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7"
            onClick={(e) => {
              e.stopPropagation();
              void toggleFavorite(document, !document.is_favorite);
            }}
          >
            <Star className={cn("h-4 w-4", document.is_favorite && "fill-yellow-400 text-yellow-400")} />
          </Button>
        </CardHeader>
        <CardContent className="px-4 pb-2 flex-grow">
          {document.tags && document.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {document.tags.map((tag: string) => (
                <Badge key={tag} variant="outline" className="text-[10px] px-1 py-0">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </CardContent>
        <CardFooter className="bg-muted/20 px-4 py-2 text-xs flex justify-between items-center border-t mt-auto">
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
            <span className="text-muted-foreground">{formattedDate}</span>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 px-1" onClick={(e) => e.stopPropagation()}>
                Ações
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent onClick={(e) => e.stopPropagation()}>
              <DropdownMenuItem onClick={() => handleView(document)}>
                <FileText className="mr-2 h-4 w-4" /> Visualizar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDownload(document)}>
                <Upload className="mr-2 h-4 w-4 rotate-180" /> Baixar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => { handleEdit(document); }}>
                <FileEdit className="mr-2 h-4 w-4" /> Editar metadados
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => toggleFavorite(document, !document.is_favorite)}>
                <Star className={cn("mr-2 h-4 w-4", document.is_favorite && "fill-yellow-400 text-yellow-400")} />
                {document.is_favorite ? "Remover dos favoritos" : "Adicionar aos favoritos"}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleDelete(document)}
                className="text-destructive focus:text-destructive"
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Excluindo...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" /> Excluir
                  </>
                )}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardFooter>
      </Card>
    );
  };

  // Renderizar lista de documentos
  const renderDocumentsList = () => {
    // Exibir estado de carregamento
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center mt-10">
          <Loader2 className="h-16 w-16 text-primary mb-4 animate-spin" />
          <h3 className="text-lg font-medium mb-2">Carregando documentos</h3>
          <p className="text-muted-foreground max-w-md">
            Aguarde enquanto carregamos seus documentos...
          </p>
        </div>
      );
    }

    // Exibir estado de erro
    if (isError) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center mt-10">
          <AlertCircle className="h-16 w-16 text-destructive mb-4" />
          <h3 className="text-lg font-medium mb-2">Erro ao carregar documentos</h3>
          <p className="text-muted-foreground max-w-md">
            {error?.message || "Ocorreu um erro ao carregar os documentos. Tente novamente mais tarde."}
          </p>
          <Button className="mt-4" onClick={() => refetch()}>
            Tentar novamente
          </Button>
        </div>
      );
    }

    // Exibir mensagem quando não há documentos
    if (documentsLocal.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center mt-10">
          <FolderOpen className="h-16 w-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Nenhum documento encontrado</h3>
          <p className="text-muted-foreground max-w-md">
            Não encontramos documentos com os filtros atuais. Tente ajustar seus critérios de busca ou faça upload de novos documentos.
          </p>
          <Button className="mt-4" onClick={() => { setIsUploadDialogOpen(true); }}>
            <Upload className="mr-2 h-4 w-4" /> Enviar documento
          </Button>
        </div>
      );
    }

    // Renderizar lista de documentos
    return (
      <div className={cn(
        "grid gap-4 p-4",
        viewMode === "grid" ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" : "grid-cols-1"
      )}>
        {documentsLocal.map(document => (
          viewMode === "grid"
            ? renderDocumentCard(document)
            : <DocumentCard
                key={document.id}
                document={document}
                viewMode="list"
                onView={() => handleView(document)}
                onDownload={() => handleDownload(document)}
                onEdit={() => { handleEdit(document); }}
                onDelete={() => handleDelete(document)}
                isDeleting={isDeleting}
              />
        ))}
      </div>
    );
  };

  // Renderizar conteúdo principal
  return (
    <main className="container max-w-full h-[calc(100vh-var(--navbar-height))] flex flex-col p-0">
      <DocumentUploadDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        onSuccess={() => {
          toast({
            title: "Documento enviado",
            description: "O documento foi enviado com sucesso.",
          });
          refetch();
        }}
      />

      <EditDocumentMetadataDialog
        document={documentToEdit}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSuccess={() => refetch()}
      />

      <div className="flex-1 overflow-hidden">
        <div className="flex flex-col h-full">
          <div className="border-b p-4">
            <div className="flex flex-wrap gap-2">
              {documentCategories.map((category) => (
                <Button
                  key={category.id}
                  variant={activeFilter === category.id ? "default" : "outline"}
                  size="sm"
                  className="h-9"
                  onClick={() => { setActiveFilter(category.id); }}
                >
                  <category.icon className="h-4 w-4 mr-2" />
                  {category.name}
                </Button>
              ))}
              <Button
                variant={activeFilter === "favorites" ? "default" : "outline"}
                size="sm"
                className="h-9"
                onClick={() => { setActiveFilter("favorites"); }}
              >
                <Star className="h-4 w-4 mr-2" />
                Favoritos
              </Button>
            </div>
          </div>

          <div className="border-b p-4 flex items-center justify-between gap-4">
            <div className="relative flex-grow max-w-xs">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar documentos..."
                value={searchTerm}
                onChange={handleSearch}
                className="pl-9 h-9"
              />
            </div>
            <div className="flex items-center space-x-2 flex-shrink-0">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => { setShowAdvancedFilters(!showAdvancedFilters); }}
                title="Filtros avançados"
                className="h-9 w-9"
              >
                <SlidersHorizontal className="h-4 w-4" />
              </Button>
              <Tabs value={viewMode} onValueChange={(v) => { setViewMode(v as "grid" | "list"); }}>
                <TabsList className="h-9">
                  <TabsTrigger value="grid" className="h-7 px-2">
                    <GridIcon className="h-4 w-4" />
                  </TabsTrigger>
                  <TabsTrigger value="list" className="h-7 px-2">
                    <ListIcon className="h-4 w-4" />
                  </TabsTrigger>
                </TabsList>
              </Tabs>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="h-9 w-[150px] text-xs">
                  <SelectValue placeholder="Ordenar por" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recent">Mais Recentes</SelectItem>
                  <SelectItem value="name">Nome (A-Z)</SelectItem>
                  <SelectItem value="size">Tamanho</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={() => { setIsUploadDialogOpen(true); }} size="sm" className="h-9">
                <Plus className="h-4 w-4 mr-2" />
                Novo Documento
              </Button>
            </div>
          </div>

          {showAdvancedFilters && (
            <div className="border-b p-4 bg-muted/30 space-y-4">
              <h4 className="text-sm font-medium">Filtros Avançados</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label className="text-xs">Tipo de Arquivo</Label>
                  <Select
                    value={fileType}
                    onValueChange={setFileType}
                  >
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue placeholder="Tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos</SelectItem>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="docx">Word</SelectItem>
                      <SelectItem value="xlsx">Excel</SelectItem>
                      <SelectItem value="image">Imagens</SelectItem>
                      <SelectItem value="audio">Áudio</SelectItem>
                      <SelectItem value="video">Vídeo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs">Paciente</Label>
                  <Select
                    value={selectedPatientId || 'all'}
                    onValueChange={(value) => { setSelectedPatientId(value === 'all' ? '' : value); }}
                    disabled={isPatientsLoading}
                  >
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue placeholder="Selecione um paciente" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos</SelectItem>
                      {isPatientsLoading ? (
                        <SelectItem value="loading" disabled>
                          <Loader2 className="h-3 w-3 animate-spin mr-1 inline" />
                          Carregando pacientes...
                        </SelectItem>
                      ) : (
                        patients?.map((patient) => (
                          <SelectItem key={patient.id} value={patient.id.toString()}>
                            {patient.full_name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs">Data</Label>
                  <Select defaultValue="all">
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue placeholder="Período" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Qualquer data</SelectItem>
                      <SelectItem value="today">Hoje</SelectItem>
                      <SelectItem value="week">Esta semana</SelectItem>
                      <SelectItem value="month">Este mês</SelectItem>
                      <SelectItem value="year">Este ano</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          <ScrollArea className="flex-1">
            {renderDocumentsList()}

            {/* Paginação */}
            {!isLoading && !isError && documentsLocal.length > 0 && totalPages > 1 && (
              <div className="flex justify-center items-center gap-2 my-4 pb-4">
                <Button
                  size="sm"
                  variant="outline"
                  disabled={currentPage === 0}
                  onClick={() => { handlePageChange(currentPage - 1); }}
                >
                  Anterior
                </Button>
                <span className="text-xs">
                  Página {currentPage + 1} de {totalPages}
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  disabled={currentPage >= totalPages}
                  onClick={() => { handlePageChange(currentPage + 1); }}
                >
                  Próximo
                </Button>
              </div>
            )}
          </ScrollArea>
        </div>
      </div>
    </main>
  );
}

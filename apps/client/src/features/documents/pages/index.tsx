import { DocumentContent } from "./DocumentsPage";
import { useAuthQuery } from "@/features/auth/hooks";
import { Loader2 } from "lucide-react";

export function DocumentsPage() {
  const { user, isLoading } = useAuthQuery();

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-sm text-muted-foreground">Carregando dados do usuário...</span>
      </div>
    );
  }

  if (!user) {
    return <div className="p-6 text-sm text-muted-foreground">Não foi possível carregar os dados do usuário.</div>;
  }

  return (
    <div className="flex min-h-screen bg-background">
      <div className="flex-1">
        <DocumentContent />
      </div>
    </div>
  );
}

export default DocumentsPage; 
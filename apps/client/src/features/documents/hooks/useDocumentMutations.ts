import { useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '@/shared/lib/api.client';
import { useToast } from '@/shared/hooks/use-toast';
import { Document, UpdateDocumentPayload, documentSchema, UpdateDocumentContentPayload, CreateDocumentFromHtmlPayload } from '@/features/documents/types/document.schema';
import { DOCUMENTS_QUERY_KEYS } from './constants';

export function useDocumentMutations() {
    const queryClient = useQueryClient();
    const { toast } = useToast();

    const createDocumentFromHtmlMutation = useMutation({
        mutationFn: async (payload: CreateDocumentFromHtmlPayload): Promise<Document> => {
            const response = await axiosInstance.post(`/protected/documents/from-html`, payload);
            const parsedData = documentSchema.safeParse(response.data);
            if (!parsedData.success) {
                console.error("Create from HTML validation error:", parsedData.error);
                throw new Error("Dados inválidos da API na criação de documento.");
            }
            return parsedData.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: DOCUMENTS_QUERY_KEYS.lists() });
            if (data.patient_id) {
                 queryClient.invalidateQueries({ queryKey: DOCUMENTS_QUERY_KEYS.filtered({ patient_id: data.patient_id }) });
            }
            toast({
                title: "Documento criado",
                description: "O documento foi criado com sucesso."
            });
        },
        onError: (error: any) => {
            toast({
                title: "Erro ao criar documento",
                description: error?.response?.data?.message || error.message,
                variant: "destructive"
            });
        }
    });

    const uploadDocumentMutation = useMutation({
        mutationFn: async (formData: FormData): Promise<Document> => {
            const response = await axiosInstance.post(`/protected/documents/upload`, formData, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            const parsedData = documentSchema.safeParse(response.data);
            if (!parsedData.success) {
                console.error("Upload validation error:", parsedData.error);
                throw new Error("Dados inválidos da API no upload de documento.");
            }
            return parsedData.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: DOCUMENTS_QUERY_KEYS.lists() });
            if (data.patient_id) {
                 queryClient.invalidateQueries({ queryKey: DOCUMENTS_QUERY_KEYS.filtered({ patient_id: data.patient_id }) });
            }
            toast({
                title: "Documento enviado",
                description: "O documento foi enviado com sucesso."
            });
        },
        onError: (error: any) => {
            toast({
                title: "Erro ao enviar documento",
                description: error?.response?.data?.message || error.message,
                variant: "destructive"
            });
        }
    });

    const updateDocumentMutation = useMutation({
        mutationFn: async ({ id, data }: { id: string, data: UpdateDocumentPayload }): Promise<Document> => {
            const response = await axiosInstance.put(`/protected/documents/${id}`, data);
            const parsedData = documentSchema.safeParse(response.data);
            if (!parsedData.success) {
                throw new Error("Invalid data from API on document update.");
            }
            return parsedData.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: DOCUMENTS_QUERY_KEYS.lists() });
            queryClient.invalidateQueries({ queryKey: DOCUMENTS_QUERY_KEYS.detail(data.id) });
            toast({
                title: "Documento atualizado",
                description: "O documento foi atualizado com sucesso."
            });
        },
        onError: (error: any) => {
            toast({
                title: "Erro ao atualizar documento",
                description: error?.response?.data?.message || error.message,
                variant: "destructive"
            });
        }
    });

    const updateDocumentContentMutation = useMutation({
        mutationFn: async ({ id, data }: { id: string, data: UpdateDocumentContentPayload }): Promise<Document> => {
            const response = await axiosInstance.put(`/protected/documents/${id}/content`, data);
            const parsedData = documentSchema.safeParse(response.data);
            if (!parsedData.success) {
                throw new Error("Invalid data from API on document content update.");
            }
            return parsedData.data;
        },
        onSuccess: (data) => {
            queryClient.invalidateQueries({ queryKey: DOCUMENTS_QUERY_KEYS.detail(data.id) });
        },
        onError: (error: any) => {
            toast({
                title: "Erro ao atualizar conteúdo",
                description: "Ocorreu um erro ao salvar o conteúdo do documento.",
                variant: "destructive"
            });
        }
    });

    const deleteDocumentMutation = useMutation({
        mutationFn: async (id: string): Promise<string> => {
            await axiosInstance.delete(`/protected/documents/${id}`);
            return id;
        },
        onSuccess: (id) => {
            queryClient.invalidateQueries({ queryKey: DOCUMENTS_QUERY_KEYS.lists() });
            queryClient.removeQueries({ queryKey: DOCUMENTS_QUERY_KEYS.detail(id) });
            
            // Invalidate filtered queries for any patient_id to ensure all document lists are refreshed
            queryClient.invalidateQueries({
                predicate: (query) => {
                    const queryKey = query.queryKey;
                    return Array.isArray(queryKey) && 
                           queryKey[0] === 'documents' && 
                           queryKey[2] === 'filtered';
                },
            });
            
            toast({
                title: "Documento removido",
                description: "O documento foi removido com sucesso."
            });
        },
        onError: (error: any) => {
            toast({
                title: "Erro ao remover documento",
                description: error?.response?.data?.message || error.message,
                variant: "destructive"
            });
        }
    });

    return {
        // Create/Upload
        createDocumentFromHtml: createDocumentFromHtmlMutation.mutate,
        isCreatingFromHtml: createDocumentFromHtmlMutation.isPending,
        uploadDocument: uploadDocumentMutation.mutate,
        isUploading: uploadDocumentMutation.isPending,
        
        // Update
        updateDocument: updateDocumentMutation.mutate,
        isUpdatingMetadata: updateDocumentMutation.isPending,
        updateDocumentContent: updateDocumentContentMutation.mutate,
        isUpdatingContent: updateDocumentContentMutation.isPending,

        // Delete
        deleteDocument: deleteDocumentMutation.mutate,
        isDeleting: deleteDocumentMutation.isPending,
    };
} 
import { DocumentFilterParams } from "../api/documents.api";

export const DOCUMENTS_QUERY_KEYS = {
    all: () => ['documents'] as const,
    lists: () => [...DOCUMENTS_QUERY_KEYS.all(), 'list'] as const,
    detail: (id: string) => [...DOCUMENTS_QUERY_KEYS.all(), 'detail', id] as const,
    filtered: (filters: { [key: string]: any }) => [...DOCUMENTS_QUERY_KEYS.all(), 'filtered', filters] as const,
}; 
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { documentsApi, DocumentFilterParams } from '@/features/documents/api/documents.api';
import { DocumentListResponse } from '@/features/documents/types/document.schema';
import { useToast } from '@/shared/hooks/use-toast';
import { DOCUMENTS_QUERY_KEYS } from './constants';
import { axiosInstance } from '@/shared/lib/api.client';
import { 
    documentSchema, 
    documentListResponseSchema,
    downloadUrlResponseSchema,
    Document,
    DocumentContent,
    documentContentSchema
} from '@/features/documents/types/document.schema';

export function useDocumentsQuery(filters: DocumentFilterParams = {}) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const documentsQuery = useQuery<DocumentListResponse, Error>({
    queryKey: DOCUMENTS_QUERY_KEYS.filtered(filters),
    queryFn: () => documentsApi.getDocuments(filters),
    staleTime: 1000 * 60 * 2, // 2 minutos
  });

  const getDownloadUrl = async (documentId: string): Promise<{ url: string, filename: string }> => {
    const response = await axiosInstance.get(`/protected/documents/${documentId}/download`);
    const parsed = downloadUrlResponseSchema.safeParse(response.data);
    if (!parsed.success) {
      console.error("Falha na validação da URL de download:", parsed.error.format());
      throw new Error('Invalid download URL data from API');
    }
    return { url: parsed.data.download_url, filename: parsed.data.filename };
  };

  const getDocumentContent = async (documentId: string): Promise<string> => {
    const response = await axiosInstance.get(`/protected/documents/${documentId}/content`);
    const parsed = documentContentSchema.safeParse(response.data);
    if (!parsed.success) {
      console.error("Get content validation error:", parsed.error);
      throw new Error('Invalid content data from API');
    }
    return parsed.data.content;
  };

  return {
    documents: documentsQuery.data?.documents || [],
    totalCount: documentsQuery.data?.total_count || 0,
    isLoading: documentsQuery.isLoading,
    isError: documentsQuery.isError,
    error: documentsQuery.error,
    refetch: documentsQuery.refetch,
    getDownloadUrl,
    getDocumentContent
  };
}

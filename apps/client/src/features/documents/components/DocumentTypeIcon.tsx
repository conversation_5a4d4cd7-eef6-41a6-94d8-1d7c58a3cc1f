import {
  File,
  FileText,
  Image,
  Music,
  Video,
  FileSpreadsheet,
  FileCode,
  Archive,
  Presentation
} from "lucide-react";

type DocumentTypeIconProps = {
  type?: string;
  mimeType?: string | null;
  filename?: string;
  className?: string;
};

export function DocumentTypeIcon({
  type,
  mimeType,
  filename,
  className = "h-8 w-8"
}: DocumentTypeIconProps) {
  // Se o tipo for fornecido diretamente, use-o
  if (type) {
    switch (type.toLowerCase()) {
      case "pdf":
        return <FileText className={`${className} text-red-500`} />;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
      case "webp":
      case "image":
        return <Image className={`${className} text-blue-500`} />;
      case "mp3":
      case "wav":
      case "ogg":
      case "audio":
        return <Music className={`${className} text-purple-500`} />;
      case "mp4":
      case "webm":
      case "avi":
      case "mov":
      case "video":
        return <Video className={`${className} text-pink-500`} />;
      case "doc":
      case "docx":
        return <FileText className={`${className} text-blue-400`} />;
      case "xls":
      case "xlsx":
      case "csv":
        return <FileSpreadsheet className={`${className} text-green-500`} />;
      case "ppt":
      case "pptx":
        return <Presentation className={`${className} text-orange-500`} />;
      case "zip":
      case "rar":
      case "7z":
        return <Archive className={`${className} text-amber-500`} />;
      case "js":
      case "ts":
      case "html":
      case "css":
      case "json":
        return <FileCode className={`${className} text-yellow-500`} />;
      default:
        return <File className={`${className} text-gray-500`} />;
    }
  }

  // Determinar o tipo com base no MIME type
  if (mimeType) {
    if (mimeType.startsWith("image/")) {
      return <Image className={`${className} text-blue-500`} />;
    }
    if (mimeType.startsWith("audio/")) {
      return <Music className={`${className} text-purple-500`} />;
    }
    if (mimeType.startsWith("video/")) {
      return <Video className={`${className} text-pink-500`} />;
    }
    if (mimeType.includes("pdf")) {
      return <FileText className={`${className} text-red-500`} />;
    }
    if (mimeType.includes("spreadsheet") || mimeType.includes("excel") || mimeType.includes("csv")) {
      return <FileSpreadsheet className={`${className} text-green-500`} />;
    }
    if (mimeType.includes("presentation") || mimeType.includes("powerpoint")) {
      return <Presentation className={`${className} text-orange-500`} />;
    }
    if (mimeType.includes("word") || mimeType.includes("document")) {
      return <FileText className={`${className} text-blue-400`} />;
    }
    if (mimeType.includes("html") || mimeType.includes("xml") || mimeType.includes("json")) {
      return <FileCode className={`${className} text-yellow-500`} />;
    }
    if (mimeType.includes("zip") || mimeType.includes("rar") || mimeType.includes("compressed")) {
      return <Archive className={`${className} text-amber-500`} />;
    }
  }

  // Determinar o tipo com base na extensão do arquivo
  if (filename) {
    const extension = filename.split('.').pop()?.toLowerCase();
    if (extension) {
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension)) {
        return <Image className={`${className} text-blue-500`} />;
      }
      if (['mp3', 'wav', 'ogg', 'aac', 'm4a'].includes(extension)) {
        return <Music className={`${className} text-purple-500`} />;
      }
      if (['mp4', 'webm', 'avi', 'mov', 'wmv'].includes(extension)) {
        return <Video className={`${className} text-pink-500`} />;
      }
      if (extension === 'pdf') {
        return <FileText className={`${className} text-red-500`} />;
      }
      if (['xls', 'xlsx', 'csv', 'ods'].includes(extension)) {
        return <FileSpreadsheet className={`${className} text-green-500`} />;
      }
      if (['ppt', 'pptx', 'odp'].includes(extension)) {
        return <Presentation className={`${className} text-orange-500`} />;
      }
      if (['doc', 'docx', 'odt', 'rtf', 'txt'].includes(extension)) {
        return <FileText className={`${className} text-blue-400`} />;
      }
      if (['html', 'htm', 'xml', 'json', 'js', 'css', 'ts', 'jsx', 'tsx'].includes(extension)) {
        return <FileCode className={`${className} text-yellow-500`} />;
      }
      if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
        return <Archive className={`${className} text-amber-500`} />;
      }
    }
  }

  // Tipo genérico para quando não conseguimos determinar
  return <File className={`${className} text-gray-500`} />;
}

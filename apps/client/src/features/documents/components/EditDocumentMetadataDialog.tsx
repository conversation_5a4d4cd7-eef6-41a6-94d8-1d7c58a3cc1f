import { useState, useEffect } from "react";
import { Document } from "@/features/documents/types/document.schema";
import { useDocumentMutations } from "@/features/documents/hooks/useDocumentMutations";
import { useDocumentsQuery } from "@/features/documents/hooks/useDocumentsQuery";
import { usePatientQuery } from "@/features/patients/hooks/usePatientQuery";
import { useToast } from "@/shared/hooks/use-toast";
import { Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Textarea } from "@/shared/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";

interface EditDocumentMetadataDialogProps {
  document: Document | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditDocumentMetadataDialog({
  document,
  open,
  onOpenChange,
  onSuccess,
}: EditDocumentMetadataDialogProps) {
  const [title, setTitle] = useState("");
  const [patientId, setPatientId] = useState<string>("");
  const [content, setContent] = useState<string>("");
  const [isContentLoading, setIsContentLoading] = useState(false);

  const { updateDocument, updateDocumentContent, isUpdatingMetadata, isUpdatingContent } = useDocumentMutations();
  const { getDownloadUrl } = useDocumentsQuery();
  const { allPatients, isLoadingList: isPatientsLoading } = usePatientQuery();
  const { toast } = useToast();

  useEffect(() => {
    if (document && open) {
      setTitle(document.title || "");
      setPatientId(document.patient_id || "");
      setContent(""); // Reset content on open

      // Fetch content only for HTML documents
      if (document.mime_type === 'text/html') {
        setIsContentLoading(true);
        const fetchContent = async () => {
          try {
            const { url } = await getDownloadUrl(document.id);
            const response = await fetch(url);
            if (!response.ok) throw new Error("Failed to fetch content");
            const textContent = await response.text();
            setContent(textContent);
          } catch (error) {
            console.error("Error fetching document content:", error);
            toast({
              title: "Erro ao carregar conteúdo",
              description: "Não foi possível carregar o conteúdo do documento para edição.",
              variant: "destructive",
            });
          } finally {
            setIsContentLoading(false);
          }
        };
        fetchContent();
      }
    }
  }, [document, open, getDownloadUrl, toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!document) return;

    const isHtml = document.mime_type === 'text/html';
    const metadataPromise = updateDocument({ id: document.id, data: { title, patient_id: patientId || undefined } });
    const contentPromise = isHtml 
      ? updateDocumentContent({ id: document.id, data: { content } })
      : Promise.resolve();

    Promise.all([metadataPromise, contentPromise])
      .then(() => {
        toast({
          title: "Documento atualizado",
          description: "O documento foi atualizado com sucesso.",
        });
        onOpenChange(false);
        onSuccess?.();
      })
      .catch(() => {
        // Individual mutations already show toasts on error
      });
  };
  
  const isSaving = isUpdatingMetadata || isUpdatingContent;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Editar Documento</DialogTitle>
          <DialogDescription>
            Atualize as informações e o conteúdo do documento. Clique em salvar quando terminar.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4 max-h-[70vh] overflow-y-auto pr-6">
          <div className="space-y-2">
            <Label htmlFor="title">Título</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Título do documento"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="patient">Paciente</Label>
            <Select
              value={patientId}
              onValueChange={setPatientId}
              disabled={isPatientsLoading}
            >
              <SelectTrigger id="patient">
                <SelectValue placeholder="Selecione um paciente (opcional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Nenhum</SelectItem>
                {isPatientsLoading ? (
                  <SelectItem value="" disabled>
                    <Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
                    Carregando pacientes...
                  </SelectItem>
                ) : (
                  allPatients?.map((patient) => (
                    <SelectItem key={patient.id} value={patient.id.toString()}>
                      {patient.full_name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
          
          {document?.mime_type === 'text/html' && (
            <div className="space-y-2">
              <Label htmlFor="content">Conteúdo</Label>
              {isContentLoading ? (
                 <div className="flex items-center justify-center h-40 border rounded-md">
                   <Loader2 className="h-6 w-6 animate-spin" />
                 </div>
              ) : (
                <Textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Conteúdo do documento HTML..."
                  rows={15}
                  className="font-mono"
                />
              )}
            </div>
          )}

          <DialogFooter className="mt-6 sticky bottom-0 bg-background py-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSaving}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Salvando...
                </>
              ) : (
                "Salvar"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

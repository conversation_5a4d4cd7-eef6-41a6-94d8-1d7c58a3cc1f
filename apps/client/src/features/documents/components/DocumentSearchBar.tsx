import { useState } from "react";
import { Input } from "@/shared/ui/input";
import { Button } from "@/shared/ui/button";
import { Search, LayoutGrid, List } from "lucide-react";

type Props = {
  onSearch: (query: string) => void;
  onFilter: (filter: string) => void;
  filter: string;
  viewMode: "grid" | "list";
  onViewModeChange: (mode: "grid" | "list") => void;
};

const filters = [
  { label: "Todos", value: "all" },
  { label: "Meus documentos", value: "meus" },
  { label: "Por paciente", value: "paciente" },
  { label: "Por sessão", value: "sessao" },
  { label: "Por contato", value: "contato" },
  { label: "Não vinculados", value: "nao-vinculado" },
];

export function DocumentSearchBar({ onSearch, onFilter, filter, viewMode, onViewModeChange }: Props) {
  const [query, setQuery] = useState("");

  function handleSearch(e: React.FormEvent) {
    e.preventDefault();
    onSearch(query);
  }

  return (
    <div className="flex flex-col md:flex-row gap-2 mb-6">
      <form className="flex-1 flex" onSubmit={handleSearch}>
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar por nome, paciente, tipo..."
            value={query}
            onChange={e => setQuery(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button type="submit" className="ml-2">Buscar</Button>
      </form>
      
      <div className="flex gap-2 mt-2 md:mt-0">
        <div className="flex border rounded-md overflow-hidden">
          <Button
            type="button"
            variant={viewMode === "grid" ? "default" : "ghost"}
            size="sm"
            className="rounded-none"
            onClick={() => onViewModeChange("grid")}
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
          <Button
            type="button"
            variant={viewMode === "list" ? "default" : "ghost"}
            size="sm"
            className="rounded-none"
            onClick={() => onViewModeChange("list")}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

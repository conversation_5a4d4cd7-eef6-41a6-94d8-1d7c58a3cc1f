import { DocumentTypeIcon } from "./DocumentTypeIcon";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import {
  MoreVertical,
  Eye,
  Download,
  Trash2,
  Share2,
  Star,
  Upload,
  Clock,
  Loader2,
  User,
  FileEdit
} from "lucide-react";
import { useState } from "react";
import { cn, formatFileSize } from "@/shared/lib/utils";
import { Badge } from "@/shared/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/shared/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/shared/ui/alert-dialog";
import { Document } from "@/features/documents/types/document.schema";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

type Props = {
  document: Document;
  viewMode: "grid" | "list";
  onView?: (document: Document) => void;
  onDownload?: (document: Document) => void;
  onEdit?: (document: Document) => void;
  onDelete?: (document: Document) => void;
  isDeleting?: boolean;
};

export function DocumentCard({
  document,
  viewMode,
  onView,
  onDownload,
  onEdit,
  onDelete,
  isDeleting = false
}: Props) {
  const [starred, setStarred] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Formatar a data de criação/atualização
  const formattedDate = document.created_at
    ? format(new Date(document.created_at), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })
    : "";

  // Formatar o tamanho do arquivo
  const size = document.size_bytes
    ? formatFileSize(document.size_bytes)
    : "";

  // Manipuladores de eventos
  const handleView = () => onView && onView(document);
  const handleDownload = () => onDownload && onDownload(document);
  const handleEdit = () => onEdit && onEdit(document);
  const handleDelete = () => {
    if (onDelete) {
      setIsDeleteDialogOpen(false);
      onDelete(document);
    }
  };

  if (viewMode === "list") {
    return (
      <>
        <div className="bg-card rounded-lg shadow-sm border p-3 flex items-center gap-3 group hover:border-primary/50 transition-colors">
          <DocumentTypeIcon
            mimeType={document.mime_type}
            filename={document.filename}
            className="h-10 w-10"
          />
          <div className="flex-1 min-w-0">
            <div className="font-semibold truncate flex items-center gap-1">
              {document.title}
              {starred && <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />}
            </div>
            <div className="text-xs text-muted-foreground truncate">
              {document.patient_name && (
                <span className="flex items-center">
                  <User className="h-3 w-3 mr-1" />
                  {document.patient_name}
                </span>
              )}
            </div>
            <div className="flex items-center text-xs text-muted-foreground mt-1">
              {size && (
                <>
                  <span>{size}</span>
                  <span className="mx-1">•</span>
                </>
              )}
              <span>{formattedDate}</span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            {onDownload && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleDownload}
              >
                <Download className="h-4 w-4" />
              </Button>
            )}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onView && (
                  <DropdownMenuItem onClick={handleView}>
                    <Eye className="h-4 w-4 mr-2" /> Visualizar
                  </DropdownMenuItem>
                )}
                {onDownload && (
                  <DropdownMenuItem onClick={handleDownload}>
                    <Download className="h-4 w-4 mr-2" /> Baixar
                  </DropdownMenuItem>
                )}
                {onEdit && (
                  <DropdownMenuItem onClick={handleEdit}>
                    <FileEdit className="h-4 w-4 mr-2" /> Editar metadados
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <DropdownMenuItem
                    onClick={() => setIsDeleteDialogOpen(true)}
                    className="text-destructive"
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Excluindo...
                      </>
                    ) : (
                      <>
                        <Trash2 className="h-4 w-4 mr-2" /> Excluir
                      </>
                    )}
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Excluir documento</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza que deseja excluir o documento "{document.title}"?
                Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Excluindo...
                  </>
                ) : (
                  "Excluir"
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  }

  return (
    <>
      <div className="bg-card rounded-lg shadow-sm border p-4 flex flex-col gap-2 relative group hover:border-primary/50 transition-colors">
        <div className="flex items-center gap-3">
          <DocumentTypeIcon
            mimeType={document.mime_type}
            filename={document.filename}
            className="h-8 w-8"
          />
          <div className="flex-1">
            <div className="font-semibold truncate flex items-center gap-1">
              {document.title}
            </div>
            {document.patient_name && (
              <div className="text-xs text-muted-foreground truncate flex items-center">
                <User className="h-3 w-3 mr-1" />
                {document.patient_name}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between text-xs text-muted-foreground mt-auto">
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            <span>{formattedDate}</span>
          </div>
          {size && <span>{size}</span>}
        </div>

        <div className="flex justify-between mt-2">
          {onView && (
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={handleView}
            >
              <Eye className="h-3.5 w-3.5 mr-1" /> Visualizar
            </Button>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 ml-1"
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onDownload && (
                <DropdownMenuItem onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" /> Baixar
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={handleEdit}>
                  <FileEdit className="h-4 w-4 mr-2" /> Editar metadados
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem
                  onClick={() => setIsDeleteDialogOpen(true)}
                  className="text-destructive"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Excluindo...
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4 mr-2" /> Excluir
                    </>
                  )}
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir documento</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir o documento "{document.title}"?
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Excluindo...
                </>
              ) : (
                "Excluir"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Textarea } from "@/shared/ui/textarea";
import { Badge } from "@/shared/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { Upload, X, File, Plus, Loader2 } from "lucide-react";
import { useToast } from "@/shared/hooks/use-toast";
import { useDocumentMutations } from "@/features/documents/hooks/useDocumentMutations";
import { usePatientQuery } from "@/features/patients/hooks/usePatientQuery";
import { formatFileSize } from "@/shared/lib/utils";
import { Patient } from "@/features/patients/types/patient.schema";

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  patientId?: string; // ID do paciente pré-selecionado (opcional)
  onSuccess?: () => void; // Callback opcional para quando o upload for bem-sucedido
}

export function DocumentUploadDialog({
  open,
  onOpenChange,
  patientId,
  onSuccess
}: Props) {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [vinculoType, setVinculoType] = useState("");
  const [vinculoId, setVinculoId] = useState("");
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const { toast } = useToast();

  // Hooks para API
  const { uploadDocument, isUploading } = useDocumentMutations();
  const { allPatients: patients, isLoadingList: isPatientsLoading } = usePatientQuery();

  // Definir o paciente pré-selecionado quando o componente é montado ou quando patientId muda
  useEffect(() => {
    if (patientId) {
      setSelectedPatientId(patientId);
      setVinculoType("patient");
      setVinculoId(patientId);
    }
  }, [patientId]);

  // Resetar o formulário quando o diálogo é fechado
  useEffect(() => {
    if (!open) {
      resetForm();
    }
  }, [open]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);

      // Usar o nome do arquivo como título se o título estiver vazio
      if (!title) {
        // Remover a extensão do arquivo para o título
        const fileName = selectedFile.name.split('.').slice(0, -1).join('.');
        setTitle(fileName || selectedFile.name); // Usar o nome completo se não houver extensão
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      toast({
        title: "Erro",
        description: "Por favor, selecione um arquivo para upload.",
        variant: "destructive",
      });
      return;
    }

    if (!title.trim()) {
      toast({
        title: "Erro",
        description: "Por favor, informe um título para o documento.",
        variant: "destructive",
      });
      return;
    }

    // Determinar o ID do paciente com base na seleção
    let finalPatientId: string | null = null;
    if (vinculoType === "patient" && vinculoId) {
      finalPatientId = vinculoId;
    } else if (patientId) {
      finalPatientId = patientId;
    }

    // Criar FormData para o upload
    const formData = new FormData();
    formData.append('file', file);

    // Adicionar metadados como JSON
    const metadata = {
      title: title.trim(),
      patient_id: finalPatientId,
    };

    formData.append('metadata', JSON.stringify(metadata));

    // Enviar para a API
    uploadDocument(formData, {
      onSuccess: () => {
        resetForm();
        onOpenChange(false);
        if (onSuccess) onSuccess();
      }
    });
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const resetForm = () => {
    setTitle("");
    setDescription("");
    setVinculoType(patientId ? "patient" : "");
    setVinculoId(patientId ?? "");
    setSelectedPatientId(patientId ?? null);
    setFile(null);
    setTags([]);
    setNewTag("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Enviar novo documento</DialogTitle>
          <DialogDescription>
            Faça upload de um documento e vincule-o a um paciente.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Título do documento</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => { setTitle(e.target.value); }}
                placeholder="Ex: Laudo Fonoaudiológico"
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Descrição (opcional)</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => { setDescription(e.target.value); }}
                placeholder="Adicione uma descrição para este documento"
                rows={3}
              />
            </div>

            {!patientId && (
              <div className="grid gap-2">
                <Label htmlFor="vinculo-type">Vincular a</Label>
                <Select value={vinculoType} onValueChange={setVinculoType}>
                  <SelectTrigger id="vinculo-type">
                    <SelectValue placeholder="Selecione um tipo de vínculo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Não vincular</SelectItem>
                    <SelectItem value="patient">Paciente</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {((vinculoType === "patient" && !patientId) || patientId) && (
              <div className="grid gap-2">
                <Label htmlFor="vinculo-id">Paciente</Label>
                <Select
                  value={vinculoId}
                  onValueChange={setVinculoId}
                  disabled={!!patientId || isPatientsLoading}
                >
                  <SelectTrigger id="vinculo-id">
                    <SelectValue placeholder="Selecione um paciente" />
                  </SelectTrigger>
                  <SelectContent>
                    {isPatientsLoading ? (
                      <SelectItem value="loading" disabled>
                        <Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
                        Carregando pacientes...
                      </SelectItem>
                    ) : (
                      patients?.map((patient: Patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {patient.full_name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="tags">Tags (opcional)</Label>
              <div className="flex gap-2">
                <Input
                  id="tags"
                  value={newTag}
                  onChange={(e) => { setNewTag(e.target.value); }}
                  placeholder="Adicionar tag"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                />
                <Button type="button" size="icon" onClick={addTag}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => { removeTag(tag); }}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="file">Arquivo</Label>
              {file ? (
                <div className="flex items-center p-2 border rounded-md">
                  <File className="h-5 w-5 mr-2 text-primary" />
                  <div className="text-sm truncate flex-1">
                    <div>{file.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {formatFileSize(file.size)}
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => { setFile(null); }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-center border border-dashed rounded-md p-6">
                  <label
                    htmlFor="file-upload"
                    className="flex flex-col items-center cursor-pointer"
                  >
                    <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                    <span className="text-sm font-medium">
                      Clique para selecionar um arquivo
                    </span>
                    <span className="text-xs text-muted-foreground mt-1">
                      PDF, Word, Excel, imagens, áudio
                    </span>
                    <input
                      id="file-upload"
                      type="file"
                      className="hidden"
                      onChange={handleFileChange}
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.mp3,.wav"
                    />
                  </label>
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => { onOpenChange(false); }}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isUploading || !file}>
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Enviando...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Enviar documento
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { User } from '@/features/auth/types/user.schema';
import { axiosInstance } from '@/shared/lib/api.client';
import { USER_QUERY_KEYS } from './constants';
import { useSession } from './useSession';

export function useProfile() {
  const queryClient = useQueryClient();
  const { isAuthenticated } = useSession();

  const profileQuery = useQuery({
    queryKey: USER_QUERY_KEYS.profile,
    queryFn: async (): Promise<User> => {
      const response = await axiosInstance.get('/protected/user/profile');
      return response.data;
    },
    enabled: isAuthenticated,
    staleTime: 1000 * 60 * 5,
    retry: false,
  });

  return {
    user: profileQuery.data,
    isLoading: profileQuery.isLoading,
    isEmailVerified: !!profileQuery.data?.is_verified,
    invalidateProfile: () => queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile }),
  };
}

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { useToast } from '@/shared/hooks/use-toast';
import { authClient } from '@/shared/lib/api.client';
import { useEffect } from 'react';
import { AUTH_QUERY_KEYS } from './constants';

export interface SessionResult {
  isAuthenticated: boolean;
  token: string | null;
  redirectTo?: string;
}

export function useSession() {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { toast } = useToast();

  const sessionQuery = useQuery<SessionResult>({
    queryKey: AUTH_QUERY_KEYS.session,
    queryFn: async () => {
      const isAuthenticated = await authClient.checkSession();
      return {
        isAuthenticated,
        token: isAuthenticated ? authClient.getToken() : null
      };
    },
    enabled: true,
    retry: false,
    placeholderData: {
      isAuthenticated: !!authClient.getToken(),
      token: authClient.getToken(),
    }
  });

  useEffect(() => {
    const sessionData = sessionQuery.data;

    if (!sessionQuery.isLoading &&
        sessionData?.redirectTo &&
        !sessionData.isAuthenticated &&
        sessionData.redirectTo !== '/login') {

      queryClient.setQueryData(AUTH_QUERY_KEYS.session, {
        ...sessionData,
        redirectTo: undefined
      });

      const redirectCount = parseInt(sessionStorage.getItem('auth_redirect_count') || '0');
      if (redirectCount > 3) {
        sessionStorage.removeItem('auth_redirect_count');
        toast({
          title: "Erro de autenticação",
          description: "Ocorreu um problema com sua sessão. Por favor, faça login novamente.",
          variant: "destructive"
        });
        navigate({ to: '/login' });
        return;
      }

      sessionStorage.setItem('auth_redirect_count', (redirectCount + 1).toString());

      void navigate({
        to: "/login",
        search: { redirect: sessionData.redirectTo }
      });
    }
  }, [sessionQuery.data, sessionQuery.isLoading, navigate, queryClient, toast]);

  return {
    isAuthenticated: sessionQuery.data?.isAuthenticated || false,
    isLoading: sessionQuery.isLoading,
    sessionData: sessionQuery.data,
    checkSession: authClient.checkSession,
    invalidateSession: () => queryClient.invalidateQueries({ queryKey: AUTH_QUERY_KEYS.session }),
  };
}

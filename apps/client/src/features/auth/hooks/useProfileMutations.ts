import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/shared/hooks/use-toast';
import { UpdateUserData } from '@/features/auth/types/user.schema';
import { axiosInstance } from '@/shared/lib/api.client';
import { USER_QUERY_KEYS } from './constants';

export function useProfileMutations() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const updateProfileMutation = useMutation({
    mutationFn: async (data: UpdateUserData) => {
      const response = await axiosInstance.put('/protected/user/profile', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile });
      toast({
        title: "Perfil atualizado",
        description: "Suas informações foram atualizadas com sucesso."
      });
    },
    onError: (error: unknown) => {
      const errorResponse = error as { response?: { data?: { error?: string } } };
      toast({
        title: "Erro ao atualizar perfil",
        description: errorResponse.response?.data?.error || "Não foi possível atualizar seu perfil.",
        variant: "destructive"
      });
    }
  });

  const uploadProfilePictureMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      const response = await axiosInstance.post('/protected/user/profile-picture', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile });
      toast({
        title: "Imagem atualizada",
        description: "Sua foto de perfil foi atualizada com sucesso."
      });
    },
    onError: (error: unknown) => {
      const errorResponse = error as { response?: { data?: { error?: string } } };
      toast({
        title: "Erro ao atualizar imagem",
        description: errorResponse.response?.data?.error || "Não foi possível atualizar sua foto de perfil.",
        variant: "destructive"
      });
    }
  });

  return {
    updateProfile: (data: UpdateUserData) => updateProfileMutation.mutate(data),
    uploadProfilePicture: (file: File) => uploadProfilePictureMutation.mutate(file),
    isLoading: updateProfileMutation.isPending || uploadProfilePictureMutation.isPending,
  };
}

import { useSession } from './useSession';
import { useProfile } from './useProfile';
import { useAuthMutations } from './useAuthMutations';
import { useProfileMutations } from './useProfileMutations';
import { useEmailVerification } from './useEmailVerification';
import { usePasswordManagement } from './usePasswordManagement';
import { authClient } from '@/shared/lib/api.client';

export function useAuthQuery() {
  const { isAuthenticated, isLoading: sessionLoading, checkSession } = useSession();
  const { user, isEmailVerified, isLoading: profileLoading } = useProfile();
  const { login, register, logout, isLoading: authMutationsLoading } = useAuthMutations();
  const { updateProfile, uploadProfilePicture, isLoading: profileMutationsLoading } = useProfileMutations();
  const { resendVerificationEmail, verifyEmail, isLoading: emailVerificationLoading } = useEmailVerification();
  const { forgotPassword, resetPassword, changePassword, isLoading: passwordManagementLoading } = usePasswordManagement();

  return {
    isAuthenticated,
    isLoading: sessionLoading || profileLoading || authMutationsLoading || 
               profileMutationsLoading || emailVerificationLoading || passwordManagementLoading,
    user,
    isEmailVerified,


    login,
    register,
    logout,
    

    forgotPassword,
    resetPassword,
    changePassword,


    updateProfile,
    uploadProfilePicture,


    resendVerificationEmail,
    verifyEmail,
    

    checkSession,
    authClient
  };
}


export {
  useSession,
  useProfile,
  useAuthMutations,
  useProfileMutations,
  useEmailVerification,
  usePasswordManagement
};


export { AUTH_QUERY_KEYS, USER_QUERY_KEYS } from './constants';

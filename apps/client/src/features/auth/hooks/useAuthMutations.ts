import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { useToast } from '@/shared/hooks/use-toast';
import { LoginFormData, RegisterFormData } from '@/features/auth/types/auth.schema';
import { authClient } from '@/shared/lib/api.client';
import { AUTH_QUERY_KEYS } from './constants';
import { USER_QUERY_KEYS } from './constants';

export function useAuthMutations() {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { toast } = useToast();

  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginFormData) => {
      return await authClient.login(credentials);
    },
    onSuccess: (data) => {
      void queryClient.invalidateQueries({ queryKey: AUTH_QUERY_KEYS.session });
      void queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile });

      toast({
        title: "Login realizado com sucesso",
        description: data.is_verified
          ? "Bem-vindo de volta!"
          : "Por favor, verifique seu email para ativar sua conta.",
      });

      void navigate({
        to: data.is_verified ? '/dashboard' : '/verification-needed'
      });
    },
    onError: (error: unknown) => {
      const errorResponse = error as { response?: { data?: { error?: string } } };
      toast({
        title: "Erro ao realizar login",
        description: errorResponse.response?.data?.error || "Verifique suas credenciais",
        variant: "destructive",
      });
    }
  });

  const registerMutation = useMutation({
    mutationFn: authClient.register,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: AUTH_QUERY_KEYS.session });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile });

      toast({
        title: "Registro realizado com sucesso",
        description: "Sua conta foi criada com sucesso!"
      });

      navigate({ to: '/dashboard' });
    },
    onError: (error: unknown) => {
      const errorResponse = error as { response?: { data?: { error?: string } } };
      toast({
        title: "Erro ao realizar cadastro",
        description: errorResponse.response?.data?.error || "Verifique os dados informados",
        variant: "destructive",
      });
    }
  });

  const logoutMutation = useMutation({
    mutationFn: authClient.logout,
    onSuccess: () => {
      queryClient.clear();
      toast({
        title: "Logout realizado",
        description: "Você foi desconectado com sucesso"
      });
      navigate({ to: '/' });
    },
    onError: (error: unknown) => {
      console.error("Erro ao realizar logout:", error);
      navigate({ to: '/' });
    }
  });

  return {
    login: (credentials: LoginFormData) => loginMutation.mutate(credentials),
    register: (data: RegisterFormData) => registerMutation.mutate(data),
    logout: () => logoutMutation.mutate(),
    isLoading: loginMutation.isPending || registerMutation.isPending || logoutMutation.isPending,
  };
}

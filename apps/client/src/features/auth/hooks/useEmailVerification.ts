import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/shared/hooks/use-toast';
import { axiosInstance } from '@/shared/lib/api.client';
import { USER_QUERY_KEYS } from './constants';

export function useEmailVerification() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const resendVerificationEmailMutation = useMutation({
    mutationFn: async (email: string) => {
      const response = await axiosInstance.post('/auth/resend-verification-email', { email });
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Email reenviado",
        description: "Verifique sua caixa de entrada para ativar sua conta."
      });
    },
    onError: (error: unknown) => {
      const errorResponse = error as { response?: { data?: { error?: string } } };
      toast({
        title: "Erro ao reenviar email",
        description: errorResponse.response?.data?.error || "Não foi possível reenviar o email de verificação.",
        variant: "destructive"
      });
    }
  });

  const verifyEmailMutation = useMutation({
    mutationFn: async (token: string) => {
      const response = await axiosInstance.get(`/auth/verify-email/${token}`);
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Email verificado",
        description: "Seu email foi verificado com sucesso."
      });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile });
    },
    onError: (error: unknown) => {
      const errorResponse = error as { response?: { data?: { error?: string } } };
      toast({
        title: "Erro ao verificar email",
        description: errorResponse.response?.data?.error || "Link inválido ou expirado.",
        variant: "destructive"
      });
    }
  });

  return {
    resendVerificationEmail: (email: string) => resendVerificationEmailMutation.mutate(email),
    verifyEmail: (token: string) => verifyEmailMutation.mutate(token),
    isLoading: resendVerificationEmailMutation.isPending || verifyEmailMutation.isPending,
  };
}

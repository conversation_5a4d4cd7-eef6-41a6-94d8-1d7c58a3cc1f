import { useMutation } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { useToast } from '@/shared/hooks/use-toast';
import { ForgotPasswordFormData, ResetPasswordFormData } from '@/features/auth/types/auth.schema';
import { authClient, axiosInstance } from '@/shared/lib/api.client';

export function usePasswordManagement() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const forgotPasswordMutation = useMutation({
    mutationFn: authClient.forgotPassword,
    onSuccess: () => {
      toast({
        title: "E-mail enviado",
        description: "Se o e-mail estiver cadastrado, você receberá instruções para redefinir sua senha."
      });
    },
    onError: (error: unknown) => {
      const errorResponse = error as { response?: { data?: { error?: string } } };
      toast({
        title: "Erro ao solicitar recuperação de senha",
        description: errorResponse.response?.data?.error || "Houve um problema ao processar sua solicitação.",
        variant: "destructive"
      });
    }
  });

  const resetPasswordMutation = useMutation({
    mutationFn: ({ token, data }: { token: string, data: ResetPasswordFormData }) =>
      authClient.resetPassword(token, data.password),
    onSuccess: () => {
      toast({
        title: "Senha alterada com sucesso",
        description: "Sua nova senha foi definida. Você já pode fazer login."
      });
      setTimeout(() => navigate({ to: '/login' }), 2000);
    },
    onError: (error: unknown) => {
      const errorResponse = error as { response?: { data?: { error?: string } } };
      toast({
        title: "Erro ao redefinir senha",
        description: errorResponse.response?.data?.error || "Link inválido ou expirado.",
        variant: "destructive"
      });
    }
  });

  const changePasswordMutation = useMutation({
    mutationFn: async ({ currentPassword, newPassword }: { currentPassword: string, newPassword: string }) => {
      const response = await axiosInstance.post('/protected/change-password', {
        current_password: currentPassword,
        new_password: newPassword
      });
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Senha alterada",
        description: "Sua senha foi alterada com sucesso."
      });
    },
    onError: (error: unknown) => {
      const errorResponse = error as { response?: { data?: { error?: string } } };
      toast({
        title: "Erro ao alterar senha",
        description: errorResponse.response?.data?.error || "Verifique se a senha atual está correta.",
        variant: "destructive"
      });
    }
  });

  return {
    forgotPassword: (data: ForgotPasswordFormData) => forgotPasswordMutation.mutate(data),
    resetPassword: (token: string, data: ResetPasswordFormData) => 
      resetPasswordMutation.mutate({ token, data }),
    changePassword: (currentPassword: string, newPassword: string) =>
      changePasswordMutation.mutate({ currentPassword, newPassword }),
    isLoading: forgotPasswordMutation.isPending || resetPasswordMutation.isPending || changePasswordMutation.isPending,
  };
}

import { VerifyEmailContent } from '@/features/auth/components/verifyEmailContent';
import { useParams } from '@tanstack/react-router';

export default function VerifyEmailPage() {
  const { token } = useParams({ from: '/verify-email/$token' });

  return (
    <div className="flex min-h-svh flex-col items-center justify-center bg-muted p-6 md:p-10">
      <div className="w-full max-w-md">
        <div className="border rounded-lg bg-card p-6 shadow-sm">
          <VerifyEmailContent token={token} />
        </div>
      </div>
    </div>
  );
}
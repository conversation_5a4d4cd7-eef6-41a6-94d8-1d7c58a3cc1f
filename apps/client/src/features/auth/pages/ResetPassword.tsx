import { ResetPasswordForm } from "@/features/auth/components/forms/resetPasswordForm";
import { useParams } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { AlertCircle } from "lucide-react";
import { Link } from "@tanstack/react-router";
import { Button } from "@/shared/ui/button";

export default function ResetPasswordPage() {

  const { token } = useParams({ from: '/reset-password/$token' });
  const [isInvalidToken, setIsInvalidToken] = useState(false);

  useEffect(() => {
    if (!token || token.length < 20) {
      setIsInvalidToken(true);
    }
  }, [token]);

  return (
    <div className="flex min-h-svh flex-col items-center justify-center bg-muted p-6 md:p-10">
      <div className="w-full max-w-md">
        <div className="border rounded-lg bg-card p-6 shadow-sm">
          {isInvalidToken ? (
            <div className="space-y-4">
              <div className="flex flex-col space-y-2 text-center">
                <h1 className="text-2xl font-semibold tracking-tight">
                  Link inválido
                </h1>
                <p className="text-sm text-muted-foreground">
                  O link de redefinição de senha é inválido ou expirou.
                </p>
              </div>

              <div className="rounded-md border border-destructive/20 bg-destructive/10 p-4">
                <div className="flex items-center space-x-2 text-destructive">
                  <AlertCircle className="h-5 w-5" />
                  <h3 className="font-medium">Link inválido ou expirado</h3>
                </div>
                <p className="mt-2 text-sm text-destructive/80">
                  O link de redefinição de senha que você está tentando acessar é inválido ou expirou. Por favor, solicite um novo link de redefinição.
                </p>
              </div>

              <Button asChild className="w-full">
                <Link to="/forgot-password">Solicitar nova redefinição</Link>
              </Button>
            </div>
          ) : (
            <ResetPasswordForm token={token} />
          )}
        </div>
      </div>
    </div>
  );
}
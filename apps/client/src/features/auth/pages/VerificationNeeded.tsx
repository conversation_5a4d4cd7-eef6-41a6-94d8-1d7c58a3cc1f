import { useState } from "react";
import { useToast } from "@/shared/hooks/use-toast";
import { useAuthQuery } from "@/features/auth/hooks";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";

export default function VerificationNeeded() {
  const { user, resendVerificationEmail } = useAuthQuery();
  const [isResending, setIsResending] = useState(false);
  const { toast } = useToast();

  const handleResendEmail = async () => {
    if (!user?.email) {
      toast({
        title: "Erro",
        description: "Não foi possível identificar seu email",
        variant: "destructive"
      });
      return;
    }

    setIsResending(true);

    try {
      resendVerificationEmail(user.email);
      toast({
        title: "Email reenviado",
        description: "Verifique sua caixa de entrada para ativar sua conta."
      });
    } catch (error) {
      toast({
        title: "Erro ao reenviar email",
        description: "Não foi possível reenviar o email. Tente novamente mais tarde.",
        variant: "destructive"
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="container mx-auto max-w-md p-6">
      <Card>
        <CardHeader>
          <CardTitle>Verificação de Email Necessária</CardTitle>
          <CardDescription>
            Sua conta precisa de verificação antes de continuar.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Enviamos um email para <strong>{user?.email}</strong> com um link de verificação.
            Verifique sua caixa de entrada e spam.
          </p>
          <Button
            onClick={handleResendEmail}
            disabled={isResending}
            className="w-full"
          >
            {isResending ? "Reenviando..." : "Reenviar Email de Verificação"}
          </Button>
        </CardContent>
        <CardFooter>
          <Button variant="outline" onClick={() => window.location.reload()} className="w-full">
            Já verifiquei meu email
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
// src/features/auth/api/user.api.ts
import { axiosInstance } from '@/shared/lib/api.client';
import {UpdateUserData, User} from '@/features/auth/types/user.schema';
export const userApi = {
  /**
   * Busca o perfil do usuário autenticado
   */
  getUser: async (): Promise<User> => {
    const response = await axiosInstance.get('/protected/user/profile');
    return response.data;
  },

  /**
   * Atualiza o perfil do usuário
   */
  updateUser: async (data: UpdateUserData): Promise<User> => {
    const response = await axiosInstance.put('/protected/user/profile', data);
    return response.data;
  },

  /**
   * Altera a senha do usuário
   */
  changePassword: async (currentPassword: string, newPassword: string): Promise<{ message: string }> => {
    const response = await axiosInstance.post('/protected/change-password', {
      current_password: currentPassword,
      new_password: newPassword
    });
    return response.data;
  },

  /**
   * Upload de imagem de perfil
   */
  uploadUserPicture: async (file: File): Promise<{ profile_picture: string }> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await axiosInstance.post('/protected/user/profile-picture', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    return response.data;
  },

  /**
   * Verifica o email do usuário com o token enviado
   */
  verifyEmail: async (token: string): Promise<{ message: string }> => {
    const response = await axiosInstance.get(`/auth/verify-email/${token}`);
    return response.data;
  },

  /**
   * Solicita reenvio de email de verificação
   */
  resendVerificationEmail: async (email: string): Promise<{ message: string }> => {
    const response = await axiosInstance.post('/auth/resend-verification-email', { email });
    return response.data;
  }
};
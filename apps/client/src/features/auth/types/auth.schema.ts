import { z } from 'zod';

// Schema de validação para login
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, { message: "E-mail é obrigatório" })
    .email({ message: "E-mail inválido" }),
  password: z
    .string()
    .min(1, { message: "Senha é obrigatória" })
});

export type LoginFormData = z.infer<typeof loginSchema>;

// Schema de validação para registro
export const registerSchema = z.object({
  first_name: z.string().min(2, { message: "Nome deve ter pelo menos 2 caracteres" }),
  last_name: z.string().min(2, { message: "Sobrenome deve ter pelo menos 2 caracteres" }),
  email: z.string().min(1, { message: "E-mail é obrigatório" }).email({ message: "E-mail inválido" }),
  occupation: z.string().optional(),
  password: z.string().min(8, { message: "Senha deve ter pelo menos 8 caracteres" })
    .regex(/[A-Z]/, { message: "<PERSON>ha deve conter pelo menos uma letra maiúscula" })
    .regex(/[a-z]/, { message: "<PERSON>ha deve conter pelo menos uma letra minúscula" })
    .regex(/[0-9]/, { message: "Senha deve conter pelo menos um número" })
    .regex(/[^A-Za-z0-9]/, { message: "Senha deve conter pelo menos um caractere especial" }),
  confirmPassword: z.string().min(1, { message: "Confirmação de senha é obrigatória" })
});

// Adiciona validação de confirmação de senha
export const registerSchemaWithConfirmation = registerSchema.refine(
  (data) => data.password === data.confirmPassword,
  {
    message: "As senhas não coincidem",
    path: ["confirmPassword"],
  }
);

export type RegisterFormData = z.infer<typeof registerSchemaWithConfirmation>;

// Schema para esqueci minha senha
export const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, { message: "E-mail é obrigatório" })
    .email({ message: "E-mail inválido" })
});

export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

// Schema para redefinição de senha
export const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(8, { message: "Senha deve ter pelo menos 8 caracteres" })
    .regex(/[A-Z]/, { message: "Senha deve conter pelo menos uma letra maiúscula" })
    .regex(/[a-z]/, { message: "Senha deve conter pelo menos uma letra minúscula" })
    .regex(/[0-9]/, { message: "Senha deve conter pelo menos um número" })
    .regex(/[^A-Za-z0-9]/, { message: "Senha deve conter pelo menos um caractere especial" }),
  confirmPassword: z
    .string()
    .min(1, { message: "Confirmação de senha é obrigatória" })
}).refine(
  (data) => data.password === data.confirmPassword,
  {
    message: "As senhas não coincidem",
    path: ["confirmPassword"],
  }
);

export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

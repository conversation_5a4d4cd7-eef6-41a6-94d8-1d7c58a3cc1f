import { z } from 'zod';

export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  first_name: z.string(),
  last_name: z.string(),
  date_of_birth: z.string().optional(),
  cpf: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  occupation: z.string().optional(),
  phone: z.string().optional(),
  is_verified: z.boolean().optional(),
  profile_picture: z.string().optional(),
  // Outros campos que podem ser retornados pela API
});

export const updateUserSchema = z.object({
  email: z.string().email().optional(),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
  date_of_birth: z.string().optional(),
  cpf: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  occupation: z.string().optional(),
  phone: z.string().optional(),

  profile_picture: z.string().optional(),
});

export type UpdateUserData = z.infer<typeof updateUserSchema>;
export type User = z.infer<typeof userSchema>;
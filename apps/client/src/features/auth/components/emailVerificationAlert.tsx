// src/components/auth/emailVerificationAlert.tsx
import { useState } from 'react';
import { Alert, AlertTitle, AlertDescription } from '@/shared/ui/alert';
import { Button } from '@/shared/ui/button';
import { AlertTriangle, Loader2 } from 'lucide-react';
import { useAuthQuery } from '@/features/auth/hooks';

interface EmailVerificationAlertProps {
  email: string;
}

export function EmailVerificationAlert({ email }: EmailVerificationAlertProps) {
  const [isDismissed, setIsDismissed] = useState(false);
  const { resendVerificationEmail, isLoading } = useAuthQuery();

  if (isDismissed) {
    return null;
  }

  const handleResend = () => {
    resendVerificationEmail(email);
  };

  return (
    <Alert className="mb-6 border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-900 dark:bg-yellow-950 dark:text-yellow-300">
      <AlertTriangle className="h-5 w-5" />
      <AlertTitle>Email não verificado</AlertTitle>
      <AlertDescription className="mt-2">
        <p>
          Seu email <strong>{email}</strong> ainda não foi verificado. Algumas funcionalidades podem estar indisponíveis.
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleResend}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Reenviando...
              </>
            ) : (
              'Reenviar email de verificação'
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsDismissed(true)}
          >
            Dispensar
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
}
import React, { useState, useEffect } from 'react';
import { Input } from '@/shared/ui/input';
import { Textarea } from '@/shared/ui/textarea';
import { Checkbox } from '@/shared/ui/checkbox';
import { Label } from '@/shared/ui/label';
import { Button } from '@/shared/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/shared/ui/select';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/shared/ui/card';

interface FormField {
  type: 'text' | 'textarea' | 'checkbox' | 'select' | 'date';
  id: string;
  label: string;
  required?: boolean;
  placeholder?: string;
  options?: string[];
  defaultValue?: string | boolean;
  rows?: number;
}

interface InteractiveFormProps {
  title: string;
  content: string;
  onSubmit: (formData: Record<string, any>) => void;
  onCancel?: () => void;
}

export function InteractiveForm({ title, content, onSubmit, onCancel }: InteractiveFormProps) {
  const [fields, setFields] = useState<FormField[]>([]);
  const [formValues, setFormValues] = useState<Record<string, any>>({});

  // Extrair campos do conteúdo HTML
  useEffect(() => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    
    const extractedFields: FormField[] = [];
    
    // Extrair campos de texto
    doc.querySelectorAll('form-text-input').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `text-${extractedFields.length}`;
      extractedFields.push({
        type: 'text',
        id,
        label: element.getAttribute('data-label') || 'Campo de texto',
        required: element.getAttribute('data-required') === 'true',
        placeholder: element.getAttribute('data-placeholder') || '',
      });
    });
    
    // Extrair áreas de texto
    doc.querySelectorAll('form-textarea').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `textarea-${extractedFields.length}`;
      extractedFields.push({
        type: 'textarea',
        id,
        label: element.getAttribute('data-label') || 'Área de texto',
        required: element.getAttribute('data-required') === 'true',
        placeholder: element.getAttribute('data-placeholder') || '',
        rows: element.getAttribute('data-rows') ? parseInt(element.getAttribute('data-rows') || '3', 10) : 3,
      });
    });
    
    // Extrair checkboxes
    doc.querySelectorAll('form-checkbox').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `checkbox-${extractedFields.length}`;
      extractedFields.push({
        type: 'checkbox',
        id,
        label: element.getAttribute('data-label') || 'Opção',
        required: element.getAttribute('data-required') === 'true',
        defaultValue: element.getAttribute('data-default-checked') === 'true',
      });
    });
    
    // Extrair selects
    doc.querySelectorAll('form-select').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `select-${extractedFields.length}`;
      const optionsStr = element.getAttribute('data-options') || 'Opção 1;Opção 2;Opção 3';
      extractedFields.push({
        type: 'select',
        id,
        label: element.getAttribute('data-label') || 'Selecione uma opção',
        required: element.getAttribute('data-required') === 'true',
        options: optionsStr.split(';'),
      });
    });
    
    // Extrair campos de data
    doc.querySelectorAll('form-date-input').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `date-${extractedFields.length}`;
      extractedFields.push({
        type: 'date',
        id,
        label: element.getAttribute('data-label') || 'Data',
        required: element.getAttribute('data-required') === 'true',
      });
    });
    
    setFields(extractedFields);
    
    // Inicializar valores do formulário
    const initialValues: Record<string, any> = {};
    extractedFields.forEach((field) => {
      if (field.type === 'checkbox') {
        initialValues[field.id] = field.defaultValue || false;
      } else if (field.type === 'select' && field.options && field.options.length > 0) {
        initialValues[field.id] = field.defaultValue || field.options[0];
      } else {
        initialValues[field.id] = field.defaultValue || '';
      }
    });
    
    setFormValues(initialValues);
  }, [content]);

  // Manipular mudanças nos campos
  const handleChange = (id: string, value: any) => {
    setFormValues((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  // Manipular envio do formulário
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formValues);
  };

  // Renderizar campos do formulário
  const renderField = (field: FormField) => {
    switch (field.type) {
      case 'text':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
            />
          </div>
        );
      
      case 'textarea':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Textarea
              id={field.id}
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              rows={field.rows}
            />
          </div>
        );
      
      case 'checkbox':
        return (
          <div className="flex items-center space-x-2" key={field.id}>
            <Checkbox
              id={field.id}
              checked={formValues[field.id] || false}
              onCheckedChange={(checked) => handleChange(field.id, checked)}
              required={field.required}
            />
            <Label htmlFor={field.id} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
          </div>
        );
      
      case 'select':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Select
              value={formValues[field.id] || ''}
              onValueChange={(value) => handleChange(field.id, value)}
              required={field.required}
            >
              <SelectTrigger id={field.id}>
                <SelectValue placeholder={field.label} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );
      
      case 'date':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="date"
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              required={field.required}
            />
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {fields.map(renderField)}
        </CardContent>
        <CardFooter className="flex justify-end space-x-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancelar
            </Button>
          )}
          <Button type="submit">
            Enviar
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}

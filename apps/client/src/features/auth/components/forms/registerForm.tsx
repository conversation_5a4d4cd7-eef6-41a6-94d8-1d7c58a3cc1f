import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle } from "lucide-react";
import { Link } from "@tanstack/react-router";

import { Button } from "@/shared/ui/button";
import { Card, CardContent } from "@/shared/ui/card";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { PasswordInput } from "./passwordInput";
import { cn } from "@/shared/lib/utils";
import {
  registerSchemaWithConfirmation,
  type RegisterFormData
} from "@/features/auth/types/auth.schema";
import { useAuthQuery } from "@/features/auth/hooks";

export function RegisterForm({ className, ...props }: React.ComponentProps<"div">) {
  const { register: registerUser, isLoading } = useAuthQuery();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchemaWithConfirmation),
    mode: "onChange",
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      occupation: "",
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = (data: RegisterFormData) => {
    registerUser(data);
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card className="overflow-hidden">
        <CardContent className="p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="flex flex-col items-center text-center">
              <h1 className="text-2xl font-bold">Crie sua conta</h1>
              <p className="text-muted-foreground">
                Já possui uma conta?{" "}
                <Link to="/login" className="underline text-[hsl(var(--primary))]">
                  Entre aqui
                </Link>
              </p>
            </div>

            <div className="grid gap-4">
              {/* Nome e Sobrenome */}
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="first_name">Nome</Label>
                  <Input id="first_name" placeholder="Ex: João" {...register("first_name")} />
                  {errors.first_name && <p className="text-sm text-destructive flex items-center gap-1"><AlertCircle className="h-4 w-4" />{errors.first_name.message}</p>}
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="last_name">Sobrenome</Label>
                  <Input id="last_name" placeholder="Ex: Silva" {...register("last_name")} />
                  {errors.last_name && <p className="text-sm text-destructive flex items-center gap-1"><AlertCircle className="h-4 w-4" />{errors.last_name.message}</p>}
                </div>
              </div>

              {/* Email */}
              <div className="grid gap-2">
                <Label htmlFor="email">E-mail</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" {...register("email")} />
                {errors.email && <p className="text-sm text-destructive flex items-center gap-1"><AlertCircle className="h-4 w-4" />{errors.email.message}</p>}
              </div>

              {/* Profissão (Opcional) */}
              <div className="grid gap-2">
                <Label htmlFor="occupation">Profissão</Label>
                <Input id="occupation" placeholder="Ex: Fonoaudiólogo(a)" {...register("occupation")} />
                {errors.occupation && <p className="text-sm text-destructive flex items-center gap-1"><AlertCircle className="h-4 w-4" />{errors.occupation.message}</p>}
              </div>
              
              {/* Senha */}
              <div className="grid gap-2">
                <Label htmlFor="password">Senha</Label>
                <PasswordInput id="password" {...register("password")} />
                {errors.password && <p className="text-sm text-destructive flex items-center gap-1"><AlertCircle className="h-4 w-4" />{errors.password.message}</p>}
                <p className="text-xs text-muted-foreground">Mínimo de 8 caracteres, com letras, números e símbolos.</p>
              </div>

              {/* Confirmação de Senha */}
              <div className="grid gap-2">
                <Label htmlFor="confirmPassword">Confirme a senha</Label>
                <PasswordInput id="confirmPassword" {...register("confirmPassword")} />
                {errors.confirmPassword && <p className="text-sm text-destructive flex items-center gap-1"><AlertCircle className="h-4 w-4" />{errors.confirmPassword.message}</p>}
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Registrando..." : "Criar conta"}
            </Button>
          </form>
        </CardContent>
      </Card>

      <div className="text-center text-xs text-muted-foreground">
        Ao clicar em continuar, você concorda com nossos{" "}
        <a href="#" className="underline">Termos de Serviços</a> e{" "}
        <a href="#" className="underline">Política de Privacidade</a>.
      </div>
    </div>
  );
}


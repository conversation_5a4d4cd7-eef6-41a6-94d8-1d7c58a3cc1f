import { ChangeEvent } from "react";
import { Input } from "@/shared/ui/input";
import { cn } from "@/shared/lib/utils";

interface PhoneInputProps extends React.ComponentProps<typeof Input> {
  value: string;
  onValueChange: (value: string) => void; 
}

function formatPhoneNumber(value: string): string {
  const digits = value.replace(/\D/g, "");

  if (digits.length <= 2) {
    return `(${digits}`;
  } else if (digits.length <= 7) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2)}`;
  } else if (digits.length <= 11) {
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 7)}-${digits.slice(7)}`;
  } else {
    return `(${digits.slice(0, 2)}) ${digits.slice(2, 7)}-${digits.slice(7, 11)}`;
  }
}

export function PhoneInput({
  value,
  onValueChange,
  className,
  ...props
}: PhoneInputProps) {
  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    onValueChange(formatPhoneNumber(e.target.value));
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Input
        id="phone"
        type="tel"
        value={value}
        onChange={handleChange}
        placeholder="(00) 00000-0000"
        required
        {...props}
      />
    </div>
  );
}

import { <PERSON>O<PERSON>, Eye, Check, AlertCircle } from "lucide-react";
import { useState, forwardRef } from "react";
import { Input } from "@/shared/ui/input";
import { cn } from "@/shared/lib/utils";

// Função para validar a força da senha
function isPasswordStrong(password: string): boolean {
  const strongPasswordRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#_-])[A-Za-z\d@$!%*?&#_-]{8,}$/;
  return strongPasswordRegex.test(password);
}

// Interface para as props do componente
type PasswordInputProps = Omit<React.ComponentPropsWithoutRef<typeof Input>, "type">;

export const PasswordInput = forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, placeholder = "Senha", onChange, ...props }, ref) => {
    const [isVisible, setIsVisible] = useState<boolean>(false);
    const [internalValue, setInternalValue] = useState<string>("");

    const toggleVisibility = () => setIsVisible((prev) => !prev);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setInternalValue(e.target.value);

      // Importante: propagar o evento para o React Hook Form
      if (onChange) {
        onChange(e);
      }
    };

    const isStrong = isPasswordStrong(internalValue);

    return (
      <div className="relative">
        <Input
          className={cn(className, "pe-12")}
          placeholder={placeholder}
          type={isVisible ? "text" : "password"}
          onChange={handleChange}
          ref={ref}
          {...props}
        />
        <button
          className="absolute inset-y-0 end-9 flex w-9 items-center justify-center rounded-e-lg text-muted-foreground/80 outline-offset-2 transition-colors hover:text-foreground focus:z-10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
          type="button"
          onClick={toggleVisibility}
          aria-label={isVisible ? "Esconder senha" : "Mostrar senha"}
        >
          {isVisible ? (
            <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
          ) : (
            <Eye size={16} strokeWidth={2} aria-hidden="true" />
          )}
        </button>
        <div className="absolute inset-y-0 end-0 flex items-center pr-3">
          {isStrong ? (
            <Check size={16} className="text-green-500" aria-hidden="true" />
          ) : internalValue ? (
            <AlertCircle size={16} className="text-red-500" aria-hidden="true" />
          ) : null}
        </div>
      </div>
    );
  }
);

// Adicionar displayName para depuração
PasswordInput.displayName = "PasswordInput";
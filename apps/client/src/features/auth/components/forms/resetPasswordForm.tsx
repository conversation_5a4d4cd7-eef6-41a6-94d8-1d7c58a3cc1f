// src/components/forms/resetPasswordForm.tsx
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, Check } from "lucide-react";

import { But<PERSON> } from "@/shared/ui/button";
import { Label } from "@/shared/ui/label";
import { cn } from "@/shared/lib/utils";
import { PasswordInput } from "./passwordInput";
import { resetPasswordSchema, type ResetPasswordFormData } from "@/features/auth/types/auth.schema";
import { Link } from "@tanstack/react-router";
import { useAuthQuery } from "@/features/auth/hooks";

interface ResetPasswordFormProps extends React.ComponentProps<"div"> {
  token: string;
}

export function ResetPasswordForm({ token, className, ...props }: ResetPasswordFormProps) {
  const { resetPassword, isLoading } = useAuthQuery();
  const [isSuccess, setIsSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: ResetPasswordFormData) => {
    resetPassword(token, data);
    setIsSuccess(true);
  };

  return (
    <div className={cn("flex flex-col space-y-6", className)} {...props}>
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          Crie uma nova senha
        </h1>
        <p className="text-sm text-muted-foreground">
          Digite e confirme sua nova senha para acessar sua conta.
        </p>
      </div>

      {!isSuccess ? (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="password">Nova senha</Label>
            <PasswordInput
              id="password"
              {...register("password")}
              aria-invalid={errors.password ? "true" : "false"}
            />
            {errors.password && (
              <p className="text-sm text-destructive flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.password.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirme a nova senha</Label>
            <PasswordInput
              id="confirmPassword"
              {...register("confirmPassword")}
              aria-invalid={errors.confirmPassword ? "true" : "false"}
            />
            {errors.confirmPassword && (
              <p className="text-sm text-destructive flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.confirmPassword.message}
              </p>
            )}
          </div>

          <div className="pt-2">
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Redefinindo..." : "Redefinir senha"}
            </Button>
          </div>
        </form>
      ) : (
        <div className="rounded-md border border-green-200 bg-green-50 p-4 dark:border-green-900 dark:bg-green-950">
          <div className="flex items-center space-x-2 text-green-700 dark:text-green-300">
            <Check className="h-5 w-5" />
            <h3 className="font-medium">Senha alterada com sucesso!</h3>
          </div>
          <p className="mt-2 text-sm text-green-600 dark:text-green-400">
            Sua senha foi alterada com sucesso. Você será redirecionado para a tela de login em alguns segundos.
          </p>
        </div>
      )}

      <div className="text-center text-sm">
        <Link to="/login" className="text-primary underline underline-offset-4">
          Voltar para o login
        </Link>
      </div>
    </div>
  );
}
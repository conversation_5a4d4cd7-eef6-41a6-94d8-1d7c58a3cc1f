import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { AlertCircle, ArrowLeft } from "lucide-react";

import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { cn } from "@/shared/lib/utils";
import { forgotPasswordSchema, type ForgotPasswordFormData } from "@/features/auth/types/auth.schema";
import { Link } from "@tanstack/react-router";
import { useAuthQuery } from "@/features/auth/hooks";

export function ForgotPasswordForm({ className, ...props }: React.ComponentProps<"div">) {
  const { forgotPassword, isLoading } = useAuthQuery();
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    forgotPassword(data);
    setIsSubmitted(true);
  };

  return (
    <div className={cn("flex flex-col space-y-6", className)} {...props}>
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          Recuperação de senha
        </h1>
        <p className="text-sm text-muted-foreground">
          Digite seu e-mail para receber um link de redefinição de senha.
        </p>
      </div>

      {!isSubmitted ? (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">E-mail</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              autoComplete="email"
              autoCapitalize="none"
              autoCorrect="off"
              {...register("email")}
              aria-invalid={errors.email ? "true" : "false"}
            />
            {errors.email && (
              <p className="text-sm text-destructive flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.email.message}
              </p>
            )}
          </div>
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Enviando..." : "Enviar link de recuperação"}
          </Button>
        </form>
      ) : (
        <div className="rounded-md border border-border bg-muted p-4">
          <p className="text-sm">
            Se o e-mail informado estiver cadastrado em nossa plataforma, você receberá em instantes um link para criar uma nova senha.
          </p>
          <p className="mt-2 text-sm">
            Verifique também sua caixa de spam caso não encontre o e-mail.
          </p>
        </div>
      )}

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t border-border" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-background px-2 text-muted-foreground">
            ou
          </span>
        </div>
      </div>

      <div className="flex justify-center">
        <Button variant="outline" asChild>
          <Link to="/login" className="flex items-center space-x-2">
            <ArrowLeft className="h-4 w-4" />
            <span>Voltar para o login</span>
          </Link>
        </Button>
      </div>
    </div>
  );
}
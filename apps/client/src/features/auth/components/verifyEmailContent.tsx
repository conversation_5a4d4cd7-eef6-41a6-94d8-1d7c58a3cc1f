import { useState, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { Button } from '@/shared/ui/button';
import { Alert, AlertTitle, AlertDescription } from '@/shared/ui/alert';
import { Check<PERSON>ircle2, <PERSON>Circle, AlertTriangle, Loader2 } from 'lucide-react';
import { useToast } from '@/shared/hooks/use-toast';
import { Link } from '@tanstack/react-router';
import { useAuthQuery } from '@/features/auth/hooks';

type VerificationStatus = 'loading' | 'success' | 'error' | 'expired';

interface VerifyEmailContentProps {
  token: string;
}

export function VerifyEmailContent({ token }: VerifyEmailContentProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { authClient } = useAuthQuery();
  const {resendVerificationEmail } = useAuthQuery();

  const [status, setStatus] = useState<VerificationStatus>('loading');
  const [email, setEmail] = useState<string>('');
  const [message, setMessage] = useState<string>('');
  const [isResendingEmail, setIsResendingEmail] = useState(false);

  useEffect(() => {
    const verifyEmailToken = async () => {
      try {
        await authClient.verifyEmail(token);
        setStatus('success');
        setMessage('Email verificado com sucesso!');

        // Feedback de sucesso
        toast({
          title: 'Email verificado com sucesso',
          description: 'Sua conta foi verificada. Agora você pode acessar todas as funcionalidades.',
        });

        // Redirecionar após um curto período
        setTimeout(() => {
          navigate({ to: '/login' });
        }, 5000);
      } catch (error: any) {
        if (error.response?.data?.error?.includes('expirado')) {
          setStatus('expired');
        } else {
          setStatus('error');
        }
        setMessage(error.response?.data?.error || 'Erro ao verificar email');
      }
    };

    verifyEmailToken();
  }, [token, navigate, toast, authClient]);

  const handleResendEmail = async () => {
    if (!email) {
      toast({
        title: 'Erro',
        description: 'Por favor, informe seu email',
        variant: 'destructive',
      });
      return;
    }

    setIsResendingEmail(true);
    try {
      resendVerificationEmail(email);
    } catch (error: any) {
      toast({
        title: 'Erro',
        description: error.response?.data?.error || 'Erro ao reenviar email de verificação',
        variant: 'destructive',
      });
    } finally {
      setIsResendingEmail(false);
    }
  };

  return (
    <div className="flex flex-col space-y-6 text-center">
      <h1 className="text-2xl font-semibold tracking-tight">Verificação de Email</h1>

      {status === 'loading' && (
        <div className="flex flex-col items-center justify-center space-y-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p>Verificando seu email...</p>
        </div>
      )}

      {status === 'success' && (
        <Alert className="border-green-200 bg-green-50 text-green-800 dark:border-green-900 dark:bg-green-950 dark:text-green-300">
          <CheckCircle2 className="h-5 w-5" />
          <AlertTitle>Verificação concluída</AlertTitle>
          <AlertDescription>{message || 'Seu email foi verificado com sucesso!'}</AlertDescription>

          <div className="mt-6">
            <p className="mb-4 text-sm">Você será redirecionado para a página de login em alguns segundos.</p>
            <Button asChild className="w-full">
              <Link to="/login">Ir para o Login</Link>
            </Button>
          </div>
        </Alert>
      )}

      {status === 'error' && (
        <Alert className="border-destructive/50 bg-destructive/10 text-destructive">
          <XCircle className="h-5 w-5" />
          <AlertTitle>Erro na verificação</AlertTitle>
          <AlertDescription>{message || 'Não foi possível verificar seu email.'}</AlertDescription>

          <div className="mt-6 space-y-4">
            <Button asChild variant="outline" className="w-full">
              <Link to="/">Voltar para Home</Link>
            </Button>
            <Button asChild className="w-full">
              <Link to="/login">Ir para Login</Link>
            </Button>
          </div>
        </Alert>
      )}

      {status === 'expired' && (
        <Alert className="border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-900 dark:bg-yellow-950 dark:text-yellow-300">
          <AlertTriangle className="h-5 w-5" />
          <AlertTitle>Link expirado</AlertTitle>
          <AlertDescription>{message || 'O link de verificação expirou.'}</AlertDescription>

          <div className="mt-6 space-y-4">
            <div className="space-y-4 rounded-md border border-border p-4">
              <p className="text-sm font-medium">Reenviar email de verificação</p>
              <input
                type="email"
                placeholder="Seu email"
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              <Button
                onClick={handleResendEmail}
                disabled={isResendingEmail || !email}
                className="w-full"
              >
                {isResendingEmail ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Reenviando...
                  </>
                ) : (
                  'Reenviar email'
                )}
              </Button>
            </div>
            <Button asChild variant="outline" className="w-full">
              <Link to="/login">Voltar para Login</Link>
            </Button>
          </div>
        </Alert>
      )}
    </div>
  );
}
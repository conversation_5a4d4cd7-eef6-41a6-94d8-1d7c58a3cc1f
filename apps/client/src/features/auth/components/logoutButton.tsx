import { Button } from "@/shared/ui/button";
import { LogOut } from "lucide-react";
import { useAuthQuery } from "@/features/auth/hooks";

export function LogoutButton() {
  const { logout, isLoading } = useAuthQuery();

  return (
    <Button
      onClick={logout}
      variant="destructive"
      disabled={isLoading}
      className="flex items-center gap-2"
    >
      <LogOut size={16} />
      {isLoading ? "Saindo..." : "Sair"}
    </Button>
  );
}
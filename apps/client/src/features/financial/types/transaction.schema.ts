import { z } from 'zod';

export const transactionSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  patient_id: z.string().uuid().nullable(),
  appointment_id: z.string().uuid().nullable(),
  description: z.string(),
  amount: z.string(), // BigDecimal is serialized as a string
  type: z.enum(['income', 'expense']),
  status: z.enum(['pending', 'paid', 'overdue']),
  transaction_date: z.string().datetime(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

export type Transaction = z.infer<typeof transactionSchema>;

export const createTransactionSchema = transactionSchema.omit({
  id: true,
  user_id: true,
  created_at: true,
  updated_at: true,
});

export type CreateTransaction = z.infer<typeof createTransactionSchema>;

export const updateTransactionSchema = createTransactionSchema.partial();

export type UpdateTransaction = z.infer<typeof updateTransactionSchema>;

export const updateTransactionStatusSchema = z.object({
  status: transactionSchema.shape.status,
});

export type UpdateTransactionStatus = z.infer<typeof updateTransactionStatusSchema>; 
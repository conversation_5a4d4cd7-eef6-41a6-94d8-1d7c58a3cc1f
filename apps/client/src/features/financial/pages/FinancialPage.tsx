import { useMemo, useState } from 'react';
import {
  ArrowUpRight,
  Calendar,
  CreditCard,
  DollarSign,
  Download,
  Filter,
  FileText,
  Loader2,
  MoreVertical,
  Plus,
  Search,
  TrendingDown,
  TrendingUp,
  Users,
} from 'lucide-react';
import { Button } from '@/shared/ui/button';
import { Card, CardContent } from '@/shared/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/shared/ui/dropdown-menu';
import { Input } from '@/shared/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useGetFinancialSummary, useGetTransactions } from '../hooks/useFinancialQuery';
import { useUpdateTransactionStatus, useDeleteTransaction, useExportTransactions } from '../hooks/useFinancialMutations';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery';
import { Patient } from '@/features/patients/types/patient.schema';
import { Transaction } from '../types/transaction.schema';
import { TransactionFormDialog } from '../components/TransactionFormDialog';
import { useToast } from '@/shared/hooks/use-toast';
import { ConfirmDeleteDialog } from '@/shared/components/ConfirmDeleteDialog';
import { TransactionDetailDialog } from '../components/TransactionDetailDialog';

export function FinancialPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [transactionToDelete, setTransactionToDelete] = useState<Transaction | null>(null);
  const [transactionToEdit, setTransactionToEdit] = useState<Transaction | null>(null);
  const [viewingTransaction, setViewingTransaction] = useState<Transaction | null>(null);
  const [defaultTxType, setDefaultTxType] = useState<'income' | 'expense'>('income');
  const { toast } = useToast();

  const { data: transactions = [], isLoading: isLoadingTransactions } = useGetTransactions();
  const { data: summary, isLoading: isLoadingSummary } = useGetFinancialSummary();
  const { allPatients: patients, isLoading: isLoadingPatients } = usePatientQuery();
  const { mutate: updateStatus, isPending: isUpdatingStatus } = useUpdateTransactionStatus();
  const { mutate: deleteTransaction, isPending: isDeleting } = useDeleteTransaction();
  const { mutate: exportCsv, isPending: isExporting } = useExportTransactions();

  const isLoading = isLoadingTransactions || isLoadingPatients || isLoadingSummary;

  const patientMap = useMemo(() => {
    return new Map(patients.map((p: Patient) => [p.id, p.full_name]));
  }, [patients]);

  const filteredTransactions = useMemo(() => {
    if (!searchTerm) return transactions;
    const lowercasedFilter = searchTerm.toLowerCase();
    return transactions.filter(t => {
      const patientName = t.patient_id ? patientMap.get(t.patient_id) : '';
      return patientName?.toLowerCase().includes(lowercasedFilter) ||
      t.description.toLowerCase().includes(lowercasedFilter)
    });
  }, [transactions, searchTerm, patientMap]);

  const handleUpdateStatus = (transactionId: string, status: 'pending' | 'paid' | 'overdue') => {
    updateStatus({ id: transactionId, status });
  }

  const handleOpenChange = (isOpen: boolean) => {
    setIsFormOpen(isOpen);
    if (!isOpen) {
        setTransactionToEdit(null);
    }
  }

  const formatCurrency = (value: number | string) => {
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(numericValue);
  };

  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">Pago</span>;
      case 'pending':
        return <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">Pendente</span>;
      case 'overdue':
        return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">Atrasado</span>;
      default:
        return null;
    }
  };
  
  if (isLoading || !summary) {
    return (
      <div className="p-6 flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-sm text-muted-foreground">Carregando dados financeiros...</span>
      </div>
    );
  }

  return (
    <>
    <TransactionFormDialog 
      isOpen={isFormOpen} 
      onOpenChange={handleOpenChange}
      transactionToEdit={transactionToEdit}
      defaultType={defaultTxType}
    />
    <ConfirmDeleteDialog
        open={!!transactionToDelete}
        onOpenChange={(open) => !open && setTransactionToDelete(null)}
        title="Excluir Transação"
        description="Você tem certeza que deseja excluir esta transação?"
        itemName={transactionToDelete?.description}
        itemType="transação"
        onConfirm={() => {
            if (transactionToDelete) {
                deleteTransaction(transactionToDelete.id, {
                    onSuccess: () => setTransactionToDelete(null),
                });
            }
        }}
        isDeleting={isDeleting}
    />
    <TransactionDetailDialog 
        isOpen={!!viewingTransaction}
        onOpenChange={(open) => !open && setViewingTransaction(null)}
        transaction={viewingTransaction}
        patientName={viewingTransaction?.patient_id ? patientMap.get(viewingTransaction.patient_id) : undefined}
    />
    <div className="flex min-h-screen bg-background">
      <div className="flex-1">
        <main className="container max-w-full p-6 space-y-6">
          {/* Barra de Ações */}
          <div className="flex items-center justify-between">
            <div className="flex-1 flex items-center gap-4">
              <div className="relative w-96">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar transações, pacientes..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filtros
              </Button>
              <Button variant="outline" size="sm">
                <Calendar className="h-4 w-4 mr-2" />
                Período
              </Button>
            </div>

            <div className="flex items-center gap-3">
              <Button variant="outline" onClick={() => exportCsv({})} disabled={isExporting}>
                {isExporting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Download className="h-4 w-4 mr-2" />
                )}
                Exportar
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Nova Transação
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onSelect={() => {
                    setDefaultTxType('income');
                    setIsFormOpen(true);
                  }}>
                    Adicionar Receita
                  </DropdownMenuItem>
                  <DropdownMenuItem onSelect={() => {
                    setDefaultTxType('expense');
                    setIsFormOpen(true);
                  }}>
                    Adicionar Despesa
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Cards Resumo Financeiro */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card className="border-l-4 border-l-primary">
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Saldo</h3>
                  <DollarSign className="h-5 w-5 text-primary" />
                </div>
                <p className="text-2xl font-bold">{formatCurrency(summary.balance)}</p>
                <div className="flex items-center mt-2 text-xs text-muted-foreground">
                  <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                  <span className="ml-1">calculado do período</span>
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-green-500">
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Receitas</h3>
                  <TrendingUp className="h-5 w-5 text-green-500" />
                </div>
                <p className="text-2xl font-bold">{formatCurrency(summary.income)}</p>
                <div className="mt-2 text-xs text-muted-foreground">
                  Total pago no período
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-destructive">
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Despesas</h3>
                  <TrendingDown className="h-5 w-5 text-destructive" />
                </div>
                <p className="text-2xl font-bold">{formatCurrency(summary.expenses)}</p>
                <div className="mt-2 text-xs text-muted-foreground">
                  Total pago no período
                </div>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-yellow-500">
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Pendentes</h3>
                  <Calendar className="h-5 w-5 text-yellow-500" />
                </div>
                <p className="text-2xl font-bold">{formatCurrency(summary.pending)}</p>
                <div className="mt-2 text-xs text-muted-foreground">
                  {transactions.filter(t => t.status === 'pending').length} transações a receber
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Tabs de conteúdo */}
          <Tabs defaultValue="transactions">
            <TabsList className="mb-4">
              <TabsTrigger value="transactions" className="flex items-center">
                <CreditCard className="h-4 w-4 mr-2" />
                Transações
              </TabsTrigger>
              <TabsTrigger value="patients" className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                Pacientes
              </TabsTrigger>
              <TabsTrigger value="reports" className="flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                Relatórios
              </TabsTrigger>
            </TabsList>

            <Card>
              <CardContent className="p-6">
                {/* Conteúdo das Transações */}
                <TabsContent value="transactions" className="mt-0">
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold">Todas as Transações</h3>
                      </div>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b border-border text-muted-foreground">
                              <th className="pb-3 text-left font-medium">Paciente</th>
                              <th className="pb-3 text-left font-medium">Descrição</th>
                              <th className="pb-3 text-left font-medium">Data</th>
                              <th className="pb-3 text-left font-medium">Valor</th>
                              <th className="pb-3 text-left font-medium">Status</th>
                              <th className="pb-3 text-left font-medium">Ações</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-border">
                            {filteredTransactions.map((transaction) => (
                              <tr key={transaction.id} className="hover:bg-muted/30 transition-colors">
                                <td className="py-3">
                                  <div className="flex items-center gap-3">
                                    <div className="h-8 w-8 rounded-full bg-secondary flex items-center justify-center">
                                      <span className="text-sm font-medium text-secondary-foreground">
                                        {transaction.patient_id ? patientMap.get(transaction.patient_id)?.charAt(0) : '?'}
                                      </span>
                                    </div>
                                    <span className="font-medium text-foreground">
                                      {transaction.patient_id ? patientMap.get(transaction.patient_id) : 'N/A'}
                                    </span>
                                  </div>
                                </td>
                                <td className="py-3 text-muted-foreground">
                                  {transaction.description}
                                </td>
                                <td className="py-3 text-muted-foreground">
                                  {format(transaction.transaction_date, "dd/MM/yyyy", { locale: ptBR })}
                                </td>
                                <td className={`py-3 font-medium ${transaction.type === 'income' ? 'text-green-500' : 'text-destructive'}`}>
                                  {transaction.type === 'expense' && '- '}
                                  {formatCurrency(parseFloat(transaction.amount))}
                                </td>
                                <td className="py-3">
                                  {renderStatusBadge(transaction.status)}
                                </td>
                                <td className="py-3">
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="icon" disabled={isUpdatingStatus}>
                                        <MoreVertical className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem onSelect={() => setViewingTransaction(transaction)}>
                                        Ver detalhes
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onSelect={() => {
                                        setTransactionToEdit(transaction);
                                        setIsFormOpen(true);
                                      }}>
                                        Editar
                                      </DropdownMenuItem>
                                      {transaction.status !== 'paid' && (
                                        <DropdownMenuItem onSelect={() => handleUpdateStatus(transaction.id, 'paid')}>
                                          Marcar como pago
                                        </DropdownMenuItem>
                                      )}
                                      <DropdownMenuItem className="text-destructive" onSelect={() => setTransactionToDelete(transaction)}>
                                        Excluir
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                        {filteredTransactions.length === 0 && (
                            <div className="text-center py-12 text-muted-foreground">
                                Nenhuma transação encontrada.
                            </div>
                        )}
                      </div>
                    </div>
                </TabsContent>

                {/* Conteúdo de Pacientes */}
                <TabsContent value="patients" className="mt-0">
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <Users className="h-12 w-12 text-muted-foreground/50 mb-4" />
                    <h3 className="text-lg font-medium mb-2">Análise Financeira por Paciente</h3>
                    <p className="text-muted-foreground mb-6 max-w-sm">
                      Relatórios e análises de pagamentos por paciente estarão disponíveis em breve.
                    </p>
                  </div>
                </TabsContent>

                {/* Conteúdo de Relatórios */}
                <TabsContent value="reports" className="mt-0">
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <FileText className="h-12 w-12 text-muted-foreground/50 mb-4" />
                    <h3 className="text-lg font-medium mb-2">Relatórios Financeiros</h3>
                    <p className="text-muted-foreground mb-6 max-w-sm">
                      Aqui você poderá gerar relatórios financeiros detalhados para análise e controle.
                    </p>
                  </div>
                </TabsContent>
              </CardContent>
            </Card>
          </Tabs>
        </main>
      </div>
    </div>
    </>
  );
}

export default FinancialPage;
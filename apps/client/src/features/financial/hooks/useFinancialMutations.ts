import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  createTransaction, 
  updateTransactionStatus, 
  deleteTransaction, 
  updateTransaction,
  exportTransactions,
  TransactionFilters,
} from '../api/financial.api';
import { financialQueryKeys } from './constants';
import { useToast } from '@/shared/hooks/use-toast';
import { UpdateTransaction } from '../types/transaction.schema';

export const useCreateTransaction = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: createTransaction,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: financialQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: financialQueryKeys.all });
      toast({
        title: 'Sucesso',
        description: 'Transação criada com sucesso.',
      });
    },
    onError: (error: any) => {
      console.error('Error creating transaction:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        fullError: error
      });
      toast({
        title: 'Erro',
        description: error.response?.data?.message || error.message || 'Ocorreu um erro ao criar a transação.',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateTransactionStatus = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: updateTransactionStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: financialQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: financialQueryKeys.all });
      toast({
        title: 'Sucesso',
        description: 'Status da transação atualizado com sucesso.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro',
        description: error.message || 'Ocorreu um erro ao atualizar o status.',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateTransaction = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string, data: UpdateTransaction }) => updateTransaction({ id, data }),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: financialQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: financialQueryKeys.all });
      toast({
        title: 'Sucesso',
        description: 'Transação atualizada com sucesso.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro',
        description: error.message || 'Ocorreu um erro ao atualizar a transação.',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteTransaction = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: deleteTransaction,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: financialQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: financialQueryKeys.all });
      toast({
        title: 'Sucesso',
        description: 'Transação excluída com sucesso.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro',
        description: error.message || 'Ocorreu um erro ao excluir a transação.',
        variant: 'destructive',
      });
    },
  });
};

export const useExportTransactions = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (filters: TransactionFilters) => exportTransactions(filters),
    onSuccess: (blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'transacoes.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      toast({ 
        title: "Exportação Concluída", 
        description: "O download do seu arquivo CSV foi iniciado." 
      });
    },
    onError: (error) => {
      toast({
        title: 'Erro na Exportação',
        description: error.message || 'Não foi possível exportar os dados financeiros.',
        variant: 'destructive',
      });
    },
  });
};
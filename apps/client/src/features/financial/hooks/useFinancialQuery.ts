import { useQuery } from '@tanstack/react-query';
import { getTransactions, getFinancialSummary, TransactionFilters } from '../api/financial.api';
import { financialQueryKeys } from './constants';

export const useGetTransactions = (filters: TransactionFilters = {}) => {
  return useQuery({
    queryKey: financialQueryKeys.list(filters),
    queryFn: () => getTransactions(filters),
  });
};

export const useGetFinancialSummary = () => {
    return useQuery({
        queryKey: financialQueryKeys.all,
        queryFn: getFinancialSummary,
    });
} 
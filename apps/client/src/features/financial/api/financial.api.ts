import api from '@/shared/lib/api.client';
import {
  Transaction,
  CreateTransaction,
  UpdateTransaction,
  UpdateTransactionStatus,
  transactionSchema,
} from '../types/transaction.schema';

export type FinancialSummary = {
  balance: string;
  income: string;
  expenses: string;
  pending: string;
}

export type TransactionFilters = {
  patient_id?: string;
  start_date?: string; // ISO string date
  end_date?: string; // ISO string date
};

export const getTransactions = async (filters: TransactionFilters): Promise<Transaction[]> => {
  const params = new URLSearchParams();
  if (filters.patient_id) params.append('patient_id', filters.patient_id);
  if (filters.start_date) params.append('start_date', filters.start_date);
  if (filters.end_date) params.append('end_date', filters.end_date);

  const response = await api.get('/protected/financial', { params });
  return response.data;
};

export const getFinancialSummary = async (): Promise<FinancialSummary> => {
  const response = await api.get('/protected/financial/summary');
  return response.data;
}

export const createTransaction = async (data: CreateTransaction): Promise<Transaction> => {
  console.log('Creating transaction with data:', JSON.stringify(data, null, 2));
  const response = await api.post('/protected/financial', data);
  return response.data;
};

export const updateTransactionStatus = async ({
  id,
  status,
}: {
  id: string;
  status: UpdateTransactionStatus['status'];
}): Promise<Transaction> => {
  const response = await api.patch(`/protected/financial/${id}/status`, { status });
  return response.data;
};

export const deleteTransaction = async (id: string): Promise<void> => {
  await api.delete(`/protected/financial/${id}`);
};

export const updateTransaction = async ({ id, data }: { id: string, data: UpdateTransaction }): Promise<Transaction> => {
  const response = await api.put(`/protected/financial/${id}`, data);
  return response.data;
};

export const exportTransactions = async (filters: TransactionFilters): Promise<Blob> => {
  const params = new URLSearchParams();
  if (filters.patient_id) params.append('patient_id', filters.patient_id);
  if (filters.start_date) params.append('start_date', filters.start_date);
  if (filters.end_date) params.append('end_date', filters.end_date);

  const response = await api.get('/protected/financial/export', {
    params,
    responseType: 'blob',
  });
  return response.data;
}; 
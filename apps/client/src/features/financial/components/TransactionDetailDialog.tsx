import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
  DialogDescription,
} from '@/shared/ui/dialog';
import { Button } from '@/shared/ui/button';
import { Transaction } from '../types/transaction.schema';
import { format, parseISO, isValid } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Badge } from '@/shared/ui/badge';

interface TransactionDetailDialogProps {
  transaction: Transaction | null;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  patientName?: string;
}

export function TransactionDetailDialog({
  transaction,
  isOpen,
  onOpenChange,
  patientName,
}: TransactionDetailDialogProps) {
  if (!transaction) return null;

  const formatCurrency = (value: number | string) => {
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(numericValue);
  };
  
  const statusMap: Record<string, { label: string; className: string }> = {
    paid: { label: 'Pago', className: 'bg-green-100 text-green-800' },
    pending: { label: 'Pendente', className: 'bg-yellow-100 text-yellow-800' },
    overdue: { label: 'Atrasado', className: 'bg-red-100 text-red-800' },
  };

  const typeMap: Record<string, { label: string; className: string }> = {
      income: { label: 'Receita', className: 'text-green-600' },
      expense: { label: 'Despesa', className: 'text-red-600' }
  }

  const detailItem = (label: string, value: React.ReactNode) => (
    <div className="flex justify-between py-2 border-b border-dashed">
      <dt className="text-sm text-muted-foreground">{label}</dt>
      <dd className="text-sm font-medium text-right">{value}</dd>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Detalhes da Transação</DialogTitle>
          <DialogDescription>{transaction.description}</DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <dl>
            {detailItem('Valor', <span className={typeMap[transaction.type].className || ''}>{formatCurrency(transaction.amount)}</span>)}
            {detailItem('Tipo', <span className={typeMap[transaction.type].className || ''}>{typeMap[transaction.type]?.label || transaction.type}</span>)}
            {detailItem('Data', (() => {
              try {
                if (transaction.transaction_date && typeof transaction.transaction_date === 'string') {
                  const date = parseISO(transaction.transaction_date);
                  return isValid(date) ? format(date, 'dd/MM/yyyy', { locale: ptBR }) : 'Data inválida';
                }
                return 'Data inválida';
              } catch (error) {
                console.error('Error parsing transaction date:', error);
                return 'Data inválida';
              }
            })())}
            {detailItem('Status', <Badge className={statusMap[transaction.status].className}>{statusMap[transaction.status]?.label || transaction.status}</Badge>)}
            {transaction.patient_id && detailItem('Paciente', patientName ?? 'N/A')}
            {detailItem('ID da Transação', <span className="text-xs font-mono">{transaction.id}</span>)}
          </dl>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => { onOpenChange(false); }}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@/shared/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shared/ui/form';
import { Input } from '@/shared/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/ui/select';
import { CreateTransaction, createTransactionSchema, Transaction, UpdateTransaction } from '../types/transaction.schema';
import { useCreateTransaction, useUpdateTransaction } from '../hooks/useFinancialMutations';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery';
import { Patient } from '@/features/patients/types/patient.schema';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { Calendar } from '@/shared/ui/calendar';
import { cn } from '@/shared/lib/utils';
import { format, parseISO, isValid } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useEffect } from 'react';
import { z } from 'zod';
import { CurrencyInput } from '@/shared/components/CurrencyInput';

// Schema específico para o formulário, onde transaction_date é Date
const formTransactionSchema = createTransactionSchema.extend({
  transaction_date: z.date(),
});

type FormTransactionData = z.infer<typeof formTransactionSchema>;

interface TransactionFormDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  transactionToEdit?: Transaction | null;
  defaultType?: 'income' | 'expense';
}

export function TransactionFormDialog({ 
  isOpen, 
  onOpenChange, 
  transactionToEdit,
  defaultType = 'income'
}: TransactionFormDialogProps) {
  const { mutate: createTransaction, isPending: isCreating } = useCreateTransaction();
  const { mutate: updateTransaction, isPending: isUpdating } = useUpdateTransaction();
  const { allPatients: patients = [] } = usePatientQuery();

  const isEditing = !!transactionToEdit;
  const isPending = isCreating || isUpdating;

  const form = useForm<FormTransactionData>({
    resolver: zodResolver(formTransactionSchema),
  });

  useEffect(() => {
    if (isOpen) {
      if (isEditing && transactionToEdit) {
        // Safely parse the transaction date
        let transactionDate: Date;
        try {
          if (transactionToEdit.transaction_date && typeof transactionToEdit.transaction_date === 'string') {
            transactionDate = parseISO(transactionToEdit.transaction_date);
            if (!isValid(transactionDate)) {
              transactionDate = new Date();
            }
          } else {
            transactionDate = new Date();
          }
        } catch (error) {
          console.error('Error parsing transaction date:', error);
          transactionDate = new Date();
        }

        form.reset({
          ...transactionToEdit,
          amount: String(transactionToEdit.amount), // Ensure amount is string for the form
          patient_id: transactionToEdit.patient_id ?? 'none',
          transaction_date: transactionDate, // Convert string to Date for the form
        });
      } else {
        form.reset({
          patient_id: 'none',
          appointment_id: null,
          description: '',
          amount: '',
          type: defaultType,
          status: 'pending',
          transaction_date: new Date(),
        });
      }
    }
  }, [isOpen, isEditing, transactionToEdit, form, defaultType]);

  const onSubmit = (values: FormTransactionData) => {
    // Formatar a data sem milissegundos e sem o 'Z'
    const formattedDate = values.transaction_date.toISOString().replace(/\.\d{3}Z$/, '');
    
    const dataToSend: CreateTransaction = {
      ...values,
      amount: String(values.amount),
      transaction_date: formattedDate,
      // Garantir que patient_id seja null se não houver valor ou se for "none"
      patient_id: values.patient_id && values.patient_id !== 'none' ? values.patient_id : null,
    };

    if (isEditing && transactionToEdit) {
      updateTransaction({ id: transactionToEdit.id, data: dataToSend }, {
        onSuccess: () => {
          onOpenChange(false);
        },
      });
    } else {
      createTransaction(dataToSend, {
        onSuccess: () => {
          onOpenChange(false);
        },
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[480px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Editar Transação' : 'Nova Transação'}</DialogTitle>
          <DialogDescription>
            {isEditing ? 'Altere os detalhes da transação.' : 'Adicione uma nova transação manual. Campos marcados com * são obrigatórios.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="income">Receita</SelectItem>
                        <SelectItem value="expense">Despesa</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="pending">Pendente</SelectItem>
                        <SelectItem value="paid">Pago</SelectItem>
                        <SelectItem value="overdue">Atrasado</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição *</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Sessão de Terapia" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-2 gap-4">
               <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Valor *</FormLabel>
                    <FormControl>
                      <CurrencyInput 
                        value={field.value} 
                        onChange={field.onChange} 
                        placeholder="0,00" 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="transaction_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col pt-2">
                    <FormLabel>Data da Transação *</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value && isValid(field.value) ? (
                              format(field.value, "PPP", { locale: ptBR })
                            ) : (
                              <span>Selecione uma data</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="patient_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Paciente</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value || ""}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione um paciente (opcional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">Nenhum</SelectItem>
                      {patients.map((patient: Patient) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {patient.full_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => { onOpenChange(false); }}
                disabled={isPending}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending && (
                  <span className="mr-1">
                    <svg
                      className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </span>
                )}
                {isEditing ? "Atualizar" : "Criar"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

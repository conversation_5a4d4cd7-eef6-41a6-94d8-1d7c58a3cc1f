import { QueryClient } from '@tanstack/react-query';
import { Appointment } from '@/features/calendar/types/appointment.schema';
import { APPOINTMENT_QUERY_KEYS } from './constants';
import { parseISO } from 'date-fns';

export function updateAppointmentInCache(
  queryClient: QueryClient,
  queryKey: unknown[],
  updatedAppointment: Appointment
) {
  queryClient.setQueryData<Appointment[]>(queryKey, (old = []) => {
    if (!old || !old.length) return old;

    const appointmentExists = old.some(apt => apt.id === updatedAppointment.id);

    if (appointmentExists) {
      return old.map(apt => apt.id === updatedAppointment.id ? updatedAppointment : apt);
    }

    return old;
  });
}

export function updateAppointmentInAllCaches(
  queryClient: QueryClient,
  updatedAppointment: Appointment,
  currentQueryKey: unknown[]
) {
  queryClient.setQueryData<Appointment[]>(currentQuery<PERSON>ey, (old = []) =>
    old?.map(apt => apt.id === updatedAppointment.id ? updatedAppointment : apt) || []
  );

  queryClient.setQueryData(
    APPOINTMENT_QUERY_KEYS.detail(updatedAppointment.id),
    updatedAppointment
  );

  updateAppointmentInCache(queryClient, [...APPOINTMENT_QUERY_KEYS.all], updatedAppointment);

  if (updatedAppointment.patient_id) {
    updateAppointmentInCache(
      queryClient,
      [...APPOINTMENT_QUERY_KEYS.byPatient(updatedAppointment.patient_id)],
      updatedAppointment
    );
  }

  updateAppointmentInCache(queryClient, [...APPOINTMENT_QUERY_KEYS.today], updatedAppointment);

  queryClient.invalidateQueries({
    predicate: (query) => {
      const queryKey = query.queryKey;
      return Array.isArray(queryKey) &&
             queryKey[0] === 'appointments' &&
             (queryKey[1] === 'range' || queryKey[1] === 'upcoming');
    }
  });

  const oldAppointment = queryClient.getQueryData<Appointment>(
    APPOINTMENT_QUERY_KEYS.detail(updatedAppointment.id)
  );

  if (oldAppointment) {
    const oldStartTime = parseISO(oldAppointment.start_time);
    const newStartTime = parseISO(updatedAppointment.start_time);
    const dateChanged = oldStartTime.toDateString() !== newStartTime.toDateString();

    if (dateChanged) {
      queryClient.invalidateQueries({ queryKey: APPOINTMENT_QUERY_KEYS.today });
      queryClient.invalidateQueries({
        predicate: query => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) &&
                queryKey[0] === 'appointments' &&
                (queryKey[1] === 'range' || queryKey[1] === 'upcoming');
        }
      });
    }
  }
}
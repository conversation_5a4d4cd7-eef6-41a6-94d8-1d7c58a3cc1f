import { CalendarAppointment } from '@/features/calendar/utils/calendar';
import { isBefore, isAfter, isSameDay } from 'date-fns';

export function useAppointmentFilters(appointments: CalendarAppointment[] = []) {
  // Funções auxiliares
  const getAppointmentsForDay = (date: Date) => {
    return appointments.filter(apt => isSameDay(apt.start, date));
  };

  // Função para buscar compromissos com filtros avançados
  const searchAppointments = (filters: {
    patient_id?: string;
    patient_name?: string;
    type?: string;
    status?: string;
    dateFrom?: Date;
    dateTo?: Date;
  }) => {
    return appointments.filter(apt => {
      if (filters.patient_id && apt.patient_id !== filters.patient_id) return false;
      if (filters.patient_name && !apt.patient_name?.toLowerCase().includes(filters.patient_name.toLowerCase())) return false;
      // As propriedades 'type' e 'status' não existem no schema, devem ser adicionadas se necessário
      // if (filters.type && apt.type !== filters.type) return false;
      // if (filters.status && apt.status !== filters.status) return false;
      if (filters.dateFrom && apt.start < filters.dateFrom) return false;
      return !(filters.dateTo && apt.start > filters.dateTo);
    });
  };

  // Categorizar compromissos passados, atuais e futuros
  const now = new Date();
  
  const pastAppointments = appointments.filter(apt => isBefore(apt.end, now));
  const upcomingAppointments = appointments.filter(apt => isAfter(apt.start, now));
  const currentAppointments = appointments.filter(apt =>
    isBefore(apt.start, now) && isAfter(apt.end, now)
  );

  return {
    getAppointmentsForDay,
    searchAppointments,
    pastAppointments,
    upcomingAppointments,
    currentAppointments
  };
}

import { useAppointmentList } from './useAppointmentList';
import { useAppointmentMutations } from './useAppointmentMutations';
import { useAppointmentFilters } from './useAppointmentFilters';
import { useMemo } from 'react';
import { Appointment } from '../types/appointment.schema';
import { CalendarAppointment } from '../utils/calendar';
import { parseISO } from 'date-fns';

export { APPOINTMENT_QUERY_KEYS } from './constants';

export function useAppointmentQuery(options?: {
  initialDate?: Date;
  viewMode?: 'day' | 'week' | 'month';
  patientId?: string;
  status?: string;
}) {
  const {
    appointments,
    isLoading,
    isError,
    error,
    refetch,
    queryKey
  } = useAppointmentList(options);

  const processedAppointments: CalendarAppointment[] = useMemo(() => {
    if (!appointments) return [];
    return appointments.map((apt) => ({
      ...apt,
      start: parseISO(apt.start_time),
      end: parseISO(apt.end_time),
    }));
  }, [appointments]);

  const mutations = useAppointmentMutations();
  const filters = useAppointmentFilters(processedAppointments);
  
  const getAppointmentById = useMemo(() => {
    return (id: string): Appointment | undefined => {
      return appointments.find(a => a.id === id);
    }
  }, [appointments]);


  return {
    appointments: processedAppointments,
    isLoading,
    isError,
    error,
    refetch,
    getAppointmentById,
    ...mutations,
    ...filters,
  };
} 
import { useQuery } from '@tanstack/react-query';
import { endOfMonth, endOfWeek, startOfMonth, startOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { appointmentApi } from '@/features/calendar/api/appointment.api';
import { APPOINTMENT_QUERY_KEYS } from './constants';
import { Appointment } from '../types/appointment.schema';

type ViewMode = 'day' | 'week' | 'month';

interface UseAppointmentListOptions {
  initialDate?: Date;
  viewMode?: ViewMode;
  patientId?: string;
  status?: string;
}

export function useAppointmentList(options?: UseAppointmentListOptions) {
  const currentDate = options?.initialDate || new Date();

  const getDateRange = (date: Date, viewMode = options?.viewMode || 'month') => {
    switch (viewMode) {
      case 'day':
        return { start: date, end: date };
      case 'week':
        return {
          start: startOfWeek(date, { locale: ptBR, weekStartsOn: 1 }),
          end: endOfWeek(date, { locale: ptBR, weekStartsOn: 1 })
        };
      case 'month':
      default:
        return {
          start: startOfMonth(date),
          end: endOfMonth(date)
        };
    }
  };

  const { start, end } = getDateRange(currentDate);

  const getQueryKey = () => {
    if (options?.patientId) {
      return APPOINTMENT_QUERY_KEYS.byPatient(options.patientId);
    }
    if (options?.status) {
      return APPOINTMENT_QUERY_KEYS.byStatus(options.status);
    }
    return APPOINTMENT_QUERY_KEYS.range(start.toISOString(), end.toISOString());
  };

  const queryKey = getQueryKey();

  const appointmentsQuery = useQuery({
    queryKey,
    queryFn: async (): Promise<Appointment[]> => {
      let params: {
        patient_id?: string;
        status?: string;
        start?: string;
        end?: string;
      } = {
        start: start.toISOString(),
        end: end.toISOString()
      };
      
      if (options?.patientId) {
        params = { patient_id: options.patientId };
      } else if (options?.status) {
        params = { status: options.status };
      }

      return appointmentApi.getAppointments(params);
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: false
  });

  return {
    appointments: appointmentsQuery.data || [],
    isLoading: appointmentsQuery.isLoading,
    isError: appointmentsQuery.isError,
    error: appointmentsQuery.error,
    refetch: appointmentsQuery.refetch,
    queryKey
  };
}


import { useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '@/shared/lib/api.client';
import { useToast } from '@/shared/hooks/use-toast';
import { Appointment, CreateAppointmentData, UpdateAppointmentData, appointmentSchema } from '@/features/calendar/types/appointment.schema';
import { RecurrenceModifyMode } from '@/features/calendar/types/page';
import { appointmentApi } from '@/features/calendar/api/appointment.api';
import { APPOINTMENT_QUERY_KEYS } from './constants';
import { QueryKey } from '@tanstack/react-query';

export function useAppointmentMutations() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const createAppointmentMutation = useMutation({
    mutationFn: async (data: CreateAppointmentData): Promise<Appointment> => {
      const response = await axiosInstance.post('/protected/appointments', data);
      const parsedData = appointmentSchema.safeParse(response.data);
      if (!parsedData.success) {
        throw new Error("Invalid data from API on appointment creation.");
      }
      return parsedData.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: APPOINTMENT_QUERY_KEYS.all });
      toast({
        title: "Agendamento criado",
        description: "O agendamento foi criado com sucesso."
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao criar agendamento",
        description: error?.response?.data?.message || error.message,
        variant: "destructive"
      });
    }
  });

  const updateAppointmentMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string, data: UpdateAppointmentData }): Promise<Appointment> => {
      const response = await axiosInstance.put(`/protected/appointments/${id}`, data);
      const parsedData = appointmentSchema.safeParse(response.data);
      if (!parsedData.success) {
        throw new Error("Invalid data from API on appointment update.");
      }
      return parsedData.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: APPOINTMENT_QUERY_KEYS.all });
      queryClient.invalidateQueries({ queryKey: APPOINTMENT_QUERY_KEYS.detail(data.id) });
      toast({
        title: "Agendamento atualizado",
        description: "O agendamento foi atualizado com sucesso."
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao atualizar agendamento",
        description: error?.response?.data?.message || error.message,
        variant: "destructive"
      });
    }
  });

  const updateRecurringAppointmentMutation = useMutation({
    mutationFn: ({ id, data, updateMode }: { id: string; data: UpdateAppointmentData; updateMode: RecurrenceModifyMode }) =>
      appointmentApi.updateRecurringAppointment(id, data, updateMode),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: APPOINTMENT_QUERY_KEYS.all });
      
      // Ajustar mensagem com base no modo de atualização
      let message = "";
      if (response.updated === "single") {
        message = "O agendamento foi atualizado com sucesso.";
      } else {
        message = `${response.count || 0} agendamentos foram atualizados com sucesso.`;
      }
      
      toast({
        title: "Agendamentos atualizados",
        description: message,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao atualizar agendamentos",
        description: error?.response?.data?.message || "Ocorreu um erro.",
        variant: "destructive"
      });
    }
  });

  const deleteAppointmentMutation = useMutation({
    mutationFn: async (id: string): Promise<string> => {
      await axiosInstance.delete(`/protected/appointments/${id}`);
      return id;
    },
    onSuccess: (id) => {
      queryClient.invalidateQueries({ queryKey: APPOINTMENT_QUERY_KEYS.all });
      queryClient.removeQueries({ queryKey: APPOINTMENT_QUERY_KEYS.detail(id) });
      toast({
        title: "Agendamento removido",
        description: "O agendamento foi removido com sucesso."
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao remover agendamento",
        description: error?.response?.data?.message || error.message,
        variant: "destructive"
      });
    }
  });

  const deleteRecurringAppointmentMutation = useMutation({
    mutationFn: ({ id, deleteMode }: { id: string; deleteMode: RecurrenceModifyMode }) =>
      appointmentApi.deleteRecurringAppointment(id, deleteMode),
    onSuccess: (_, { deleteMode }) => {
      queryClient.invalidateQueries({ queryKey: APPOINTMENT_QUERY_KEYS.all });
      const messageMap = {
        'single': 'A consulta selecionada foi cancelada',
        'future': 'Esta e todas as próximas consultas da série foram canceladas',
        'all': 'Todas as consultas da série foram canceladas'
      };
      toast({
        title: "Consulta(s) cancelada(s)",
        description: messageMap[deleteMode] || "As consultas foram canceladas.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao cancelar consultas",
        description: error?.response?.data?.message || "Ocorreu um erro.",
        variant: "destructive"
      });
    }
  });

  return {
    createAppointment: createAppointmentMutation.mutate,
    updateAppointment: updateAppointmentMutation.mutate,
    updateRecurringAppointment: updateRecurringAppointmentMutation.mutate,
    deleteAppointment: deleteAppointmentMutation.mutate,
    deleteRecurringAppointment: deleteRecurringAppointmentMutation.mutate,
    isCreating: createAppointmentMutation.isPending,
    isUpdating: updateAppointmentMutation.isPending || updateRecurringAppointmentMutation.isPending,
    isDeleting: deleteAppointmentMutation.isPending || deleteRecurringAppointmentMutation.isPending,
  };
}
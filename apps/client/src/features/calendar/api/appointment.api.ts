import { axiosInstance } from '@/shared/lib/api.client';
import {
  Appointment,
  CreateAppointmentData,
  UpdateAppointmentData,
} from '@/features/calendar/types/appointment.schema';
import { RecurrenceModifyMode } from '@/features/calendar/types/page';

// A resposta da API deve corresponder diretamente ao schema Zod
type AppointmentApiResponse = Appointment;

interface GetAppointmentsParams {
  start?: string;
  end?: string;
  patient_id?: string;
  status?: string;
}

export const appointmentApi = {
  async getAppointments(params: GetAppointmentsParams = {}): Promise<AppointmentApiResponse[]> {
    const queryParams = new URLSearchParams();

    if (params.start) {
      queryParams.append('start', params.start);
    }

    if (params.end) {
      queryParams.append('end', params.end);
    }

    if (params.patient_id) {
      queryParams.append('patient_id', params.patient_id);
    }

    if (params.status) {
      queryParams.append('status', params.status);
    }

    const response = await axiosInstance.get(
      `/protected/appointments?${queryParams.toString()}`,
    );

    return response.data;
  },

  async getAppointmentById(id: string): Promise<AppointmentApiResponse> {
    const response = await axiosInstance.get(`/protected/appointments/${id}`);
    return response.data;
  },

  async getAppointmentsBySeriesId(seriesId: string): Promise<AppointmentApiResponse[]> {
    const response = await axiosInstance.get(`/protected/appointments/series/${seriesId}`);
    return response.data;
  },

  async createAppointment(data: CreateAppointmentData): Promise<AppointmentApiResponse[]> {
    const response = await axiosInstance.post('/protected/appointments', data);
    return response.data;
  },

  async updateAppointment(id: string, data: UpdateAppointmentData): Promise<AppointmentApiResponse> {
    const response = await axiosInstance.put(`/protected/appointments/${id}`, data);
    return response.data;
  },

  async updateRecurringAppointment(
    id: string,
    data: UpdateAppointmentData,
    updateMode: RecurrenceModifyMode,
  ): Promise<{ updated: 'single' | 'future' | 'all'; count?: number; appointment?: AppointmentApiResponse }> {
    const payload = {
      ...data,
      update_mode: updateMode,
    };

    const response = await axiosInstance.put(
      `/protected/appointments/${id}/recurring`,
      payload,
    );

    return response.data;
  },

  async deleteAppointment(id: string): Promise<void> {
    await axiosInstance.delete(`/protected/appointments/${id}`);
  },

  async deleteRecurringAppointment(
    id: string,
    deleteMode: RecurrenceModifyMode,
  ): Promise<void> {
    await axiosInstance.delete(`/protected/appointments/${id}`, {
      params: { delete_mode: deleteMode },
    });
  },

  async getTodayAppointments(): Promise<AppointmentApiResponse[]> {
    const response = await axiosInstance.get('/protected/appointments/today');
    return response.data;
  },

  async getUpcomingAppointments(days = 7, limit = 10): Promise<AppointmentApiResponse[]> {
    const response = await axiosInstance.get(
      `/protected/appointments/upcoming?days=${days}&limit=${limit}`,
    );
    return response.data;
  },

  async getAppointmentsSummary(): Promise<{
    today_count: number;
    upcoming_count: number;
    upcoming_list: AppointmentApiResponse[];
  }> {
    const response = await axiosInstance.get('/protected/appointments/summary');
    return response.data;
  },
};
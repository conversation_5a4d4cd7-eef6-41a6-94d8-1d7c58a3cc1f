import { z } from 'zod';

// Enumerações
export const calendarViewModes = ['day', 'week', 'month'] as const;
export type CalendarViewMode = typeof calendarViewModes[number];

export const appointmentStatusValues = [
  'scheduled',   // Agendado
  'confirmed',   // Confirmado
  'completed',   // Realizado
  'cancelled',   // Cancelado
  'rescheduled', // Remarcado
  'no_show'      // Falta
] as const;
export type AppointmentStatus = typeof appointmentStatusValues[number];

export const appointmentTypeValues = [
  'consultation',     // Consulta padrão
  'evaluation',       // Avaliação
  'therapy_session',  // Sessão de terapia
  'follow_up',        // Retorno
  'report_review'     // Revisão de relatório
] as const;
export type AppointmentType = typeof appointmentTypeValues[number];

// Enumeração para recorrência
export const recurrenceTypeValues = [
  'none',       // Não recorrente
  'daily',      // Diário
  'weekly',     // Semanal
  'biweekly',   // Quinzenal
  'monthly',    // Mensal
  'custom'      // Personalizado
] as const;
export type RecurrenceType = typeof recurrenceTypeValues[number];

// Enumeração para modos de atualização/exclusão de recorrências
export const recurrenceModifyModes = [
  'single',     // Apenas esta ocorrência
  'future',     // Esta e todas as futuras
  'all'         // Todas as ocorrências
] as const;
export type RecurrenceModifyMode = typeof recurrenceModifyModes[number];

// Schema para recorrência
export const recurrenceSchema = z.object({
  type: z.enum(recurrenceTypeValues).default('none'),
  interval: z.number().int().positive().default(1), // Para custom_{days}
  endDate: z.date().optional(),
  occurrences: z.number().int().positive().optional(), // Número de ocorrências
  seriesId: z.string().optional(), // ID da série de recorrência
});

export type AppointmentRecurrence = z.infer<typeof recurrenceSchema>;

// Schema do Appointment
export const appointmentSchema = z.object({
  id: z.string().or(z.number().transform(n => n.toString())),
  patientId: z.string(),
  patientName: z.string(),
  title: z.string(),
  start: z.date(),
  end: z.date(),
  status: z.enum(appointmentStatusValues).default('scheduled'),
  type: z.enum(appointmentTypeValues),
  notes: z.string().optional(),
  color: z.string().default('blue'),
  location: z.string().optional(),
  professional: z.string().optional(),

  // Campos para recorrência
  isRecurring: z.boolean().optional(),
  recurrence: recurrenceSchema.optional(),
  seriesId: z.string().optional(), // ID da série para eventos recorrentes
  parentId: z.string().optional(), // Para instâncias de eventos recorrentes
  isMoved: z.boolean().optional(), // Indica se a instância foi movida da posição original
});

// Tipo para os appointments do calendário
export type CalendarAppointment = z.infer<typeof appointmentSchema>;

// Schema para criação de appointment
export const createAppointmentSchema = appointmentSchema.omit({
  id: true,
  parentId: true,
  isMoved: true,
});
export type CreateAppointmentData = z.infer<typeof createAppointmentSchema>;

// Schema para atualização de appointment
export const updateAppointmentSchema = createAppointmentSchema.partial();
export type UpdateAppointmentData = z.infer<typeof updateAppointmentSchema>;

// Opções de cores para appointments
export const colorOptions = [
  { value: 'blue', label: 'Azul' },
  { value: 'green', label: 'Verde' },
  { value: 'purple', label: 'Roxo' },
  { value: 'red', label: 'Vermelho' },
  { value: 'orange', label: 'Laranja' },
  { value: 'amber', label: 'Âmbar' },
  { value: 'pink', label: 'Rosa' },
];

// Máximo de ocorrências recorrentes permitidas
export const MAX_RECURRENCE_OCCURRENCES = 50;

// Propriedades do contexto do calendário
export interface CalendarContextProps {
  // Estado
  viewMode: CalendarViewMode;
  currentDate: Date;
  appointments: CalendarAppointment[];
  selectedAppointment: CalendarAppointment | null;
  isCreatingAppointment: boolean;
  isEditingAppointment: boolean;
  hoveredDate: Date | null;

  // Ações
  setViewMode: (mode: CalendarViewMode) => void;
  setCurrentDate: (date: Date) => void;
  handleNewAppointment: (date?: Date) => void;
  handleEditAppointment: (appointment: CalendarAppointment) => void;
  handleViewAppointment: (appointment: CalendarAppointment) => void;
  setHoveredDate: (date: Date | null) => void;
  closeDialogs: () => void;

  // Utilitários
  checkForConflicts: (start: Date, end: Date, excludeId?: string) => CalendarAppointment[];
}
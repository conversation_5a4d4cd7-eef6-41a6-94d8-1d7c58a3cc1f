import { z } from 'zod';

export const appointmentSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  patient_id: z.string().uuid(),
  patient_name: z.string().optional().nullable(),
  title: z.string().min(3, "O título deve ter pelo menos 3 caracteres."),
  notes: z.string().optional().nullable(),
  start_time: z.string(),
  end_time: z.string(),
  duration_minutes: z.number(),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled', 'no_show']),
  type: z.string(),
  color: z.string().optional().nullable(),
  location: z.string().optional().nullable(),
  is_recurring: z.boolean(),
  series_id: z.string().optional().nullable(),
  recurrence_pattern: z.string().optional().nullable(),
  recurrence_end_date: z.string().optional().nullable(),
  recurrence_count: z.number().optional().nullable(),
});

export const createAppointmentSchema = z.object({
  patient_id: z.string().uuid(),
  title: z.string().min(1, "Título <PERSON> obrigatório."),
  notes: z.string().optional().nullable(),
  start_time: z.string(),
  end_time: z.string(),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled', 'no_show']).optional(),
  type: z.string().optional(),
  color: z.string().optional().nullable(),
  location: z.string().optional().nullable(),
  is_recurrent: z.boolean().optional(),
  recurrence_pattern: z.string().optional(),
  recurrence_end_date: z.string().optional(),
  occurrences: z.number().optional(),
}).refine(data => {
    // Garante que a data de término seja posterior à data de início
    return new Date(data.end_time) > new Date(data.start_time);
}, {
    message: "O horário de término deve ser posterior ao horário de início.",
    path: ["end_time"], // O erro será associado ao campo end_time
});

export const updateAppointmentSchema = z.object({
  patient_id: z.string().uuid().optional(),
  title: z.string().min(3, { message: "Título deve ter pelo menos 3 caracteres" }).optional(),
  notes: z.string().optional(),
  start_time: z.string().optional(),
  end_time: z.string().optional(),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled', 'no_show']).optional(),
});

export type Appointment = z.infer<typeof appointmentSchema>;
export type CreateAppointmentData = z.infer<typeof createAppointmentSchema>;
export type UpdateAppointmentData = z.infer<typeof updateAppointmentSchema>;
import { create } from 'zustand';
import { Appointment } from '@/features/calendar/types/appointment.schema';
import { CalendarViewMode } from '../types/page';

interface CalendarState {
  // Estado
  viewMode: CalendarViewMode;
  currentDate: Date;
  selectedAppointment: Appointment | null;
  isCreatingAppointment: boolean;
  isEditingAppointment: boolean;
  hoveredDate: Date | null;
  
  // Ações
  setViewMode: (mode: CalendarViewMode) => void;
  setCurrentDate: (date: Date) => void;
  setHoveredDate: (date: Date | null) => void;
  selectAppointment: (appointment: Appointment) => void;
  startCreatingAppointment: (date?: Date) => void;
  startEditingAppointment: (appointment: Appointment) => void;
  closeDialogs: () => void;
}

export const useCalendarStore = create<CalendarState>((set) => ({
  // Estado inicial
  viewMode: 'month',
  currentDate: new Date(),
  selectedAppointment: null,
  isCreatingAppointment: false,
  isEditingAppointment: false,
  hoveredDate: null,
  
  // Ações
  setViewMode: (mode) => set({ viewMode: mode }),
  
  setCurrentDate: (date) => set({ currentDate: date }),
  
  setHoveredDate: (date) => set({ hoveredDate: date }),
  
  selectAppointment: (appointment) => set({ 
    selectedAppointment: appointment,
    isCreatingAppointment: false,
    isEditingAppointment: false
  }),
  
  startCreatingAppointment: (date) => set((state) => ({ 
    selectedAppointment: null,
    isCreatingAppointment: true,
    isEditingAppointment: false,
    currentDate: date || state.currentDate
  })),
  
  startEditingAppointment: (appointment) => set({ 
    selectedAppointment: appointment,
    isCreatingAppointment: false,
    isEditingAppointment: true
  }),
  
  closeDialogs: () => set({ 
    selectedAppointment: null,
    isCreatingAppointment: false,
    isEditingAppointment: false
  })
}));

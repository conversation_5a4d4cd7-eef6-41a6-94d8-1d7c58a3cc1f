import { useState, useEffect } from "react";
import { useCalendarStore } from "@/features/calendar/stores/useCalendarStore";
import { Calendar } from "@/features/calendar/components/Calendar";
import { useAuthQuery } from "@/features/auth/hooks";
import { Loader2 } from "lucide-react";

export default function CalendarPage() {
  const { user, isLoading } = useAuthQuery();
  const { setCurrentDate, setViewMode, closeDialogs } = useCalendarStore();
  const [initialDate] = useState(new Date());

  useEffect(() => {
    setCurrentDate(initialDate);
    setViewMode('month');

    return () => {
      closeDialogs();
    };
  }, [initialDate, setCurrentDate, setViewMode, closeDialogs]);

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-sm text-muted-foreground">Carregando...</span>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6 text-sm text-muted-foreground">
        Não foi possível carregar os dados do usuário.
      </div>
    );
  }

  return (
    <div className="container max-w-full p-6 h-[calc(100vh-var(--navbar-height)-theme(spacing.12))]">
      <Calendar />
    </div>
  );
}
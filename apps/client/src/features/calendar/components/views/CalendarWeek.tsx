import { useEffect, useMemo, useRef, useState } from 'react';
import { useCalendarStore } from '@/features/calendar/stores/useCalendarStore';
import { useAppointmentQuery } from '@/features/calendar/hooks/useAppointmentQuery';
import {
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  isSameDay,
  format,
  differenceInMinutes,
  parseISO,
} from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/shared/lib/utils';
import {
  organizeOverlappingAppointments,
  calculateAppointmentViewPosition,
} from '@/features/calendar/utils/calendar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/shared/ui/tooltip';
import { Appointment } from '@/features/calendar/types/appointment.schema';
import { CalendarAppointment } from '@/features/calendar/utils/calendar';

// O tipo de appointment processado para a UI
type ProcessedAppointment = CalendarAppointment & {
  color: string;
};

// Horários para exibição - 24 horas
const HOURS = Array.from({ length: 24 }, (_, i) => i);
const HOUR_HEIGHT = 80; // Altura em pixels de cada hora

export function CalendarWeek() {
  const { currentDate, selectAppointment, startCreatingAppointment } =
    useCalendarStore();

  const { appointments } = useAppointmentQuery({
    initialDate: currentDate,
    viewMode: 'week',
  });

  const [now, setNow] = useState(new Date());
  const containerRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);

  const processedAppointments: ProcessedAppointment[] = useMemo(() => {
    if (!appointments) return [];
    return appointments.map((apt) => ({
      ...apt,
      start: parseISO(apt.start_time),
      end: parseISO(apt.end_time),
      color: 'blue',
    }));
  }, [appointments]);

  const weekDays = useMemo(() => {
    const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 });
    return eachDayOfInterval({ start: weekStart, end: weekEnd });
  }, [currentDate]);

  const today = new Date();

  useEffect(() => {
    const timer = setInterval(() => setNow(new Date()), 60000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      const currentHour = now.getHours();
      const scrollPosition = currentHour * HOUR_HEIGHT;
      containerRef.current.scrollTop = scrollPosition - 100;
    }
  }, [now]);

  const formatDayTitle = (date: Date) => {
    return format(date, 'EEE, dd', { locale: ptBR });
  };

  const timeIndicatorPosition = useMemo(() => {
    const totalMinutes = 24 * 60;
    const currentMinutes = now.getHours() * 60 + now.getMinutes();
    return (currentMinutes / totalMinutes) * 100;
  }, [now]);

  return (
    <div className="flex flex-col h-full">
      <div className="grid grid-cols-[60px_repeat(7,1fr)] border-b">
        <div className="bg-card border-r p-2"></div>
        {weekDays.map((day) => (
          <div
            key={day.toISOString()}
            className={cn("bg-card border-r p-2 text-center", isSameDay(day, today) && "bg-accent/20 font-bold")}
          >
            <div className="text-sm">{formatDayTitle(day)}</div>
          </div>
        ))}
      </div>

      <div ref={containerRef} className="flex-grow overflow-y-auto">
        <div className="grid grid-cols-[60px_repeat(7,1fr)] relative">
          <div className="border-r bg-card z-10">
            {HOURS.map((hour) => (
              <div key={hour} className="h-20 border-b px-2 py-1 text-xs text-right">
                {hour}:00
              </div>
            ))}
          </div>

          {timeIndicatorPosition > 0 && (
            <div
              ref={timelineRef}
              className="absolute left-[60px] right-0 z-20 pointer-events-none"
              style={{ top: `${timeIndicatorPosition}%` }}
            >
              <div className="relative">
                <div className="absolute left-0 right-0 h-0.5 bg-red-500"></div>
                <div className="absolute -left-1.5 -top-1.5 h-3 w-3 rounded-full bg-red-500"></div>
              </div>
            </div>
          )}

          {weekDays.map((day) => {
            const isCurrentDay = isSameDay(day, today);
            const dayAppointments = processedAppointments.filter((apt) => isSameDay(apt.start, day));
            const organizedAppointments = organizeOverlappingAppointments(dayAppointments);
            
            const dayStart = startOfWeek(day, { weekStartsOn: 1 });
            const dayEnd = endOfWeek(day, { weekStartsOn: 1 });

            return (
              <div
                key={day.toISOString()}
                className={cn("border-r relative", isCurrentDay && "bg-accent/10")}
                onClick={(e) => {
                  // Calcular a hora com base na posição do clique
                  const rect = e.currentTarget.getBoundingClientRect();
                  const offsetY = e.clientY - rect.top;
                  const percentage = offsetY / rect.height;
                  const totalMinutesInDay = 24 * 60;
                  const minutesSinceMidnight = Math.floor(percentage * totalMinutesInDay);
                  const hours = Math.floor(minutesSinceMidnight / 60);
                  const minutes = Math.round(minutesSinceMidnight % 60 / 15) * 15; // Arredondar para intervalos de 15min
                  
                  // Criar um novo objeto Date com a data do dia e a hora calculada
                  const clickedDate = new Date(day);
                  clickedDate.setHours(hours, minutes, 0, 0);
                  
                  // Iniciar o agendamento com a data/hora calculada
                  startCreatingAppointment(clickedDate);
                }}
              >
                {HOURS.map((hour) => (
                  <div key={hour} className={cn("h-20 border-b hover:bg-muted/20 transition-colors relative", isCurrentDay && now.getHours() === hour && "bg-accent/20")}>
                    <div className="absolute top-1/2 left-0 right-0 border-t border-dashed border-border/50"></div>
                  </div>
                ))}

                <TooltipProvider>
                  {organizedAppointments.map(
                    ({ appointment, column, totalColumns }) => {
                      const position = calculateAppointmentViewPosition(appointment, dayStart, dayEnd, column, totalColumns);
                      const durationMins = differenceInMinutes(appointment.end, appointment.start);
                      const durationText = durationMins >= 60 ? `${Math.floor(durationMins / 60)}h${durationMins % 60 ? ` ${durationMins % 60}min` : ''}` : `${durationMins}min`;

                      return (
                        <Tooltip key={appointment.id}>
                          <TooltipTrigger asChild>
                            <div
                              className={cn("absolute rounded-md p-1.5 overflow-hidden cursor-pointer shadow-sm transition-all hover:shadow-md z-10", `bg-${appointment.color}-100 dark:bg-${appointment.color}-900/30 border-l-2 border-${appointment.color}-500`)}
                              style={{ top: position.top, height: position.height, left: position.left, width: position.width, }}
                              onClick={(e) => { e.stopPropagation(); selectAppointment(appointment); }}
                            >
                              <div className="flex flex-col h-full text-xs">
                                <div className="font-medium truncate">
                                  {format(appointment.start, 'HH:mm')} - {appointment.title}
                                </div>
                                {parseInt(position.height) > 8 && (
                                  <>
                                    <div className="truncate">{appointment.patient_name}</div>
                                    <div className="text-xs text-muted-foreground mt-auto">{durationText}</div>
                                  </>
                                )}
                              </div>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="space-y-1">
                              <p className="font-bold">{appointment.title}</p>
                              <p>{appointment.patient_name}</p>
                              <p className="text-xs">
                                {format(appointment.start, 'HH:mm')} - {format(appointment.end, 'HH:mm')} ({durationText})
                              </p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      );
                    }
                  )}
                </TooltipProvider>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

import { useEffect, useMemo, useState } from 'react';
import { useCalendarStore } from '@/features/calendar/stores/useCalendarStore';
import { useAppointmentQuery } from '@/features/calendar/hooks/useAppointmentQuery';
import {
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  format,
  parseISO,
} from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/shared/lib/utils';
import { Button } from '@/shared/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/shared/ui/dialog';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { Calendar, Repeat } from 'lucide-react';
import { getStatusLabel } from '@/features/calendar/utils/calendar';
import { Badge } from '@/shared/ui/badge';
import { Appointment } from '@/features/calendar/types/appointment.schema';
import { CalendarAppointment } from '@/features/calendar/utils/calendar';

// O tipo de appointment processado para a UI
type ProcessedAppointment = CalendarAppointment & {
  color: string;
  isRecurring: boolean;
  seriesId: string | null;
  type: string;
  status: string;
  location: string;
};

export function CalendarMonth() {
  const {
    currentDate,
    hoveredDate,
    setHoveredDate,
    selectAppointment,
    startCreatingAppointment,
  } = useCalendarStore();

  const { appointments } = useAppointmentQuery({
    initialDate: currentDate,
    viewMode: 'month',
  });

  const [showMoreDate, setShowMoreDate] = useState<Date | null>(null);

  const processedAppointments: ProcessedAppointment[] = useMemo(() => {
    if (!appointments) return [];
    return appointments.map((apt) => ({
      ...apt,
      start: parseISO(apt.start_time),
      end: parseISO(apt.end_time),
      color: 'blue',
      isRecurring: false,
      seriesId: null,
      type: 'consultation',
      status: 'scheduled',
      location: '',
    }));
  }, [appointments]);

  const calendarDays = useMemo(() => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const calendarStart = startOfWeek(monthStart, { weekStartsOn: 1 });
    const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 1 });
    return eachDayOfInterval({ start: calendarStart, end: calendarEnd });
  }, [currentDate]);

  const weekDays = ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'];
  const today = new Date();

  const appointmentsByDay = useMemo(() => {
    const byDay: Record<string, ProcessedAppointment[]> = {};
    processedAppointments.forEach((appointment) => {
      const dateKey = format(appointment.start, 'yyyy-MM-dd');
      if (!byDay[dateKey]) {
        byDay[dateKey] = [];
      }
      byDay[dateKey].push(appointment);
    });

    Object.keys(byDay).forEach((date) => {
      byDay[date].sort((a, b) => a.start.getTime() - b.start.getTime());
    });
    return byDay;
  }, [processedAppointments]);

  const MAX_VISIBLE_APPOINTMENTS = 3;

  const getAppointmentsForShowMoreDialog = () => {
    if (!showMoreDate) return [];
    const dateKey = format(showMoreDate, 'yyyy-MM-dd');
    return appointmentsByDay[dateKey] || [];
  };

  useEffect(() => {
    setShowMoreDate(null);
  }, [currentDate]);

  return (
    <div className="flex flex-col h-full">
      <div className="grid grid-cols-7 gap-px bg-border text-center">
        {weekDays.map((day) => (
          <div key={day} className="bg-card py-2 text-sm font-medium text-muted-foreground">
            {day}
          </div>
        ))}
      </div>

      <div className="flex-grow grid grid-cols-7 grid-rows-[repeat(6,minmax(100px,1fr))] gap-px bg-border overflow-hidden">
        {calendarDays.map((day) => {
          const isCurrentMonth = isSameMonth(day, currentDate);
          const isToday = isSameDay(day, today);
          const isHovered = hoveredDate ? isSameDay(day, hoveredDate) : false;
          const dateKey = format(day, 'yyyy-MM-dd');
          const dayAppointments = appointmentsByDay[dateKey] || [];
          const hasMore = dayAppointments.length > MAX_VISIBLE_APPOINTMENTS;

          return (
            <div
              key={day.toISOString()}
              className={cn("relative min-h-[100px] bg-card transition-colors p-1", !isCurrentMonth && "bg-muted/40 text-muted-foreground", isToday && "bg-accent/20", isHovered && "bg-accent/30", "flex flex-col")}
              onClick={() => startCreatingAppointment(day)}
              onMouseEnter={() => setHoveredDate(day)}
              onMouseLeave={() => setHoveredDate(null)}
            >
              <div className="flex justify-between items-center p-1 mb-1">
                <div className="flex items-center">
                  <span className={cn("flex items-center justify-center text-sm h-7 w-7 rounded-full", isToday && "bg-primary text-primary-foreground font-bold")}>
                    {format(day, 'd')}
                  </span>
                  {isToday && (<span className="text-xs ml-1 text-primary font-medium">Hoje</span>)}
                </div>
                {isCurrentMonth && (
                  <Button size="sm" variant="ghost" className="h-6 w-6 p-0 rounded-full opacity-70 hover:opacity-100" onClick={(e) => { e.stopPropagation(); startCreatingAppointment(day); }} aria-label="Adicionar compromisso">
                    +
                  </Button>
                )}
              </div>

              <div className="flex-1 overflow-hidden space-y-1">
                {dayAppointments.slice(0, MAX_VISIBLE_APPOINTMENTS).map((appointment) => (
                  <div
                    key={appointment.id}
                    className={cn(
                      "text-xs px-2 py-1 rounded-md truncate cursor-pointer transition-colors relative",
                      `bg-${appointment.color}-100 hover:bg-${appointment.color}-200 dark:bg-${appointment.color}-900/30 dark:hover:bg-${appointment.color}-800/40 border-l-2 border-${appointment.color}-500`,
                      appointment.status === 'completed' && 'opacity-75',
                      appointment.status === 'cancelled' && 'opacity-50 line-through'
                    )}
                    onClick={(e) => { e.stopPropagation(); selectAppointment(appointment); }}
                  >
                    <div className="flex items-center gap-1">
                      <span className="font-medium">{format(appointment.start, 'HH:mm')}</span>
                      <span className="truncate">{appointment.title}</span>
                      <div className="flex items-center gap-1 ml-auto">
                        {appointment.status === 'completed' && (
                          <div className="w-2 h-2 bg-green-500 rounded-full" title="Concluída" />
                        )}
                        {appointment.status === 'cancelled' && (
                          <div className="w-2 h-2 bg-red-500 rounded-full" title="Cancelada" />
                        )}
                        {(appointment.isRecurring || appointment.seriesId) && (
                          <Repeat className="h-3 w-3 flex-shrink-0" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {hasMore && (
                  <Button variant="ghost" size="sm" className="text-xs text-muted-foreground w-full justify-start py-1 h-auto font-normal" onClick={(e) => { e.stopPropagation(); setShowMoreDate(day); }}>
                    + mais {dayAppointments.length - MAX_VISIBLE_APPOINTMENTS} consultas
                  </Button>
                )}
              </div>

              {isToday && (<div className="absolute bottom-0 left-0 right-0 h-1 bg-primary rounded-b-md"></div>)}
            </div>
          );
        })}
      </div>

      <Dialog open={!!showMoreDate} onOpenChange={(open) => !open && setShowMoreDate(null)}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              {showMoreDate && format(showMoreDate, "EEEE, dd 'de' MMMM", { locale: ptBR }).replace(/^\w/, c => c.toUpperCase())}
            </DialogTitle>
          </DialogHeader>

          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-3 p-1">
              {getAppointmentsForShowMoreDialog().map((appointment) => (
                <div
                  key={appointment.id}
                  className={cn("p-3 rounded-md cursor-pointer transition-colors", `bg-${appointment.color}-50 dark:bg-${appointment.color}-900/20 hover:bg-${appointment.color}-100 dark:hover:bg-${appointment.color}-900/30 border-l-2 border-${appointment.color}-500`)}
                  onClick={() => { setShowMoreDate(null); selectAppointment(appointment); }}
                >
                  <div className="flex justify-between items-start mb-1">
                    <span className="font-medium">{appointment.title}</span>
                    <div className="flex gap-1">
                      {(appointment.isRecurring || appointment.seriesId) && (
                        <Badge variant="outline" className="text-xs flex items-center gap-1"><Repeat className="h-3 w-3" />Recorrente</Badge>
                      )}
                      <Badge variant="outline" className="text-xs">{getStatusLabel(appointment.status)}</Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <span className="font-medium">{format(appointment.start, 'HH:mm')} - {format(appointment.end, 'HH:mm')}</span>
                    </div>
                    <span>{appointment.patient_name}</span>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </div>
  );
}
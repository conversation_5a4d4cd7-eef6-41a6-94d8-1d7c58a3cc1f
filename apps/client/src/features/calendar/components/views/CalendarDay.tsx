import { useEffect, useMemo, useRef, useState } from 'react';
import { useCalendarStore } from '@/features/calendar/stores/useCalendarStore';
import { useAppointmentQuery } from '@/features/calendar/hooks/useAppointmentQuery';
import {
  isSameDay,
  format,
  differenceInMinutes,
  isBefore,
  isAfter,
  parseISO,
} from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/shared/lib/utils';
import { Card, CardContent } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Button } from '@/shared/ui/button';
import {
  organizeOverlappingAppointments,
  calculateAppointmentViewPosition,
} from '@/features/calendar/utils/calendar';
import {
  CheckCircle,
  Clock,
  MapPin,
  XCircle,
  AlertCircle,
  User,
  Calendar as CalendarIcon,
  Repeat,
} from 'lucide-react';
import { ScrollArea } from '@/shared/ui/scroll-area';
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  Too<PERSON><PERSON><PERSON><PERSON>ider,
  TooltipTrigger,
} from '@/shared/ui/tooltip';
import { Appointment } from '@/features/calendar/types/appointment.schema';
import { CalendarAppointment } from '@/features/calendar/utils/calendar';

// O tipo de appointment processado para a UI - agora usa CalendarAppointment
type ProcessedAppointment = CalendarAppointment & {
  color: string;
  isRecurring: boolean;
  seriesId: string | null;
  type: string;
  status: string;
  location: string;
};

// Horários para exibição - 24 horas
const HOURS = Array.from({ length: 24 }, (_, i) => i);
const HOUR_HEIGHT = 80; // Altura em pixels de cada hora

export function CalendarDay() {
  const { currentDate, startCreatingAppointment, selectAppointment } =
    useCalendarStore();

  const { appointments } = useAppointmentQuery({
    initialDate: currentDate,
    viewMode: 'day',
  });

  const processedAppointments: ProcessedAppointment[] = useMemo(() => {
    if (!appointments) return [];
    return appointments.map((apt) => ({
      ...apt,
      start: parseISO(apt.start_time),
      end: parseISO(apt.end_time),
      // TODO: Buscar dados reais em vez de mock
      color: 'blue',
      isRecurring: false,
      seriesId: null,
      type: 'consultation',
      status: 'scheduled',
      location: '',
    }));
  }, [appointments]);

  const containerRef = useRef<HTMLDivElement>(null);
  const [now, setNow] = useState(new Date());

  // Filtrar e ordenar consultas do dia atual
  const dayAppointments = useMemo(() =>
    processedAppointments
      .filter((apt) => isSameDay(apt.start, currentDate))
      .sort((a, b) => a.start.getTime() - b.start.getTime()),
    [processedAppointments, currentDate]
  );

  // Organizar eventos que podem se sobrepor
  const organizedAppointments = useMemo(() =>
    organizeOverlappingAppointments(dayAppointments),
    [dayAppointments]
  );

  // Categorizar compromissos
  const pastAppointments = useMemo(() => dayAppointments.filter((apt) => isBefore(apt.end, now)), [dayAppointments, now]);
  const upcomingAppointments = useMemo(() => dayAppointments.filter((apt) => isAfter(apt.start, now)), [dayAppointments, now]);
  const currentAppointments = useMemo(() => dayAppointments.filter(
    (apt) => isBefore(apt.start, now) && isAfter(apt.end, now)
  ), [dayAppointments, now]);


  // Efeitos
  useEffect(() => {
    const timer = setInterval(() => { setNow(new Date()); }, 60000);
    return () => { clearInterval(timer); };
  }, []);

  useEffect(() => {
    if (containerRef.current) {
      const currentHour = now.getHours();
      const scrollPosition = currentHour * HOUR_HEIGHT;
      containerRef.current.scrollTop = scrollPosition - 100;
    }
  }, [now]);


  const dayStart = useMemo(() => {
    const d = new Date(currentDate);
    d.setHours(0, 0, 0, 0);
    return d;
  }, [currentDate]);

  const dayEnd = useMemo(() => {
    const d = new Date(currentDate);
    d.setHours(23, 59, 59, 999);
    return d;
  }, [currentDate]);

  const timeIndicatorPosition = useMemo(() => {
    const totalMinutes = 24 * 60;
    const currentMinutes = now.getHours() * 60 + now.getMinutes();
    return (currentMinutes / totalMinutes) * 100;
  }, [now]);


  // Funções de Renderização e Lógica
  const getStatusIcon = (status: string) => {
    const statusMap = {
      confirmed: <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"><CheckCircle className="mr-1 h-3 w-3" /> Confirmado</Badge>,
      cancelled: <Badge variant="outline" className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"><XCircle className="mr-1 h-3 w-3" /> Cancelado</Badge>,
      no_show: <Badge variant="outline" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"><AlertCircle className="mr-1 h-3 w-3" /> Falta</Badge>,
      completed: <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"><CheckCircle className="mr-1 h-3 w-3" /> Realizado</Badge>,
    };
    return statusMap[status as keyof typeof statusMap] || <Badge variant="outline" className="bg-muted text-muted-foreground"><Clock className="mr-1 h-3 w-3" /> Agendado</Badge>;
  };

  const getAppointmentTypeLabel = (type: string) => {
    const typeMap = {
        consultation: 'Consulta padrão',
        evaluation: 'Avaliação',
        therapy_session: 'Sessão de terapia',
        follow_up: 'Retorno',
        report_review: 'Revisão de relatório',
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };


  return (
    <div className="flex h-full">
      {/* Grid de horários */}
      <div className="flex-1 flex flex-col border-r">
        <div className="border-b px-4 py-3 bg-card sticky top-0 z-10">
          <h2 className="text-xl font-semibold flex items-center">
            <CalendarIcon className="h-5 w-5 mr-2 text-primary" />
            {format(currentDate, "EEEE, dd 'de' MMMM", { locale: ptBR }).replace(/^\w/, c => c.toUpperCase())}
          </h2>
        </div>

        <div className="flex-grow grid grid-cols-[60px_1fr] overflow-hidden">
          <div ref={containerRef} className="col-span-2 relative overflow-y-auto grid grid-cols-[60px_1fr]">
            {/* Coluna de horários */}
            <div className="border-r bg-card z-10">
              {HOURS.map((hour) => (
                <div key={hour} className="h-20 border-b px-2 py-1 text-xs text-right">
                  {hour}:00
                </div>
              ))}
            </div>

            {/* Área das consultas */}
            <div className="relative" onClick={(e) => {
              // Calcular a hora com base na posição do clique
              const rect = e.currentTarget.getBoundingClientRect();
              const offsetY = e.clientY - rect.top;
              const percentage = offsetY / rect.height;
              const totalMinutesInDay = 24 * 60;
              const minutesSinceMidnight = Math.floor(percentage * totalMinutesInDay);
              const hours = Math.floor(minutesSinceMidnight / 60);
              const minutes = Math.round(minutesSinceMidnight % 60 / 15) * 15; // Arredondar para intervalos de 15min
              
              // Criar um novo objeto Date com a data atual e a hora calculada
              const clickedDate = new Date(currentDate);
              clickedDate.setHours(hours, minutes, 0, 0);
              
              // Iniciar o agendamento com a data/hora calculada
              startCreatingAppointment(clickedDate);
            }}>
              {/* Linhas de fundo */}
              {HOURS.map((hour) => (
                <div key={hour} className={cn("h-20 border-b hover:bg-muted/20 transition-colors relative", now.getHours() === hour && "bg-accent/20")}>
                  <div className="absolute top-1/2 left-0 right-0 border-t border-dashed border-border/50"></div>
                </div>
              ))}

              {/* Indicador da hora atual */}
              {timeIndicatorPosition > 0 && (
                <div className="absolute left-0 right-0 z-20 pointer-events-none" style={{ top: `${timeIndicatorPosition}%` }}>
                  <div className="relative">
                    <div className="absolute left-0 right-0 h-0.5 bg-red-500"></div>
                    <div className="absolute -left-1.5 -top-1.5 h-3 w-3 rounded-full bg-red-500"></div>
                    <div className="absolute -left-[60px] -top-2.5 bg-red-500 text-white text-xs px-1 py-0.5 rounded">
                      {format(now, 'HH:mm')}
                    </div>
                  </div>
                </div>
              )}

              {/* Consultas Renderizadas */}
              <TooltipProvider>
                {organizedAppointments.map(({ appointment, column, totalColumns }) => {
                  const position = calculateAppointmentViewPosition(appointment, dayStart, dayEnd, column, totalColumns);
                  const durationMins = differenceInMinutes(appointment.end, appointment.start);
                  const durationText = durationMins >= 60 ? `${Math.floor(durationMins / 60)}h${durationMins % 60 ? ` ${durationMins % 60}min` : ''}` : `${durationMins}min`;
                  const isPast = isBefore(appointment.end, now);
                  const isCurrent = isBefore(appointment.start, now) && isAfter(appointment.end, now);

                  return (
                    <Tooltip key={appointment.id}>
                      <TooltipTrigger asChild>
                        <div
                          className={cn("absolute rounded-md p-2 overflow-hidden cursor-pointer shadow transition-all hover:shadow-md z-10", `bg-${appointment.color}-100 dark:bg-${appointment.color}-900/30 border-l-2 border-${appointment.color}-500`, isPast && "opacity-60", isCurrent && "ring-2 ring-primary")}
                          style={{ top: position.top, height: position.height, left: position.left, width: position.width }}
                          onClick={(e) => { e.stopPropagation(); selectAppointment(appointment); }}
                        >
                          <div className="flex flex-col h-full">
                            <div className="font-bold truncate flex items-center">
                              {appointment.title}
                              {appointment.isRecurring && <Repeat className="h-3 w-3 ml-1 flex-shrink-0" />}
                            </div>
                            <div className="text-sm">
                              <span>{format(appointment.start, 'HH:mm')}</span> - <span>{format(appointment.end, 'HH:mm')}</span>
                              <span className="text-xs text-muted-foreground ml-1">({durationText})</span>
                            </div>
                            <div className="text-sm font-medium mt-1 flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {appointment.patient_name}
                            </div>
                            {parseInt(position.height) > 15 && (
                              <div className="mt-1 text-xs text-muted-foreground">
                                {appointment.location && (
                                  <div className="flex items-center gap-1"><MapPin className="h-3 w-3" />{appointment.location}</div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="space-y-1">
                          <p className="font-bold flex items-center">
                            {appointment.title}
                            {appointment.isRecurring && <Badge variant="outline" className="ml-1 text-xs flex items-center gap-1"><Repeat className="h-3 w-3" /> Recorrente</Badge>}
                          </p>
                          <p>{appointment.patient_name}</p>
                          <p>{getAppointmentTypeLabel(appointment.type)}</p>
                          <p className="text-xs">{format(appointment.start, 'HH:mm')} - {format(appointment.end, 'HH:mm')} ({durationText})</p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  );
                })}
              </TooltipProvider>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar */}
      <div className="w-96 overflow-hidden border-l hidden lg:flex flex-col">
        <div className="border-b px-4 py-3 bg-card sticky top-0 z-10">
          <h3 className="font-semibold">Consultas de Hoje</h3>
          <p className="text-xs text-muted-foreground">{dayAppointments.length === 0 ? 'Nenhuma consulta agendada' : `${dayAppointments.length} consultas`}</p>
        </div>

        <ScrollArea className="flex-grow">
          <div className="p-4">
            {currentAppointments.length > 0 && (
              <div className="mb-6">
                <h4 className="text-sm font-medium text-primary mb-2 flex items-center"><Clock className="h-4 w-4 mr-1" /> Em andamento</h4>
                <div className="space-y-3">
                  {currentAppointments.map((appointment) => (
                    <Card key={appointment.id} className={cn("border-l-4 cursor-pointer hover:shadow-md transition-shadow", `border-l-${appointment.color}-500`)} onClick={() => selectAppointment(appointment)}>
                      <CardContent className="p-3">
                        <div className="flex justify-between items-start mb-1">
                          <div>
                            <h4 className="font-semibold flex items-center">{appointment.title} {appointment.isRecurring && <Repeat className="h-3 w-3 ml-1" />}</h4>
                            <p className="text-xs">{getAppointmentTypeLabel(appointment.type)}</p>
                          </div>
                          <Badge variant="outline" className="bg-primary/20">Agora</Badge>
                        </div>
                        <div className="flex items-center text-muted-foreground mb-2 text-xs">
                          <Clock className="mr-1 h-3 w-3" />
                          <span>{format(appointment.start, 'HH:mm')} - {format(appointment.end, 'HH:mm')}</span>
                        </div>
                        <div className="flex justify-between items-center"><div className="text-sm font-medium">{appointment.patient_name}</div></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
            
            {upcomingAppointments.length > 0 && (
              <div className="mb-6">
                <h4 className="text-sm font-medium mb-2 flex items-center"><Clock className="h-4 w-4 mr-1" /> Próximas consultas</h4>
                <div className="space-y-3">
                  {upcomingAppointments.map((appointment) => (
                    <Card key={appointment.id} className={cn("border-l-4 cursor-pointer hover:shadow-md transition-shadow", `border-l-${appointment.color}-500`)} onClick={() => selectAppointment(appointment)}>
                      <CardContent className="p-3">
                        <div className="flex justify-between items-start mb-1">
                            <div>
                                <h4 className="font-semibold flex items-center">{appointment.title} {appointment.isRecurring && <Repeat className="h-3 w-3 ml-1" />}</h4>
                                <p className="text-xs">{getAppointmentTypeLabel(appointment.type)}</p>
                            </div>
                            {getStatusIcon(appointment.status)}
                        </div>
                        <div className="flex items-center text-muted-foreground mb-2 text-xs"><Clock className="mr-1 h-3 w-3" /><span>{format(appointment.start, 'HH:mm')} - {format(appointment.end, 'HH:mm')}</span></div>
                        <div className="flex justify-between items-center"><div className="text-sm font-medium">{appointment.patient_name}</div></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {pastAppointments.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2 flex items-center text-muted-foreground"><Clock className="h-4 w-4 mr-1" /> Consultas anteriores</h4>
                <div className="space-y-3 opacity-70">
                  {pastAppointments.map((appointment) => (
                    <Card key={appointment.id} className={cn("border-l-4 cursor-pointer hover:shadow-md transition-shadow", `border-l-${appointment.color}-500`)} onClick={() => selectAppointment(appointment)}>
                      <CardContent className="p-3">
                        <div className="flex justify-between items-start mb-1">
                            <div>
                                <h4 className="font-semibold flex items-center">{appointment.title} {appointment.isRecurring && <Repeat className="h-3 w-3 ml-1" />}</h4>
                                <p className="text-xs">{getAppointmentTypeLabel(appointment.type)}</p>
                            </div>
                            {getStatusIcon(appointment.status)}
                        </div>
                        <div className="flex items-center text-muted-foreground mb-2 text-xs"><Clock className="mr-1 h-3 w-3" /><span>{format(appointment.start, 'HH:mm')} - {format(appointment.end, 'HH:mm')}</span></div>
                        <div className="flex justify-between items-center"><div className="text-sm font-medium">{appointment.patient_name}</div></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {dayAppointments.length === 0 && (
              <div className="flex flex-col items-center justify-center py-10 text-center">
                <CalendarIcon className="h-12 w-12 text-muted-foreground/40 mb-4" />
                <h3 className="text-lg font-medium mb-2">Nenhuma consulta agendada</h3>
                <p className="text-muted-foreground mb-6 max-w-xs">Não há consultas agendadas para hoje. Adicione uma nova consulta para começar.</p>
                <Button onClick={() => startCreatingAppointment(currentDate)}>Agendar Consulta</Button>
              </div>
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
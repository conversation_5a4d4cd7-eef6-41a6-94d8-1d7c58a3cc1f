import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/shared/ui/alert-dialog';
import { Button } from '@/shared/ui/button';
import {
  Calendar,
  Clock,
  Trash2,
  User,
  Info,
  Edit,
  Repeat,
  FileText,
} from 'lucide-react';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery';
import { Patient } from '@/features/patients/types/patient.schema';
import { Appointment } from '@/features/calendar/types/appointment.schema';
import { RecurrenceModifyDialog } from './RecurrenceModifyDialog';
import { differenceInMinutes, format, parseISO } from 'date-fns';
import { Badge } from '@/shared/ui/badge';
import { useNavigate } from '@tanstack/react-router';
import { useAppointmentMutations } from '../hooks/useAppointmentMutations';
import { useSessionNotes } from '@/features/session-notes/hooks/useSessionNotes';

interface AppointmentDetailsProps {
  selectedAppointment: Appointment | null;
  onClose: () => void;
  onEdit: (appointment: Appointment) => void;
}

export function AppointmentDetails({
  selectedAppointment,
  onClose,
  onEdit,
}: AppointmentDetailsProps) {
  const [showRecurrenceDialog, setShowRecurrenceDialog] = useState(false);
  const { deleteAppointment, deleteRecurringAppointment } = useAppointmentMutations();
  const { allPatients } = usePatientQuery();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const navigate = useNavigate();
  const { openPostAppointmentNote } = useSessionNotes();

  if (!selectedAppointment) {
    return <div className="p-4">Nenhum agendamento selecionado.</div>;
  }

  const patient = allPatients.find((p: Patient) => p.id === selectedAppointment.patient_id);

  const handleDelete = () => {
    if (selectedAppointment?.is_recurring) {
      setShowRecurrenceDialog(true);
    } else {
      setShowDeleteConfirm(true);
    }
  };

  const confirmDelete = () => {
    if (selectedAppointment) {
      deleteAppointment(selectedAppointment.id);
      setShowDeleteConfirm(false);
      onClose();
    }
  };

  const handleEdit = () => {
    if (selectedAppointment) {
      onEdit(selectedAppointment);
    }
  };

  const handleDeleteRecurrence = (mode: 'single' | 'all' | 'future') => {
    if (selectedAppointment) {
      deleteRecurringAppointment({ id: selectedAppointment.id, deleteMode: mode });
      setShowRecurrenceDialog(false);
      onClose();
    }
  };

  const handleCreateSessionNote = () => {
    if (selectedAppointment && patient) {
      const patientData = {
        id: patient.id,
        name: patient.full_name,
        avatar: undefined
      };

      const appointmentData = {
        id: selectedAppointment.id,
        patientId: selectedAppointment.patient_id,
        sessionNumber: 1, // TODO: Calcular número da sessão baseado no histórico
        scheduledAt: selectedAppointment.start_time,
        duration: selectedAppointment.duration_minutes || 50,
        status: 'completed' as const
      };

      openPostAppointmentNote(patientData, appointmentData);
      onClose();
    }
  };
  
  const startTime = parseISO(selectedAppointment.start_time);
  const endTime = parseISO(selectedAppointment.end_time);

  return (
    <>
      <div className="p-4 space-y-4">
        <div className="flex justify-between items-start">
          <h2 className="text-xl font-semibold">{selectedAppointment.title}</h2>
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleCreateSessionNote}
              title="Nova Nota de Sessão"
            >
              <FileText className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" onClick={handleEdit}>
              <Edit className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="icon" className="text-destructive" onClick={handleDelete}>
              <Trash2 className="h-5 w-5" />
            </Button>
          </div>
        </div>
        <div className="space-y-2 text-sm text-muted-foreground">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            <span>{format(startTime, "dd/MM/yyyy")}</span>
          </div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            <span>{format(startTime, 'HH:mm')} - {format(endTime, 'HH:mm')} ({differenceInMinutes(endTime, startTime)} min)</span>
          </div>
          {patient && (
            <div className="flex items-center">
              <User className="h-4 w-4 mr-2" />
              <span>{patient.full_name}</span>
            </div>
          )}
        </div>
        {selectedAppointment.notes && (
          <div className="pt-2">
            <h3 className="font-semibold mb-1">Observações</h3>
            <p className="text-sm bg-muted/50 p-3 rounded-md">{selectedAppointment.notes}</p>
          </div>
        )}
      </div>

      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Tem certeza que deseja excluir?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta ação não pode ser desfeita. Isso excluirá permanentemente o agendamento.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>Confirmar</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <RecurrenceModifyDialog
        open={showRecurrenceDialog}
        onOpenChange={setShowRecurrenceDialog}
        title="Excluir Consulta Recorrente"
        description="Esta é uma consulta recorrente. Como você gostaria de excluí-la?"
        actionText="Excluir"
        onAction={handleDeleteRecurrence}
        onCancel={() => { setShowRecurrenceDialog(false); }}
        isUpdate={false}
      />
    </>
  );
}

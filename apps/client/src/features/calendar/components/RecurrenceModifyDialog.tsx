import React from 'react';
import { AlertTriangle } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/ui/dialog';
import { Button } from '@/shared/ui/button';
import { RadioGroup, RadioGroupItem } from '@/shared/ui/radio-group';
import { Label } from '@/shared/ui/label';

export type RecurrenceModifyMode = 'single' | 'future' | 'all';

interface RecurrenceModifyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  actionText: string;
  onAction: (mode: RecurrenceModifyMode) => void;
  onCancel: () => void;
  isProcessing?: boolean;
  defaultMode?: RecurrenceModifyMode;
  isUpdate?: boolean; // True para atualização, false para exclusão
}

export function RecurrenceModifyDialog({
  open,
  onOpenChange,
  title,
  description,
  actionText,
  onAction,
  onCancel,
  isProcessing = false,
  defaultMode = 'single',
  isUpdate = true
}: RecurrenceModifyDialogProps) {
  const [selectedMode, setSelectedMode] = React.useState<RecurrenceModifyMode>(defaultMode);

  // Reset o modo selecionado quando o diálogo é aberto
  React.useEffect(() => {
    if (open) {
      setSelectedMode(defaultMode);
    }
  }, [open, defaultMode]);

  const handleAction = () => {
    onAction(selectedMode);
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      if (!isOpen) {
        // Usar setTimeout para evitar problemas de atualização de estado durante a renderização
        setTimeout(() => {
          onOpenChange(false);
        }, 0);
      } else {
        onOpenChange(true);
      }
    }}>
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            {title}
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <RadioGroup
            value={selectedMode}
            onValueChange={(value) => setSelectedMode(value as RecurrenceModifyMode)}
            className="space-y-4"
          >
            <div className="flex items-start space-x-2">
              <RadioGroupItem value="single" id="r1" />
              <Label htmlFor="r1" className="font-normal cursor-pointer">
                <span className="font-medium">Somente esta ocorrência</span>
                <p className="text-sm text-muted-foreground mt-0.5">
                  {isUpdate
                    ? "Apenas esta consulta específica será modificada."
                    : "Apenas esta consulta específica será excluída."}
                </p>
              </Label>
            </div>

            <div className="flex items-start space-x-2">
              <RadioGroupItem value="future" id="r2" />
              <Label htmlFor="r2" className="font-normal cursor-pointer">
                <span className="font-medium">Esta e as próximas ocorrências</span>
                <p className="text-sm text-muted-foreground mt-0.5">
                  {isUpdate
                    ? "Esta consulta e todas as futuras serão modificadas."
                    : "Esta consulta e todas as futuras serão excluídas."}
                </p>
              </Label>
            </div>

            <div className="flex items-start space-x-2">
              <RadioGroupItem value="all" id="r3" />
              <Label htmlFor="r3" className="font-normal cursor-pointer">
                <span className="font-medium">Todas as ocorrências</span>
                <p className="text-sm text-muted-foreground mt-0.5">
                  {isUpdate
                    ? "Toda a série de consultas recorrentes será modificada."
                    : "Toda a série de consultas recorrentes será excluída."}
                </p>
              </Label>
            </div>
          </RadioGroup>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isProcessing}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleAction}
            disabled={isProcessing}
            variant={isUpdate ? "default" : "destructive"}
          >
            {isProcessing ? "Processando..." : actionText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
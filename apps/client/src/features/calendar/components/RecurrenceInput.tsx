import React, { useState, useEffect } from 'react';
import { Calendar, Info } from 'lucide-react';
import { Label } from '@/shared/ui/label';
import { Input } from '@/shared/ui/input';
import { Switch } from '@/shared/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/ui/select';
import {
  MAX_RECURRENCE_OCCURRENCES,
} from "@/features/calendar/types/page";
import { addDays, format, parse } from 'date-fns';
import { cn } from '@/shared/lib/utils';

export interface RecurrenceData {
  isRecurring: boolean;
  type: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'custom';
  interval: number;
  occurrences: number;
  endDate?: Date;
}

const recurrenceTypeValues = ['daily', 'weekly', 'biweekly', 'monthly', 'custom'] as const;
function isRecurrenceType(value: string): value is RecurrenceData['type'] {
  return (recurrenceTypeValues as unknown as string[]).includes(value);
}

interface RecurrenceInputProps {
  value: RecurrenceData;
  onChange: (value: RecurrenceData) => void;
  disabled?: boolean;
  className?: string;
}

export function RecurrenceInput({
  value,
  onChange,
  disabled = false,
  className = ""
}: RecurrenceInputProps) {
  const handleEnableRecurrence = (enabled: boolean) => {
    onChange({
      ...value,
      isRecurring: enabled,
    });
  };

  const handleInputChange = (field: keyof RecurrenceData, val: unknown) => {
    console.log('handleInputChange - field:', field, 'val:', val, 'current value:', value[field]);
    const newValue = {
      ...value,
      [field]: val,
    };
    console.log('New recurrence data:', newValue);
    onChange(newValue);
  };

  const handleTypeChange = (val: string) => {
    if (isRecurrenceType(val)) {
      handleInputChange('type', val);
    }
  };

  const handleIntervalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const interval = parseInt(e.target.value);
    if (!isNaN(interval) && interval > 0) {
      handleInputChange('interval', interval);
    }
  };

  const handleOccurrencesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    console.log('handleOccurrencesChange - inputValue:', inputValue, 'current occurrences:', value.occurrences);

    if (inputValue === '') {
      console.log('Setting occurrences to 0 (empty input)');
      handleInputChange('occurrences', 0);
      return;
    }

    const occurrences = parseInt(inputValue);
    console.log('Parsed occurrences:', occurrences, 'isNaN:', isNaN(occurrences));

    if (!isNaN(occurrences) && occurrences >= 0) {
      const limitedValue = Math.min(Math.max(occurrences, 0), MAX_RECURRENCE_OCCURRENCES);
      console.log('Setting occurrences to:', limitedValue);

      // Fazer uma única atualização de estado para evitar problemas de concorrência
      const newValue = {
        ...value,
        occurrences: limitedValue,
        // Só limpar endDate se realmente inseriu um valor válido > 0
        endDate: limitedValue > 0 ? undefined : value.endDate,
      };
      console.log('Single update - new value:', newValue);
      onChange(newValue);
    } else {
      console.log('Invalid occurrences value, not updating');
    }
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      if (e.target.value) {
        // Criar data a partir do valor do input (formato YYYY-MM-DD)
        const dateValue = e.target.value;
        const endDate = new Date(dateValue + 'T00:00:00');

        // Verificar se a data é válida
        if (!isNaN(endDate.getTime())) {
          // Fazer uma única atualização de estado
          const newValue = {
            ...value,
            endDate: endDate,
            occurrences: 0, // Limpar occurrences quando definir endDate
          };
          onChange(newValue);
        }
      } else {
        handleInputChange('endDate', undefined);
        // Não limpar occurrences quando removendo a data
      }
    } catch (error) {
      console.error("Data inválida:", error);
    }
  };

  const formatDateForInput = (date?: Date) => {
    if (!date) return "";
    return date.toISOString().split('T')[0];
  };

  const getRecurrenceTypeLabel = (type: string) => {
    switch (type) {
      case 'daily': return 'Diário';
      case 'weekly': return 'Semanal';
      case 'biweekly': return 'Quinzenal';
      case 'monthly': return 'Mensal';
      case 'custom': return 'Personalizado';
      default: return 'Não recorrente';
    }
  };

  console.log('RecurrenceInput render - value:', value);

  return (
    <div className={className}>
      <div className="flex items-center space-x-2">
        <Switch
          id="is-recurring"
          checked={value.isRecurring}
          onCheckedChange={(checked) => { handleInputChange('isRecurring', checked); }}
          disabled={disabled}
        />
        <Label htmlFor="is-recurring">Repetir consulta</Label>
      </div>

      <div className={cn("space-y-4 pl-6 border-l-2 ml-3 pt-4", !value.isRecurring && "hidden")}>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <Label htmlFor="recurrence-type">Frequência</Label>
            <Select
              value={value.type}
              onValueChange={handleTypeChange}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione a frequência" />
              </SelectTrigger>
              <SelectContent>
                {recurrenceTypeValues.map((type) => (
                  <SelectItem key={type} value={type}>
                    {getRecurrenceTypeLabel(type)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {value.type === 'custom' && (
            <div className="space-y-1">
              <Label htmlFor="recurrence-interval">A cada (dias)</Label>
              <Input
                id="recurrence-interval"
                type="number"
                value={value.interval}
                onChange={handleIntervalChange}
                disabled={disabled}
                min="1"
              />
            </div>
          )}
        </div>
        <div className="grid grid-cols-2 gap-3">
          <div>
            <Label htmlFor="recurrence-end-mode" className="mb-1 block text-xs">
              Terminar após
            </Label>
            <Input
              id="recurrence-occurrences"
              type="number"
              min="1"
              max={MAX_RECURRENCE_OCCURRENCES}
              value={value.occurrences || ""}
              onChange={handleOccurrencesChange}
              placeholder="Número de ocorrências"
              className="h-9"
              disabled={disabled}
            />
          </div>

          <div>
            <Label htmlFor="recurrence-end-date" className="mb-1 block text-xs">
              Ou terminar em
            </Label>
            <div className="relative">
              <Calendar className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                id="recurrence-end-date"
                type="date"
                value={formatDateForInput(value.endDate)}
                onChange={handleEndDateChange}
                className="pl-8 h-9"
                disabled={disabled}
              />
            </div>
          </div>
        </div>

        <div className="flex items-start mt-2 text-xs text-muted-foreground">
          <Info className="h-3 w-3 mr-1 mt-0.5 flex-shrink-0" />
          <p>
            Você pode criar até {MAX_RECURRENCE_OCCURRENCES} ocorrências de uma consulta recorrente.
            Defina um número de ocorrências OU uma data de término. Ao preencher um campo, o outro será limpo automaticamente.
          </p>
        </div>
      </div>
    </div>
  );
}
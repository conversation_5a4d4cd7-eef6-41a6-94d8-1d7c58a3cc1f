import { Button } from "@/shared/ui/button";
import { addMinutes } from "date-fns";

interface DurationPickerProps {
  startTime: Date;
  endTime?: Date;
  onSelectDuration: (endTime: Date) => void;
}

export function DurationPicker({ startTime, endTime, onSelectDuration }: DurationPickerProps) {
  const durations = [
    { label: "30 min", minutes: 30 },
    { label: "45 min", minutes: 45 },
    { label: "50 min", minutes: 50 },
    { label: "90 min", minutes: 90 },
  ];

  // Calcular a duração atual se endTime estiver disponível
  const currentDurationMinutes = endTime
    ? Math.round((endTime.getTime() - startTime.getTime()) / 60000)
    : null;

  const handleSelectDuration = (minutes: number) => {
    const newEndTime = addMinutes(startTime, minutes);
    onSelectDuration(newEndTime);
  };

  // Verificar se a duração atual não está nas opções padrão
  const hasCustomDuration = currentDurationMinutes &&
    !durations.some(d => d.minutes === currentDurationMinutes);

  return (
    <div className="space-y-2 pt-2">
      {currentDurationMinutes && (
        <div className="text-xs text-muted-foreground text-center">
          Duração atual: {currentDurationMinutes} min
        </div>
      )}
      <div className="flex flex-wrap gap-2">
        {durations.map((duration) => {
          const isSelected = currentDurationMinutes === duration.minutes;
          return (
            <Button
              key={duration.minutes}
              type="button"
              size="sm"
              variant={isSelected ? "default" : "outline"}
              onClick={() => { handleSelectDuration(duration.minutes); }}
            >
              {duration.label}
            </Button>
          );
        })}
        {hasCustomDuration && (
          <Button
            type="button"
            size="sm"
            variant="default"
            disabled
          >
            {currentDurationMinutes} min (atual)
          </Button>
        )}
      </div>
    </div>
  );
}

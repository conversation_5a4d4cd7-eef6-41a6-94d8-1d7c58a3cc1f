import { useEffect, useMemo, useRef } from 'react';
import { CalendarHeader } from '@/features/calendar/components/header/CalendarHeader';
import { CalendarMonth } from '@/features/calendar/components/views/CalendarMonth';
import { CalendarWeek } from '@/features/calendar/components/views/CalendarWeek';
import { CalendarDay } from '@/features/calendar/components/views/CalendarDay';
import { AppointmentDialog } from '@/features/calendar/components/AppointmentDialog';
import { AppointmentDetails } from '@/features/calendar/components/AppointmentDetails';
import { useCalendarStore } from '@/features/calendar/stores/useCalendarStore';
import { useAppointmentQuery } from '@/features/calendar/hooks/useAppointmentQuery';
import { AlertCircle, Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/shared/ui/alert';
import { Button } from '@/shared/ui/button';
import { useMediaQuery } from '@/shared/hooks/useMediaQuery';

export function Calendar() {
  const { 
    viewMode, 
    setViewMode,
    isCreatingAppointment,
    isEditingAppointment,
    selectedAppointment,
    closeDialogs,
    startEditingAppointment,
    selectAppointment,
  } = useCalendarStore();
  const { isLoading, isError, refetch } = useAppointmentQuery({
    viewMode: 'month'
  });
  const containerRef = useRef<HTMLDivElement>(null);
  const isMobile = useMediaQuery('(max-width: 640px)');

  // Ajustar visualização para dispositivos móveis
  useEffect(() => {
    if (isMobile && viewMode === 'week') {
      setViewMode('day');
    }
  }, [isMobile, viewMode, setViewMode]);

  // Adicionar controles de teclado para navegação
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Verificar se não está em um campo de entrada
      if (
        document.activeElement instanceof HTMLInputElement ||
        document.activeElement instanceof HTMLTextAreaElement
      ) {
        return;
      }

      switch (e.key) {
        case 'd':
          setViewMode('day');
          break;
        case 'w':
          if (!isMobile) {
            setViewMode('week');
          }
          break;
        case 'm':
          setViewMode('month');
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [setViewMode, isMobile]);

  // Renderizar a visualização apropriada baseada no viewMode
  const calendarView = useMemo(() => {
    if (isLoading) {
      return (
        <div className="flex flex-col h-full items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Carregando calendário...</p>
        </div>
      );
    }

    if (isError) {
      return (
        <div className="flex flex-col h-full items-center justify-center p-8">
          <Alert variant="destructive" className="max-w-lg">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erro ao carregar agenda</AlertTitle>
            <AlertDescription>
              Não foi possível carregar os dados do calendário. Tente novamente.
            </AlertDescription>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => refetch()}
            >
              Tentar novamente
            </Button>
          </Alert>
        </div>
      );
    }

    switch (viewMode) {
      case 'month':
        return <CalendarMonth />;
      case 'week':
        return <CalendarWeek />;
      case 'day':
        return <CalendarDay />;
      default:
        return <CalendarMonth />;
    }
  }, [viewMode, isLoading, isError, refetch]);

  return (
    <div
      ref={containerRef}
      className="flex flex-col h-full border rounded-md overflow-hidden"
      role="region"
      aria-label="Calendário"
    >
      <CalendarHeader />
      <div className="flex-grow overflow-hidden">
        {calendarView}
      </div>

      {/* Diálogos */}
      <AppointmentDialog 
        open={isCreatingAppointment || isEditingAppointment}
        onOpenChange={(isOpen) => !isOpen && closeDialogs()}
        appointmentToEdit={isEditingAppointment ? selectedAppointment : undefined}
        onSuccess={() => refetch()}
      />
      <AppointmentDetails 
        selectedAppointment={selectedAppointment}
        onClose={closeDialogs}
        onEdit={(apt) => {
          if (apt) {
            startEditingAppointment(apt);
          }
        }}
      />

      {/* Dicas de acessibilidade */}
      <div className="sr-only" aria-live="polite">
        Use as teclas D, W, e M para alternar entre as visualizações de dia, semana e mês.
      </div>
    </div>
  );
}
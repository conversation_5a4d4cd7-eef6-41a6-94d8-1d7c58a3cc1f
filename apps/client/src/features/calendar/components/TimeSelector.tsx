import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { format, parse, set } from 'date-fns';

interface TimeSelectorProps {
  value: Date;
  onChange: (date: Date) => void;
  interval?: number; // intervalo em minutos (padrão: 15 min)
  className?: string;
}

export function TimeSelector({
  value,
  onChange,
  interval = 15,
  className
}: TimeSelectorProps) {
  // Criar opções de tempo em intervalos regulares (ex: a cada 15 min)
  const timeOptions = React.useMemo(() => {
    const options: string[] = [];
    const minutesInDay = 24 * 60;
    
    for (let minutes = 0; minutes < minutesInDay; minutes += interval) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      options.push(format(set(new Date(), { hours, minutes: mins }), 'HH:mm'));
    }
    
    return options;
  }, [interval]);

  const currentTimeString = format(value, 'HH:mm');

  const handleTimeChange = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const newDate = set(value, { hours, minutes, seconds: 0, milliseconds: 0 });
    onChange(newDate);
  };

  return (
    <Select
      value={currentTimeString}
      onValueChange={handleTimeChange}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder="Selecione um horário" />
      </SelectTrigger>
      <SelectContent>
        {timeOptions.map((timeOption) => (
          <SelectItem key={timeOption} value={timeOption}>
            {timeOption}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}

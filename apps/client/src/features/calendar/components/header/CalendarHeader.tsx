import { useMemo } from 'react';
import { format, addMonths, subMonths, addWeeks, subWeeks, addDays, subDays, isSameMonth, isSameWeek, isSameDay, startOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Button } from '@/shared/ui/button';
import { CalendarViewMode } from '@/features/calendar/types/page';
import { ChevronLeft, ChevronRight, Plus, Filter } from 'lucide-react';
import { useCalendarStore } from '@/features/calendar/stores/useCalendarStore';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/shared/ui/dropdown-menu';
import { useMediaQuery } from '@/shared/hooks/useMediaQuery';

export function CalendarHeader() {
  const {
    viewMode,
    setViewMode,
    currentDate,
    setCurrentDate,
  } = useCalendarStore();

  // Usar startCreatingAppointment em vez de handleNewAppointment
  const { startCreatingAppointment } = useCalendarStore();

  const isMobile = useMediaQuery('(max-width: 640px)');

  const capitalizeFirstLetter = (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  const formattedTitle = useMemo(() => {
    switch (viewMode) {
      case 'month':
        return capitalizeFirstLetter(format(currentDate, "MMMM 'de' yyyy", { locale: ptBR }));
      case 'week':
        const mondayOfWeek = startOfWeek(currentDate, { locale: ptBR, weekStartsOn: 1 });
        return capitalizeFirstLetter(format(mondayOfWeek, "'Semana de' d 'de' MMMM", { locale: ptBR }));
      case 'day':
        return capitalizeFirstLetter(format(currentDate, "EEEE, d 'de' MMMM", { locale: ptBR }));
      default:
        return '';
    }
  }, [viewMode, currentDate]);

  const periodLabel = useMemo(() => {
    const today = new Date();

    switch (viewMode) {
      case 'month':
        return isSameMonth(currentDate, today) ? 'Mês atual' : capitalizeFirstLetter(format(currentDate, "MMMM 'de' yyyy", { locale: ptBR }));
      case 'week':
        if (isSameWeek(currentDate, today, { locale: ptBR, weekStartsOn: 1 })) {
          return 'Semana atual';
        } else {
          const mondayOfWeek = startOfWeek(currentDate, { locale: ptBR, weekStartsOn: 1 });
          return capitalizeFirstLetter(format(mondayOfWeek, "'Semana de' d 'de' MMMM", { locale: ptBR }));
        }
      case 'day':
        return isSameDay(currentDate, today) ? 'Dia atual' : capitalizeFirstLetter(format(currentDate, "EEEE, d 'de' MMMM", { locale: ptBR }));
      default:
        return '';
    }
  }, [viewMode, currentDate]);

  const handlePrevious = () => {
    switch (viewMode) {
      case 'month':
        setCurrentDate(subMonths(currentDate, 1));
        break;
      case 'week':
        setCurrentDate(subWeeks(currentDate, 1));
        break;
      case 'day':
        setCurrentDate(subDays(currentDate, 1));
        break;
    }
  };

  const handleNext = () => {
    switch (viewMode) {
      case 'month':
        setCurrentDate(addMonths(currentDate, 1));
        break;
      case 'week':
        setCurrentDate(addWeeks(currentDate, 1));
        break;
      case 'day':
        setCurrentDate(addDays(currentDate, 1));
        break;
    }
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  const goToSpecificDate = (date: Date, view?: CalendarViewMode) => {
    setCurrentDate(date);
    if (view) {
      setViewMode(view);
    }
  };

  const tomorrow = addDays(new Date(), 1);
  const nextMonday = (() => {
    const date = new Date();
    const day = date.getDay();
    const diff = day === 0 ? 1 : 8 - day; // Se for domingo (0), próxima segunda é amanhã, senão é 8 - dia atual
    return addDays(date, diff);
  })();
  const nextWeek = addWeeks(new Date(), 1);
  const nextMonth = addMonths(new Date(), 1);

  return (
    <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 p-4 border-b">
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <div className="flex items-center text-sm text-muted-foreground">
            <Button
              variant="outline"
              size="sm"
              className="h-7 w-7 p-0"
              onClick={handlePrevious}
              aria-label="Período anterior"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="mx-2" aria-live="polite">
              {periodLabel}
            </span>
            <Button
              variant="outline"
              size="sm"
              className="h-7 w-7 p-0"
              onClick={handleNext}
              aria-label="Próximo período"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="flex flex-col space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0">
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleToday}
            className="h-8"
          >
            Hoje
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-8"
              >
                <Filter className="h-4 w-4 mr-2" />
                Ir para
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => { goToSpecificDate(tomorrow, 'day'); }}>
                Amanhã
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => { goToSpecificDate(nextMonday, 'day'); }}>
                Próxima segunda-feira
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => { goToSpecificDate(nextWeek, 'week'); }}>
                Próxima semana
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => { goToSpecificDate(nextMonth, 'month'); }}>
                Próximo mês
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => {
                const date = prompt("Digite uma data (DD/MM/AAAA):");
                if (date) {
                  const [day, month, year] = date.split('/').map(Number);
                  if (!isNaN(day) && !isNaN(month) && !isNaN(year)) {
                    goToSpecificDate(new Date(year, month - 1, day), 'day');
                  }
                }
              }}>
                Data específica...
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {!isMobile && (
            <div className="flex rounded-md shadow-sm">
              <Button
                variant={viewMode === 'day' ? 'default' : 'outline'}
                size="sm"
                className="h-8 rounded-r-none"
                onClick={() => setViewMode('day')}
              >
                Dia
              </Button>
              <Button
                variant={viewMode === 'week' ? 'default' : 'outline'}
                size="sm"
                className="h-8 rounded-none border-x-0"
                onClick={() => setViewMode('week')}
              >
                Semana
              </Button>
              <Button
                variant={viewMode === 'month' ? 'default' : 'outline'}
                size="sm"
                className="h-8 rounded-l-none"
                onClick={() => setViewMode('month')}
              >
                Mês
              </Button>
            </div>
          )}
        </div>

        <Button
          size="sm"
          className="h-8"
          onClick={() => startCreatingAppointment()}
        >
          <Plus className="mr-2 h-4 w-4" />
          Nova Consulta
        </Button>
      </div>
    </div>
  );
}

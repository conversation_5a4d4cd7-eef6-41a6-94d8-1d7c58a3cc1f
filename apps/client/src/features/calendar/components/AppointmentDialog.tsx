import { useState, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/shared/ui/dialog";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/ui/form";
import { useCalendarStore } from "@/features/calendar/stores/useCalendarStore";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import { Textarea } from "@/shared/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/ui/popover";
import { Calendar as CalendarIcon, Clock } from "lucide-react";
import { Calendar } from "@/shared/ui/calendar";
import { format, parseISO, set, isBefore, addMinutes } from "date-fns";
import { useAppointmentMutations } from "@/features/calendar/hooks/useAppointmentMutations";
import { usePatientQuery } from "@/features/patients/hooks/usePatientQuery";
import { Appointment, CreateAppointmentData, UpdateAppointmentData } from "@/features/calendar/types/appointment.schema";
import { Patient } from "@/features/patients/types/patient.schema";
import { useToast } from "@/shared/hooks/use-toast";
import { cn } from "@/shared/lib/utils";
import { RecurrenceModifyDialog } from "./RecurrenceModifyDialog";
import { RecurrenceModifyMode } from "../types/page";
import { RecurrenceInput, RecurrenceData } from './RecurrenceInput';
import { DurationPicker } from './DurationPicker';
import { TimeSelector } from './TimeSelector';

const statusOptions: { value: string, label: string }[] = [
    { value: 'scheduled', label: 'Agendado' },
    { value: 'confirmed', label: 'Confirmado' },
    { value: 'completed', label: 'Realizada' },
    { value: 'cancelled', label: 'Cancelado' },
    { value: 'rescheduled', label: 'Remarcado' },
    { value: 'no_show', label: 'Falta' },
];

const appointmentFormSchema = z.object({
  title: z.string().min(1, "Título é obrigatório."),
  patient_id: z.string().min(1, "Paciente é obrigatório."),
  start_time: z.string(),
  end_time: z.string(),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'rescheduled', 'no_show']),
  notes: z.string().optional(),
});

type AppointmentFormData = z.infer<typeof appointmentFormSchema>;

interface AppointmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  appointmentToEdit?: Appointment | null;
  onSuccess?: () => void;
}

export function AppointmentDialog({
  open,
  onOpenChange,
  appointmentToEdit,
  onSuccess
}: AppointmentDialogProps) {
  const { toast } = useToast();
  const { allPatients, isLoadingList: isLoadingPatients } = usePatientQuery();
  const {
    createAppointment,
    updateAppointment,
    updateRecurringAppointment,
    isCreating,
    isUpdating,
  } = useAppointmentMutations();
  const [showRecurrenceDialog, setShowRecurrenceDialog] = useState(false);
  const [stagedUpdatePayload, setStagedUpdatePayload] = useState<UpdateAppointmentData | null>(null);
  const [recurrenceData, setRecurrenceData] = useState<RecurrenceData>({
    isRecurring: false,
    type: 'weekly',
    interval: 1,
    occurrences: 0,
    endDate: undefined,
  });

  const isEditing = !!appointmentToEdit;
  const form = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentFormSchema),
    defaultValues: {
      title: "",
      patient_id: "",
      start_time: "",
      end_time: "",
      notes: "",
      status: "scheduled",
    },
  });

  useEffect(() => {
    if (open) {
      if (isEditing && appointmentToEdit) {
        form.reset({
          title: appointmentToEdit.title,
          patient_id: appointmentToEdit.patient_id,
          start_time: format(parseISO(appointmentToEdit.start_time), "yyyy-MM-dd'T'HH:mm:ss"),
          end_time: format(parseISO(appointmentToEdit.end_time), "yyyy-MM-dd'T'HH:mm:ss"),
          notes: appointmentToEdit.notes || "",
          status: appointmentToEdit.status,
        });
      } else {
        // Usar a currentDate do CalendarStore, que pode ter sido definida por um clique na grade
        const { currentDate } = useCalendarStore.getState();
        const start = new Date(currentDate);
        
        // Arredondar para intervalos de 15 minutos se não foi definida por um clique
        if (!start.getMinutes() && !start.getSeconds()) {
          const roundedMinutes = Math.ceil(start.getMinutes() / 15) * 15;
          start.setMinutes(roundedMinutes, 0, 0);
        }
        
        const end = addMinutes(start, 50);
        form.reset({
          title: "",
          patient_id: "",
          start_time: format(start, "yyyy-MM-dd'T'HH:mm:ss"),
          end_time: format(end, "yyyy-MM-dd'T'HH:mm:ss"),
          notes: "",
          status: "scheduled",
        });
        setRecurrenceData({
          isRecurring: false,
          type: 'weekly',
          interval: 1,
          occurrences: 0,
          endDate: undefined,
        });
      }
    }
  }, [open, isEditing, appointmentToEdit, form]);

  const onSubmit = (data: AppointmentFormData) => {
    const start = parseISO(data.start_time);
    const end = parseISO(data.end_time);

    if (isBefore(end, start)) {
      form.setError("end_time", { message: "O término deve ser após o início." });
      return;
    }

    const commonPayload = {
      ...data,
      start_time: start.toISOString(),
      end_time: end.toISOString(),
    };

    const handleSuccess = () => {
      toast({ title: isEditing ? "Agendamento atualizado!" : "Agendamento criado!" });
      onOpenChange(false);
      onSuccess?.();
    };

    if (isEditing && appointmentToEdit) {
      const payload: UpdateAppointmentData = commonPayload;
      if(appointmentToEdit.is_recurring) {
        setStagedUpdatePayload(payload);
        setShowRecurrenceDialog(true);
      } else {
        updateAppointment({ id: appointmentToEdit.id, data: payload }, { onSuccess: handleSuccess });
      }
    } else {
      const payload: CreateAppointmentData = {
        ...commonPayload,
        type: 'consultation',
      };

      if (recurrenceData.isRecurring) {
        payload.is_recurrent = true;
        payload.occurrences = recurrenceData.occurrences || undefined;
        payload.recurrence_end_date = recurrenceData.endDate?.toISOString();
        
        switch (recurrenceData.type) {
          case 'daily':
            payload.recurrence_pattern = 'custom_1';
            break;
          case 'custom':
            payload.recurrence_pattern = `custom_${recurrenceData.interval}`;
            break;
          default:
            payload.recurrence_pattern = recurrenceData.type;
            break;
        }
      }

      createAppointment(payload, { onSuccess: handleSuccess });
    }
  };

  const handleUpdateRecurrence = (updateMode: RecurrenceModifyMode) => {
    if (appointmentToEdit && stagedUpdatePayload) {
      updateRecurringAppointment(
        { id: appointmentToEdit.id, data: stagedUpdatePayload, updateMode },
        {
          onSuccess: () => {
            toast({ title: "Agendamento(s) atualizado(s)!" });
            onOpenChange(false);
            onSuccess?.();
          }
        }
      );
    }
    setShowRecurrenceDialog(false);
  };

  const isLoading = isCreating || isUpdating;

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>{isEditing ? "Editar Agendamento" : "Novo Agendamento"}</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-2">
              <FormField control={form.control} name="title" render={({ field }) => (
                <FormItem><FormLabel>Título</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )}/>
              <FormField control={form.control} name="patient_id" render={({ field }) => (
                <FormItem><FormLabel>Paciente</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoadingPatients}>
                    <FormControl><SelectTrigger><SelectValue placeholder="Selecione um paciente" /></SelectTrigger></FormControl>
                    <SelectContent>
                      {isLoadingPatients ? <FormItem className="p-2">Carregando...</FormItem> : allPatients.map((p: Patient) => <SelectItem key={p.id} value={p.id}>{p.full_name}</SelectItem>)}
                    </SelectContent>
                  </Select><FormMessage />
                </FormItem>
              )}/>
              <div className="grid grid-cols-2 gap-4">
                <FormField control={form.control} name="start_time" render={({ field }) => {
                  const startDate = field.value ? parseISO(field.value) : new Date();

                  const handleStartTimeChange = (newStartTime: string) => {
                    field.onChange(newStartTime);

                    // Preservar a duração atual se já existir um horário de término
                    const currentEndTime = form.getValues("end_time");
                    const currentStartTime = form.getValues("start_time");

                    let durationMinutes = 50; // Duração padrão

                    // Se já existem horários de início e fim, calcular a duração atual
                    if (currentStartTime && currentEndTime) {
                      const currentStart = parseISO(currentStartTime);
                      const currentEnd = parseISO(currentEndTime);
                      durationMinutes = Math.round((currentEnd.getTime() - currentStart.getTime()) / 60000);
                    }

                    // Aplicar a duração preservada ao novo horário de início
                    const startDateTime = parseISO(newStartTime);
                    const endDateTime = addMinutes(startDateTime, durationMinutes);
                    form.setValue("end_time", format(endDateTime, "yyyy-MM-dd'T'HH:mm:ss"));
                  };

                  return (
                    <FormItem><FormLabel>Início</FormLabel><FormControl>
                       <Popover>
                            <PopoverTrigger asChild>
                                <Button variant={"outline"} className={cn("w-full justify-start text-left font-normal", !field.value && "text-muted-foreground")}>
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {field.value ? format(parseISO(field.value), "dd/MM/yy HH:mm") : <span>Escolha uma data</span>}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                                <Calendar mode="single" selected={startDate} onSelect={(d: Date | undefined) => {
                                  if (d) {
                                    const newStartTime = format(d, "yyyy-MM-dd'T'HH:mm:ss");
                                    handleStartTimeChange(newStartTime);
                                  }
                                }} initialFocus />
                                <div className="p-3 border-t border-border">
                                   <TimeSelector
                                     value={startDate}
                                     onChange={(d) => { handleStartTimeChange(format(d, "yyyy-MM-dd'T'HH:mm:ss")); }}
                                     interval={15}
                                   />
                                </div>
                            </PopoverContent>
                        </Popover>
                    </FormControl><FormMessage /></FormItem>
                  );
                }}/>
                <FormField control={form.control} name="end_time" render={({ field }) => {
                  const startTime = form.watch("start_time");
                  const startDate = startTime ? parseISO(startTime) : new Date();
                  const endDate = field.value ? parseISO(field.value) : new Date();
                  
                  return (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>Término</FormLabel>
                        <span className="text-xs text-muted-foreground">
                          {startTime && field.value && `Duração: ${Math.round((parseISO(field.value).getTime() - parseISO(startTime).getTime()) / 60000)} min`}
                        </span>
                      </div>
                      <FormControl>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant={"outline"} className={cn("w-full justify-start text-left font-normal", !field.value && "text-muted-foreground")}>
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {field.value ? format(parseISO(field.value), "dd/MM/yy HH:mm") : <span>Escolha uma data</span>}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar mode="single" selected={endDate} onSelect={(d: Date | undefined) => d && field.onChange(format(d, "yyyy-MM-dd'T'HH:mm:ss"))} initialFocus />
                            <div className="p-3 border-t border-border">
                              <TimeSelector 
                                value={endDate} 
                                onChange={(d) => field.onChange(format(d, "yyyy-MM-dd'T'HH:mm:ss"))}
                                interval={15}
                              />
                              {startTime && (
                                <DurationPicker
                                  startTime={startDate}
                                  endTime={endDate}
                                  onSelectDuration={(endTime) => field.onChange(format(endTime, "yyyy-MM-dd'T'HH:mm:ss"))}
                                />
                              )}
                            </div>
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}/>
              </div>
              <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                      <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                  <SelectTrigger>
                                      <SelectValue placeholder="Selecione o status" />
                                  </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                  {statusOptions.map(option => (
                                      <SelectItem key={option.value} value={option.value}>
                                          {option.label}
                                      </SelectItem>
                                  ))}
                              </SelectContent>
                          </Select>
                          <FormMessage />
                      </FormItem>
                  )}
              />
              <FormField control={form.control} name="notes" render={({ field }) => (
                <FormItem><FormLabel>Observações (Opcional)</FormLabel><FormControl><Textarea {...field} value={field.value ?? ''} /></FormControl><FormMessage /></FormItem>
              )}/>
              <RecurrenceInput
                value={recurrenceData}
                onChange={setRecurrenceData}
                disabled={isEditing || isLoading}
                className="pt-4"
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => { onOpenChange(false); }} disabled={isLoading}>Cancelar</Button>
                <Button type="submit" disabled={isLoading}>{isLoading ? "Salvando..." : isEditing ? "Salvar Alterações" : "Criar Agendamento"}</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <RecurrenceModifyDialog
        open={showRecurrenceDialog}
        onOpenChange={setShowRecurrenceDialog}
        title="Editar Consulta Recorrente"
        description="Como você deseja aplicar as alterações a esta série de consultas?"
        actionText="Salvar Alterações"
        onAction={handleUpdateRecurrence}
        onCancel={() => { setShowRecurrenceDialog(false); }}
        isUpdate={true}
      />
    </>
  );
}

// Funções auxiliares
function getStatusLabel(status: string): string {
  switch (status) {
    case 'scheduled': return 'Agendado';
    case 'confirmed': return 'Confirmado';
    case 'completed': return 'Realizado';
    case 'cancelled': return 'Cancelado';
    case 'rescheduled': return 'Remarcado';
    case 'no_show': return 'Falta';
    default: return status;
  }
}

function getAppointmentTypeLabel(type: string): string {
  switch (type) {
    case 'consultation': return 'Consulta padrão';
    case 'evaluation': return 'Avaliação';
    case 'therapy_session': return 'Sessão de terapia';
    case 'follow_up': return 'Retorno';
    case 'report_review': return 'Revisão de relatório';
    default: return type;
  }
}

function getColorBackground(color: string): string {
  const colorMap: Record<string, string> = {
    'blue': '#93c5fd',
    'green': '#86efac',
    'purple': '#d8b4fe',
    'red': '#fca5a5',
    'orange': '#fdba74',
    'amber': '#fcd34d',
    'pink': '#f9a8d4'
  };

  return colorMap[color] || '#93c5fd';
}

import { Appointment } from "@/features/calendar/types/appointment.schema";

export type CalendarAppointment = Appointment & {
  start: Date;
  end: Date;
};

export const getStatusLabel = (status: string): string => {
  switch (status) {
    case 'scheduled':
      return 'Agendado';
    case 'confirmed':
      return 'Confirmado';
    case 'completed':
      return 'Realizado';
    case 'cancelled':
      return 'Cancelado';
    case 'rescheduled':
      return 'Remarcado';
    case 'no_show':
      return 'Falta';
    default:
      return status;
  }
};

export const checkForConflicts = (
  start: Date,
  end: Date,
  appointments: CalendarAppointment[],
  excludeId?: string
): CalendarAppointment[] => {
  return appointments.filter(apt =>
    apt.id !== excludeId &&
    (start < apt.end && end > apt.start)
  );
};

// Organizar compromissos sobrepostos para exibição
export interface OrganizedAppointment<T extends CalendarAppointment> {
  appointment: T;
  column: number;
  totalColumns: number;
}

export const organizeOverlappingAppointments = <T extends CalendarAppointment>(
  appointments: T[]
): OrganizedAppointment<T>[] => {
  if (appointments.length === 0) return [];

  // Ordenar por horário de início
  const sortedAppointments = [...appointments].sort(
    (a, b) => a.start.getTime() - b.start.getTime()
  );

  const groups: T[][] = [];

  // Agrupar compromissos que se sobrepõem
  for (const appointment of sortedAppointments) {
    let placed = false;

    for (const group of groups) {
      const lastAppointment = group[group.length - 1];
      if (appointment.start >= lastAppointment.end) {
        group.push(appointment);
        placed = true;
        break;
      }
    }

    if (!placed) {
      groups.push([appointment]);
    }
  }

  // Determinar a coluna e número total de colunas para cada compromisso
  const organized: OrganizedAppointment<T>[] = [];

  for (const group of groups) {
    for (let i = 0; i < group.length; i++) {
      organized.push({
        appointment: group[i],
        column: i,
        totalColumns: group.length
      });
    }
  }

  return organized;
};

interface AppointmentPosition {
  top: string;
  left: string;
  width: string;
  height: string;
}

export const calculateAppointmentViewPosition = (
  appointment: CalendarAppointment,
  dayStart: Date,
  dayEnd: Date,
  column: number,
  totalColumns: number
): AppointmentPosition => {
  // Calcular a posição vertical (início)
  const totalMinutesInDay = (dayEnd.getTime() - dayStart.getTime()) / (1000 * 60);
  const startMinutesFromDayStart = (appointment.start.getTime() - dayStart.getTime()) / (1000 * 60);
  const endMinutesFromDayStart = (appointment.end.getTime() - dayStart.getTime()) / (1000 * 60);

  const topPercentage = (startMinutesFromDayStart / totalMinutesInDay) * 100;
  const heightPercentage = ((endMinutesFromDayStart - startMinutesFromDayStart) / totalMinutesInDay) * 100;

  // Calcular a posição horizontal (coluna)
  const columnWidth = 100 / totalColumns;
  const leftPercentage = column * columnWidth;
  return {
    top: `${topPercentage}%`,
    left: `${leftPercentage}%`,
    width: `${columnWidth}%`,
    height: `${heightPercentage}%`
  };
};

export function getPositionFromEvent(
  event: React.MouseEvent | React.TouchEvent,
  container: HTMLElement
): { x: number; y: number } {
  const rect = container.getBoundingClientRect();
  let clientX: number;
  let clientY: number;

  if ('touches' in event) {
    // Evento de toque
    clientX = event.touches[0].clientX;
    clientY = event.touches[0].clientY;
  } else {
    // Evento de mouse
    clientX = event.clientX;
    clientY = event.clientY;
  }

  return {
    x: clientX - rect.left,
    y: clientY - rect.top
  };
}
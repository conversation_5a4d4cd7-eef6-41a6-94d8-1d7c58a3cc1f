import React from 'react';
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/shared/ui/dialog';
import { InteractiveForm } from '@/features/auth/components/forms/InteractiveForm';

interface InteractiveFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  content: string;
  onSubmit: (formData: Record<string, any>) => void;
}

export function InteractiveFormDialog({
  isOpen,
  onClose,
  title,
  description,
  content,
  onSubmit,
}: InteractiveFormDialogProps) {
  const handleSubmit = (formData: Record<string, any>) => {
    onSubmit(formData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        <InteractiveForm
          title=""
          content={content}
          onSubmit={handleSubmit}
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  );
}

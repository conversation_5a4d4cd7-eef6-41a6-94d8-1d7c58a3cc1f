import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/shared/ui/dialog';
import { Button } from '@/shared/ui/button';
import { Progress } from '@/shared/ui/progress';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { 
  ChevronLeft, 
  ChevronRight, 
  Eye, 
  FileText, 
  Download,
  CheckCircle,
} from 'lucide-react';
import { WizardSection } from './WizardSection';
import { FinishButton } from './FinishButton';
import { DynamicModel, FormSection, ValidationError } from './ModelEditor';

export interface FormResponse {
  fieldId: string;
  value: unknown;
}

interface ModelApplicationWizardProps {
  isOpen: boolean;
  onClose: () => void;
  model: DynamicModel;
  patient?: {
    id: string;
    name: string;
    [key: string]: unknown;
  };
  appointment?: {
    id: string;
    date: string;
    time: string;
    [key: string]: unknown;
  };
  onFinish: (responses: FormResponse[], outputType: 'note' | 'pdf' | 'docx') => void;
}

export function ModelApplicationWizard({
  isOpen,
  onClose,
  model,
  patient,
  appointment,
  onFinish,
}: ModelApplicationWizardProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [responses, setResponses] = useState<FormResponse[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [showPreview, setShowPreview] = useState(false);

  // Evaluate conditional logic to determine visible sections
  const visibleSections = useMemo(() => {
    // Verificar se o modelo tem form schema definido
    if (!model.form?.sections) {
      return [];
    }
    return evaluateConditionals(model.form.sections, responses);
  }, [model.form, responses]);

  const currentSection = visibleSections[currentStep];
  const isLastStep = currentStep === visibleSections.length - 1;
  const canGoNext = currentStep < visibleSections.length - 1;
  const canGoPrevious = currentStep > 0;

  const handleResponseChange = (fieldId: string, value: unknown) => {
    const newResponses = responses.filter(r => r.fieldId !== fieldId);
    if (value !== undefined && value !== null && value !== '') {
      newResponses.push({ fieldId, value });
    }
    setResponses(newResponses);

    // Clear validation errors for this field
    setValidationErrors(prev => prev.filter(error => error.fieldId !== fieldId));
  };

  const validateCurrentSection = (): boolean => {
    if (!currentSection) return true;

    const errors: ValidationError[] = [];
    const responseMap = responses.reduce((acc, response) => {
      acc[response.fieldId] = response.value;
      return acc;
    }, {} as Record<string, unknown>);

    currentSection.fields.forEach(field => {
      const value = responseMap[field.id];
      
      // Required field validation
      if (field.required && (value === undefined || value === null || value === '')) {
        errors.push({
          fieldId: field.id,
          message: 'Este campo é obrigatório',
          type: 'required',
        });
      }

      // Additional validations based on field type and validation rules
      if (value && field.validation) {
        const fieldErrors = validateField(field.id, value, field.validation);
        errors.push(...fieldErrors);
      }
    });

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const goToNextStep = () => {
    if (validateCurrentSection() && canGoNext) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const goToPreviousStep = () => {
    if (canGoPrevious) {
      setCurrentStep(prev => prev - 1);
      setValidationErrors([]); // Clear errors when going back
    }
  };

  const handleShowPreview = () => {
    setShowPreview(true);
  };

  const handleFinish = (outputType: 'note' | 'pdf' | 'docx') => {
    if (validateCurrentSection()) {
      onFinish(responses, outputType);
    }
  };

  const progressPercentage = ((currentStep + 1) / visibleSections.length) * 100;

  if (showPreview) {
    return (
      <ModelPreviewDialog
        isOpen={isOpen}
        onClose={() => { setShowPreview(false); }}
        onBack={() => { setShowPreview(false); }}
        model={model}
        responses={responses}
        patient={patient}
        appointment={appointment}
        onFinish={handleFinish}
      />
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header with progress */}
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            {model.title}
          </DialogTitle>
          <DialogDescription>{model.description}</DialogDescription>
          <div className="mt-4 space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>
                Etapa {currentStep + 1} de {visibleSections.length}
                {currentSection && `: ${currentSection.title}`}
              </span>
              <span>{Math.round(progressPercentage)}% concluído</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        </DialogHeader>

        {/* Section content */}
        <ScrollArea className="flex-1 px-6 max-h-[50vh]">
          {currentSection ? (
            <WizardSection
              section={currentSection}
              responses={responses}
              errors={validationErrors}
              onChange={handleResponseChange}
              patient={patient}
              appointment={appointment}
            />
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Nenhuma seção disponível para exibir</p>
            </div>
          )}
        </ScrollArea>

        {/* Footer with navigation */}
        <DialogFooter className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={goToPreviousStep}
            disabled={!canGoPrevious}
            className="flex items-center"
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Anterior
          </Button>

          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={handleShowPreview}
              className="flex items-center"
            >
              <Eye className="h-4 w-4 mr-2" />
              Visualizar
            </Button>

            {isLastStep ? (
              <FinishButton
                model={model}
                responses={responses}
                onFinish={handleFinish}
                disabled={validationErrors.length > 0}
              />
            ) : (
              <Button 
                onClick={goToNextStep}
                disabled={!canGoNext}
                className="flex items-center"
              >
                Próximo
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Helper function to evaluate conditional logic
function evaluateConditionals(sections: FormSection[], responses: FormResponse[]): FormSection[] {
  const responseMap = responses.reduce((acc, response) => {
    acc[response.fieldId] = response.value;
    return acc;
  }, {} as Record<string, unknown>);

  return sections.filter(section => {
    if (!section.conditional) return true;
    
    const rule = section.conditional;
    const fieldValue = responseMap[rule.fieldId];
    
    return evaluateCondition(rule, fieldValue);
  });
}

// Helper function to evaluate a single condition
function evaluateCondition(rule: unknown, fieldValue: unknown): boolean {
  if (!rule || typeof rule !== 'object') return false;

  const ruleObj = rule as { value?: unknown; operator?: string };
  const ruleValue = ruleObj.value;

  switch (ruleObj.operator) {
    case 'equals':
      return fieldValue === ruleValue;
    case 'not_equals':
      return fieldValue !== ruleValue;
    case 'contains':
      if (typeof fieldValue === 'string' && typeof ruleValue === 'string') {
        return fieldValue.toLowerCase().includes(ruleValue.toLowerCase());
      }
      return false;
    case 'not_contains':
      if (typeof fieldValue === 'string' && typeof ruleValue === 'string') {
        return !fieldValue.toLowerCase().includes(ruleValue.toLowerCase());
      }
      return true;
    case 'greater_than':
      return Number(fieldValue) > Number(ruleValue);
    case 'less_than':
      return Number(fieldValue) < Number(ruleValue);
    default:
      return true;
  }
}

// Helper function to validate a field
function validateField(fieldId: string, value: unknown, validation: any): ValidationError[] {
  const errors: ValidationError[] = [];

  if (validation.minLength && typeof value === 'string' && value.length < validation.minLength) {
    errors.push({
      fieldId,
      message: `Mínimo de ${validation.minLength} caracteres`,
      type: 'minLength',
    });
  }

  if (validation.maxLength && typeof value === 'string' && value.length > validation.maxLength) {
    errors.push({
      fieldId,
      message: `Máximo de ${validation.maxLength} caracteres`,
      type: 'maxLength',
    });
  }

  if (validation.min && Number(value) < validation.min) {
    errors.push({
      fieldId,
      message: `Valor mínimo: ${validation.min}`,
      type: 'min',
    });
  }

  if (validation.max && Number(value) > validation.max) {
    errors.push({
      fieldId,
      message: `Valor máximo: ${validation.max}`,
      type: 'max',
    });
  }

  return errors;
}

// Import the preview dialog component (will be created next)
import { ModelPreviewDialog } from './ModelPreviewDialog';

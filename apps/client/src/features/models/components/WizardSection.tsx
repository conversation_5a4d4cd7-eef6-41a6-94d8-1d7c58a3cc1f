import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from '@/shared/ui/card';
import { DynamicFieldRenderer } from './DynamicFieldRenderer';
import { FormSection, FormResponse, ValidationError } from './ModelEditor';

interface WizardSectionProps {
  section: FormSection;
  responses: FormResponse[];
  errors: ValidationError[];
  onChange: (fieldId: string, value: unknown) => void;
  patient?: {
    id: string;
    name: string;
    [key: string]: unknown;
  };
  appointment?: {
    id: string;
    date: string;
    time: string;
    [key: string]: unknown;
  };
}

export function WizardSection({
  section,
  responses,
  errors,
  onChange,
  patient,
  appointment,
}: WizardSectionProps) {
  // Convert responses array to map for easier lookup
  const responseMap = responses.reduce((acc, response) => {
    (acc as Record<string, unknown>)[response.fieldId] = response.value;
    return acc;
  }, {} as Record<string, unknown>);

  // Convert errors array to map for easier lookup
  const errorMap = errors.reduce((acc, error) => {
    (acc as Record<string, unknown>)[error.fieldId] = error;
    return acc;
  }, {} as Record<string, unknown>);

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="px-0 pb-6">
        <CardTitle className="text-xl">{section.title}</CardTitle>
        {section.description && (
          <CardDescription className="text-base">
            {section.description}
          </CardDescription>
        )}
      </CardHeader>
      
      <CardContent className="px-0 space-y-6">
        {section.fields.map((field) => {
          const value = responseMap[field.id];
          const error = errorMap[field.id];

          return (
            <DynamicFieldRenderer
              key={field.id}
              field={field}
              value={value}
              error={error as any}
              onChange={onChange}
              patient={patient}
              appointment={appointment}
            />
          );
        })}
      </CardContent>
    </Card>
  );
}

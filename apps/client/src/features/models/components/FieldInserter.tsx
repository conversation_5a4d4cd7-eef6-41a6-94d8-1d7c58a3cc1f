import React from 'react';
import { Button } from '@/shared/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/shared/ui/dropdown-menu';
import { 
  FormInput,
  Type,
  AlignLeft,
  ChevronDown,
  Circle,
  CheckSquare,
  Calendar,
  Hash,
} from 'lucide-react';

interface FieldInserterProps {
  onInsert: (fieldType: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'number') => void;
}

const FIELD_TYPES = [
  {
    type: 'text' as const,
    icon: Type,
    label: 'Campo de Texto',
    description: 'Entrada de texto simples'
  },
  {
    type: 'textarea' as const,
    icon: AlignLeft,
    label: 'Área de Texto',
    description: 'Entrada de texto longo'
  },
  {
    type: 'select' as const,
    icon: ChevronDown,
    label: 'Lista Suspensa',
    description: 'Seleção única de opções'
  },
  {
    type: 'radio' as const,
    icon: Circle,
    label: 'Múltipla Escolha',
    description: 'Seleção única com botões'
  },
  {
    type: 'checkbox' as const,
    icon: CheckSquare,
    label: 'Caixa de Seleção',
    description: 'Seleção verdadeiro/falso'
  },
  {
    type: 'date' as const,
    icon: Calendar,
    label: 'Campo de Data',
    description: 'Seletor de data'
  },
  {
    type: 'number' as const,
    icon: Hash,
    label: 'Campo Numérico',
    description: 'Entrada de números'
  },
];

export function FieldInserter({ onInsert }: FieldInserterProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" title="Inserir campo dinâmico">
          <FormInput className="h-4 w-4 mr-1" />
          Campo
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64" align="start">
        <DropdownMenuLabel>Campos Dinâmicos</DropdownMenuLabel>
        
        {FIELD_TYPES.map((field) => (
          <DropdownMenuItem
            key={field.type}
            onClick={() => { onInsert(field.type); }}
            className="flex items-start space-x-3 p-3"
          >
            <field.icon className="h-4 w-4 mt-0.5 text-muted-foreground" />
            <div className="flex-1 space-y-1">
              <div className="text-sm font-medium">{field.label}</div>
              <div className="text-xs text-muted-foreground">{field.description}</div>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
} from '@/shared/ui/dialog';
import { ModelFormEditor } from '@/features/models/components/ModelFormEditor';
import { PlaceholderContext } from '@/shared/lib/placeholder-engine';
import { Button } from '@/shared/ui/button';
import { Maximize2, Minimize2, X, Save } from 'lucide-react';
import { cn } from '@/shared/lib/utils';

interface ModelDocumentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  modelContent: string;
  contextData: PlaceholderContext;
  initialTitle?: string;
  onSubmit: (title: string, generatedContent: string) => void;
}

export function ModelDocumentDialog({
  isOpen,
  onClose,
  modelContent,
  contextData,
  initialTitle,
  onSubmit,
}: ModelDocumentDialogProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [activeTab, setActiveTab] = useState<'form' | 'document' | 'split' | 'builder'>('split');
  const [title, setTitle] = useState(initialTitle || '');
  const [isDraft, setIsDraft] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const handleSubmit = (title: string, generatedContent: string) => {
    onSubmit(title, generatedContent);
    onClose();
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleSaveDraft = () => {
    setIsDraft(true);
    setLastSaved(new Date());
    // Lógica para salvar rascunho
  };

  // Componente para a barra de ferramentas superior
  const ToolbarComponent = () => (
    <div className="flex items-center justify-between p-2 border-b bg-[#1e1b2e] text-white">
      <div className="flex items-center space-x-2">
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="bg-transparent border-none focus:outline-none focus:ring-0 text-lg font-medium w-[250px] text-white"
          placeholder="Título do documento"
        />
        {lastSaved && (
          <span className="text-xs text-gray-400">
            Último: {lastSaved.toLocaleTimeString()}
          </span>
        )}
      </div>

      <div className="flex items-center gap-3">
        <div className="flex">
          <button
            className={cn(
              "px-2 py-1.5 text-xs rounded-none",
              activeTab === 'form' ? "bg-[#8b5cf6] text-white" : "bg-transparent text-white hover:bg-[#2d2a3d]"
            )}
            onClick={() => setActiveTab('form')}
          >
            Formulário
          </button>
          <button
            className={cn(
              "px-2 py-1.5 text-xs rounded-none",
              activeTab === 'document' ? "bg-[#8b5cf6] text-white" : "bg-transparent text-white hover:bg-[#2d2a3d]"
            )}
            onClick={() => setActiveTab('document')}
          >
            Documento
          </button>
          <button
            className={cn(
              "px-2 py-1.5 text-xs rounded-none",
              activeTab === 'split' ? "bg-[#8b5cf6] text-white" : "bg-transparent text-white hover:bg-[#2d2a3d]"
            )}
            onClick={() => setActiveTab('split')}
          >
            Dividido
          </button>
          <button
            className={cn(
              "px-2 py-1.5 text-xs rounded-none",
              activeTab === 'builder' ? "bg-[#8b5cf6] text-white" : "bg-transparent text-white hover:bg-[#2d2a3d]"
            )}
            onClick={() => setActiveTab('builder')}
          >
            Construtor
          </button>
        </div>

        <Button variant="outline" size="sm" onClick={handleSaveDraft} className="h-7 text-xs border-gray-600 text-white hover:bg-[#2d2a3d] hover:text-white whitespace-nowrap px-2 ml-2">
          <Save className="h-3 w-3 mr-1" />
          Rascunho
        </Button>

        <Button
          size="sm"
          className="h-7 text-xs bg-[#8b5cf6] hover:bg-[#7c4dff] text-white px-2 ml-2"
          onClick={() => {
            if (typeof window !== 'undefined' && (window as any).handleModelFormSubmit) {
              (window as any).handleModelFormSubmit();
            }
          }}
        >
          <Save className="h-3 w-3 mr-1" />
          Salvar
        </Button>

        {isFullscreen ? (
          <Button variant="ghost" size="icon" onClick={toggleFullscreen} className="h-7 w-7 text-white hover:bg-[#2d2a3d] p-1 ml-4">
            <Minimize2 className="h-3.5 w-3.5" />
          </Button>
        ) : (
          <Button variant="ghost" size="icon" onClick={toggleFullscreen} className="h-7 w-7 text-white hover:bg-[#2d2a3d] p-1 ml-4">
            <Maximize2 className="h-3.5 w-3.5" />
          </Button>
        )}

        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="h-7 w-7 text-white hover:bg-[#2d2a3d] p-1 ml-1"
        >
          <X className="h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  );

  // Se estiver em modo de tela cheia, renderizar sem o Dialog
  if (isFullscreen && isOpen) {
    return (
      <div className="fixed inset-0 z-50 bg-[#121016] flex flex-col">

        <ToolbarComponent />
        <div className="flex-1 overflow-hidden" style={{ height: 'calc(100vh - 60px)' }}>
          <ModelFormEditor
            modelContent={modelContent}
            contextData={contextData}
            initialTitle={title}
            onSubmit={handleSubmit}
            onCancel={onClose}
            onToggleFullscreen={toggleFullscreen}
            isFullscreen={isFullscreen}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            darkMode={true}
          />
        </div>
      </div>
    );
  }

  // Modo normal (dialog)
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[98vw] max-h-[98vh] w-[1400px] h-[850px] min-w-[1000px] min-h-[700px] p-0 overflow-hidden bg-[#121016] border-[#2d2a3d]" style={{ maxHeight: 'calc(100vh - 20px)' }}>

        <ToolbarComponent />
        <div className="flex-1 overflow-hidden" style={{ height: 'calc(98vh - 60px)' }}>
          <ModelFormEditor
            modelContent={modelContent}
            contextData={contextData}
            initialTitle={title}
            onSubmit={handleSubmit}
            onCancel={onClose}
            onToggleFullscreen={toggleFullscreen}
            isFullscreen={isFullscreen}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            darkMode={true}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}

import React, { useState } from 'react';
import { Button } from '@/shared/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/shared/ui/dropdown-menu';
import { 
  CheckCircle, 
  ChevronDown, 
  FileText, 
  Download, 
  StickyNote,
} from 'lucide-react';
import { DynamicModel, FormResponse } from './ModelEditor';

interface FinishButtonProps {
  model: DynamicModel;
  responses: FormResponse[];
  onFinish: (outputType: 'note' | 'pdf' | 'docx') => void;
  disabled?: boolean;
}

export function FinishButton({ model, responses, onFinish, disabled }: FinishButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleFinish = async (outputType: 'note' | 'pdf' | 'docx') => {
    setIsLoading(true);
    try {
       onFinish(outputType);
    } finally {
      setIsLoading(false);
    }
  };

  // Get available output types from model configuration
  const availableOutputs = [];

  if (model.outputs?.note ?? model.outputTypes.includes('note')) {
    availableOutputs.push({
      type: 'note' as const,
      label: 'Salvar como Nota',
      description: 'Adicionar às notas de sessão',
      icon: StickyNote,
    });
  }

  if (model.outputs?.pdf ?? model.outputTypes.includes('pdf')) {
    availableOutputs.push({
      type: 'pdf' as const,
      label: 'Gerar PDF',
      description: 'Baixar documento PDF',
      icon: FileText,
    });
  }

  if (model.outputs?.docx ?? model.outputTypes.includes('docx')) {
    availableOutputs.push({
      type: 'docx' as const,
      label: 'Gerar DOCX',
      description: 'Baixar documento Word',
      icon: Download,
    });
  }

  // If only one output type is available, show a simple button
  if (availableOutputs.length === 1) {
    const output = availableOutputs[0];
    return (
      <Button
        onClick={() => handleFinish(output.type)}
        disabled={disabled ?? isLoading}
        className="flex items-center"
      >
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            Processando...
          </>
        ) : (
          <>
            <output.icon className="h-4 w-4 mr-2" />
            {output.label}
          </>
        )}
      </Button>
    );
  }

  // If multiple output types are available, show a dropdown
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          disabled={disabled || isLoading}
          className="flex items-center"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Processando...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Finalizar
              <ChevronDown className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        {availableOutputs.map((output) => (
          <DropdownMenuItem
            key={output.type}
            onClick={() => handleFinish(output.type)}
            className="flex items-start space-x-3 p-3"
          >
            <output.icon className="h-4 w-4 mt-0.5 text-muted-foreground" />
            <div className="flex-1 space-y-1">
              <div className="text-sm font-medium">{output.label}</div>
              <div className="text-xs text-muted-foreground">{output.description}</div>
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

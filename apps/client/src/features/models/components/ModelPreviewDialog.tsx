import React, { useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from '@/shared/ui/dialog';
import { But<PERSON> } from '@/shared/ui/button';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { 
  ArrowLeft, 
  Eye, 
  User, 
  Calendar, 
  Clock,
} from 'lucide-react';
import { FinishButton } from './FinishButton';
import { DynamicModel, FormResponse } from './ModelEditor';

interface ModelPreviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  model: DynamicModel;
  responses: FormResponse[];
  patient?: {
    id: string;
    name: string;
    [key: string]: unknown;
  };
  appointment?: {
    id: string;
    date: string;
    time: string;
    [key: string]: unknown;
  };
  onFinish: (outputType: 'note' | 'pdf' | 'docx') => void;
}

export function ModelPreviewDialog({
  isOpen,
  onClose,
  onBack,
  model,
  responses,
  patient,
  appointment,
  onFinish,
}: ModelPreviewDialogProps) {
  // Convert responses to a map for easier lookup
  const responseMap = useMemo(() => {
    return responses.reduce((acc: Record<string, any>, response) => {
      acc[response.fieldId] = response.value;
      return acc;
    }, {});
  }, [responses]);

  // Create context data for placeholder processing
  const contextData = useMemo(() => {
    const now = new Date();
    
    return {
      paciente: {
        nome: patient?.name ?? 'Nome do Paciente',
        idade: patient?.age || '',
        dataNascimento: patient?.birthDate || '',
        telefone: patient?.phone || '',
        email: patient?.email || '',
        endereco: patient?.address || '',
        responsavel: patient?.guardian || '',
        ...patient,
      },
      sessao: {
        data: appointment?.date ?? now.toLocaleDateString('pt-BR'),
        hora: appointment?.time ?? now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
        duracao: appointment?.duration || '50 minutos',
        tipo: appointment?.type || 'Consulta',
        numero: appointment?.sessionNumber || '1',
        ...appointment,
      },
      profissional: {
        nome: 'Dr. Profissional',
        registro: 'CRP 00/000000',
        especialidade: 'Especialidade',
        telefone: '(00) 0000-0000',
        email: '<EMAIL>',
      },
      sistema: {
        dataAtual: now.toLocaleDateString('pt-BR'),
        horaAtual: now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
        nomeClinica: 'Clínica Evolua Care',
      },
      form: responseMap,
    };
  }, [patient, appointment, responseMap]);

  // Process the template content with placeholders and form responses
  const processedContent = useMemo(() => {
    if (!model.template.content) return '';

    // This is a simplified version - in a real implementation,
    // you would need to properly process the TipTap JSON content
    let content = JSON.stringify(model.template.content);
    
    // Process placeholders
    const placeholderRegex = /\[([^|\]]+)(?:\|([^|\]]+))?(?:\|([^\]]+))?\]/g;
    content = content.replace(placeholderRegex, (match, key, transform, fallback) => {
      const keys = key.split('.');
      let value = contextData;
      
      for (const k of keys) {
        value = (value as any)[k];
      }
      
      if (value !== undefined && value !== null) {
        let processedValue = String(value);
        
        // Apply transformations
        if (transform) {
          const [transformType] = transform.split(':');
          switch (transformType) {
            case 'uppercase':
              processedValue = processedValue.toUpperCase();
              break;
            case 'lowercase':
              processedValue = processedValue.toLowerCase();
              break;
            case 'capitalize':
              processedValue = processedValue.charAt(0).toUpperCase() + processedValue.slice(1).toLowerCase();
              break;
          }
        }
        
        return processedValue;
      }
      
      return fallback || `[${key}]`;
    });

    // For demo purposes, convert back to a readable format
    // In a real implementation, you'd render the TipTap content properly
    return content.replace(/"/g, '').replace(/\{|\}/g, '').replace(/,/g, ' ');
  }, [model.template.content, contextData]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Visualização: {model.title}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-12 gap-6 flex-1 overflow-hidden">
          {/* Context Information */}
          <div className="col-span-4 space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  Informações do Contexto
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {patient && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Paciente</h4>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p><strong>Nome:</strong> {patient.name}</p>
                      {patient.age && <p><strong>Idade:</strong> {patient.age}</p>}
                      {patient.phone && <p><strong>Telefone:</strong> {patient.phone}</p>}
                    </div>
                  </div>
                )}

                {appointment && (
                  <div>
                    <h4 className="text-sm font-medium mb-2 flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      Sessão
                    </h4>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p><strong>Data:</strong> {appointment.date}</p>
                      <p><strong>Hora:</strong> {appointment.time}</p>
                      {appointment.type && <p><strong>Tipo:</strong> {appointment.type as any}</p>}
                    </div>
                  </div>
                )}

                {responses.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Respostas do Formulário</h4>
                    <ScrollArea className="h-32">
                      <div className="text-sm text-muted-foreground space-y-1">
                        {responses.map((response) => (
                          <p key={response.fieldId}>
                            <strong>{response.fieldId}:</strong> {String(response.value)}
                          </p>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Preview Content */}
          <div className="col-span-8">
            <Card className="h-full">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Documento Gerado</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[50vh]">
                  <div className="prose prose-sm max-w-none">
                    <h1 className="text-xl font-bold mb-4">{model.title}</h1>
                    
                    {/* This is a simplified preview - in a real implementation,
                        you would properly render the TipTap content */}
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {processedContent || 'Conteúdo do modelo será exibido aqui após o processamento...'}
                    </div>

                    {/* Show form responses in a structured way */}
                    {responses.length > 0 && (
                      <div className="mt-6 p-4 bg-muted/20 rounded-md">
                        <h3 className="text-sm font-medium mb-3">Dados do Formulário:</h3>
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          {responses.map((response) => (
                            <div key={response.fieldId} className="flex justify-between">
                              <span className="font-medium">{response.fieldId}:</span>
                              <span className="text-muted-foreground">{String(response.value)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onBack} className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar ao Formulário
          </Button>

          <FinishButton
            model={model}
            responses={responses}
            onFinish={onFinish}
          />
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

import React, { useState, useEffect, useMemo } from 'react';
import { Editor } from '@tiptap/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { <PERSON><PERSON> } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Textarea } from '@/shared/ui/textarea';
import { Label } from '@/shared/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { RadioGroup, RadioGroupItem } from '@/shared/ui/radio-group';
import { Checkbox } from '@/shared/ui/checkbox';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { 
  Eye, 
  Play, 
  RotateCcw,
  User,
  Calendar,
  FileText,
} from 'lucide-react';
import { DynamicModel, DynamicField, SelectOption } from './ModelEditor';

interface LivePreviewProps {
  editor: Editor;
  model: DynamicModel;
  formResponses: Record<string, unknown>;
  onFormResponsesChange: (responses: Record<string, unknown>) => void;
}

// Mock data for preview
const MOCK_CONTEXT = {
  paciente: {
    nome: 'João Silva',
    idade: '35',
    dataNascimento: '1988-05-15',
    telefone: '(11) 99999-9999',
    email: '<EMAIL>',
    endereco: 'Rua das Flores, 123 - São Paulo/SP',
    responsavel: 'Maria Silva',
  },
  sessao: {
    data: '2024-01-19',
    hora: '14:30',
    duracao: '50 minutos',
    tipo: 'Terapia Individual',
    numero: '15',
  },
  profissional: {
    nome: 'Dr. Ana Santos',
    registro: 'CRP 06/123456',
    especialidade: 'Psicologia Clínica',
    telefone: '(11) 3333-3333',
    email: '<EMAIL>',
  },
  sistema: {
    dataAtual: new Date().toLocaleDateString('pt-BR'),
    horaAtual: new Date().toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
    nomeClinica: 'Clínica Evolua Care',
  },
};

export function LivePreview({ editor, model, formResponses, onFormResponsesChange }: LivePreviewProps) {
  const [activeTab, setActiveTab] = useState<'form' | 'preview'>('form');
  const [dynamicFields, setDynamicFields] = useState<DynamicField[]>([]);

  // Extract dynamic fields from editor content
  useEffect(() => {
    if (!editor) return;

    const fields: DynamicField[] = [];
    const doc = editor.state.doc;

    doc.descendants((node) => {
      if (node.type.name === 'dynamicField' && node.attrs) {
        const field: DynamicField = {
          id: node.attrs.fieldId || `field-${Date.now()}`,
          type: node.attrs.fieldType || 'text',
          label: node.attrs.label || 'Campo',
          placeholder: node.attrs.placeholder,
          required: node.attrs.required || false,
          validation: node.attrs.validation,
          options: node.attrs.options || [],
          conditional: node.attrs.conditional,
        };
        fields.push(field);
      }
    });

    setDynamicFields(fields);
  }, [editor, editor?.state.doc]);

  // Generate preview HTML with processed placeholders and form responses
  const previewHtml = useMemo(() => {
    if (!editor) return '';

    let html = editor.getHTML();
    
    // Process placeholders with mock context
    const processPlaceholders = (text: string, context: Record<string, unknown>): string => {
      const placeholderRegex = /\[([^|\]]+)(?:\|([^|\]]+))?(?:\|([^\]]+))?\]/g;
      
      return text.replace(placeholderRegex, (match, key, transform, fallback) => {
        const keys = key.split('.');
        let value: any = context;

        for (const k of keys) {
          if (value && typeof value === 'object' && k in value) {
            value = value[k];
          } else {
            value = undefined;
            break;
          }
        }
        
        if (value !== undefined && value !== null) {
          let processedValue = String(value);
          
          // Apply transformations
          if (transform) {
            const [transformType, format] = transform.split(':');
            switch (transformType) {
              case 'uppercase':
                processedValue = processedValue.toUpperCase();
                break;
              case 'lowercase':
                processedValue = processedValue.toLowerCase();
                break;
              case 'capitalize':
                processedValue = processedValue.charAt(0).toUpperCase() + processedValue.slice(1).toLowerCase();
                break;
              case 'date-format':
                try {
                  // Verificar se value é uma string, número ou Date válido
                  let dateValue: Date;
                  if (value instanceof Date) {
                    dateValue = value;
                  } else if (typeof value === 'string' || typeof value === 'number') {
                    dateValue = new Date(value);
                  } else {
                    throw new Error('Invalid date value');
                  }

                  if (isNaN(dateValue.getTime())) {
                    throw new Error('Invalid date');
                  }

                  processedValue = dateValue.toLocaleDateString('pt-BR');
                } catch {
                  processedValue = String(value);
                }
                break;
            }
          }
          
          return processedValue;
        }
        
        return fallback || match;
      });
    };

    // Process form field responses
    const processFormFields = (text: string, responses: Record<string, unknown>): string => {
      // Replace dynamic field nodes with their values
      dynamicFields.forEach(field => {
        const value = responses[field.id];
        if (value !== undefined && value !== null) {
          // This is a simplified replacement - in a real implementation,
          // you'd need to properly parse and replace the HTML nodes
          const fieldRegex = new RegExp(`<dynamic-field[^>]*data-field-id="${field.id}"[^>]*>.*?</dynamic-field>`, 'g');
          text = text.replace(fieldRegex, `<span class="form-field-value">${value}</span>`);
        }
      });
      return text;
    };

    html = processPlaceholders(html, MOCK_CONTEXT);
    html = processFormFields(html, formResponses);
    
    return html;
  }, [editor, formResponses, dynamicFields]);

  const handleFieldChange = (fieldId: string, value: unknown) => {
    const newResponses = { ...formResponses, [fieldId]: value };
    onFormResponsesChange(newResponses);
  };

  const handleResetForm = () => {
    onFormResponsesChange({});
  };

  const getStringValue = (val: unknown): string => {
    if (typeof val === 'string') return val;
    if (typeof val === 'number') return String(val);
    if (val === null || val === undefined) return '';
    return String(val);
  };

  const renderFormField = (field: DynamicField) => {
    const value = formResponses[field.id];
    const stringValue = getStringValue(value);
    const fieldId = `preview-${field.id}`;

    switch (field.type) {
      case 'text':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={fieldId} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={fieldId}
              value={stringValue}
              onChange={(e) => { handleFieldChange(field.id, e.target.value); }}
              placeholder={field.placeholder}
              className="text-sm"
            />
          </div>
        );

      case 'textarea':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={fieldId} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Textarea
              id={fieldId}
              value={stringValue}
              onChange={(e) => { handleFieldChange(field.id, e.target.value); }}
              placeholder={field.placeholder}
              className="text-sm resize-none"
              rows={3}
            />
          </div>
        );

      case 'select':
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Select value={stringValue} onValueChange={(val) => { handleFieldChange(field.id, val); }}>
              <SelectTrigger className="text-sm">
                <SelectValue placeholder={field.placeholder ?? 'Selecione uma opção'} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option: SelectOption) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );

      case 'radio':
        return (
          <div key={field.id} className="space-y-2">
            <Label className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <RadioGroup value={stringValue} onValueChange={(val) => { handleFieldChange(field.id, val); }}>
              {field.options?.map((option: SelectOption) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem value={option.value} />
                  <Label className="text-sm">{option.label}</Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        );

      case 'checkbox':
        return (
          <div key={field.id} className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={value === true}
                onCheckedChange={(checked) => { handleFieldChange(field.id, checked); }}
              />
              <Label className="text-sm font-medium">
                {field.label}
                {field.required && <span className="text-destructive ml-1">*</span>}
              </Label>
            </div>
          </div>
        );

      case 'date':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={fieldId} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={fieldId}
              type="date"
              value={stringValue}
              onChange={(e) => handleFieldChange(field.id, e.target.value)}
              className="text-sm"
            />
          </div>
        );

      case 'number':
        return (
          <div key={field.id} className="space-y-2">
            <Label htmlFor={fieldId} className="text-sm font-medium">
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={fieldId}
              type="number"
              value={stringValue}
              onChange={(e) => handleFieldChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              className="text-sm"
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center justify-between">
          <div className="flex items-center">
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleResetForm}
            className="h-6 px-2"
            title="Limpar formulário"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={(value) => { setActiveTab(value as 'form' | 'preview'); }}>
          <div className="px-4 pb-2">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="form" className="text-xs">
                <FileText className="h-3 w-3 mr-1" />
                Formulário
              </TabsTrigger>
              <TabsTrigger value="preview" className="text-xs">
                <Eye className="h-3 w-3 mr-1" />
                Resultado
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="form" className="mt-0">
            <ScrollArea className="h-[calc(100vh-280px)]">
              <div className="px-4 space-y-4">
                {dynamicFields.length > 0 ? (
                  dynamicFields.map(renderFormField)
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Nenhum campo dinâmico encontrado</p>
                    <p className="text-xs">Adicione campos ao modelo para testá-los aqui</p>
                  </div>
                )}
                <div className="h-4" />
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="preview" className="mt-0">
            <ScrollArea className="h-[calc(100vh-280px)]">
              <div className="px-4">
                <div 
                  className="prose prose-sm max-w-none text-sm"
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                />
                <div className="h-4" />
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

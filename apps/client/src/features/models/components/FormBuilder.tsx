import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Label } from '@/shared/ui/label';
import { Textarea } from '@/shared/ui/textarea';
import { Checkbox } from '@/shared/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { RadioGroup, RadioGroupItem } from '@/shared/ui/radio-group';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/shared/ui/card';
import { Separator } from '@/shared/ui/separator';
import { cn } from '@/shared/lib/utils';
import { FormField } from '@/shared/lib/placeholder-engine';
import {
  Trash2,
  MoveUp,
  MoveDown,
  Plus,
  FormInput,
  TextCursorInput,
  CheckSquare,
  ListFilter,
  CalendarDays,
  GripVertical,
  Settings,
  Copy
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/shared/ui/dropdown-menu';

interface FormBuilderProps {
  fields: FormField[];
  onChange: (fields: FormField[]) => void;
  onInsertField: (field: FormField) => void;
  darkMode?: boolean;
}

export function FormBuilder({ fields, onChange, onInsertField, darkMode = false }: FormBuilderProps) {
  const [editingField, setEditingField] = useState<FormField | null>(null);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  // Função para adicionar um novo campo
  const addField = (type: string) => {
    const id = `field_${Date.now()}`;
    let newField: FormField = {
      id,
      type: type as any,
      label: getDefaultLabel(type),
      required: false,
    };

    // Adicionar propriedades específicas por tipo
    switch (type) {
      case 'textarea':
        newField.rows = 3;
        newField.placeholder = 'Digite aqui...';
        break;
      case 'select':
      case 'radio':
        newField.options = ['Opção 1', 'Opção 2', 'Opção 3'];
        break;
      case 'number':
        newField.min = 0;
        newField.max = 100;
        newField.step = 1;
        break;
      case 'checkbox':
        newField.defaultValue = false;
        break;
      case 'text':
        newField.placeholder = 'Digite aqui...';
        break;
    }

    setEditingField(newField);
    setEditingIndex(null);
  };

  // Função para obter o rótulo padrão com base no tipo
  const getDefaultLabel = (type: string): string => {
    switch (type) {
      case 'text': return 'Campo de texto';
      case 'textarea': return 'Área de texto';
      case 'number': return 'Campo numérico';
      case 'select': return 'Lista de seleção';
      case 'checkbox': return 'Caixa de seleção';
      case 'radio': return 'Botões de opção';
      case 'date': return 'Data';
      case 'time': return 'Hora';
      case 'datetime': return 'Data e hora';
      default: return 'Campo';
    }
  };

  // Função para editar um campo existente
  const editField = (index: number) => {
    setEditingField({ ...fields[index] });
    setEditingIndex(index);
  };

  // Função para salvar as alterações em um campo
  const saveField = () => {
    if (!editingField) return;

    if (editingIndex !== null) {
      // Editando um campo existente
      const updatedFields = [...fields];
      updatedFields[editingIndex] = editingField;
      onChange(updatedFields);
    } else {
      // Adicionando um novo campo
      onChange([...fields, editingField]);
      
      // Também inserir o campo no editor
      onInsertField(editingField);
    }

    setEditingField(null);
    setEditingIndex(null);
  };

  // Função para cancelar a edição
  const cancelEdit = () => {
    setEditingField(null);
    setEditingIndex(null);
  };

  // Função para remover um campo
  const removeField = (index: number) => {
    const updatedFields = [...fields];
    updatedFields.splice(index, 1);
    onChange(updatedFields);
  };

  // Função para mover um campo para cima
  const moveUp = (index: number) => {
    if (index === 0) return;
    const updatedFields = [...fields];
    const temp = updatedFields[index];
    updatedFields[index] = updatedFields[index - 1];
    updatedFields[index - 1] = temp;
    onChange(updatedFields);
  };

  // Função para mover um campo para baixo
  const moveDown = (index: number) => {
    if (index === fields.length - 1) return;
    const updatedFields = [...fields];
    const temp = updatedFields[index];
    updatedFields[index] = updatedFields[index + 1];
    updatedFields[index + 1] = temp;
    onChange(updatedFields);
  };

  // Função para duplicar um campo
  const duplicateField = (index: number) => {
    const fieldToDuplicate = { ...fields[index] };
    fieldToDuplicate.id = `field_${Date.now()}`;
    fieldToDuplicate.label = `${fieldToDuplicate.label} (cópia)`;
    const updatedFields = [...fields];
    updatedFields.splice(index + 1, 0, fieldToDuplicate);
    onChange(updatedFields);
  };

  // Função para atualizar um atributo do campo em edição
  const updateEditingField = (key: string, value: any) => {
    if (!editingField) return;
    setEditingField({
      ...editingField,
      [key]: value,
    });
  };

  // Função para renderizar o formulário de edição com base no tipo de campo
  const renderFieldEditor = () => {
    if (!editingField) return null;

    return (
      <Card className={cn(
        "mb-4 border",
        darkMode ? "bg-[#1e1b2e] border-[#2d2a3d] text-white" : ""
      )}>
        <CardHeader className={cn(
          "pb-2",
          darkMode ? "border-[#2d2a3d]" : ""
        )}>
          <CardTitle className="text-lg flex items-center">
            {getFieldIcon(editingField.type)}
            <span className="ml-2">
              {editingIndex !== null ? 'Editar' : 'Adicionar'} {getFieldTypeName(editingField.type)}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 pt-0">
          <div className="space-y-2">
            <Label htmlFor="field-label" className={cn(
              "text-sm font-medium",
              darkMode ? "text-gray-300" : ""
            )}>
              Rótulo do campo
            </Label>
            <Input
              id="field-label"
              value={editingField.label}
              onChange={(e) => updateEditingField('label', e.target.value)}
              className={cn(
                "h-9",
                darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white" : ""
              )}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="field-id" className={cn(
              "text-sm font-medium",
              darkMode ? "text-gray-300" : ""
            )}>
              ID do campo (usado nos placeholders)
            </Label>
            <Input
              id="field-id"
              value={editingField.id}
              onChange={(e) => updateEditingField('id', e.target.value)}
              className={cn(
                "h-9",
                darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white" : ""
              )}
            />
            <p className={cn(
              "text-xs",
              darkMode ? "text-gray-400" : "text-muted-foreground"
            )}>
              Este ID será usado no placeholder: [Form.{editingField.id}:{editingField.type}]
            </p>
          </div>

          {/* Campos específicos por tipo */}
          {(editingField.type === 'text' || editingField.type === 'textarea') && (
            <div className="space-y-2">
              <Label htmlFor="field-placeholder" className={cn(
                "text-sm font-medium",
                darkMode ? "text-gray-300" : ""
              )}>
                Placeholder
              </Label>
              <Input
                id="field-placeholder"
                value={editingField.placeholder || ''}
                onChange={(e) => updateEditingField('placeholder', e.target.value)}
                className={cn(
                  "h-9",
                  darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white" : ""
                )}
              />
            </div>
          )}

          {editingField.type === 'textarea' && (
            <div className="space-y-2">
              <Label htmlFor="field-rows" className={cn(
                "text-sm font-medium",
                darkMode ? "text-gray-300" : ""
              )}>
                Número de linhas
              </Label>
              <Input
                id="field-rows"
                type="number"
                min="1"
                max="20"
                value={editingField.rows || 3}
                onChange={(e) => updateEditingField('rows', parseInt(e.target.value))}
                className={cn(
                  "h-9",
                  darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white" : ""
                )}
              />
            </div>
          )}

          {editingField.type === 'number' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="field-min" className={cn(
                  "text-sm font-medium",
                  darkMode ? "text-gray-300" : ""
                )}>
                  Valor mínimo
                </Label>
                <Input
                  id="field-min"
                  type="number"
                  value={editingField.min || 0}
                  onChange={(e) => updateEditingField('min', parseFloat(e.target.value))}
                  className={cn(
                    "h-9",
                    darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white" : ""
                  )}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="field-max" className={cn(
                  "text-sm font-medium",
                  darkMode ? "text-gray-300" : ""
                )}>
                  Valor máximo
                </Label>
                <Input
                  id="field-max"
                  type="number"
                  value={editingField.max || 100}
                  onChange={(e) => updateEditingField('max', parseFloat(e.target.value))}
                  className={cn(
                    "h-9",
                    darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white" : ""
                  )}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="field-step" className={cn(
                  "text-sm font-medium",
                  darkMode ? "text-gray-300" : ""
                )}>
                  Incremento
                </Label>
                <Input
                  id="field-step"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={editingField.step || 1}
                  onChange={(e) => updateEditingField('step', parseFloat(e.target.value))}
                  className={cn(
                    "h-9",
                    darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white" : ""
                  )}
                />
              </div>
            </>
          )}

          {(editingField.type === 'select' || editingField.type === 'radio') && (
            <div className="space-y-2">
              <Label htmlFor="field-options" className={cn(
                "text-sm font-medium",
                darkMode ? "text-gray-300" : ""
              )}>
                Opções (uma por linha)
              </Label>
              <Textarea
                id="field-options"
                value={(editingField.options || []).join('\n')}
                onChange={(e) => updateEditingField('options', e.target.value.split('\n').filter(Boolean))}
                rows={5}
                className={cn(
                  darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white" : ""
                )}
              />
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Checkbox
              id="field-required"
              checked={editingField.required || false}
              onCheckedChange={(checked) => updateEditingField('required', checked)}
              className={cn(
                darkMode ? "border-[#3d3a4d] data-[state=checked]:bg-[#8b5cf6] data-[state=checked]:border-[#8b5cf6]" : ""
              )}
            />
            <Label htmlFor="field-required" className={cn(
              "text-sm font-medium cursor-pointer",
              darkMode ? "text-gray-300" : ""
            )}>
              Campo obrigatório
            </Label>
          </div>
        </CardContent>
        <CardFooter className={cn(
          "flex justify-end space-x-2 pt-0",
          darkMode ? "border-[#2d2a3d]" : ""
        )}>
          <Button
            variant="outline"
            onClick={cancelEdit}
            className={cn(
              darkMode ? "bg-transparent border-[#3d3a4d] text-white hover:bg-[#2d2a3d] hover:text-white" : ""
            )}
          >
            Cancelar
          </Button>
          <Button
            onClick={saveField}
            className={cn(
              darkMode ? "bg-[#8b5cf6] hover:bg-[#7c4dff] text-white" : ""
            )}
          >
            {editingIndex !== null ? 'Atualizar' : 'Adicionar'} Campo
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Função para obter o ícone com base no tipo de campo
  const getFieldIcon = (type: string) => {
    switch (type) {
      case 'text': return <FormInput className="h-4 w-4" />;
      case 'textarea': return <TextCursorInput className="h-4 w-4" />;
      case 'number': return <FormInput className="h-4 w-4" />;
      case 'select': return <ListFilter className="h-4 w-4" />;
      case 'checkbox': return <CheckSquare className="h-4 w-4" />;
      case 'radio': return <ListFilter className="h-4 w-4" />;
      case 'date': return <CalendarDays className="h-4 w-4" />;
      case 'time': return <CalendarDays className="h-4 w-4" />;
      case 'datetime': return <CalendarDays className="h-4 w-4" />;
      default: return <FormInput className="h-4 w-4" />;
    }
  };

  // Função para obter o nome do tipo de campo
  const getFieldTypeName = (type: string): string => {
    switch (type) {
      case 'text': return 'Campo de texto';
      case 'textarea': return 'Área de texto';
      case 'number': return 'Campo numérico';
      case 'select': return 'Lista de seleção';
      case 'checkbox': return 'Caixa de seleção';
      case 'radio': return 'Botões de opção';
      case 'date': return 'Data';
      case 'time': return 'Hora';
      case 'datetime': return 'Data e hora';
      default: return 'Campo';
    }
  };

  // Renderizar a visualização de um campo
  const renderFieldPreview = (field: FormField, index: number) => {
    return (
      <Card key={field.id} className={cn(
        "mb-3 border",
        darkMode ? "bg-[#1e1b2e] border-[#2d2a3d] text-white" : ""
      )}>
        <div className="flex items-center p-3">
          <div className={cn(
            "mr-2 cursor-move",
            darkMode ? "text-gray-400" : "text-muted-foreground"
          )}>
            <GripVertical className="h-5 w-5" />
          </div>
          <div className="flex-1">
            <div className="flex items-center">
              {getFieldIcon(field.type)}
              <span className={cn(
                "ml-2 font-medium",
                darkMode ? "text-white" : ""
              )}>
                {field.label}
                {field.required && <span className="text-destructive ml-1">*</span>}
              </span>
            </div>
            <div className={cn(
              "text-xs mt-1",
              darkMode ? "text-gray-400" : "text-muted-foreground"
            )}>
              {getFieldTypeName(field.type)} • ID: {field.id}
            </div>
          </div>
          <div className="flex space-x-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => editField(index)}
              className={cn(
                "h-8 w-8",
                darkMode ? "hover:bg-[#2d2a3d] text-gray-300" : ""
              )}
              title="Editar campo"
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => duplicateField(index)}
              className={cn(
                "h-8 w-8",
                darkMode ? "hover:bg-[#2d2a3d] text-gray-300" : ""
              )}
              title="Duplicar campo"
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => moveUp(index)}
              disabled={index === 0}
              className={cn(
                "h-8 w-8",
                darkMode ? "hover:bg-[#2d2a3d] text-gray-300 disabled:text-gray-600" : ""
              )}
              title="Mover para cima"
            >
              <MoveUp className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => moveDown(index)}
              disabled={index === fields.length - 1}
              className={cn(
                "h-8 w-8",
                darkMode ? "hover:bg-[#2d2a3d] text-gray-300 disabled:text-gray-600" : ""
              )}
              title="Mover para baixo"
            >
              <MoveDown className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => removeField(index)}
              className={cn(
                "h-8 w-8",
                darkMode ? "hover:bg-[#2d2a3d] text-gray-300" : ""
              )}
              title="Remover campo"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className="space-y-4">
      {/* Formulário de edição de campo */}
      {editingField && renderFieldEditor()}

      {/* Lista de campos existentes */}
      <div className="space-y-2">
        {fields.length === 0 ? (
          <div className={cn(
            "text-center p-8 border border-dashed rounded-md",
            darkMode ? "border-[#3d3a4d] text-gray-400" : "border-gray-200 text-muted-foreground"
          )}>
            <p>Nenhum campo de formulário adicionado.</p>
            <p className="text-sm mt-1">Clique em "Adicionar Campo" para começar.</p>
          </div>
        ) : (
          fields.map((field, index) => renderFieldPreview(field, index))
        )}
      </div>

      {/* Botão para adicionar novo campo */}
      {!editingField && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button className={cn(
              "w-full",
              darkMode ? "bg-[#8b5cf6] hover:bg-[#7c4dff] text-white" : ""
            )}>
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Campo
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className={cn(
            darkMode ? "bg-[#1e1b2e] border-[#2d2a3d] text-white" : ""
          )}>
            <DropdownMenuItem onClick={() => addField('text')} className={cn(
              "flex items-center",
              darkMode ? "hover:bg-[#2d2a3d]" : ""
            )}>
              <FormInput className="h-4 w-4 mr-2" />
              <span>Campo de texto</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => addField('textarea')} className={cn(
              "flex items-center",
              darkMode ? "hover:bg-[#2d2a3d]" : ""
            )}>
              <TextCursorInput className="h-4 w-4 mr-2" />
              <span>Área de texto</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => addField('number')} className={cn(
              "flex items-center",
              darkMode ? "hover:bg-[#2d2a3d]" : ""
            )}>
              <FormInput className="h-4 w-4 mr-2" />
              <span>Campo numérico</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => addField('select')} className={cn(
              "flex items-center",
              darkMode ? "hover:bg-[#2d2a3d]" : ""
            )}>
              <ListFilter className="h-4 w-4 mr-2" />
              <span>Lista de seleção</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => addField('checkbox')} className={cn(
              "flex items-center",
              darkMode ? "hover:bg-[#2d2a3d]" : ""
            )}>
              <CheckSquare className="h-4 w-4 mr-2" />
              <span>Caixa de seleção</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => addField('radio')} className={cn(
              "flex items-center",
              darkMode ? "hover:bg-[#2d2a3d]" : ""
            )}>
              <ListFilter className="h-4 w-4 mr-2" />
              <span>Botões de opção</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => addField('date')} className={cn(
              "flex items-center",
              darkMode ? "hover:bg-[#2d2a3d]" : ""
            )}>
              <CalendarDays className="h-4 w-4 mr-2" />
              <span>Campo de data</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}

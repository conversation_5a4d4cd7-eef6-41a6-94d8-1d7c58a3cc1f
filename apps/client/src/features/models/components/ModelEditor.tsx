import React, { useState, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import TextAlign from '@tiptap/extension-text-align';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Placeholder from '@tiptap/extension-placeholder';

// Import our custom extensions
import { 
  DynamicFieldExtension, 
  ConditionalLogicExtension, 
  ValidationExtension 
} from '@/shared/lib/tiptap-extensions';
import { PlaceholderHighlight } from '@/features/models/components/PlaceholderExtension';

// Import components
import { ModelEditorToolbar } from './ModelEditorToolbar';
import { FieldsSidebar } from './FieldsSidebar';
import { LivePreview } from './LivePreview';
import { Card } from '@/shared/ui/card';
import { cn } from '@/shared/lib/utils';

export interface DynamicModel {
  id?: string;
  title: string;
  description: string;
  category: string;

  // Template TipTap
  template: {
    content: unknown; // TipTap JSON format
    placeholders?: DynamicPlaceholder[];
  };

  // Dynamic form
  form: {
    sections: FormSection[];
    validation?: ValidationRules;
    conditional?: ConditionalLogic[];
  };

  // Output settings
  outputTypes: string[];
  outputs?: {
    note: boolean;
    pdf: boolean;
    docx: boolean;
  };

  // Metadata
  isFavorite?: boolean;
  tags?: string[];
  usageCount?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface FormResponse {
  fieldId: string;
  value: unknown;
}

export interface ValidationError {
  fieldId: string;
  message: string;
  type?: string;
}

export interface DynamicPlaceholder {
  id: string;
  fieldId: string;
  label: string;
  transform?: 'uppercase' | 'lowercase' | 'capitalize' | 'date-format';
  fallback?: string;
}

export interface FormSection {
  id: string;
  title: string;
  description?: string;
  fields: DynamicField[];
  conditional?: ConditionalRule;
}

export interface DynamicField {
  id: string;
  type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'number';
  label: string;
  placeholder?: string;
  required: boolean;
  validation?: FieldValidation;
  options?: SelectOption[];
  conditional?: ConditionalRule;
}

export interface SelectOption {
  value: string;
  label: string;
}

export interface FieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
}

export interface ConditionalRule {
  fieldId: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
  value: unknown;
}

export interface ValidationRules {
  [fieldId: string]: FieldValidation;
}

export interface ConditionalLogic {
  fieldId: string;
  rules: ConditionalRule[];
}

interface ModelEditorProps {
  model?: DynamicModel;
  onChange: (model: DynamicModel) => void;
  className?: string;
}

export function ModelEditor({ model, onChange, className }: ModelEditorProps) {
  const [formResponses, setFormResponses] = useState<Record<string, unknown>>({});

  // Criar modelo padrão se não existir
  const defaultModel: DynamicModel = {
    id: '',
    title: '',
    description: '',
    category: 'general',
    template: {
      content: '',
      placeholders: [],
    },
    form: {
      sections: [],
      validation: {},
      conditional: [],
    },
    outputTypes: ['note'],
  };

  const currentModel = model ?? defaultModel;

  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextStyle,
      Color,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Placeholder.configure({
        placeholder: 'Digite o conteúdo do seu modelo aqui...',
      }),
      PlaceholderHighlight.configure({
        showPreview: true,
      }),
      DynamicFieldExtension,
      ConditionalLogicExtension.configure({
        formResponses,
      }),
      ValidationExtension.configure({
        formResponses,
        showInlineErrors: true,
      }),
    ],
    content: currentModel.template.content as any,
    onUpdate: ({ editor }) => {
      const updatedModel = {
        ...currentModel,
        template: {
          ...currentModel.template,
          content: editor.getJSON(),
        },
      };
      onChange(updatedModel);
    },
  });

  const handleInsertField = useCallback((fieldType: DynamicField['type']) => {
    if (!editor) return;

    const fieldId = `field-${Date.now()}`;
    const label = getFieldTypeLabel(fieldType);

    editor.commands.insertDynamicField({
      fieldType,
      label,
      fieldId,
      required: false,
    });
  }, [editor]);

  const handleInsertPlaceholder = useCallback((placeholderKey: string, transform?: string, fallback?: string) => {
    if (!editor) return;

    const placeholderData = {
      key: placeholderKey,
      transform: transform ? { type: transform as "lowercase" | "capitalize" | "uppercase" | "date-format" } : undefined,
      fallback,
    };

    editor.commands.insertPlaceholder(placeholderData);
  }, [editor]);

  const handleFormResponsesChange = useCallback((responses: Record<string, unknown>) => {
    setFormResponses(responses);
    
    if (editor) {
      editor.commands.updateFormResponses(responses);
    }
  }, [editor]);

  const getFieldTypeLabel = (type: DynamicField['type']): string => {
    const labels = {
      text: 'Campo de Texto',
      textarea: 'Área de Texto',
      select: 'Lista Suspensa',
      radio: 'Múltipla Escolha',
      checkbox: 'Caixa de Seleção',
      date: 'Campo de Data',
      number: 'Campo Numérico',
    };
    return labels[type] || 'Campo Dinâmico';
  };

  if (!editor) {
    return <div>Carregando editor...</div>;
  }

  return (
    <div className={cn("flex flex-col h-screen p-6", className)}>
      {/* Toolbar */}
      <div className="flex-shrink-0 mb-6">
        <ModelEditorToolbar
          editor={editor}
          onInsertPlaceholder={handleInsertPlaceholder}
          onInsertField={handleInsertField}
        />
      </div>

      {/* Editor e Preview */}
      <div className="flex-1 flex gap-8 min-h-0">
        {/* Editor principal */}
        <div className="flex-1">
          <Card className="h-full shadow-sm">
            <EditorContent
              editor={editor}
              className="prose max-w-none p-6 h-full overflow-y-auto"
            />
          </Card>
        </div>

        {/* Preview */}
        <div className="w-80 flex-shrink-0">
          <LivePreview
            editor={editor}
            model={currentModel}
            formResponses={formResponses}
            onFormResponsesChange={handleFormResponsesChange}
          />
        </div>
      </div>
    </div>
  );
}

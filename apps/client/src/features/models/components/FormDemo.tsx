import React, { useState } from 'react';
import { Button } from '@/shared/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/shared/ui/card';
import { InteractiveFormDialog } from '@/features/models/components/InteractiveFormDialog';
import { useToast } from '@/shared/hooks/use-toast';

// Exemplo de modelo com campos de formulário
const exampleFormModel = `
<h1>Formulário de Avaliação Inicial</h1>
<p>Por favor, preencha os campos abaixo para iniciar a avaliação.</p>

<form-text-input data-label="Nome do Paciente" data-field-id="patientName" data-required="true" data-placeholder="Digite o nome completo"></form-text-input>

<form-date-input data-label="Data de Nascimento" data-field-id="birthDate" data-required="true"></form-date-input>

<form-textarea data-label="Queixa Principal" data-field-id="mainComplaint" data-required="true" data-placeholder="Descreva a queixa principal" data-rows="4"></form-textarea>

<form-select data-label="Tipo de Avaliação" data-field-id="evaluationType" data-required="true" data-options="Avaliação Inicial;Reavaliação;Avaliação de Acompanhamento"></form-select>

<h2>Histórico</h2>

<form-checkbox data-label="Já realizou terapia anteriormente?" data-field-id="previousTherapy"></form-checkbox>

<form-textarea data-label="Observações Adicionais" data-field-id="additionalNotes" data-placeholder="Informações complementares"></form-textarea>
`;

export function FormDemo() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [lastSubmittedData, setLastSubmittedData] = useState<Record<string, any> | null>(null);
  const { toast } = useToast();

  const handleFormSubmit = (formData: Record<string, any>) => {
    setLastSubmittedData(formData);
    toast({
      title: 'Formulário enviado com sucesso!',
      description: 'Os dados foram recebidos e processados.',
    });
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Demonstração de Formulário Interativo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            Este é um exemplo de como os modelos com campos de formulário podem ser utilizados para criar formulários interativos.
            Clique no botão abaixo para abrir um formulário de exemplo.
          </p>
          <Button onClick={() => setIsDialogOpen(true)}>
            Abrir Formulário de Exemplo
          </Button>
        </CardContent>
      </Card>

      {lastSubmittedData && (
        <Card>
          <CardHeader>
            <CardTitle>Dados Enviados</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-md overflow-auto">
              {JSON.stringify(lastSubmittedData, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      <InteractiveFormDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        title="Formulário de Avaliação Inicial"
        description="Preencha os campos abaixo para iniciar a avaliação do paciente."
        content={exampleFormModel}
        onSubmit={handleFormSubmit}
      />
    </div>
  );
}

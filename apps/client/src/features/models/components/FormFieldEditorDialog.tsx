import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  Di<PERSON>Footer,
} from '@/shared/ui/dialog';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Label } from '@/shared/ui/label';
import { Textarea } from '@/shared/ui/textarea';
import { Checkbox } from '@/shared/ui/checkbox';
import { Separator } from '@/shared/ui/separator';
import { FormInput, TextCursorInput, ListFilter } from 'lucide-react';

interface FormFieldEditorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  fieldType: 'text' | 'textarea' | 'checkbox' | 'select' | 'date' | 'radio';
  initialValues: {
    label: string;
    fieldId?: string;
    placeholder?: string;
    required?: boolean;
    options?: string;
    rows?: number;
    defaultChecked?: boolean;
  };
  onSave: (values: {
    label: string;
    fieldId: string;
    placeholder?: string;
    required: boolean;
    options?: string;
    rows?: number;
    defaultChecked?: boolean;
  }) => void;
}

export function FormFieldEditorDialog({
  isOpen,
  onClose,
  fieldType,
  initialValues,
  onSave,
}: FormFieldEditorDialogProps) {
  const [values, setValues] = useState({
    label: '',
    fieldId: '',
    placeholder: '',
    required: false,
    options: '',
    rows: 3,
    defaultChecked: false,
  });

  useEffect(() => {
    if (isOpen) {
      setValues({
        label: initialValues.label || '',
        fieldId: initialValues.fieldId || '',
        placeholder: initialValues.placeholder || '',
        required: initialValues.required || false,
        options: initialValues.options || 'Opção 1;Opção 2;Opção 3',
        rows: initialValues.rows || 3,
        defaultChecked: initialValues.defaultChecked || false,
      });
    }
  }, [isOpen, initialValues]);

  const handleChange = (field: string, value: any) => {
    setValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      ...values,
      fieldId: values.fieldId || `field-${Date.now()}`,
    });
  };

  const getFieldTypeIcon = () => {
    switch (fieldType) {
      case 'text':
        return <FormInput className="h-5 w-5 mr-2" />;
      case 'textarea':
        return <TextCursorInput className="h-5 w-5 mr-2" />;
      case 'select':
      case 'radio':
        return <ListFilter className="h-5 w-5 mr-2" />;
      default:
        return <FormInput className="h-5 w-5 mr-2" />;
    }
  };

  const getFieldTypeName = () => {
    switch (fieldType) {
      case 'text':
        return 'Campo de Texto';
      case 'textarea':
        return 'Área de Texto';
      case 'checkbox':
        return 'Caixa de Seleção';
      case 'select':
        return 'Lista de Seleção';
      case 'date':
        return 'Campo de Data';
      case 'radio':
        return 'Botões de Opção';
      default:
        return 'Campo';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            {getFieldTypeIcon()}
            Editar {getFieldTypeName()}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="label" className="text-right">
                Rótulo
              </Label>
              <Input
                id="label"
                value={values.label}
                onChange={(e) => handleChange('label', e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="fieldId" className="text-right">
                ID do Campo
              </Label>
              <Input
                id="fieldId"
                value={values.fieldId}
                onChange={(e) => handleChange('fieldId', e.target.value)}
                className="col-span-3"
                placeholder="Gerado automaticamente se vazio"
              />
            </div>
            
            {(fieldType === 'text' || fieldType === 'textarea') && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="placeholder" className="text-right">
                  Placeholder
                </Label>
                <Input
                  id="placeholder"
                  value={values.placeholder}
                  onChange={(e) => handleChange('placeholder', e.target.value)}
                  className="col-span-3"
                  placeholder="Digite aqui..."
                />
              </div>
            )}
            
            {fieldType === 'textarea' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="rows" className="text-right">
                  Linhas
                </Label>
                <Input
                  id="rows"
                  type="number"
                  min="1"
                  max="20"
                  value={values.rows}
                  onChange={(e) => handleChange('rows', parseInt(e.target.value))}
                  className="col-span-3"
                />
              </div>
            )}
            
            {(fieldType === 'select' || fieldType === 'radio') && (
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="options" className="text-right pt-2">
                  Opções
                </Label>
                <div className="col-span-3">
                  <Textarea
                    id="options"
                    value={values.options}
                    onChange={(e) => handleChange('options', e.target.value)}
                    placeholder="Opção 1;Opção 2;Opção 3"
                    className="resize-none"
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Separe as opções com ponto e vírgula (;)
                  </p>
                </div>
              </div>
            )}
            
            {fieldType === 'checkbox' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="defaultChecked" className="text-right">
                  Marcado por padrão
                </Label>
                <div className="flex items-center h-10">
                  <Checkbox
                    id="defaultChecked"
                    checked={values.defaultChecked}
                    onCheckedChange={(checked) => handleChange('defaultChecked', checked)}
                  />
                </div>
              </div>
            )}
            
            <Separator />
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="required" className="text-right">
                Obrigatório
              </Label>
              <div className="flex items-center h-10">
                <Checkbox
                  id="required"
                  checked={values.required}
                  onCheckedChange={(checked) => handleChange('required', checked)}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button type="submit">Salvar</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

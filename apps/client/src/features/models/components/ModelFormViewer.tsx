import React, { useState, useEffect } from 'react';
import { Input } from '@/shared/ui/input';
import { Textarea } from '@/shared/ui/textarea';
import { Checkbox } from '@/shared/ui/checkbox';
import { Label } from '@/shared/ui/label';
import { Button } from '@/shared/ui/button';
import { RadioGroup, RadioGroupItem } from '@/shared/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/shared/ui/select';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/shared/ui/card';
import { Alert, AlertDescription } from '@/shared/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { AlertCircle, Eye } from 'lucide-react';
import {
  PlaceholderContext,
  processPlaceholders,
  extractFormFields,
  replaceF<PERSON><PERSON>ields,
  FormField as PlaceholderFormField
} from '@/shared/lib/placeholder-engine';

// Estendendo a interface FormField para incluir campos adicionais específicos do componente
interface FormField extends PlaceholderFormField {
  // Campos adicionais específicos para o componente, se necessário
}

interface ModelFormViewerProps {
  modelContent: string;
  contextData: PlaceholderContext;
  initialTitle?: string;
  onSubmit: (title: string, generatedContent: string) => void;
  onCancel?: () => void;
  isSaving?: boolean;
}

export function ModelFormViewer({
  modelContent,
  contextData,
  initialTitle = '',
  onSubmit,
  onCancel,
  isSaving = false,
}: ModelFormViewerProps) {
  const [title, setTitle] = useState(initialTitle);
  const [fields, setFields] = useState<FormField[]>([]);
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [processedContent, setProcessedContent] = useState('');

  // Estado para controlar a aba ativa (formulário ou pré-visualização)
  const [activeTab, setActiveTab] = useState<string>('form');

  // Processar o conteúdo do modelo e extrair campos de formulário
  useEffect(() => {
    // Primeiro, processar os placeholders padrão
    const contentWithPlaceholders = processPlaceholders(modelContent, contextData);
    setProcessedContent(contentWithPlaceholders);

    // Extrair campos usando a nova sintaxe [Form.campo:tipo:opções]
    const newSyntaxFields = extractFormFields(contentWithPlaceholders);

    // Extrair campos usando a sintaxe antiga (tags HTML)
    const parser = new DOMParser();
    const doc = parser.parseFromString(contentWithPlaceholders, 'text/html');
    const oldSyntaxFields: FormField[] = [];

    // Extrair campos de texto
    doc.querySelectorAll('form-text-input').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `text-${oldSyntaxFields.length}`;
      oldSyntaxFields.push({
        type: 'text',
        id,
        label: element.getAttribute('data-label') || 'Campo de texto',
        required: element.getAttribute('data-required') === 'true',
        placeholder: element.getAttribute('data-placeholder') || '',
      });
    });

    // Extrair áreas de texto
    doc.querySelectorAll('form-textarea').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `textarea-${oldSyntaxFields.length}`;
      oldSyntaxFields.push({
        type: 'textarea',
        id,
        label: element.getAttribute('data-label') || 'Área de texto',
        required: element.getAttribute('data-required') === 'true',
        placeholder: element.getAttribute('data-placeholder') || '',
        rows: element.getAttribute('data-rows') ? parseInt(element.getAttribute('data-rows') || '3', 10) : 3,
      });
    });

    // Extrair checkboxes
    doc.querySelectorAll('form-checkbox').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `checkbox-${oldSyntaxFields.length}`;
      oldSyntaxFields.push({
        type: 'checkbox',
        id,
        label: element.getAttribute('data-label') || 'Opção',
        required: element.getAttribute('data-required') === 'true',
        defaultValue: element.getAttribute('data-default-checked') === 'true',
      });
    });

    // Extrair selects
    doc.querySelectorAll('form-select').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `select-${oldSyntaxFields.length}`;
      const optionsStr = element.getAttribute('data-options') || 'Opção 1;Opção 2;Opção 3';
      oldSyntaxFields.push({
        type: 'select',
        id,
        label: element.getAttribute('data-label') || 'Selecione uma opção',
        required: element.getAttribute('data-required') === 'true',
        options: optionsStr.split(';'),
      });
    });

    // Extrair campos de data
    doc.querySelectorAll('form-date-input').forEach((element) => {
      const id = element.getAttribute('data-field-id') || `date-${oldSyntaxFields.length}`;
      oldSyntaxFields.push({
        type: 'date',
        id,
        label: element.getAttribute('data-label') || 'Data',
        required: element.getAttribute('data-required') === 'true',
      });
    });

    // Combinar campos de ambas as sintaxes
    const allFields = [...newSyntaxFields, ...oldSyntaxFields];
    setFields(allFields);

    // Inicializar valores do formulário
    const initialValues: Record<string, any> = {};
    allFields.forEach((field) => {
      if (field.type === 'checkbox') {
        initialValues[field.id] = field.defaultValue || false;
      } else if (field.type === 'radio' && field.options && field.options.length > 0) {
        initialValues[field.id] = field.defaultValue || field.options[0];
      } else if (field.type === 'select' && field.options && field.options.length > 0) {
        initialValues[field.id] = field.defaultValue || field.options[0];
      } else if (field.type === 'number') {
        initialValues[field.id] = field.defaultValue !== undefined ? field.defaultValue : '';
      } else {
        initialValues[field.id] = field.defaultValue || '';
      }
    });

    setFormValues(initialValues);
  }, [modelContent, contextData]);

  // Manipular mudanças nos campos
  const handleChange = (id: string, value: any) => {
    setFormValues((prev) => ({
      ...prev,
      [id]: value,
    }));

    // Limpar erro se o campo foi preenchido
    if (errors[id] && value) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  // Validar formulário
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    fields.forEach((field) => {
      if (field.required) {
        const value = formValues[field.id];
        if (field.type === 'checkbox') {
          if (!value) {
            newErrors[field.id] = `${field.label} é obrigatório`;
          }
        } else if (!value || (typeof value === 'string' && value.trim() === '')) {
          newErrors[field.id] = `${field.label} é obrigatório`;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Gerar HTML final
  const generateFinalHtml = (): string => {
    // Criar contexto com os valores do formulário
    const contextWithForm: PlaceholderContext = {
      ...contextData,
      form: formValues
    };

    // Processar placeholders padrão primeiro
    let finalHtml = processPlaceholders(modelContent, contextWithForm);

    // Processar campos de formulário da nova sintaxe
    finalHtml = replaceFormFields(finalHtml, formValues);

    // Criar um parser para manipular o HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(finalHtml, 'text/html');

    // Substituir campos de formulário da sintaxe antiga (tags HTML)
    fields.forEach((field) => {
      // Pular campos da nova sintaxe (já processados acima)
      if (field.id.includes('.')) return;

      const value = formValues[field.id];
      let formattedValue = '';

      if (field.type === 'checkbox') {
        formattedValue = value ? 'Sim' : 'Não';
      } else if (field.type === 'radio' || field.type === 'select') {
        formattedValue = value || '';
      } else if (field.type === 'number') {
        formattedValue = value !== undefined && value !== '' ? String(value) : '';
      } else {
        formattedValue = value || '';
      }

      // Criar o padrão de substituição para cada tipo de campo
      let tagPattern: RegExp;

      switch (field.type) {
        case 'text':
          tagPattern = new RegExp(`<form-text-input[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        case 'textarea':
          tagPattern = new RegExp(`<form-textarea[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        case 'checkbox':
          tagPattern = new RegExp(`<form-checkbox[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        case 'select':
          tagPattern = new RegExp(`<form-select[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        case 'date':
          tagPattern = new RegExp(`<form-date-input[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        default:
          tagPattern = new RegExp(`<form-[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
      }

      // Substituir a tag pelo valor formatado com formatação adequada
      switch (field.type) {
        case 'textarea':
          // Para áreas de texto, preservar quebras de linha
          finalHtml = finalHtml.replace(tagPattern, `<div class="form-field-result">
            <p class="form-field-label"><strong>${field.label}:</strong></p>
            <div class="form-field-value">${formattedValue.replace(/\n/g, '<br/>')}</div>
          </div>`);
          break;
        case 'checkbox':
        case 'radio':
          // Para checkboxes e radio buttons, mostrar texto sim/não
          finalHtml = finalHtml.replace(tagPattern, `<p class="form-field-result"><strong>${field.label}:</strong> ${formattedValue}</p>`);
          break;
        default:
          // Para outros campos, formato padrão
          finalHtml = finalHtml.replace(tagPattern, `<p class="form-field-result"><strong>${field.label}:</strong> ${formattedValue}</p>`);
      }
    });

    return finalHtml;
  };

  // Manipular envio do formulário
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      const finalHtml = generateFinalHtml();
      onSubmit(title, finalHtml);
    } else {
      // Rolar até o primeiro campo com erro
      const firstErrorId = Object.keys(errors)[0];
      const errorElement = document.getElementById(firstErrorId);
      if (errorElement) {
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };

  // Renderizar campos do formulário
  const renderField = (field: FormField) => {
    // Helper para renderizar mensagens de erro
    const renderError = (fieldId: string) => {
      if (!errors[fieldId]) return null;
      return (
        <p className="text-xs text-destructive flex items-center mt-1">
          <AlertCircle className="h-3 w-3 mr-1" />
          {errors[fieldId]}
        </p>
      );
    };

    switch (field.type) {
      case 'text':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              aria-invalid={!!errors[field.id]}
            />
            {renderError(field.id)}
          </div>
        );

      case 'textarea':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Textarea
              id={field.id}
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              rows={field.rows}
              aria-invalid={!!errors[field.id]}
            />
            {renderError(field.id)}
          </div>
        );

      case 'number':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="number"
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              min={field.min}
              max={field.max}
              step={field.step}
              aria-invalid={!!errors[field.id]}
            />
            {renderError(field.id)}
          </div>
        );

      case 'checkbox':
        return (
          <div className="space-y-2" key={field.id}>
            <div className="flex items-center space-x-2">
              <Checkbox
                id={field.id}
                checked={formValues[field.id] || false}
                onCheckedChange={(checked) => handleChange(field.id, checked)}
                required={field.required}
                aria-invalid={!!errors[field.id]}
              />
              <Label htmlFor={field.id} className="text-sm font-medium">
                {field.label}
                {field.required && <span className="text-destructive ml-1">*</span>}
              </Label>
            </div>
            {renderError(field.id)}
          </div>
        );

      case 'radio':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <RadioGroup
              value={formValues[field.id] || ''}
              onValueChange={(value) => handleChange(field.id, value)}
              required={field.required}
            >
              {field.options?.map((option) => (
                <div className="flex items-center space-x-2" key={option}>
                  <RadioGroupItem value={option} id={`${field.id}-${option}`} />
                  <Label htmlFor={`${field.id}-${option}`}>{option}</Label>
                </div>
              ))}
            </RadioGroup>
            {renderError(field.id)}
          </div>
        );

      case 'select':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Select
              value={formValues[field.id] || ''}
              onValueChange={(value) => handleChange(field.id, value)}
              required={field.required}
            >
              <SelectTrigger id={field.id} aria-invalid={!!errors[field.id]}>
                <SelectValue placeholder={field.label} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {renderError(field.id)}
          </div>
        );

      case 'date':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="date"
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              required={field.required}
              aria-invalid={!!errors[field.id]}
            />
            {renderError(field.id)}
          </div>
        );

      case 'time':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="time"
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              required={field.required}
              aria-invalid={!!errors[field.id]}
            />
            {renderError(field.id)}
          </div>
        );

      case 'datetime':
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="datetime-local"
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              required={field.required}
              aria-invalid={!!errors[field.id]}
            />
            {renderError(field.id)}
          </div>
        );

      default:
        // Fallback para tipos desconhecidos - usar campo de texto
        return (
          <div className="space-y-2" key={field.id}>
            <Label htmlFor={field.id}>
              {field.label} ({field.type})
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              aria-invalid={!!errors[field.id]}
            />
            {renderError(field.id)}
          </div>
        );
    }
  };

  // Renderizar conteúdo do modelo com campos de formulário
  const renderModelContent = () => {
    // Verificar se há campos de formulário
    if (fields.length === 0) {
      return (
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Este modelo não contém campos de formulário para preencher.
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <div className="space-y-4">
        {fields.map(renderField)}
      </div>
    );
  };

  // Renderizar pré-visualização do documento
  const renderPreview = () => {
    // Gerar HTML com os valores atuais
    const previewHtml = generateFinalHtml();

    return (
      <div className="border rounded-md p-4 bg-white">
        <div
          className="prose max-w-none"
          dangerouslySetInnerHTML={{ __html: previewHtml }}
        />
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Preencher Formulário</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="document-title">
              Título do Documento <span className="text-destructive">*</span>
            </Label>
            <Input
              id="document-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Digite o título do documento"
              required
            />
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="form">Formulário</TabsTrigger>
              <TabsTrigger value="preview">Pré-visualização</TabsTrigger>
            </TabsList>
            <TabsContent value="form" className="mt-4">
              {renderModelContent()}
            </TabsContent>
            <TabsContent value="preview" className="mt-4">
              {renderPreview()}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-end space-x-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSaving}>
              Cancelar
            </Button>
          )}
          <Button type="submit" disabled={!title.trim() || isSaving}>
            {isSaving ? 'Gerando...' : 'Gerar Documento'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}

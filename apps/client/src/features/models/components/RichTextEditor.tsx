import React, { useState, useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
// Importar nós customizados para formulários
import { TextInput, TextArea, Checkbox, Select, DateInput, Radio } from '@/shared/lib/tiptap-extensions';
import { Button } from "@/shared/ui/button";
import { Toggle } from "@/shared/ui/toggle";
import { Separator } from "@/shared/ui/separator";
import { cn } from "@/shared/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuGroup,
  DropdownMenuLabel,
} from "@/shared/ui/dropdown-menu";

import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Heading1,
  Heading2,
  Heading3,
  Table as TableIcon,
  Undo,
  Redo,
  BracesIcon,
  User,
  Calendar,
  Clock,
  FileText,
  Settings,
  FormInput,
  TextCursorInput,
  CheckSquare,
  ListFilter,
  CalendarDays,
} from "lucide-react";
import { PlaceholderHighlight } from "@/features/models/components/PlaceholderExtension";
import { PlaceholderContext, processPlaceholders } from "@/shared/lib/placeholder-engine";

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  error?: boolean;
  context?: PlaceholderContext;
  onApplyTemplate?: (processedContent: string) => void;
  onEditorReady?: (editor: any) => void;
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = "Digite o conteúdo do seu modelo aqui...",
  className,
  error,
  context,
  onApplyTemplate,
  onEditorReady
}: RichTextEditorProps) {

  const safeValue = value || "";

  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Placeholder.configure({
        placeholder,
      }),
      PlaceholderHighlight,
      // Adicionar nós customizados para formulários
      TextInput,
      TextArea,
      Checkbox,
      Select,
      DateInput,
      Radio,
    ],
    content: safeValue,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange(html);
    },
  });

  // Chamar onEditorReady quando o editor estiver pronto
  React.useEffect(() => {
    if (editor && onEditorReady) {
      onEditorReady(editor);
    }
  }, [editor, onEditorReady]);

  if (!editor) {
    return null;
  }

  const isBold = editor.isActive('bold');
  const isItalic = editor.isActive('italic');
  const isUnderline = editor.isActive('underline');
  const isStrike = editor.isActive('strike');
  const isBulletList = editor.isActive('bulletList');
  const isOrderedList = editor.isActive('orderedList');
  const isH1 = editor.isActive('heading', { level: 1 });
  const isH2 = editor.isActive('heading', { level: 2 });
  const isH3 = editor.isActive('heading', { level: 3 });
  const isAlignLeft = editor.isActive({ textAlign: 'left' });
  const isAlignCenter = editor.isActive({ textAlign: 'center' });
  const isAlignRight = editor.isActive({ textAlign: 'right' });

  const insertPlaceholder = (placeholder: string) => {
    editor.chain().focus().insertContent(placeholder).run();
  };

  // Processar o conteúdo com placeholders se houver contexto
  const processContent = () => {
    if (!context || !onApplyTemplate) return;

    // Processar placeholders com o contexto fornecido
    const processedContent = processPlaceholders(editor.getHTML(), context);

    onApplyTemplate(processedContent);
  };

  // Categorias de placeholders organizadas
  const placeholderCategories = [
    {
      category: 'Paciente',
      icon: <User className="h-4 w-4" />,
      placeholders: [
        { name: 'Paciente.Nome', display: 'Nome Completo' },
        { name: 'Paciente.DataNascimento|DD/MM/AAAA', display: 'Data de Nascimento' },
        { name: 'Paciente.Idade', display: 'Idade' },
        { name: 'Paciente.Telefone', display: 'Telefone' },
        { name: 'Paciente.Email', display: 'Email' },
        { name: 'Paciente.Endereco', display: 'Endereço' },
        { name: 'Paciente.Cidade', display: 'Cidade' },
        { name: 'Paciente.Estado', display: 'Estado' },
        { name: 'Paciente.CPF', display: 'CPF' },
        { name: 'Paciente.Escola', display: 'Escola' },
      ],
    },
    {
      category: 'Terapeuta',
      icon: <Settings className="h-4 w-4" />,
      placeholders: [
        { name: 'Terapeuta.Nome', display: 'Nome Completo' },
        { name: 'Terapeuta.PrimeiroNome', display: 'Primeiro Nome' },
        { name: 'Terapeuta.Email', display: 'Email' },
        { name: 'Terapeuta.Telefone', display: 'Telefone' },
      ],
    },
    {
      category: 'Sessão',
      icon: <FileText className="h-4 w-4" />,
      placeholders: [
        { name: 'Sessao.Data|DD/MM/AAAA', display: 'Data da Sessão' },
        { name: 'Sessao.Hora', display: 'Hora da Sessão' },
        { name: 'Sessao.Duracao', display: 'Duração' },
        { name: 'Sessao.Status', display: 'Status' },
      ],
    },
    {
      category: 'Sistema',
      icon: <Calendar className="h-4 w-4" />,
      placeholders: [
        { name: 'Sistema.DataAtual|DD/MM/AAAA', display: 'Data Atual' },
        { name: 'Sistema.HoraAtual', display: 'Hora Atual' },
        { name: 'Sistema.DiaSemana', display: 'Dia da Semana' },
        { name: 'Sistema.Mes', display: 'Mês Atual' },
        { name: 'Sistema.Ano', display: 'Ano Atual' },
      ],
    },
  ];

  return (
    <div className={cn("border rounded-md overflow-hidden", error && "border-destructive", className)}>
      <div className="bg-muted/50 border-b p-1 flex flex-wrap items-center gap-0.5">
        <div className="flex items-center">
          <Toggle
            size="sm"
            pressed={isBold}
            onPressedChange={() => editor.chain().focus().toggleBold().run()}
            aria-label="Negrito"
          >
            <Bold className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isItalic}
            onPressedChange={() => editor.chain().focus().toggleItalic().run()}
            aria-label="Itálico"
          >
            <Italic className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isUnderline}
            onPressedChange={() => editor.chain().focus().toggleUnderline().run()}
            aria-label="Sublinhado"
          >
            <UnderlineIcon className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isStrike}
            onPressedChange={() => editor.chain().focus().toggleStrike().run()}
            aria-label="Tachado"
          >
            <Strikethrough className="h-4 w-4" />
          </Toggle>
        </div>

        <Separator orientation="vertical" className="mx-1 h-6" />

        <div className="flex items-center">
          <Toggle
            size="sm"
            pressed={isH1}
            onPressedChange={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            aria-label="Título 1"
          >
            <Heading1 className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isH2}
            onPressedChange={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            aria-label="Título 2"
          >
            <Heading2 className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isH3}
            onPressedChange={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
            aria-label="Título 3"
          >
            <Heading3 className="h-4 w-4" />
          </Toggle>
        </div>

        <Separator orientation="vertical" className="mx-1 h-6" />

        <div className="flex items-center">
          <Toggle
            size="sm"
            pressed={isBulletList}
            onPressedChange={() => editor.chain().focus().toggleBulletList().run()}
            aria-label="Lista com marcadores"
          >
            <List className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isOrderedList}
            onPressedChange={() => editor.chain().focus().toggleOrderedList().run()}
            aria-label="Lista numerada"
          >
            <ListOrdered className="h-4 w-4" />
          </Toggle>
        </div>

        <Separator orientation="vertical" className="mx-1 h-6" />

        <div className="flex items-center">
          <Toggle
            size="sm"
            pressed={isAlignLeft}
            onPressedChange={() => editor.chain().focus().setTextAlign('left').run()}
            aria-label="Alinhar à esquerda"
          >
            <AlignLeft className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isAlignCenter}
            onPressedChange={() => editor.chain().focus().setTextAlign('center').run()}
            aria-label="Centralizar"
          >
            <AlignCenter className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isAlignRight}
            onPressedChange={() => editor.chain().focus().setTextAlign('right').run()}
            aria-label="Alinhar à direita"
          >
            <AlignRight className="h-4 w-4" />
          </Toggle>
        </div>

        <Separator orientation="vertical" className="mx-1 h-6" />

        <Button
          variant="ghost"
          size="icon"
          onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
          className="h-8 w-8"
          title="Inserir tabela"
        >
          <TableIcon className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-1 h-6" />

        {/* Dropdown para inserir elementos de formulário */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8"
              >
              <FormInput className="h-4 w-4 mr-1" />
              <span className="text-xs">Formulário</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem
              onClick={() => editor.chain().focus().setTextInput({ label: 'Campo de texto' }).run()}
              className="flex items-center"
            >
              <TextCursorInput className="h-4 w-4 mr-2" />
              <span>Campo de texto</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => editor.chain().focus().setTextArea({ label: 'Área de texto', rows: 3 }).run()}
              className="flex items-center"
            >
              <TextCursorInput className="h-4 w-4 mr-2" />
              <span>Área de texto</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => editor.chain().focus().setCheckbox({ label: 'Opção de seleção' }).run()}
              className="flex items-center"
            >
              <CheckSquare className="h-4 w-4 mr-2" />
              <span>Caixa de seleção</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => editor.chain().focus().setSelect({ label: 'Lista de opções', options: 'Opção 1;Opção 2;Opção 3' }).run()}
              className="flex items-center"
            >
              <ListFilter className="h-4 w-4 mr-2" />
              <span>Lista de seleção</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => editor.chain().focus().setDateInput({ label: 'Data' }).run()}
              className="flex items-center"
            >
              <CalendarDays className="h-4 w-4 mr-2" />
              <span>Campo de data</span>
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => editor.chain().focus().setRadio({ label: 'Botões de opção', options: 'Opção 1;Opção 2;Opção 3' }).run()}
              className="flex items-center"
            >
              <ListFilter className="h-4 w-4 mr-2" />
              <span>Botões de opção</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Separator orientation="vertical" className="mx-1 h-6" />

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8"
              >
              <BracesIcon className="h-4 w-4 mr-1" />
              <span className="text-xs">Placeholders</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-64 max-h-[400px] overflow-y-auto">
            {placeholderCategories.map((category) => (
              <div key={category.category}>
                <DropdownMenuLabel className="flex items-center">
                  {category.icon}
                  <span className="ml-2">{category.category}</span>
                </DropdownMenuLabel>
                <DropdownMenuGroup>
                  {category.placeholders.map((placeholder) => (
                    <DropdownMenuItem
                      key={placeholder.name}
                      onClick={() => insertPlaceholder(`[${placeholder.name}]`)}
                    >
                      {placeholder.display}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
              </div>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {context && onApplyTemplate && (
          <>
            <Separator orientation="vertical" className="mx-1 h-6" />
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={processContent}
            >
              <Clock className="h-4 w-4 mr-1" />
              <span className="text-xs">Processar Placeholders</span>
            </Button>
          </>
        )}

        <div className="ml-auto flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            className="h-8 w-8"
            title="Desfazer"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            className="h-8 w-8"
            title="Refazer"
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <EditorContent
        editor={editor}
        className="prose prose-sm max-w-none p-4 min-h-[300px] focus-visible:outline-none"
      />
    </div>
  );
}
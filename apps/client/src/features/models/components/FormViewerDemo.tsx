import React, { useState } from 'react';
import { But<PERSON> } from '@/shared/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/shared/ui/card';
import { ModelFormDialog } from '@/features/models/components/ModelFormDialog';
import { useToast } from '@/shared/hooks/use-toast';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/shared/ui/tabs';
import { PlaceholderContext } from '@/shared/lib/placeholder-engine';

// Exemplo de modelo com campos de formulário
const exampleFormModel = `
<h1>Formulário de Avaliação Inicial</h1>
<p>Paciente: [Paciente.Nome] - Data de Nascimento: [Paciente.DataNascimento|DD/MM/AAAA] - Idade: [Paciente.Idade] anos</p>
<p>Data da Avaliação: [Sistema.DataAtual|DD/MM/AAAA]</p>

<h2>Informações Gerais</h2>

<form-text-input data-label="Queixa Principal" data-field-id="mainComplaint" data-required="true" data-placeholder="Descreva a queixa principal do paciente"></form-text-input>

<form-textarea data-label="História da Queixa Atual" data-field-id="complaintHistory" data-required="true" data-placeholder="Descreva a história da queixa atual" data-rows="4"></form-textarea>

<form-select data-label="Tipo de Avaliação" data-field-id="evaluationType" data-required="true" data-options="Avaliação Inicial;Reavaliação;Avaliação de Acompanhamento"></form-select>

<h2>Histórico</h2>

<form-checkbox data-label="Já realizou terapia anteriormente?" data-field-id="previousTherapy"></form-checkbox>

<form-textarea data-label="Histórico de Terapias Anteriores" data-field-id="therapyHistory" data-placeholder="Descreva as terapias anteriores, se houver"></form-textarea>

<h2>Avaliação</h2>

<form-date-input data-label="Data da Próxima Avaliação" data-field-id="nextEvaluationDate"></form-date-input>

<form-textarea data-label="Observações Adicionais" data-field-id="additionalNotes" data-placeholder="Informações complementares"></form-textarea>
`;

// Exemplo de contexto com dados do paciente, alinhado com PlaceholderContext
const exampleContext: PlaceholderContext = {
  patient: {
    full_name: 'Maria Silva',
    date_of_birth: '2010-05-15', // Usar formato ISO para datas
    age: 14,
    phone: '(11) 98765-4321',
    email: '<EMAIL>'
  },
  therapist: {
    first_name: 'João',
    last_name: 'Santos',
    email: '<EMAIL>'
  },
  system: {
    currentDate: new Date()
  }
};

export function FormViewerDemo() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('preview');
  const { toast } = useToast();

  const handleFormSubmit = (title: string, content: string) => {
    setGeneratedContent(content);
    setActiveTab('result');
    toast({
      title: 'Documento gerado com sucesso!',
      description: `"${title}" foi criado com base no modelo e nos dados fornecidos.`,
    });
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Demonstração do Preenchedor de Formulário</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            Este é um exemplo de como os modelos com campos de formulário podem ser preenchidos para gerar documentos.
            Clique no botão abaixo para abrir o formulário de exemplo.
          </p>
          <Button onClick={() => setIsDialogOpen(true)}>
            Abrir Formulário de Exemplo
          </Button>
        </CardContent>
      </Card>

      {generatedContent && (
        <Card>
          <CardHeader>
            <CardTitle>Resultado</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="preview">Visualização</TabsTrigger>
                <TabsTrigger value="html">HTML</TabsTrigger>
              </TabsList>
              <TabsContent value="preview">
                <div 
                  className="bg-white p-4 rounded-md border"
                  dangerouslySetInnerHTML={{ __html: generatedContent }}
                />
              </TabsContent>
              <TabsContent value="html">
                <pre className="bg-muted p-4 rounded-md overflow-auto text-xs">
                  {generatedContent}
                </pre>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      <ModelFormDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        title="Formulário de Avaliação Inicial"
        description="Preencha os campos abaixo para gerar o documento de avaliação."
        modelContent={exampleFormModel}
        contextData={exampleContext}
        initialTitle="Avaliação Inicial - Maria Silva"
        onSubmit={handleFormSubmit}
      />
    </div>
  );
}

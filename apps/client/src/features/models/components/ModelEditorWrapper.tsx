import React, { useState, useEffect } from 'react';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Label } from '@/shared/ui/label';
import { Textarea } from '@/shared/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { ArrowLeft, Save, X } from 'lucide-react';
import { ModelEditor, DynamicModel } from './ModelEditor';
import { Model } from '@/features/models/types/model.schema';
import { useModelMutations } from '@/features/models/hooks/useModelMutations';
import { useToast } from '@/shared/hooks/use-toast';

interface ModelEditorWrapperProps {
  model?: Model;
  onCancel: () => void;
  isNew?: boolean;
}

export function ModelEditorWrapper({ model, onCancel, isNew }: ModelEditorWrapperProps) {
  const { toast } = useToast();
  const { createModel, updateModel, isCreating, isUpdating } = useModelMutations();
  
  // Estado para os metadados do modelo
  const [title, setTitle] = useState(model?.title || '');
  const [description, setDescription] = useState(model?.description || '');
  const [category, setCategory] = useState(model?.category || 'general');
  const [tags, setTags] = useState(model?.tags || []);
  
  // Estado para o modelo dinâmico
  const [dynamicModel, setDynamicModel] = useState<DynamicModel>(() => {
    // Sempre criar estrutura padrão para novos modelos ou converter modelos existentes
    return {
      id: model?.id || '',
      title: model?.title || '',
      description: model?.description || '',
      category: model?.category || 'general',
      template: {
        content: model?.content || '',
        placeholders: [],
      },
      form: {
        sections: [],
        validation: {},
        conditional: [],
      },
      outputTypes: ['note'],
    };
  });

  // Sincronizar metadados com o modelo dinâmico
  useEffect(() => {
    setDynamicModel(prev => ({
      ...prev,
      title,
      description,
      category,
    }));
  }, [title, description, category]);

  const handleSave = async () => {
    if (!title.trim()) {
      toast({
        title: "Erro de validação",
        description: "O título é obrigatório.",
        variant: "destructive",
      });
      return;
    }

    try {
      const modelData = {
        title: title.trim(),
        description: description.trim(),
        category,
        tags,
        content: typeof dynamicModel.template.content === 'string'
          ? dynamicModel.template.content
          : JSON.stringify(dynamicModel.template.content),
        templateContent: dynamicModel.template.content as any,
        formSchema: {
          ...dynamicModel.form,
          validation: dynamicModel.form.validation ?? {},
          conditional: (dynamicModel.form.conditional ?? []) as any,
        },
        outputTypes: dynamicModel.outputTypes,
        type: 'custom' as const,
      };

      if (isNew) {
        await createModel(modelData as any);
        toast({
          title: "Modelo criado",
          description: "O modelo foi criado com sucesso.",
        });
      } else if (model) {
        await updateModel({ id: model.id, data: modelData as any });
        toast({
          title: "Modelo atualizado",
          description: "O modelo foi atualizado com sucesso.",
        });
      }

      onCancel();
    } catch (error) {
      toast({
        title: "Erro ao salvar",
        description: "Ocorreu um erro ao salvar o modelo. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  const isLoading = isCreating || isUpdating;

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={onCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar aos modelos
          </Button>

          {/* Informações do Modelo */}
          <div className="flex-1 flex items-center gap-4">
            <div className="flex flex-col gap-1">
              <Label htmlFor="title" className="text-xs">Título *</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => { setTitle(e.target.value); }}
                placeholder="Digite o título do modelo"
                className="h-8 w-48"
              />
            </div>

            <div className="flex flex-col gap-1">
              <Label htmlFor="category" className="text-xs">Categoria</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger className="h-8 w-40">
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">Geral</SelectItem>
                  <SelectItem value="session_note">Nota de Sessão</SelectItem>
                  <SelectItem value="assessment">Avaliação</SelectItem>
                  <SelectItem value="report">Relatório</SelectItem>
                  <SelectItem value="letter">Carta</SelectItem>
                  <SelectItem value="prescription">Prescrição</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col gap-1">
              <Label htmlFor="description" className="text-xs">Descrição</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => { setDescription(e.target.value); }}
                placeholder="Descreva brevemente para que serve este modelo..."
                rows={1}
                className="h-8 w-64 resize-none"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onCancel} disabled={isLoading}>
              <X className="h-4 w-4 mr-2" />
              Cancelar
            </Button>
            <Button onClick={handleSave} disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Salvando...' : 'Salvar'}
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        <ModelEditor
          model={dynamicModel}
          onChange={setDynamicModel}
          className="h-full"
        />
      </div>
    </div>
  );
}

import React from 'react';
import { Input } from '@/shared/ui/input';
import { Textarea } from '@/shared/ui/textarea';
import { Label } from '@/shared/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { RadioGroup, RadioGroupItem } from '@/shared/ui/radio-group';
import { Checkbox } from '@/shared/ui/checkbox';
import { Alert, AlertDescription } from '@/shared/ui/alert';
import { AlertTriangle } from 'lucide-react';
import { cn } from '@/shared/lib/utils';
import { DynamicField, ValidationError, SelectOption } from './ModelEditor';

interface DynamicFieldRendererProps {
  field: DynamicField;
  value: unknown;
  error?: ValidationError;
  onChange: (fieldId: string, value: unknown) => void;
  patient?: {
    id: string;
    name: string;
    [key: string]: unknown;
  };
  appointment?: {
    id: string;
    date: string;
    time: string;
    [key: string]: unknown;
  };
}

export function DynamicFieldRenderer({
  field,
  value,
  error,
  onChange,
  patient,
  appointment,
}: DynamicFieldRendererProps) {
  const fieldId = `field-${field.id}`;
  const hasError = !!error;

  const commonProps = {
    className: cn(hasError && "border-destructive focus:ring-destructive"),
  };

  const getStringValue = (val: unknown): string => {
    if (typeof val === 'string') return val;
    if (typeof val === 'number') return String(val);
    if (val === null || val === undefined) return '';
    return String(val);
  };

  const renderField = () => {
    const stringValue = getStringValue(value);

    switch (field.type) {
      case 'text':
        return (
          <Input
            id={fieldId}
            value={stringValue}
            onChange={(e) => { onChange(field.id, e.target.value); }}
            placeholder={field.placeholder}
            {...commonProps}
          />
        );

      case 'textarea':
        return (
          <Textarea
            id={fieldId}
            value={stringValue}
            onChange={(e) => { onChange(field.id, e.target.value); }}
            placeholder={field.placeholder}
            rows={4}
            className={cn("resize-none", commonProps.className)}
          />
        );

      case 'select':
        return (
          <Select
            value={stringValue}
            onValueChange={(val) => { onChange(field.id, val); }}
          >
            <SelectTrigger className={commonProps.className}>
              <SelectValue placeholder={field.placeholder ?? "Selecione uma opção"} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option: SelectOption) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'radio':
        return (
          <RadioGroup
            value={stringValue}
            onValueChange={(val) => { onChange(field.id, val); }}
            className={cn("space-y-3", hasError && "border border-destructive rounded-md p-3")}
          >
            {field.options?.map((option: SelectOption) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`${fieldId}-${option.value}`} />
                <Label 
                  htmlFor={`${fieldId}-${option.value}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fieldId}
              checked={value === true}
              onCheckedChange={(checked) => { onChange(field.id, checked); }}
              className={hasError ? "border-destructive" : ""}
            />
            <Label 
              htmlFor={fieldId}
              className="text-sm font-normal cursor-pointer"
            >
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
          </div>
        );

      case 'date':
        return (
          <Input
            id={fieldId}
            type="date"
            value={stringValue}
            onChange={(e) => onChange(field.id, e.target.value)}
            {...commonProps}
          />
        );

      case 'number':
        return (
          <Input
            id={fieldId}
            type="number"
            value={stringValue}
            onChange={(e) => onChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            min={field.validation?.min}
            max={field.validation?.max}
            {...commonProps}
          />
        );

      default:
        return (
          <div className="p-4 border border-dashed border-muted-foreground/20 rounded-md text-center text-muted-foreground">
            Tipo de campo não suportado: {field.type}
          </div>
        );
    }
  };

  // For checkbox fields, the label is rendered inside the field
  if (field.type === 'checkbox') {
    return (
      <div className="space-y-2">
        {renderField()}
        {hasError && (
          <Alert variant="destructive" className="py-2">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-sm">
              {error.message}
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  }

  // For all other field types, render label separately
  return (
    <div className="space-y-2">
      <Label htmlFor={fieldId} className="text-sm font-medium">
        {field.label}
        {field.required && <span className="text-destructive ml-1">*</span>}
      </Label>
      
      {renderField()}
      
      {hasError && (
        <Alert variant="destructive" className="py-2">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {error.message}
          </AlertDescription>
        </Alert>
      )}
      
      {field.validation?.minLength && field.type === 'text' && (
        <p className="text-xs text-muted-foreground">
          Mínimo de {field.validation.minLength} caracteres
        </p>
      )}
      
      {field.validation?.maxLength && (field.type === 'text' || field.type === 'textarea') && (
        <p className="text-xs text-muted-foreground">
          Máximo de {field.validation.maxLength} caracteres
          {getStringValue(value) && (
            <span className="ml-1">
              ({getStringValue(value).length}/{field.validation.maxLength})
            </span>
          )}
        </p>
      )}
      
      {(field.validation?.min !== undefined || field.validation?.max !== undefined) && field.type === 'number' && (
        <p className="text-xs text-muted-foreground">
          {field.validation.min !== undefined && field.validation.max !== undefined
            ? `Entre ${field.validation.min} e ${field.validation.max}`
            : field.validation.min !== undefined
            ? `Mínimo: ${field.validation.min}`
            : `Máximo: ${field.validation.max}`
          }
        </p>
      )}
    </div>
  );
}

import React, { useState } from 'react';
import { Button } from '@/shared/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/ui/dropdown-menu';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/shared/ui/dialog';
import { Input } from '@/shared/ui/input';
import { Label } from '@/shared/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { 
  BracesIcon, 
  User, 
  Calendar, 
  Clock, 
  FileText, 
  Settings,
  Plus,
} from 'lucide-react';

interface PlaceholderInserterProps {
  onInsert: (key: string, transform?: string, fallback?: string) => void;
}

// Predefined system placeholders
const SYSTEM_PLACEHOLDERS = [
  {
    category: 'Paciente',
    icon: User,
    placeholders: [
      { key: 'paciente.nome', label: 'Nome do Paciente' },
      { key: 'paciente.idade', label: 'Idade' },
      { key: 'paciente.dataNascimento', label: 'Data de Nascimento' },
      { key: 'paciente.telefone', label: 'Telefone' },
      { key: 'paciente.email', label: 'Email' },
      { key: 'paciente.endereco', label: 'Endereço' },
      { key: 'paciente.responsavel', label: 'Responsável' },
    ]
  },
  {
    category: 'Sessão',
    icon: Calendar,
    placeholders: [
      { key: 'sessao.data', label: 'Data da Sessão' },
      { key: 'sessao.hora', label: 'Hora da Sessão' },
      { key: 'sessao.duracao', label: 'Duração' },
      { key: 'sessao.tipo', label: 'Tipo de Sessão' },
      { key: 'sessao.numero', label: 'Número da Sessão' },
    ]
  },
  {
    category: 'Profissional',
    icon: FileText,
    placeholders: [
      { key: 'profissional.nome', label: 'Nome do Profissional' },
      { key: 'profissional.registro', label: 'Registro Profissional' },
      { key: 'profissional.especialidade', label: 'Especialidade' },
      { key: 'profissional.telefone', label: 'Telefone' },
      { key: 'profissional.email', label: 'Email' },
    ]
  },
  {
    category: 'Sistema',
    icon: Settings,
    placeholders: [
      { key: 'sistema.dataAtual', label: 'Data Atual' },
      { key: 'sistema.horaAtual', label: 'Hora Atual' },
      { key: 'sistema.nomeClinica', label: 'Nome da Clínica' },
    ]
  },
];

const TRANSFORM_OPTIONS = [
  { value: 'uppercase', label: 'MAIÚSCULAS' },
  { value: 'lowercase', label: 'minúsculas' },
  { value: 'capitalize', label: 'Primeira Maiúscula' },
  { value: 'date-format:dd/MM/yyyy', label: 'Data (dd/MM/yyyy)' },
  { value: 'date-format:dd/MM/yyyy HH:mm', label: 'Data e Hora' },
];

export function PlaceholderInserter({ onInsert }: PlaceholderInserterProps) {
  const [isCustomDialogOpen, setIsCustomDialogOpen] = useState(false);
  const [customKey, setCustomKey] = useState('');
  const [customTransform, setCustomTransform] = useState('');
  const [customFallback, setCustomFallback] = useState('');

  const handleSystemPlaceholderInsert = (key: string, label: string) => {
    onInsert(key);
  };

  const handleCustomPlaceholderInsert = () => {
    if (customKey.trim()) {
      onInsert(
        customKey.trim(),
        customTransform || undefined,
        customFallback.trim() || undefined
      );
      
      // Reset form
      setCustomKey('');
      setCustomTransform('');
      setCustomFallback('');
      setIsCustomDialogOpen(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" title="Inserir placeholder">
            <BracesIcon className="h-4 w-4 mr-1" />
            Placeholder
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64" align="start">
          <DropdownMenuLabel>Placeholders do Sistema</DropdownMenuLabel>
          
          {SYSTEM_PLACEHOLDERS.map((category) => (
            <div key={category.category}>
              <DropdownMenuSeparator />
              <DropdownMenuLabel className="flex items-center text-xs text-muted-foreground">
                <category.icon className="h-3 w-3 mr-1" />
                {category.category}
              </DropdownMenuLabel>
              {category.placeholders.map((placeholder) => (
                <DropdownMenuItem
                  key={placeholder.key}
                  onClick={() => { handleSystemPlaceholderInsert(placeholder.key, placeholder.label); }}
                  className="text-sm"
                >
                  {placeholder.label}
                  <span className="ml-auto text-xs text-muted-foreground">
                    [{placeholder.key}]
                  </span>
                </DropdownMenuItem>
              ))}
            </div>
          ))}
          
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => { setIsCustomDialogOpen(true); }}
            className="text-sm font-medium"
          >
            <Plus className="h-3 w-3 mr-2" />
            Placeholder Personalizado
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Custom Placeholder Dialog */}
      <Dialog open={isCustomDialogOpen} onOpenChange={setIsCustomDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Criar Placeholder Personalizado</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="custom-key">
                Chave do Placeholder <span className="text-destructive">*</span>
              </Label>
              <Input
                id="custom-key"
                value={customKey}
                onChange={(e) => { setCustomKey(e.target.value); }}
                placeholder="Ex: paciente.observacoes"
              />
              <p className="text-xs text-muted-foreground">
                Use pontos para organizar hierarquicamente (ex: paciente.nome)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="custom-transform">Transformação (opcional)</Label>
              <Select value={customTransform || 'none'} onValueChange={(value) => { setCustomTransform(value === 'none' ? '' : value); }}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma transformação" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Nenhuma</SelectItem>
                  {TRANSFORM_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="custom-fallback">Valor Padrão (opcional)</Label>
              <Input
                id="custom-fallback"
                value={customFallback}
                onChange={(e) => { setCustomFallback(e.target.value); }}
                placeholder="Valor a ser usado se o campo estiver vazio"
              />
            </div>

            <div className="p-3 bg-muted rounded-md">
              <p className="text-sm font-medium mb-1">Preview:</p>
              <code className="text-sm">
                [{customKey}
                {customTransform && `|${customTransform}`}
                {customFallback && `|${customFallback}`}]
              </code>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => { setIsCustomDialogOpen(false); }}>
              Cancelar
            </Button>
            <Button 
              onClick={handleCustomPlaceholderInsert}
              disabled={!customKey.trim()}
            >
              Inserir Placeholder
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

import { useState, useEffect, useCallback } from 'react';
import { Input } from '@/shared/ui/input';
import { Textarea } from '@/shared/ui/textarea';
import { Checkbox } from '@/shared/ui/checkbox';
import { Label } from '@/shared/ui/label';
import { Button } from '@/shared/ui/button';
import { RadioGroup, RadioGroupItem } from '@/shared/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/shared/ui/select';

import { Alert, AlertDescription } from '@/shared/ui/alert';


import { AlertCircle, FileText, ToggleLeft, ToggleRight, FormInput, Layers } from 'lucide-react';
import { cn } from '@/shared/lib/utils';
import { FormBuilder } from '@/features/models/components/FormBuilder';
import {
  PlaceholderContext,
  processPlaceholders,
  extractFormFields,
  replaceF<PERSON><PERSON>ields,
  FormField as PlaceholderFormField
} from '@/shared/lib/placeholder-engine';

// Estendendo a interface FormField para incluir campos adicionais específicos do componente
interface FormField extends PlaceholderFormField {
  // Campos adicionais específicos para o componente, se necessário
}

interface ModelFormEditorProps {
  modelContent: string;
  contextData: PlaceholderContext;
  initialTitle?: string;
  onSubmit: (title: string, generatedContent: string) => void;
  onCancel?: () => void;
  onToggleFullscreen?: () => void;
  isFullscreen?: boolean;
  activeTab?: 'form' | 'document' | 'split' | 'builder';
  onTabChange?: (tab: 'form' | 'document' | 'split' | 'builder') => void;
  darkMode?: boolean;
  onModelContentChange?: (content: string) => void;
}

export function ModelFormEditor({
  modelContent,
  contextData,
  initialTitle = '',
  onSubmit,
  onCancel,
  onToggleFullscreen,
  isFullscreen = false,
  activeTab: externalActiveTab,
  onTabChange,
  darkMode = false,
  onModelContentChange: externalModelContentChange
}: ModelFormEditorProps) {
  const [title, setTitle] = useState(initialTitle);
  const [fields, setFields] = useState<FormField[]>([]);
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [previewContent, setPreviewContent] = useState('');
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);

  // Estado para controlar a visualização (formulário, documento, ambos ou construtor)
  const [internalViewMode, setInternalViewMode] = useState<'form' | 'document' | 'split' | 'builder'>('split');

  // Usar o modo de visualização externo se fornecido, caso contrário usar o interno
  const viewMode = externalActiveTab || internalViewMode;

  // Função para atualizar o modo de visualização
  const setViewMode = (mode: 'form' | 'document' | 'split' | 'builder') => {
    if (onTabChange) {
      onTabChange(mode);
    } else {
      setInternalViewMode(mode);
    }
  };

  // Estado para agrupar campos por seção
  const [fieldSections, setFieldSections] = useState<{[key: string]: FormField[]}>({});



  // Processar o conteúdo do modelo e extrair campos de formulário
  useEffect(() => {
    // Log para debug (remover em produção)
    console.debug('Processando conteúdo do modelo para extração de campos:', modelContent);
    console.debug('Contexto disponível:', contextData);

    // Primeiro, processar os placeholders padrão
    const contentWithPlaceholders = processPlaceholders(modelContent, contextData);
    // Log para debug (remover em produção)
    console.debug('Conteúdo com placeholders processados:', contentWithPlaceholders);

    // Extrair campos usando a nova sintaxe [Form.campo:tipo:opções]
    const newSyntaxFields = extractFormFields(contentWithPlaceholders);
    // Log para debug (remover em produção)
    console.debug('Campos extraídos da nova sintaxe:', newSyntaxFields);

    // Extrair campos usando a sintaxe antiga (tags HTML)
    // Importante: Precisamos usar o conteúdo original, não o processado, para extrair os campos
    const parser = new DOMParser();

    // Primeiro, vamos tentar analisar o conteúdo como HTML
    const doc = parser.parseFromString(modelContent, 'text/html');

    // Verificar se o parsing foi bem-sucedido
    const parseError = doc.querySelector('parsererror');
    if (parseError) {
      console.error('Erro ao analisar HTML:', parseError.textContent);
      // Tentar uma abordagem alternativa: envolver o conteúdo em uma div
      const wrappedDoc = parser.parseFromString(`<div>${modelContent}</div>`, 'text/html');
      if (wrappedDoc.querySelector('parsererror')) {
        console.error('Erro persistente ao analisar HTML, mesmo com wrapper');
      }
    }

    const oldSyntaxFields: FormField[] = [];

    // Log para debug (remover em produção)
    console.debug('Conteúdo HTML para extração de campos:', modelContent);

    // Função auxiliar para extrair todos os elementos de formulário
    const extractFormElements = () => {
      // Verificar se há elementos de formulário no HTML
      const allFormElements = doc.querySelectorAll('form-text-input, form-textarea, form-checkbox, form-select, form-date-input, div[data-type="radio"]');
      console.debug(`Total de elementos de formulário encontrados: ${allFormElements.length}`);

      // Se não encontrarmos elementos, vamos tentar uma abordagem alternativa
      if (allFormElements.length === 0) {
        console.debug('Tentando abordagem alternativa para encontrar elementos de formulário');

        // Procurar por padrões no texto que possam indicar elementos de formulário
        const textContent = modelContent.toLowerCase();

        // Verificar se há menções a campos de formulário no texto
        if (textContent.includes('campo de texto') || textContent.includes('área de texto') ||
            textContent.includes('data') || textContent.includes('opção')) {
          console.debug('Texto contém menções a campos de formulário');

          // Criar campos padrão com base no texto
          if (textContent.includes('campo de texto')) {
            oldSyntaxFields.push({
              type: 'text',
              id: `text-${Date.now()}`,
              label: 'Campo de texto',
              required: false,
            });
          }

          if (textContent.includes('área de texto')) {
            oldSyntaxFields.push({
              type: 'textarea',
              id: `textarea-${Date.now()}`,
              label: 'Área de texto',
              required: false,
              rows: 3,
            });
          }

          if (textContent.includes('data')) {
            oldSyntaxFields.push({
              type: 'date',
              id: `date-${Date.now()}`,
              label: 'Data',
              required: false,
            });
          }

          if (textContent.includes('botões de opção') || textContent.includes('opção')) {
            oldSyntaxFields.push({
              type: 'radio',
              id: `radio-${Date.now()}`,
              label: 'Botões de opção',
              required: false,
              options: ['Opção 1', 'Opção 2', 'Opção 3'],
            });
          }
        }
      }
    };

    // Chamar a função auxiliar para tentar extrair elementos de formulário
    extractFormElements();

    // Extrair campos de texto
    const textInputs = doc.querySelectorAll('form-text-input');
    console.debug(`Encontrados ${textInputs.length} campos de texto`);

    textInputs.forEach((element) => {
      const id = element.getAttribute('data-field-id') || `text-${oldSyntaxFields.length}`;
      const field: FormField = {
        type: 'text',
        id,
        label: element.getAttribute('data-label') || 'Campo de texto',
        required: element.getAttribute('data-required') === 'true',
        placeholder: element.getAttribute('data-placeholder') || '',
      };
      console.debug(`Campo de texto extraído:`, field);
      oldSyntaxFields.push(field);
    });

    // Extrair áreas de texto
    const textareas = doc.querySelectorAll('form-textarea');
    console.debug(`Encontradas ${textareas.length} áreas de texto`);

    textareas.forEach((element) => {
      const id = element.getAttribute('data-field-id') || `textarea-${oldSyntaxFields.length}`;
      const field: FormField = {
        type: 'textarea',
        id,
        label: element.getAttribute('data-label') || 'Área de texto',
        required: element.getAttribute('data-required') === 'true',
        placeholder: element.getAttribute('data-placeholder') || '',
        rows: element.getAttribute('data-rows') ? parseInt(element.getAttribute('data-rows') || '3', 10) : 3,
      };
      console.debug(`Área de texto extraída:`, field);
      oldSyntaxFields.push(field);
    });

    // Extrair checkboxes
    const checkboxes = doc.querySelectorAll('form-checkbox');
    console.debug(`Encontrados ${checkboxes.length} checkboxes`);

    checkboxes.forEach((element) => {
      const id = element.getAttribute('data-field-id') || `checkbox-${oldSyntaxFields.length}`;
      const field: FormField = {
        type: 'checkbox',
        id,
        label: element.getAttribute('data-label') || 'Opção',
        required: element.getAttribute('data-required') === 'true',
        defaultValue: element.getAttribute('data-default-checked') === 'true',
      };
      console.debug(`Checkbox extraído:`, field);
      oldSyntaxFields.push(field);
    });

    // Extrair selects
    const selects = doc.querySelectorAll('form-select');
    console.debug(`Encontrados ${selects.length} selects`);

    selects.forEach((element) => {
      const id = element.getAttribute('data-field-id') || `select-${oldSyntaxFields.length}`;
      const optionsStr = element.getAttribute('data-options') || 'Opção 1;Opção 2;Opção 3';
      const field: FormField = {
        type: 'select',
        id,
        label: element.getAttribute('data-label') || 'Selecione uma opção',
        required: element.getAttribute('data-required') === 'true',
        options: optionsStr.split(';'),
      };
      console.debug(`Select extraído:`, field);
      oldSyntaxFields.push(field);
    });

    // Extrair campos de data
    const dateInputs = doc.querySelectorAll('form-date-input');
    console.debug(`Encontrados ${dateInputs.length} campos de data`);

    dateInputs.forEach((element) => {
      const id = element.getAttribute('data-field-id') || `date-${oldSyntaxFields.length}`;
      const field: FormField = {
        type: 'date',
        id,
        label: element.getAttribute('data-label') || 'Data',
        required: element.getAttribute('data-required') === 'true',
      };
      console.debug(`Campo de data extraído:`, field);
      oldSyntaxFields.push(field);
    });

    // Extrair botões de opção (radio)
    const radios = doc.querySelectorAll('div[data-type="radio"]');
    console.debug(`Encontrados ${radios.length} grupos de botões de opção`);

    radios.forEach((element) => {
      const id = element.getAttribute('data-field-id') || `radio-${oldSyntaxFields.length}`;
      const optionsStr = element.getAttribute('data-options') || 'Opção 1;Opção 2;Opção 3';
      const field: FormField = {
        type: 'radio',
        id,
        label: element.getAttribute('data-label') || 'Botões de opção',
        required: element.getAttribute('data-required') === 'true',
        options: optionsStr.split(';'),
      };
      console.debug(`Grupo de botões de opção extraído:`, field);
      oldSyntaxFields.push(field);
    });

    // Verificar se encontramos campos usando as abordagens anteriores
    if (newSyntaxFields.length === 0 && oldSyntaxFields.length === 0) {
      console.debug('Nenhum campo encontrado, tentando abordagem de fallback');

      // Abordagem de fallback: criar campos padrão com base no conteúdo do modelo
      // Isso é útil quando o editor não está gerando corretamente as tags HTML

      // Verificar se há menções a campos de formulário no texto
      const textContent = modelContent.toLowerCase();

      // Criar campos padrão com base no texto
      if (textContent.includes('campo de texto')) {
        oldSyntaxFields.push({
          type: 'text',
          id: `text-${Date.now()}`,
          label: 'Campo de texto',
          required: false,
        });
      }

      if (textContent.includes('área de texto')) {
        oldSyntaxFields.push({
          type: 'textarea',
          id: `textarea-${Date.now()}`,
          label: 'Área de texto',
          required: false,
          rows: 3,
        });
      }

      if (textContent.includes('data')) {
        oldSyntaxFields.push({
          type: 'date',
          id: `date-${Date.now()}`,
          label: 'Data',
          required: false,
        });
      }

      if (textContent.includes('botões de opção') || textContent.includes('opção')) {
        oldSyntaxFields.push({
          type: 'radio',
          id: `radio-${Date.now()}`,
          label: 'Botões de opção',
          required: false,
          options: ['Opção 1', 'Opção 2', 'Opção 3'],
        });
      }
    }

    // Combinar campos de ambas as sintaxes
    const allFields = [...newSyntaxFields, ...oldSyntaxFields];

    // Log para debug (remover em produção)
    console.debug('Total de campos extraídos:', allFields.length);
    console.debug('Campos combinados:', allFields);

    // Se ainda não encontramos campos, criar campos padrão com base na imagem fornecida
    if (allFields.length === 0) {
      console.debug('Nenhum campo encontrado, criando campos padrão com base na imagem fornecida');

      // Criar campos padrão com base na imagem fornecida
      const defaultFields: FormField[] = [
        {
          type: 'text',
          id: 'campo_texto',
          label: 'Campo de texto',
          required: false,
        },
        {
          type: 'textarea',
          id: 'area_texto',
          label: 'Área de texto',
          required: false,
          rows: 3,
        },
        {
          type: 'date',
          id: 'data',
          label: 'Data',
          required: false,
        },
        {
          type: 'radio',
          id: 'botoes_opcao',
          label: 'Botões de opção',
          required: false,
          options: ['Opção 1', 'Opção 2', 'Opção 3'],
        }
      ];

      setFields(defaultFields);
    } else {
      setFields(allFields);
    }

    // Organizar campos por seções
    const sections: {[key: string]: FormField[]} = {};

    // Função para extrair a seção do ID do campo
    const getSectionFromId = (id: string): string => {
      // Para campos com sintaxe Form.campo, extrair a categoria do nome
      if (id.includes('.')) {
        const parts = id.split('_');
        if (parts.length > 1) {
          // Tentar extrair uma categoria do nome do campo
          // Ex: 'avaliacao_linguagem_compreensao' -> 'Avaliação de Linguagem'
          const possibleCategories = [
            'anamnese', 'avaliacao', 'linguagem', 'fala', 'voz', 'motricidade',
            'audicao', 'diagnostico', 'prognostico', 'plano', 'conclusao'
          ];

          for (const category of possibleCategories) {
            if (id.toLowerCase().includes(category)) {
              return category.charAt(0).toUpperCase() + category.slice(1);
            }
          }

          // Se não encontrar uma categoria específica, usar a primeira parte
          return parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
        }
      }

      // Para campos sem categoria identificada
      return 'Geral';
    };

    // Agrupar campos por seção
    allFields.forEach(field => {
      const section = getSectionFromId(field.id);
      if (!sections[section]) {
        sections[section] = [];
      }
      sections[section].push(field);
    });

    setFieldSections(sections);

    // Inicializar valores do formulário
    const initialValues: Record<string, any> = {};
    allFields.forEach((field) => {
      if (field.type === 'checkbox') {
        initialValues[field.id] = field.defaultValue || false;
      } else if (field.type === 'radio' && field.options && field.options.length > 0) {
        initialValues[field.id] = field.defaultValue || field.options[0];
      } else if (field.type === 'select' && field.options && field.options.length > 0) {
        initialValues[field.id] = field.defaultValue || field.options[0];
      } else if (field.type === 'number') {
        initialValues[field.id] = field.defaultValue !== undefined ? field.defaultValue : '';
      } else {
        initialValues[field.id] = field.defaultValue || '';
      }
    });

    setFormValues(initialValues);
  }, [modelContent, contextData]);

  // Atualizar a pré-visualização
  const updatePreview = () => {
    const previewHtml = generateFinalHtml();
    setPreviewContent(previewHtml);
  };

  // Função para inserir um campo de formulário no conteúdo do modelo
  const insertFormField = (field: FormField) => {
    // Criar o placeholder no formato [Form.id:tipo:opções]
    let placeholder = `[Form.${field.id}:${field.type}`;

    // Adicionar opções específicas por tipo
    if (field.type === 'select' || field.type === 'radio') {
      if (field.options && field.options.length > 0) {
        placeholder += `:${field.options.join(',')}`;
      }
    } else if (field.type === 'number') {
      const min = field.min !== undefined ? field.min : 0;
      const max = field.max !== undefined ? field.max : 100;
      const step = field.step !== undefined ? field.step : 1;
      placeholder += `:${min},${max},${step}`;
    } else if (field.type === 'textarea') {
      const rows = field.rows || 3;
      placeholder += `:${rows}`;
    } else if (field.type === 'checkbox') {
      const defaultValue = field.defaultValue ? 'true' : 'false';
      placeholder += `:${defaultValue}`;
    }

    placeholder += ']';

    // Inserir o placeholder no final do conteúdo do modelo
    const updatedContent = modelContent + '\n\n' + placeholder;
    handleModelContentChange(updatedContent);
  };

  // Função para atualizar o conteúdo do modelo
  const handleModelContentChange = (content: string) => {
    // Atualizar o conteúdo do modelo
    if (externalModelContentChange) {
      externalModelContentChange(content);
    } else {
      console.log('Conteúdo do modelo atualizado, mas nenhum handler externo fornecido');
    }
  };

  // Atualizar a pré-visualização quando os valores do formulário mudarem
  useEffect(() => {
    updatePreview();
    // Log para debug (remover em produção)
    console.debug('Valores do formulário atualizados, gerando nova pré-visualização:', formValues);
  }, [formValues]);

  // Manipular mudanças nos campos
  const handleChange = (id: string, value: any) => {
    setFormValues((prev) => ({
      ...prev,
      [id]: value,
    }));

    // Limpar erro se o campo foi preenchido
    if (errors[id] && value) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };



  // Gerar HTML final
  const generateFinalHtml = (): string => {
    // Criar contexto com os valores do formulário
    const contextWithForm: PlaceholderContext = {
      ...contextData,
      form: formValues
    };

    // Processar placeholders padrão primeiro
    let finalHtml = processPlaceholders(modelContent, contextWithForm);

    // Processar campos de formulário da nova sintaxe
    finalHtml = replaceFormFields(finalHtml, formValues);

    // Adicionar valores dos campos de formulário diretamente no documento
    // Isso garante que os valores apareçam mesmo que não haja tags específicas no modelo
    let formFieldsHtml = '';

    // Agrupar campos por seção para melhor organização
    Object.entries(fieldSections).forEach(([section, sectionFields]) => {
      if (sectionFields.length > 0) {
        formFieldsHtml += `<div class="form-section">`;

        // Adicionar cada campo da seção
        sectionFields.forEach(field => {
          const value = formValues[field.id];
          let formattedValue = '';

          // Formatar o valor de acordo com o tipo de campo
          if (field.type === 'checkbox') {
            formattedValue = value ? 'Sim' : 'Não';
          } else if (field.type === 'radio' || field.type === 'select') {
            formattedValue = value || '';
          } else if (field.type === 'date') {
            formattedValue = value || '';
          } else if (field.type === 'textarea') {
            formattedValue = value ? value.replace(/\n/g, '<br/>') : '';
          } else {
            formattedValue = value || '';
          }

          // Adicionar o campo ao HTML
          if (field.type === 'textarea') {
            formFieldsHtml += `
              <div class="form-field-result mb-2">
                <p class="form-field-label mb-1"><strong>${field.label}:</strong></p>
                <div class="form-field-value">${formattedValue}</div>
              </div>`;
          } else {
            formFieldsHtml += `<p class="form-field-result mb-2"><strong>${field.label}:</strong> ${formattedValue}</p>`;
          }
        });

        formFieldsHtml += `</div>`;
      }
    });

    // Substituir campos de formulário da sintaxe antiga (tags HTML)
    fields.forEach((field) => {
      // Pular campos da nova sintaxe (já processados acima)
      if (field.id.includes('.')) return;

      const value = formValues[field.id];
      let formattedValue = '';

      if (field.type === 'checkbox') {
        formattedValue = value ? 'Sim' : 'Não';
      } else if (field.type === 'radio' || field.type === 'select') {
        formattedValue = value || '';
      } else if (field.type === 'number') {
        formattedValue = value !== undefined && value !== '' ? String(value) : '';
      } else {
        formattedValue = value || '';
      }

      // Criar o padrão de substituição para cada tipo de campo
      let tagPattern: RegExp;

      switch (field.type) {
        case 'text':
          tagPattern = new RegExp(`<form-text-input[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        case 'textarea':
          tagPattern = new RegExp(`<form-textarea[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        case 'checkbox':
          tagPattern = new RegExp(`<form-checkbox[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        case 'select':
          tagPattern = new RegExp(`<form-select[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        case 'date':
        case 'time':
        case 'datetime':
          tagPattern = new RegExp(`<form-date-input[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
          break;
        default:
          tagPattern = new RegExp(`<form-[^>]*data-field-id=["']${field.id}["'][^>]*>`, 'g');
      }

      // Substituir a tag pelo valor formatado com formatação adequada
      switch (field.type) {
        case 'textarea':
          // Para áreas de texto, preservar quebras de linha
          finalHtml = finalHtml.replace(tagPattern, `<div class="form-field-result">
            <p class="form-field-label"><strong>${field.label}:</strong></p>
            <div class="form-field-value">${formattedValue.replace(/\n/g, '<br/>')}</div>
          </div>`);
          break;
        case 'checkbox':
        case 'radio':
          // Para checkboxes e radio buttons, mostrar texto sim/não
          finalHtml = finalHtml.replace(tagPattern, `<p class="form-field-result"><strong>${field.label}:</strong> ${formattedValue}</p>`);
          break;
        default:
          // Para outros campos, formato padrão
          finalHtml = finalHtml.replace(tagPattern, `<p class="form-field-result"><strong>${field.label}:</strong> ${formattedValue}</p>`);
      }
    });

    // Adicionar os valores dos campos ao final do documento se não houver conteúdo
    if (finalHtml.trim() === '') {
      finalHtml = formFieldsHtml;
    } else if (!finalHtml.includes('form-field-result')) {
      // Se o documento não contém resultados de campos de formulário, adicionar ao final
      finalHtml += formFieldsHtml;
    }

    return finalHtml;
  };



  // Manipular envio do formulário
  const handleSubmit = useCallback(() => {
    // Validar formulário
    const newErrors: Record<string, string> = {};

    fields.forEach((field) => {
      if (field.required) {
        const value = formValues[field.id];
        if (field.type === 'checkbox') {
          if (!value) {
            newErrors[field.id] = `${field.label} é obrigatório`;
          }
        } else if (!value || (typeof value === 'string' && value.trim() === '')) {
          newErrors[field.id] = `${field.label} é obrigatório`;
        }
      }
    });

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      // Gerar HTML final
      const contextWithForm: PlaceholderContext = {
        ...contextData,
        form: formValues
      };

      // Processar placeholders padrão primeiro
      let finalHtml = processPlaceholders(modelContent, contextWithForm);

      // Processar campos de formulário da nova sintaxe
      finalHtml = replaceFormFields(finalHtml, formValues);

      onSubmit(title, finalHtml);
    } else {
      // Rolar até o primeiro campo com erro
      const firstErrorId = Object.keys(newErrors)[0];
      const errorElement = document.getElementById(firstErrorId);
      if (errorElement) {
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [fields, formValues, contextData, modelContent, onSubmit, title]);

  // Expor o método handleSubmit para o componente pai
  useEffect(() => {
    // Se o componente pai forneceu uma referência, atualizá-la com o método handleSubmit
    if (typeof window !== 'undefined') {
      (window as any).handleModelFormSubmit = handleSubmit;
    }

    return () => {
      // Limpar a referência quando o componente for desmontado
      if (typeof window !== 'undefined') {
        delete (window as any).handleModelFormSubmit;
      }
    };
  }, [handleSubmit]);

  // Renderizar campos do formulário
  const renderField = (field: FormField) => {
    // Helper para renderizar mensagens de erro
    const renderError = (fieldId: string) => {
      if (!errors[fieldId]) return null;
      return (
        <p className={cn(
          "text-xs flex items-center mt-1",
          darkMode ? "text-red-400" : "text-destructive"
        )}>
          <AlertCircle className="h-3 w-3 mr-1" />
          {errors[fieldId]}
        </p>
      );
    };

    switch (field.type) {
      case 'text':
        return (
          <div className="mb-4" key={field.id}>
            <Label
              htmlFor={field.id}
              className={cn(
                "text-sm font-medium block mb-2",
                darkMode ? "text-gray-300" : "text-foreground"
              )}
            >
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              aria-invalid={!!errors[field.id]}
              className={cn(
                "h-10 text-sm",
                darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white focus:border-[#8b5cf6] focus:ring-[#8b5cf6]" : ""
              )}
            />
            {renderError(field.id)}
          </div>
        );

      case 'textarea':
        return (
          <div className="mb-4" key={field.id}>
            <Label
              htmlFor={field.id}
              className={cn(
                "text-sm font-medium block mb-2",
                darkMode ? "text-gray-300" : "text-foreground"
              )}
            >
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Textarea
              id={field.id}
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              rows={field.rows || 3}
              aria-invalid={!!errors[field.id]}
              className={cn(
                "text-sm min-h-[120px]",
                darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white focus:border-[#8b5cf6] focus:ring-[#8b5cf6]" : ""
              )}
            />
            {renderError(field.id)}
          </div>
        );

      case 'number':
        return (
          <div className="mb-4" key={field.id}>
            <Label
              htmlFor={field.id}
              className={cn(
                "text-sm font-medium block mb-2",
                darkMode ? "text-gray-300" : "text-foreground"
              )}
            >
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="number"
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              min={field.min}
              max={field.max}
              step={field.step}
              aria-invalid={!!errors[field.id]}
              className={cn(
                "h-10 text-sm",
                darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white focus:border-[#8b5cf6] focus:ring-[#8b5cf6]" : ""
              )}
            />
            {renderError(field.id)}
          </div>
        );

      case 'checkbox':
        return (
          <div className="mb-4" key={field.id}>
            <div className="flex items-center space-x-2">
              <Checkbox
                id={field.id}
                checked={formValues[field.id] || false}
                onCheckedChange={(checked) => handleChange(field.id, checked)}
                required={field.required}
                aria-invalid={!!errors[field.id]}
                className={darkMode ? "border-[#3d3a4d] data-[state=checked]:bg-[#8b5cf6] data-[state=checked]:border-[#8b5cf6]" : ""}
              />
              <Label
                htmlFor={field.id}
                className={cn(
                  "text-sm font-medium",
                  darkMode ? "text-gray-300" : "text-foreground"
                )}
              >
                {field.label}
                {field.required && <span className="text-destructive ml-1">*</span>}
              </Label>
            </div>
            {renderError(field.id)}
          </div>
        );

      case 'radio':
        return (
          <div className="mb-4" key={field.id}>
            <Label
              htmlFor={field.id}
              className={cn(
                "text-sm font-medium block mb-2",
                darkMode ? "text-gray-300" : "text-foreground"
              )}
            >
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <RadioGroup
              value={formValues[field.id] || ''}
              onValueChange={(value) => handleChange(field.id, value)}
              required={field.required}
              className="flex flex-col space-y-1"
            >
              {field.options?.map((option) => (
                <div className="flex items-center space-x-2" key={option}>
                  <RadioGroupItem
                    value={option}
                    id={`${field.id}-${option}`}
                    className={darkMode ? "border-[#3d3a4d] text-[#8b5cf6]" : ""}
                  />
                  <Label
                    htmlFor={`${field.id}-${option}`}
                    className={cn(
                      "text-sm font-medium",
                      darkMode ? "text-gray-300" : "text-foreground"
                    )}
                  >
                    {option}
                  </Label>
                </div>
              ))}
            </RadioGroup>
            {renderError(field.id)}
          </div>
        );

      case 'select':
        return (
          <div className="mb-4" key={field.id}>
            <Label
              htmlFor={field.id}
              className={cn(
                "text-sm font-medium block mb-2",
                darkMode ? "text-gray-300" : "text-foreground"
              )}
            >
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Select
              value={formValues[field.id] || ''}
              onValueChange={(value) => handleChange(field.id, value)}
              required={field.required}
            >
              <SelectTrigger
                id={field.id}
                aria-invalid={!!errors[field.id]}
                className={cn(
                  "h-10 text-sm",
                  darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white focus:border-[#8b5cf6] focus:ring-[#8b5cf6]" : ""
                )}
              >
                <SelectValue placeholder={field.label} />
              </SelectTrigger>
              <SelectContent className={darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white" : ""}>
                {field.options?.map((option) => (
                  <SelectItem
                    key={option}
                    value={option}
                    className={cn(
                      "text-sm font-medium",
                      darkMode ? "text-white focus:bg-[#3d3a4d] focus:text-white" : ""
                    )}
                  >
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {renderError(field.id)}
          </div>
        );

      case 'date':
        return (
          <div className="mb-4" key={field.id}>
            <Label
              htmlFor={field.id}
              className={cn(
                "text-sm font-medium block mb-2",
                darkMode ? "text-gray-300" : "text-foreground"
              )}
            >
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="date"
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              required={field.required}
              aria-invalid={!!errors[field.id]}
              className={cn(
                "h-10 text-sm",
                darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white focus:border-[#8b5cf6] focus:ring-[#8b5cf6]" : ""
              )}
            />
            {renderError(field.id)}
          </div>
        );

      case 'time':
        return (
          <div className="mb-4" key={field.id}>
            <Label
              htmlFor={field.id}
              className={cn(
                "text-sm font-medium block mb-2",
                darkMode ? "text-gray-300" : "text-foreground"
              )}
            >
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="time"
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              required={field.required}
              aria-invalid={!!errors[field.id]}
              className={cn(
                "h-10 text-sm",
                darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white focus:border-[#8b5cf6] focus:ring-[#8b5cf6]" : ""
              )}
            />
            {renderError(field.id)}
          </div>
        );

      case 'datetime':
        return (
          <div className="mb-4" key={field.id}>
            <Label
              htmlFor={field.id}
              className={cn(
                "text-sm font-medium block mb-2",
                darkMode ? "text-gray-300" : "text-foreground"
              )}
            >
              {field.label}
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              type="datetime-local"
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              required={field.required}
              aria-invalid={!!errors[field.id]}
              className={cn(
                "h-10 text-sm",
                darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white focus:border-[#8b5cf6] focus:ring-[#8b5cf6]" : ""
              )}
            />
            {renderError(field.id)}
          </div>
        );

      default:
        // Fallback para tipos desconhecidos - usar campo de texto
        return (
          <div className="mb-4" key={field.id}>
            <Label
              htmlFor={field.id}
              className={cn(
                "text-sm font-medium block mb-2",
                darkMode ? "text-gray-300" : "text-foreground"
              )}
            >
              {field.label} ({field.type})
              {field.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={field.id}
              value={formValues[field.id] || ''}
              onChange={(e) => handleChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              required={field.required}
              aria-invalid={!!errors[field.id]}
              className={cn(
                "h-10 text-sm",
                darkMode ? "bg-[#2d2a3d] border-[#3d3a4d] text-white focus:border-[#8b5cf6] focus:ring-[#8b5cf6]" : ""
              )}
            />
            {renderError(field.id)}
          </div>
        );
    }
  };

  // Renderizar conteúdo do modelo com campos de formulário
  const renderFormFields = () => {
    // Verificar se há campos de formulário
    if (fields.length === 0) {
      return (
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Este modelo não contém campos de formulário para preencher.
          </AlertDescription>
        </Alert>
      );
    }

    // Se temos seções, mostrar campos agrupados
    if (Object.keys(fieldSections).length > 0) {
      return (
        <div className="space-y-6 p-4">
          {Object.entries(fieldSections).map(([section, sectionFields]) => (
            <div key={section} className="mb-5">
              <div className={cn(
                "sticky top-0 z-20 py-2 px-3 rounded-md",
                darkMode ? "bg-[#2d2a3d]" : "bg-muted/40"
              )}>
                <h3 className={cn(
                  "text-base font-medium flex items-center",
                  darkMode ? "text-[#8b5cf6]" : "text-primary"
                )}>
                  <FileText className="h-3.5 w-3.5 mr-1.5" />
                  {section}
                </h3>
              </div>
              <div className="pl-3 pr-3 mt-3 space-y-4">
                {sectionFields.map(renderField)}
              </div>
            </div>
          ))}
        </div>
      );
    }

    // Fallback para mostrar todos os campos sem agrupamento
    return (
      <div className="space-y-4 p-4">
        {fields.map(renderField)}
      </div>
    );
  };

  // Renderizar pré-visualização do documento
  const renderPreview = () => {
    return (
      <div className="min-h-[500px] mx-auto max-w-[800px] p-8">
        <div
          className={cn(
            "prose max-w-none",
            darkMode ? "prose-invert" : ""
          )}
          dangerouslySetInnerHTML={{ __html: previewContent }}
        />

        {/* Adicionar estilos CSS para garantir que os campos de formulário sejam exibidos corretamente */}
        <style dangerouslySetInnerHTML={{ __html: `
          .form-field-result {
            margin-bottom: 1rem;
          }
          .form-field-label {
            margin-bottom: 0.25rem;
            font-weight: 600;
          }
          .form-field-value {
            white-space: pre-wrap;
          }
          .form-section {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid ${darkMode ? '#2d2a3d' : '#e5e7eb'};
          }
        `}} />
      </div>
    );
  };

  // Alternar salvamento automático
  const toggleAutoSave = () => {
    setAutoSaveEnabled(!autoSaveEnabled);
  };

  // Atualizar o título quando o initialTitle mudar
  useEffect(() => {
    if (initialTitle) {
      setTitle(initialTitle);
    }
  }, [initialTitle]);

  return (
    <div className={cn("flex flex-col h-full", darkMode ? "bg-[#1e1b2e] text-white" : "bg-background")}>
      <div className="flex-1 overflow-auto">
        {viewMode === 'form' && (
          <div className="h-full overflow-auto" style={{ minHeight: '500px' }}>
            <div className={cn(
              "sticky top-0 z-30 p-3 border-b flex items-center",
              darkMode ? "bg-[#1e1b2e] border-[#2d2a3d]" : "bg-background"
            )}>
              <FileText className={cn("h-4 w-4 mr-2", darkMode ? "text-gray-400" : "text-muted-foreground")} />
              <span className={cn("text-base font-medium", darkMode ? "text-white" : "")}>
                Campos do Formulário
              </span>

              {/* Alternar salvamento automático */}
              <div className="ml-auto flex items-center">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleAutoSave}
                  className={cn("h-6 w-8 p-0", darkMode ? "hover:bg-[#2d2a3d] text-white" : "")}
                  title={autoSaveEnabled ? "Desativar salvamento automático" : "Ativar salvamento automático"}
                >
                  {autoSaveEnabled ? (
                    <ToggleRight className="h-5 w-5 text-[#8b5cf6]" />
                  ) : (
                    <ToggleLeft className={cn("h-5 w-5", darkMode ? "text-gray-400" : "text-muted-foreground")} />
                  )}
                </Button>
              </div>
            </div>
            <div className="p-0 overflow-y-auto" style={{ maxHeight: 'calc(95vh - 120px)' }}>
              {renderFormFields()}
            </div>
          </div>
        )}

        {viewMode === 'document' && (
          <div className={cn("h-full overflow-auto", darkMode ? "bg-[#121016]" : "")} style={{ minHeight: '500px' }}>
            <div className={cn(
              "sticky top-0 z-30 p-3 border-b flex items-center",
              darkMode ? "bg-[#121016] border-[#2d2a3d]" : "bg-white border-gray-200"
            )}>
              <FileText className={cn("h-4 w-4 mr-2", darkMode ? "text-gray-400" : "text-muted-foreground")} />
              <span className={cn("text-base font-medium", darkMode ? "text-white" : "")}>
                Documento
              </span>

              {/* Indicador de atualização em tempo real */}
              <div className="ml-auto flex items-center">
                <span className={cn("text-xs", darkMode ? "text-gray-400" : "text-muted-foreground")}>
                  Atualização em tempo real
                </span>
                <div className="ml-2 h-2 w-2 rounded-full bg-green-500 animate-pulse"></div>
              </div>
            </div>
            <div className="overflow-y-auto" style={{ maxHeight: 'calc(95vh - 120px)' }}>
              {renderPreview()}
            </div>
          </div>
        )}

        {viewMode === 'builder' && (
          <div className="h-full overflow-auto" style={{ minHeight: '500px' }}>
            <div className={cn(
              "sticky top-0 z-30 p-3 border-b flex items-center",
              darkMode ? "bg-[#1e1b2e] border-[#2d2a3d]" : "bg-background"
            )}>
              <Layers className={cn("h-4 w-4 mr-2", darkMode ? "text-gray-400" : "text-muted-foreground")} />
              <span className={cn("text-base font-medium", darkMode ? "text-white" : "")}>
                Construtor de Formulário
              </span>
            </div>
            <div className="p-4 overflow-y-auto" style={{ maxHeight: 'calc(95vh - 120px)' }}>
              <FormBuilder
                fields={fields}
                onChange={(updatedFields) => setFields(updatedFields)}
                onInsertField={insertFormField}
                darkMode={darkMode}
              />
            </div>
          </div>
        )}

        {viewMode === 'split' && (
          <div className="flex h-full" style={{ minHeight: '500px' }}>
            <div className={cn(
              "w-[350px] border-r overflow-auto",
              darkMode ? "bg-[#1e1b2e] border-[#2d2a3d]" : "bg-muted/20"
            )} style={{ maxWidth: '350px', minWidth: '300px', maxHeight: '100%', overflowY: 'auto' }}>
              <div className={cn(
                "sticky top-0 z-30 p-3 border-b flex items-center",
                darkMode ? "bg-[#1e1b2e] border-[#2d2a3d]" : "bg-background"
              )}>
                <FileText className={cn("h-4 w-4 mr-2", darkMode ? "text-gray-400" : "text-muted-foreground")} />
                <span className={cn("text-base font-medium", darkMode ? "text-white" : "")}>
                  Campos do Formulário
                </span>

                {/* Alternar salvamento automático */}
                <div className="ml-auto flex items-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleAutoSave}
                    className={cn("h-6 w-8 p-0", darkMode ? "hover:bg-[#2d2a3d] text-white" : "")}
                    title={autoSaveEnabled ? "Desativar salvamento automático" : "Ativar salvamento automático"}
                  >
                    {autoSaveEnabled ? (
                      <ToggleRight className="h-5 w-5 text-[#8b5cf6]" />
                    ) : (
                      <ToggleLeft className={cn("h-5 w-5", darkMode ? "text-gray-400" : "text-muted-foreground")} />
                    )}
                  </Button>
                </div>
              </div>
              <div className="p-0 overflow-y-auto" style={{ maxHeight: 'calc(95vh - 100px)' }}>
                {renderFormFields()}
              </div>
            </div>
            <div className={cn("flex-1 overflow-auto", darkMode ? "bg-[#121016]" : "bg-white")} style={{ minWidth: '650px' }}>
              <div className={cn(
                "sticky top-0 z-30 p-3 border-b flex items-center",
                darkMode ? "bg-[#121016] border-[#2d2a3d]" : "bg-white border-gray-200"
              )}>
                <FileText className={cn("h-4 w-4 mr-2", darkMode ? "text-gray-400" : "text-muted-foreground")} />
                <span className={cn("text-sm font-medium", darkMode ? "text-white" : "")}>
                  Documento
                </span>

                {/* Indicador de atualização em tempo real */}
                <div className="ml-auto flex items-center">
                  <span className={cn("text-xs", darkMode ? "text-gray-400" : "text-muted-foreground")}>
                    Atualização em tempo real
                  </span>
                  <div className="ml-2 h-2 w-2 rounded-full bg-green-500 animate-pulse"></div>
                </div>
              </div>
              <div className="overflow-y-auto" style={{ maxHeight: 'calc(95vh - 120px)' }}>
                {renderPreview()}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

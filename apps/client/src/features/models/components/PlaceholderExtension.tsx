import { Extension } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

// Helper functions
function formatPlaceholder(placeholder: PlaceholderData): string {
  let result = `[${placeholder.key}`;

  if (placeholder.transform) {
    result += `|${placeholder.transform.type}`;
    if (placeholder.transform.format) {
      result += `:${placeholder.transform.format}`;
    }
  }

  if (placeholder.fallback) {
    result += `|${placeholder.fallback}`;
  }

  result += ']';
  return result;
}

function applyTransform(value: unknown, transform?: string): string {
  if (!transform || value === undefined || value === null) {
    return String(value || '');
  }

  const stringValue = String(value);
  const [transformType, formatString] = transform.split(':');

  switch (transformType) {
    case 'uppercase':
      return stringValue.toUpperCase();

    case 'lowercase':
      return stringValue.toLowerCase();

    case 'capitalize':
      return stringValue.charAt(0).toUpperCase() + stringValue.slice(1).toLowerCase();

    case 'date-format':
      try {
        const date = new Date(String(value));
        const dateFormat = formatString || 'dd/MM/yyyy';
        return format(date, dateFormat, { locale: ptBR });
      } catch {
        return stringValue;
      }

    default:
      return stringValue;
  }
}

export interface PlaceholderTransform {
  type: 'uppercase' | 'lowercase' | 'capitalize' | 'date-format';
  format?: string; // For date formatting
}

export interface PlaceholderData {
  key: string;
  transform?: PlaceholderTransform;
  fallback?: string;
}

export interface PlaceholderOptions {
  context?: Record<string, unknown>;
  showPreview?: boolean;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    placeholderHighlight: {
      updatePlaceholderContext: (context: Record<string, unknown>) => ReturnType,
      insertPlaceholder: (placeholder: PlaceholderData) => ReturnType,
    }
  }
}

export const PlaceholderHighlight = Extension.create<PlaceholderOptions>({
  name: 'placeholderHighlight',

  addOptions() {
    return {
      context: {},
      showPreview: false,
    }
  },

  addStorage() {
    return {
      context: this.options.context || {},
    }
  },

  addCommands() {
    return {
      updatePlaceholderContext: (context: Record<string, unknown>) => ({ editor }) => {
        this.storage.context = context;

        // Trigger re-render to update placeholder previews
        const { state, view } = editor;
        const tr = state.tr;
        view.dispatch(tr);

        return true;
      },

      insertPlaceholder: (placeholder: PlaceholderData) => ({ commands }) => {
        const placeholderText = formatPlaceholder(placeholder);
        return commands.insertContent(placeholderText);
      },
    }
  },

  addProseMirrorPlugins() {
    // Enhanced regex to support transformations and fallbacks
    // Format: [key|transform|fallback] or [key|transform] or [key]
    const placeholderRegex = /\[([^|\]]+)(?:\|([^|\]]+))?(?:\|([^\]]+))?\]/g;

    return [
      new Plugin({
        key: new PluginKey('placeholderHighlight'),
        props: {
          decorations: (state) => {
            const { doc } = state;
            const decorations: Decoration[] = [];
            const context = this.storage.context || {};

            doc.descendants((node, pos) => {
              if (!node.isText) return;

              const text = node.text || '';
              let match;

              while ((match = placeholderRegex.exec(text)) !== null) {
                const start = pos + match.index;
                const end = start + match[0].length;
                const key = match[1];
                const transform = match[2];
                const fallback = match[3];

                // Get the actual value for preview
                let previewValue = '';
                if (this.options.showPreview && context[key] !== undefined) {
                  previewValue = applyTransform(context[key], transform);
                } else if (fallback) {
                  previewValue = fallback;
                }

                // Create decoration with enhanced data
                decorations.push(
                  Decoration.inline(start, end, {
                    class: 'placeholder-highlight',
                    'data-placeholder': 'true',
                    'data-key': key,
                    'data-transform': transform || '',
                    'data-fallback': fallback || '',
                    'data-preview': previewValue,
                    title: previewValue ? `Preview: ${previewValue}` : `Placeholder: ${key}`,
                  })
                );
              }
            });

            return DecorationSet.create(doc, decorations);
          },
        },
      }),
    ];
  },

  // Parse placeholder text to extract components
  parsePlaceholder(placeholderText: string): PlaceholderData | null {
    const match = placeholderText.match(/\[([^|\]]+)(?:\|([^|\]]+))?(?:\|([^\]]+))?\]/);
    if (!match) return null;

    const key = match[1];
    const transform = match[2];
    const fallback = match[3];

    const result: PlaceholderData = { key };

    if (transform) {
      const [transformType, format] = transform.split(':');
      result.transform = {
        type: transformType as PlaceholderTransform['type'],
        format,
      };
    }

    if (fallback) {
      result.fallback = fallback;
    }

    return result;
  },

  // Process all placeholders in text with context
  processPlaceholders(text: string, context: Record<string, unknown>): string {
    const placeholderRegex = /\[([^|\]]+)(?:\|([^|\]]+))?(?:\|([^\]]+))?\]/g;

    return text.replace(placeholderRegex, (match, key, transform, fallback) => {
      let value = context[key];

      if (value !== undefined && value !== null) {
        return this.applyTransform(value, transform);
      }

      return fallback || match;
    });
  },
});
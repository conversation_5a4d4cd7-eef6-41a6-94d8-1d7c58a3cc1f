import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/shared/ui/dialog';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { useModelsQuery } from '@/features/models/hooks/useModelsQuery';
import { Model } from '@/features/models/types/model.schema';
import { Search, FileText, Star, Clock } from 'lucide-react';
import { Badge } from '@/shared/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { parseISO, isValid } from 'date-fns';

interface ModelSelectorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (model: Model) => void;
  filter?: {
    category?: string;
    type?: string;
  };
}

export function ModelSelectorDialog({ isO<PERSON>, onClose, onSelect, filter }: ModelSelectorDialogProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const { models, isLoading } = useModelsQuery();

  // Filtrar modelos com base no termo de pesquisa e filtros
  const filteredModels = models.filter(model => {
    // Filtrar por termo de pesquisa
    const matchesSearch = searchTerm === '' ||
      model.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (model.description && model.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Filtrar por categoria (se especificado)
    const matchesCategory = !filter?.category || model.category === filter.category;

    // Filtrar por tipo (se especificado)
    const matchesType = !filter?.type || model.type === filter.type;

    // Filtrar por favoritos (se a aba for 'favorites')
    const matchesFavorites = activeTab !== 'favorites' || model.isFavorite;

    // Filtrar por recentes (se a aba for 'recent')
    const matchesRecent = activeTab !== 'recent' || model.lastUsed;

    return matchesSearch && matchesCategory && matchesType && matchesFavorites && matchesRecent;
  });

  // Ordenar modelos: favoritos primeiro, depois por data de última utilização
  const sortedModels = [...filteredModels].sort((a, b) => {
    // Favoritos primeiro
    if (a.isFavorite && !b.isFavorite) return -1;
    if (!a.isFavorite && b.isFavorite) return 1;

    // Depois por data de última utilização (mais recente primeiro)
    const dateA = a.lastUsed ? parseISO(a.lastUsed) : null;
    const dateB = b.lastUsed ? parseISO(b.lastUsed) : null;

    if (dateA && dateB && isValid(dateA) && isValid(dateB)) {
      return dateB.getTime() - dateA.getTime();
    }
    if (dateA && isValid(dateA)) return -1;
    if (dateB && isValid(dateB)) return 1;

    // Por fim, ordem alfabética
    return a.title.localeCompare(b.title);
  });

  const handleSelectModel = (model: Model) => {
    onSelect(model);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Selecionar Modelo</DialogTitle>
          <DialogDescription>
            Escolha um modelo para aplicar ao conteúdo
          </DialogDescription>
        </DialogHeader>

        <div className="relative mb-4">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Pesquisar modelos..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">Todos</TabsTrigger>
            <TabsTrigger value="favorites">Favoritos</TabsTrigger>
            <TabsTrigger value="recent">Recentes</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-0">
            <ModelsList
              models={sortedModels}
              isLoading={isLoading}
              onSelect={handleSelectModel}
            />
          </TabsContent>

          <TabsContent value="favorites" className="mt-0">
            <ModelsList
              models={sortedModels}
              isLoading={isLoading}
              onSelect={handleSelectModel}
              emptyMessage="Nenhum modelo favorito encontrado."
            />
          </TabsContent>

          <TabsContent value="recent" className="mt-0">
            <ModelsList
              models={sortedModels}
              isLoading={isLoading}
              onSelect={handleSelectModel}
              emptyMessage="Nenhum modelo usado recentemente."
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}

interface ModelsListProps {
  models: Model[];
  isLoading: boolean;
  onSelect: (model: Model) => void;
  emptyMessage?: string;
}

function ModelsList({ models, isLoading, onSelect, emptyMessage = "Nenhum modelo encontrado." }: ModelsListProps) {
  if (isLoading) {
    return <div className="py-8 text-center text-muted-foreground">Carregando modelos...</div>;
  }

  if (models.length === 0) {
    return <div className="py-8 text-center text-muted-foreground">{emptyMessage}</div>;
  }

  return (
    <ScrollArea className="h-[300px] pr-4">
      <div className="space-y-2 mt-2">
        {models.map((model) => (
          <div
            key={model.id}
            className="flex items-start p-3 border rounded-md hover:bg-accent cursor-pointer group"
            onClick={() => onSelect(model)}
          >
            <div className="mr-3 mt-0.5">
              <FileText className="h-5 w-5 text-muted-foreground" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-sm truncate">{model.title}</h4>
                <div className="flex items-center gap-1">
                  {model.isFavorite && (
                    <Star className="h-4 w-4 fill-primary text-primary" />
                  )}
                  {model.lastUsed && (
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  )}
                </div>
              </div>
              {model.description && (
                <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                  {model.description}
                </p>
              )}
              <div className="flex flex-wrap gap-1 mt-2">
                <Badge variant="outline" className="text-xs">
                  {model.category}
                </Badge>
                {model.tags && model.tags.length > 0 && model.tags.slice(0, 2).map((tag: string, i: number) => (
                  <Badge key={i} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {model.tags && model.tags.length > 2 && (
                  <Badge variant="secondary" className="text-xs">
                    +{model.tags.length - 2}
                  </Badge>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity ml-2"
            >
              Usar
            </Button>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
}

import React from 'react';
import { <PERSON><PERSON> } from '@/shared/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { Separator } from '@/shared/ui/separator';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { 
  Type,
  AlignLeft,
  ChevronDown,
  Circle,
  CheckSquare,
  Calendar,
  Hash,
  User,
  Clock,
  FileText,
  Settings,
  BracesIcon,
} from 'lucide-react';

interface FieldsSidebarProps {
  onInsertField: (fieldType: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'number') => void;
  onInsertPlaceholder: (key: string, transform?: string, fallback?: string) => void;
}

const FIELD_TYPES = [
  { type: 'text' as const, icon: Type, label: 'Texto Simples', description: 'Campo de entrada de texto' },
  { type: 'textarea' as const, icon: AlignLeft, label: 'Text<PERSON> Longo', description: 'Área de texto multilinha' },
  { type: 'select' as const, icon: ChevronDown, label: 'Lista Suspensa', description: 'Seleção de uma opção' },
  { type: 'radio' as const, icon: Circle, label: 'Múltipla Escolha', description: 'Botões de seleção única' },
  { type: 'checkbox' as const, icon: CheckSquare, label: 'Caixa de Seleção', description: 'Verdadeiro ou falso' },
  { type: 'date' as const, icon: Calendar, label: 'Data', description: 'Seletor de data' },
  { type: 'number' as const, icon: Hash, label: 'Número', description: 'Campo numérico' },
];

const PATIENT_PLACEHOLDERS = [
  { key: 'paciente.nome', label: 'Nome do Paciente', icon: User },
  { key: 'paciente.idade', label: 'Idade', icon: User },
  { key: 'paciente.dataNascimento', label: 'Data de Nascimento', icon: Calendar },
  { key: 'paciente.telefone', label: 'Telefone', icon: User },
  { key: 'paciente.email', label: 'Email', icon: User },
  { key: 'paciente.endereco', label: 'Endereço', icon: User },
  { key: 'paciente.responsavel', label: 'Responsável', icon: User },
];

const SESSION_PLACEHOLDERS = [
  { key: 'sessao.data', label: 'Data da Sessão', icon: Calendar },
  { key: 'sessao.hora', label: 'Hora da Sessão', icon: Clock },
  { key: 'sessao.duracao', label: 'Duração', icon: Clock },
  { key: 'sessao.tipo', label: 'Tipo de Sessão', icon: FileText },
  { key: 'sessao.numero', label: 'Número da Sessão', icon: Hash },
];

const PROFESSIONAL_PLACEHOLDERS = [
  { key: 'profissional.nome', label: 'Nome do Profissional', icon: User },
  { key: 'profissional.registro', label: 'Registro Profissional', icon: FileText },
  { key: 'profissional.especialidade', label: 'Especialidade', icon: FileText },
];

const SYSTEM_PLACEHOLDERS = [
  { key: 'sistema.dataAtual', label: 'Data Atual', icon: Calendar },
  { key: 'sistema.horaAtual', label: 'Hora Atual', icon: Clock },
  { key: 'sistema.nomeClinica', label: 'Nome da Clínica', icon: Settings },
];

interface PlaceholderButtonProps {
  placeholder: { key: string; label: string; icon: React.ComponentType<{ className?: string }> };
  onInsert: (key: string) => void;
}

function PlaceholderButton({ placeholder, onInsert }: PlaceholderButtonProps) {
  return (
    <Button
      variant="ghost"
      size="sm"
      className="w-full justify-start h-auto p-2 text-left"
      onClick={() => { onInsert(placeholder.key); }}
    >
      <placeholder.icon className="h-3 w-3 mr-2 flex-shrink-0" />
      <div className="flex-1 min-w-0">
        <div className="text-xs font-medium truncate">{placeholder.label}</div>
        <div className="text-xs text-muted-foreground truncate">[{placeholder.key}]</div>
      </div>
    </Button>
  );
}

export function FieldsSidebar({ onInsertField, onInsertPlaceholder }: FieldsSidebarProps) {
  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center">
          <BracesIcon className="h-4 w-4 mr-2" />
          Elementos Disponíveis
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="px-4 space-y-4">
            {/* Dynamic Fields Section */}
            <div className="space-y-2">
              <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                Campos Dinâmicos
              </h4>
              <div className="space-y-1">
                {FIELD_TYPES.map(field => (
                  <Button
                    key={field.type}
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start h-auto p-2 text-left"
                    onClick={() => { onInsertField(field.type); }}
                  >
                    <field.icon className="h-4 w-4 mr-2 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium truncate">{field.label}</div>
                      <div className="text-xs text-muted-foreground truncate">{field.description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* Patient Placeholders */}
            <div className="space-y-2">
              <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                Dados do Paciente
              </h4>
              <div className="space-y-1">
                {PATIENT_PLACEHOLDERS.map(placeholder => (
                  <PlaceholderButton
                    key={placeholder.key}
                    placeholder={placeholder}
                    onInsert={onInsertPlaceholder}
                  />
                ))}
              </div>
            </div>

            <Separator />

            {/* Session Placeholders */}
            <div className="space-y-2">
              <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                Dados da Sessão
              </h4>
              <div className="space-y-1">
                {SESSION_PLACEHOLDERS.map(placeholder => (
                  <PlaceholderButton
                    key={placeholder.key}
                    placeholder={placeholder}
                    onInsert={onInsertPlaceholder}
                  />
                ))}
              </div>
            </div>

            <Separator />

            {/* Professional Placeholders */}
            <div className="space-y-2">
              <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                Dados do Profissional
              </h4>
              <div className="space-y-1">
                {PROFESSIONAL_PLACEHOLDERS.map(placeholder => (
                  <PlaceholderButton
                    key={placeholder.key}
                    placeholder={placeholder}
                    onInsert={onInsertPlaceholder}
                  />
                ))}
              </div>
            </div>

            <Separator />

            {/* System Placeholders */}
            <div className="space-y-2">
              <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                Dados do Sistema
              </h4>
              <div className="space-y-1">
                {SYSTEM_PLACEHOLDERS.map(placeholder => (
                  <PlaceholderButton
                    key={placeholder.key}
                    placeholder={placeholder}
                    onInsert={onInsertPlaceholder}
                  />
                ))}
              </div>
            </div>

            {/* Bottom padding for scroll */}
            <div className="h-4" />
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

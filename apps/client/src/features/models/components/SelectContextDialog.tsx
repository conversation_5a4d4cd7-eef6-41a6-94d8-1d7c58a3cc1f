import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/shared/ui/dialog";
import { PlaceholderContext } from '@/shared/lib/placeholder-engine';

interface SelectContextDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (context: PlaceholderContext) => void;
}

export function SelectContextDialog({ isOpen, onClose, onSelect }: SelectContextDialogProps) {
  // Implementação do diálogo para seleção de contexto
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Selecionar Contexto</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-2">
          <button onClick={() => onSelect({ patient: {} })}>Paciente</button>
          <button onClick={() => onSelect({ appointment: {} })}><PERSON><PERSON><PERSON></button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

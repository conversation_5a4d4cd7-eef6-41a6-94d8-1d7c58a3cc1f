import React from 'react';
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { <PERSON>rollArea, ScrollBar } from "@/shared/ui/scroll-area";
import { Separator } from "@/shared/ui/separator";
import { LayoutList, Star, FileEdit, FileText, FileBarChart, FileCog } from "lucide-react";

// Tipos para os filtros
type ActiveFilter = "all" | "favorites" | "custom";
type Category = { id: string; name: string; icon: React.ReactNode };

interface ModelFiltersProps {
  activeFilter: ActiveFilter;
  selectedCategories: string[];
  onFilterChange: (filter: ActiveFilter) => void;
  onCategoryToggle: (categoryId: string) => void;
}

// Definição das categorias (pode vir de uma constante ou API)
const categories: Category[] = [
  { id: "report", name: "Relató<PERSON><PERSON>", icon: <FileText className="h-3.5 w-3.5" /> },
  { id: "evaluation", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", icon: <FileBarChart className="h-3.5 w-3.5" /> },
  { id: "form", name: "Formulários", icon: <FileCog className="h-3.5 w-3.5" /> },
  { id: "letter", name: "Cartas", icon: <FileText className="h-3.5 w-3.5" /> }, // Ícone genérico
  { id: "exercise", name: "Exercícios", icon: <FileText className="h-3.5 w-3.5" /> }, // Ícone genérico
  { id: "other", name: "Outros", icon: <FileText className="h-3.5 w-3.5" /> }, // Ícone genérico
];

export function ModelFilters({
  activeFilter,
  selectedCategories,
  onFilterChange,
  onCategoryToggle,
}: ModelFiltersProps) {
  return (
    <div className="flex flex-col gap-4">
      {/* Filtros principais */}
      <div className="flex items-center space-x-2">
        <Button
          variant={activeFilter === "all" ? "secondary" : "ghost"}
          size="sm"
          onClick={() => onFilterChange("all")}
        >
          <LayoutList className="h-4 w-4 mr-2" />
          Todos
        </Button>
        <Button
          variant={activeFilter === "favorites" ? "secondary" : "ghost"}
          size="sm"
          onClick={() => onFilterChange("favorites")}
        >
          <Star className="h-4 w-4 mr-2" />
          Favoritos
        </Button>
        <Button
          variant={activeFilter === "custom" ? "secondary" : "ghost"}
          size="sm"
          onClick={() => onFilterChange("custom")}
        >
          <FileEdit className="h-4 w-4 mr-2" />
          Meus Modelos
        </Button>
      </div>

      <Separator />

      {/* Filtros de categoria */}
      <div className="relative">
        <ScrollArea className="w-full max-w-[calc(100vw-48px)] md:max-w-none">
          <div className="flex items-center space-x-2 px-1 py-1 w-max">
            {categories.map((category) => (
              <Badge
                key={category.id}
                variant={selectedCategories.includes(category.id) ? "default" : "outline"}
                className="cursor-pointer px-3 py-1.5 flex items-center gap-1.5 whitespace-nowrap"
                onClick={() => onCategoryToggle(category.id)}
              >
                {category.icon}
                <span>{category.name}</span>
              </Badge>
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
        {/* Gradiente para indicar scroll */}
        <div className="pointer-events-none absolute right-0 top-0 h-full w-8 bg-gradient-to-l from-background to-transparent" />
      </div>
    </div>
  );
}

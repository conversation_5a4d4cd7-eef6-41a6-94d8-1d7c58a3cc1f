import React from 'react';
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/shared/ui/dialog';
import { ModelFormViewer } from '@/features/models/components/ModelFormViewer';
import { PlaceholderContext } from '@/shared/lib/placeholder-engine';

interface ModelFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  modelContent: string;
  contextData: PlaceholderContext;
  initialTitle?: string;
  onSubmit: (title: string, generatedContent: string) => void;
}

export function ModelFormDialog({
  isOpen,
  onClose,
  title,
  description,
  modelContent,
  contextData,
  initialTitle,
  onSubmit,
}: ModelFormDialogProps) {
  const handleSubmit = (title: string, generatedContent: string) => {
    onSubmit(title, generatedContent);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        <ModelFormViewer
          modelContent={modelContent}
          contextData={contextData}
          initialTitle={initialTitle}
          onSubmit={handleSubmit}
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  );
}

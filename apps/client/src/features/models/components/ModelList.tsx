import { Model } from "@/features/models/types/model.schema";
import { ModelListItem } from "./ModelListItem";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/shared/ui/card";
import { Badge } from "@/shared/ui/badge";
import { Button } from "@/shared/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/shared/ui/dropdown-menu";
import { cn } from "@/shared/lib/utils";
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Star, Copy, FileEdit, FileText, Trash2, Clock, FileBarChart, FileCog, Loader2, Wand2 } from "lucide-react";
import { PlaceholderContext } from "@/shared/lib/placeholder-engine";

interface ModelListProps {
  models: Model[];
  isLoading: boolean;
  viewMode?: "grid" | "list";
  onToggleFavorite: (id: string, isFavorite: boolean) => void;
  onDelete: (id: string) => void;
  onDuplicate: (id: string) => void;
  onEdit: (id: string) => void;
  onUse: (model: Model, e?: React.MouseEvent, context?: PlaceholderContext) => void;
  onSelect?: (id: string) => void;
  selectedModelId?: string | null;
  onOpenWizard?: (model: Model) => void; // Nova prop para o wizard
}

export function ModelList({
  models,
  isLoading,
  viewMode = "grid",
  onToggleFavorite,
  onDelete,
  onDuplicate,
  onEdit,
  onUse,
  onSelect,
  selectedModelId,
  onOpenWizard
}: ModelListProps) {
  
  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case "report": return <FileText className="h-3.5 w-3.5" />;
      case "evaluation": return <FileBarChart className="h-3.5 w-3.5" />;
      case "form": return <FileCog className="h-3.5 w-3.5" />;
      default: return <FileText className="h-3.5 w-3.5" />;
    }
  };

  const getCategoryName = (categoryId: string) => {
    const categories = [
      { id: "report", name: "Relatórios" },
      { id: "evaluation", name: "Avaliações" },
      { id: "form", name: "Formulários" },
      { id: "letter", name: "Cartas" },
      { id: "exercise", name: "Exercícios" },
      { id: "other", name: "Outros" },
    ];
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : "Outro";
  };
  
  const handleExportModel = (model: Model) => {
    // Implementar a lógica de exportação aqui se necessário, ou recebê-la por props.
  };

  const renderModelCard = (model: Model) => (
    <Card
      key={model.id}
      className={cn(
        "cursor-pointer hover:border-primary/50 transition-colors flex flex-col",
        selectedModelId === model.id && "border-primary"
      )}
      onClick={() => onSelect?.(model.id)}
    >
      <CardHeader className="p-4 pb-2 flex flex-row items-start justify-between">
        <div>
          <Badge variant="outline" className="mb-2 text-xs flex items-center gap-1">
            {getCategoryIcon(model.category)}
            <span>{getCategoryName(model.category)}</span>
          </Badge>
          <CardTitle className="text-base line-clamp-1">{model.title}</CardTitle>
        </div>
        <div className="flex gap-0.5">
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={(e) => {
            e.stopPropagation();
            onToggleFavorite(model.id, !model.isFavorite);
          }}>
            <Star className={cn("h-4 w-4", model.isFavorite && "fill-yellow-400 text-yellow-400")} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-2 flex-grow">
        <p className="text-xs text-muted-foreground line-clamp-2">{model.description}</p>
      </CardContent>
      <CardFooter className="bg-muted/20 px-4 py-2 text-xs flex justify-between items-center border-t mt-auto">
        <div className="flex items-center">
          <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
          <span className="text-muted-foreground">{format(new Date(model.createdAt), 'dd/MM/yyyy', { locale: ptBR })}</span>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-6 px-1" onClick={(e) => e.stopPropagation()}>
              Ações
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent onClick={(e) => e.stopPropagation()}>
            <DropdownMenuItem onClick={(e) => { onUse(model, e); }}>
              <FileEdit className="mr-2 h-4 w-4" /> Usar
            </DropdownMenuItem>
            {onOpenWizard && (
              <DropdownMenuItem onClick={() => { onOpenWizard(model); }}>
                <Wand2 className="mr-2 h-4 w-4" /> Aplicar Modelo
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={() => { onDuplicate(model.id); }}>
              <Copy className="mr-2 h-4 w-4" /> Duplicar
            </DropdownMenuItem>
            {model.type === "custom" && (
              <DropdownMenuItem onClick={() => { onEdit(model.id); }}>
                <FileEdit className="mr-2 h-4 w-4" /> Editar
              </DropdownMenuItem>
            )}
            {model.type === "custom" && (
              <DropdownMenuItem
                onClick={() => { onDelete(model.id); }}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" /> Excluir
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (models.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center mt-10">
        <p className="text-muted-foreground">Nenhum modelo encontrado.</p>
      </div>
    );
  }

  return (
    <div className={cn(
      "grid gap-4 p-4",
      viewMode === "grid" ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" : "grid-cols-1"
    )}>
      {models.map(model =>
        viewMode === "grid" ? (
          renderModelCard(model)
        ) : (
          <ModelListItem
            key={model.id}
            model={model}
            isSelected={selectedModelId === model.id}
            onSelect={() => onSelect?.(model.id)}
            onToggleFavorite={onToggleFavorite}
            onUseModel={onUse}
            onDuplicateModel={onDuplicate}
            onStartEditing={onEdit}
            onDeleteModel={onDelete}
            isDeleting={false} // Ajustar se necessário
          />
        )
      )}
    </div>
  );
} 
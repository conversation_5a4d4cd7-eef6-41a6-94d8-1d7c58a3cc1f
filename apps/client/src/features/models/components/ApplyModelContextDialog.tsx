import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/shared/ui/dialog';
import { Button } from '@/shared/ui/button';
import { Label } from '@/shared/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { Loader2, AlertCircle, InfoIcon } from 'lucide-react';
import { Alert, AlertTitle, AlertDescription } from '@/shared/ui/alert';
import { Skeleton } from '@/shared/ui/skeleton';
import { usePatientQuery } from '@/features/patients/hooks';
import { useAppointmentQuery } from '@/features/calendar/hooks/useAppointmentQuery';
import { useAuthQuery } from '@/features/auth/hooks';
import { Model } from '@/features/models/types/model.schema';
import { PlaceholderContext, processPlaceholders } from '@/shared/lib/placeholder-engine';
import { formatDate } from '@/shared/lib/date';
import { useToast } from '@/shared/hooks/use-toast';
import { differenceInMinutes } from 'date-fns';
import { Patient } from '@/features/patients/types/patient.schema';

interface ApplyModelContextDialogProps {
  isOpen: boolean;
  onClose: () => void;
  model: Model;
  initialContext?: {
    patientId?: string;
    appointmentId?: string;
  };
  onApply: (
    processedContent: string,
    context: PlaceholderContext,
    modelTitle: string,
    shouldAppend: boolean
  ) => void;
}

export function ApplyModelContextDialog({
  isOpen,
  onClose,
  model,
  initialContext = {},
  onApply
}: ApplyModelContextDialogProps) {
  const [shouldAppend, setShouldAppend] = useState(false);
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    initialContext.patientId ?? null
  );
  const [selectedAppointmentId, setSelectedAppointmentId] = useState<string | null>(
    initialContext.appointmentId ?? null
  );
  const [isLoadingContextData, setIsLoadingContextData] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [patientError, setPatientError] = useState<string | null>(null);

  const requiresPatientContext = useMemo(
    () => /\[Paciente\.[^\]]+\]/i.test(model.content),
    [model.content]
  );
  const requiresSessionContext = useMemo(
    () => /\[Sessao\.[^\]]+\]/i.test(model.content),
    [model.content]
  );

  const { allPatients, isLoadingList: isLoadingPatients, currentPatient: selectedPatient, isLoadingDetails: isLoadingPatientDetails } = usePatientQuery({ initialPatientId: selectedPatientId });
  const { appointments, isLoading: isLoadingAppointments, getAppointmentById } = useAppointmentQuery(
    { patientId: selectedPatientId && requiresSessionContext ? selectedPatientId : undefined }
  );
  const { user } = useAuthQuery();
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen) {
      setError(null);
      setPatientError(null);
      setSelectedPatientId(initialContext.patientId ?? null);
      setSelectedAppointmentId(initialContext.appointmentId ?? null);
    }
  }, [isOpen, initialContext.patientId, initialContext.appointmentId]);

  const calculateAge = (dateOfBirth: string | Date): number => {
    const dob = typeof dateOfBirth === 'string' ? new Date(dateOfBirth) : dateOfBirth;
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }
    return age;
  };

  const handleConfirmContext = async () => {
    setError(null);
    setPatientError(null);

    if (requiresPatientContext && !selectedPatientId) {
      setPatientError('Selecione um paciente para aplicar este modelo.');
      return;
    }

    setIsLoadingContextData(true);

    try {
      const context: PlaceholderContext = {
        system: { currentDate: new Date() }
      };

      if (selectedPatient) {
        context.patient = {
          id: selectedPatient.id,
          full_name: selectedPatient.full_name,
          date_of_birth: selectedPatient.date_of_birth || undefined,
          age: selectedPatient.date_of_birth ? calculateAge(selectedPatient.date_of_birth) : undefined,
          phone: selectedPatient.phone || undefined,
          email: selectedPatient.email || undefined,
          address: selectedPatient.address || undefined,
          city: selectedPatient.city || undefined,
          state: selectedPatient.state || undefined,
          cpf: selectedPatient.cpf || undefined,
          school: selectedPatient.school || undefined,
                      notes: selectedPatient.notes || undefined,
          gender: selectedPatient.gender || undefined,
          marital_status: selectedPatient.marital_status || undefined,
          rg: selectedPatient.rg || undefined,
          ethnicity: selectedPatient.ethnicity || undefined,
          nationality: selectedPatient.nationality || undefined,
          naturalness: selectedPatient.naturalness || undefined,
          occupation: selectedPatient.occupation || undefined,
        };
      }

      if (user) {
        context.therapist = { ...user };
      }

      if (selectedAppointmentId) {
        const appointmentData = getAppointmentById(selectedAppointmentId);
        if (appointmentData) {
          context.appointment = {
            id: appointmentData.id,
            title: appointmentData.title || undefined,
            start_time: appointmentData.start_time,
            duration_minutes: differenceInMinutes(
              new Date(appointmentData.end_time),
              new Date(appointmentData.start_time)
            ),
            notes: appointmentData.notes || undefined,
          };
        }
      }

      const processedContent = processPlaceholders(model.content, context);
      onApply(processedContent, context, model.title, shouldAppend);

      toast({
        title: 'Modelo aplicado',
        description: `O modelo "${model.title}" foi aplicado com sucesso.`
      });

      setTimeout(() => { onClose(); }, 100);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Ocorreu um erro ao processar o modelo.';
      setError(errorMessage);
    } finally {
      setIsLoadingContextData(false);
    }
  };

  const availableAppointments = useMemo(() => {
    if (!appointments || !selectedPatientId || !requiresSessionContext) {
      return [];
    }
    return [...appointments].sort(
      (a, b) => new Date(b.start_time).getTime() - new Date(a.start_time).getTime()
    );
  }, [appointments, selectedPatientId, requiresSessionContext]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader><DialogTitle>Aplicar Modelo: {model.title}</DialogTitle></DialogHeader>
        <div className="py-4 space-y-4">
          {requiresPatientContext && !initialContext.patientId && (
            <div className="space-y-2">
              <Label htmlFor="patient-select">Selecionar Paciente <span className="text-destructive">*</span></Label>
              {isLoadingPatients ? <Skeleton className="h-10 w-full" /> : (
                <Select value={selectedPatientId ?? ''} onValueChange={setSelectedPatientId} disabled={isLoadingContextData}>
                  <SelectTrigger><SelectValue placeholder="Selecione um paciente" /></SelectTrigger>
                  <SelectContent>
                    {allPatients.map((patient) => (
                      <SelectItem key={patient.id} value={patient.id}>{patient.full_name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          )}
          {requiresSessionContext && selectedPatientId && (
            <div className="space-y-2">
              <Label htmlFor="appointment-select">Associar à Sessão (Opcional)</Label>
              {isLoadingAppointments ? <Skeleton className="h-10 w-full" /> : (
                <Select value={selectedAppointmentId || 'none'} onValueChange={(v) => { setSelectedAppointmentId(v === 'none' ? null : v); }} disabled={isLoadingContextData}>
                  <SelectTrigger><SelectValue placeholder="Selecione uma sessão" /></SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Nenhuma</SelectItem>
                    {availableAppointments.map((appointment) => (
                      <SelectItem key={appointment.id} value={appointment.id}>
                        {appointment.title || 'Sem título'} - {formatDate(new Date(appointment.start_time))}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoadingContextData}>Cancelar</Button>
          <Button onClick={handleConfirmContext} disabled={isLoadingContextData || (requiresPatientContext && !selectedPatientId)}>
            {isLoadingContextData || isLoadingPatientDetails ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Aplicar'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

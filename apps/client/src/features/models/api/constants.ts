// Query keys para TanStack Query - feature de modelos
export const MODELS_QUERY_KEYS = {
  all: ['models'] as const,
  lists: () => [...MODELS_QUERY_KEYS.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...MODELS_QUERY_KEYS.lists(), { filters }] as const,
  details: () => [...MODELS_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...MODELS_QUERY_KEYS.details(), id] as const,
  categories: () => [...MODELS_QUERY_KEYS.all, 'categories'] as const,
  category: (category: string) => [...MODELS_QUERY_KEYS.categories(), category] as const,
  favorites: () => [...MODELS_QUERY_KEYS.all, 'favorites'] as const,
  custom: () => [...MODELS_QUERY_KEYS.all, 'custom'] as const,
  recent: () => [...MODELS_QUERY_KEYS.all, 'recent'] as const,
} as const;

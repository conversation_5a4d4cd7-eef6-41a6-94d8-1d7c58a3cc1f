import { z } from 'zod';

export const assessmentSchema = z.object({
  id: z.string().uuid(),
  patient_id: z.string().uuid(),
  title: z.string(),
  content: z.string().nullable(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

export const createAssessmentSchema = z.object({
  title: z.string().min(1, 'O título é obrigatório.'),
  content: z.string().optional(),
});

export const updateAssessmentSchema = createAssessmentSchema.partial();

export type Assessment = z.infer<typeof assessmentSchema>;
export type CreateAssessmentData = z.infer<typeof createAssessmentSchema>;
export type UpdateAssessmentData = z.infer<typeof updateAssessmentSchema>; 
import { useQuery } from '@tanstack/react-query';
import { axiosInstance } from '@/shared/lib/api.client';
import { Assessment, assessmentSchema } from '../types/assessment.schema';
import { ASSESSMENT_QUERY_KEYS } from '../api/constants';
import { z } from 'zod';

async function getAssessments(patientId: string): Promise<Assessment[]> {
  const response = await axiosInstance.get(`/protected/patients/${patientId}/assessments`);
  const parsedData = z.array(assessmentSchema).safeParse(response.data);

  if (!parsedData.success) {
    console.error("Validation error on getAssessments:", parsedData.error);
    throw new Error("Invalid data received from the server.");
  }

  return parsedData.data;
}

export function useAssessmentsQuery(patientId: string) {
  return useQuery({
    queryKey: ASSESSMENT_QUERY_KEYS.listByPatient(patientId),
    queryFn: () => getAssessments(patientId),
    enabled: !!patientId,
  });
} 
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { axiosInstance } from '@/shared/lib/api.client';
import { useToast } from '@/shared/hooks/use-toast';
import { Assessment, CreateAssessmentData, assessmentSchema } from '../types/assessment.schema';
import { ASSESSMENT_QUERY_KEYS } from '../api/constants';
import { PATIENT_QUERY_KEYS } from '@/features/patients/hooks/usePatientQuery';

export function useAssessmentMutations(patientId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const createAssessmentMutation = useMutation({
    mutationFn: async (data: CreateAssessmentData): Promise<Assessment> => {
      const response = await axiosInstance.post(`/protected/patients/${patientId}/assessments`, data);
      const parsedData = assessmentSchema.safeParse(response.data);
      if (!parsedData.success) {
        console.error(parsedData.error);
        throw new Error("Invalid data from API on assessment creation.");
      }
      return parsedData.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ASSESSMENT_QUERY_KEYS.listByPatient(patientId),
      });
      // Also invalidate the patient's history/details if that's a separate query
      queryClient.invalidateQueries({ queryKey: PATIENT_QUERY_KEYS.detail(patientId) });

      toast({
        title: "Avaliação criada",
        description: `A avaliação "${data.title}" foi criada com sucesso!`
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao criar avaliação",
        description: error?.response?.data?.message || error.message,
        variant: "destructive"
      });
    }
  });

  return {
    createAssessment: createAssessmentMutation.mutate,
    isCreating: createAssessmentMutation.isPending,
  };
} 
import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/shared/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/shared/ui/avatar';
import { Badge } from '@/shared/ui/badge';
import { 
  X, 
  ChevronLeft,
  History, 
  Settings,
  Clock,
  Calendar,
  User,
  FileText,
  Minimize2
} from 'lucide-react';
import { cn } from '@/shared/lib/utils';

interface SessionNoteSlideOverProps {
  isOpen: boolean;
  onClose: () => void;
  patientId?: string;
  appointmentId?: string;
  context?: 'post_appointment' | 'patient_view' | 'quick_note';
  mode?: 'new' | 'edit';
  existingNoteId?: string;
}

// Mock data para demonstração
const mockPatient = {
  id: '1',
  name: '<PERSON>',
  initials: 'MS',
  avatar: null,
  age: 34,
  lastSession: '2024-01-15T14:00:00Z'
};

const mockSessionContext = {
  sessionNumber: 8,
  lastSessionDate: '2024-01-15T14:00:00Z',
  appointmentDuration: 50,
  pendingGoals: [
    'Trabalhar ansiedade social',
    'Técnicas de respiração'
  ],
  lastSessionSummary: 'Paciente demonstrou progresso significativo nas técnicas de relaxamento...'
};

export function SessionNoteSlideOver({
  isOpen,
  onClose,
  patientId,
  appointmentId,
  context = 'patient_view',
  mode = 'new',
  existingNoteId
}: SessionNoteSlideOverProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  
  // Determinar largura do slide-over baseado no contexto
  const getSlideOverWidth = () => {
    if (isCollapsed) return 'w-16';
    
    switch (context) {
      case 'post_appointment':
      case 'patient_view':
        return 'w-[900px]';
      case 'quick_note':
        return 'w-[600px]';
      default:
        return 'w-[750px]';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Fechar com ESC
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-opacity duration-200"
        onClick={onClose}
      />
      
      {/* Slide-over */}
      <div className={cn(
        "fixed top-0 right-0 h-full bg-white shadow-2xl z-50 transition-all duration-300 ease-out flex flex-col",
        getSlideOverWidth(),
        "border-l border-gray-200"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-white shrink-0">
          {!isCollapsed && (
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={mockPatient.avatar ?? undefined} />
                <AvatarFallback className="bg-blue-100 text-blue-700">
                  {mockPatient.initials}
                </AvatarFallback>
              </Avatar>
              
              <div>
                <h2 className="font-semibold text-lg">{mockPatient.name}</h2>
                <div className="flex items-center gap-3 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    Sessão #{mockSessionContext.sessionNumber}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(new Date().toISOString())}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {mockSessionContext.appointmentDuration} min
                  </span>
                </div>
              </div>
            </div>
          )}
          
          {/* Controles */}
          <div className="flex items-center gap-2">
            {!isCollapsed && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => { setIsCollapsed(true); }}
                  className="h-8 w-8 p-0"
                  title="Minimizar"
                >
                  <Minimize2 className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  title="Histórico"
                >
                  <History className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  title="Configurações"
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </>
            )}
            
            {isCollapsed && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => { setIsCollapsed(false); }}
                className="h-8 w-8 p-0"
                title="Expandir"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
              title="Fechar"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Corpo - só mostra se não estiver colapsado */}
        {!isCollapsed && (
          <>
            {/* Área principal */}
            <div className="flex-1 overflow-hidden">
              <div className="h-full flex">
                {/* Editor principal */}
                <div className="flex-1 p-6 overflow-y-auto">
                  <div className="space-y-4">
                    {/* Placeholder para o editor */}
                    <div className="min-h-[400px] border-2 border-dashed border-gray-200 rounded-lg flex items-center justify-center">
                      <div className="text-center text-muted-foreground">
                        <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                        <div className="text-lg font-medium mb-2">Editor de Sessão</div>
                        <div className="text-sm">
                          Aqui ficará o RichTextEditor com placeholder inteligente
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Sidebar de insights */}
                <div className="w-80 border-l bg-gray-50/50 p-4 overflow-y-auto">
                  <div className="space-y-4">
                    {/* Última sessão */}
                    <div className="bg-white rounded-lg p-3 border">
                      <h3 className="font-medium text-sm mb-2 flex items-center gap-2">
                        <History className="h-4 w-4" />
                        Última Sessão
                      </h3>
                      <div className="text-xs text-muted-foreground mb-2">
                        {formatDate(mockSessionContext.lastSessionDate)}
                      </div>
                      <p className="text-xs leading-relaxed">
                        {mockSessionContext.lastSessionSummary}
                      </p>
                    </div>
                    
                    {/* Objetivos pendentes */}
                    <div className="bg-white rounded-lg p-3 border">
                      <h3 className="font-medium text-sm mb-2">Objetivos Pendentes</h3>
                      <div className="space-y-2">
                        {mockSessionContext.pendingGoals.map((goal, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-orange-400 rounded-full" />
                            <span className="text-xs">{goal}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {/* Placeholder para mais insights */}
                    <div className="bg-white rounded-lg p-3 border">
                      <h3 className="font-medium text-sm mb-2">Evolução</h3>
                      <div className="text-xs text-muted-foreground">
                        Gráficos de progresso aparecerão aqui
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="border-t bg-white p-4 shrink-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {context === 'post_appointment' ? 'Pós-consulta' : 
                     context === 'patient_view' ? 'Visualização do paciente' : 
                     'Nota rápida'}
                  </Badge>
                  
                  {appointmentId && (
                    <Badge variant="outline" className="text-xs">
                      Vinculada ao agendamento
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  <Button variant="outline" onClick={onClose}>
                    Cancelar
                  </Button>
                  <Button>
                    {mode === 'edit' ? 'Atualizar Sessão' : 'Salvar Sessão'}
                  </Button>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
}

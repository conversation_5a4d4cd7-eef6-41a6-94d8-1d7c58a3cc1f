import { SessionNoteSlideOver } from './SessionNoteSlideOver';
import { useSessionNoteModal } from '../hooks/useSessionNoteModal';

export function SessionNoteModalProvider() {
  const {
    isOpen,
    patientId,
    appointmentId,
    context,
    mode,
    existingNoteId,
    closeModal
  } = useSessionNoteModal();

  return (
    <SessionNoteSlideOver
      isOpen={isOpen}
      onClose={closeModal}
      patientId={patientId}
      appointmentId={appointmentId}
      context={context}
      mode={mode}
      existingNoteId={existingNoteId}
    />
  );
}

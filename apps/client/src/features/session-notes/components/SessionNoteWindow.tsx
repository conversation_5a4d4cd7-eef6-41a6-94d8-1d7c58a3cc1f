import { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/shared/ui/avatar';
import { Badge } from '@/shared/ui/badge';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { FloatingWindow } from '@/shared/ui/floating-window';
import { SimpleRichTextEditor } from '@/shared/ui/simple-rich-text-editor';
import { ConfirmationDialog } from '@/shared/ui/confirmation-dialog';
import { useSessionNotesMutations } from '@/features/notes/hooks/useSessionNotesMutations';
import { useToast } from '@/shared/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { DASHBOARD_QUERY_KEYS } from '@/features/dashboard/hooks/useDashboardQuery';
import {
  History,
  Settings,
  Clock,
  Calendar,
  User
} from 'lucide-react';
import { SessionNoteData } from '../types/session-note.schema';

interface SessionNoteWindowProps {
  data: SessionNoteData;
  isMinimized: boolean;
  isFullscreen: boolean;
  zIndex: number;
  minimizedIndex?: number;
  windowIndex?: number;
  onMinimize: () => void;
  onMaximize: () => void;
  onFullscreen: () => void;
  onClose: () => void;
  onFocus: () => void;
}

export function SessionNoteWindow({
  data,
  isMinimized,
  isFullscreen,
  zIndex,
  minimizedIndex = 0,
  windowIndex = 0,
  onMinimize,
  onMaximize,
  onFullscreen,
  onClose,
  onFocus
}: SessionNoteWindowProps) {
  // Estado para o conteúdo da nota
  const [noteContent, setNoteContent] = useState(data.content || '');
  const [noteTitle, setNoteTitle] = useState(data.title || '');
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [createdNoteId, setCreatedNoteId] = useState<string | null>(null);

  // Hooks para API e toast
  const { createSessionNote, updateSessionNote } = useSessionNotesMutations(data.patientId);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Detectar mudanças não salvas
  useEffect(() => {
    const originalTitle = data.title || '';
    const originalContent = data.content || '';
    const hasChanges = noteTitle !== originalTitle || noteContent !== originalContent;
    setHasUnsavedChanges(hasChanges);
  }, [noteTitle, noteContent, data.title, data.content]);

  // Auto-save após 3 segundos de inatividade
  useEffect(() => {
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
    }

    // Só fazer auto-save se há mudanças, conteúdo válido e não está salvando
    if (hasUnsavedChanges && noteTitle.trim() && noteContent.trim() && !isSaving) {
      const timer = setTimeout(() => {
        handleAutoSave();
      }, 3000); // 3 segundos de delay

      setAutoSaveTimer(timer);
    }

    return () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }
    };
  }, [noteTitle, noteContent, hasUnsavedChanges, isSaving, createdNoteId]);

  // Cleanup do timer ao desmontar
  useEffect(() => {
    return () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }
    };
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const mockSessionContext = {
    lastSessionDate: '2024-01-15T14:00:00Z',
    appointmentDuration: 50,
    pendingGoals: [
      'Trabalhar ansiedade social',
      'Técnicas de respiração'
    ],
    lastSessionSummary: 'Paciente demonstrou progresso significativo nas técnicas de relaxamento...'
  };

  // Função de auto-save (silenciosa)
  const handleAutoSave = () => {
    if (!noteTitle.trim() || !noteContent.trim() || isSaving) return;

    console.log('🔄 Auto-save disparado:', {
      mode: data.mode,
      createdNoteId,
      existingNoteId: data.existingNoteId,
      hasUnsavedChanges
    });

    setIsSaving(true);

    // Se já temos um ID de nota criada ou estamos em modo edit, atualizar
    const noteIdToUpdate = createdNoteId ?? (data.mode === 'edit' ? data.existingNoteId : null);

    if (noteIdToUpdate) {
      console.log('📝 Atualizando nota existente:', noteIdToUpdate);
      updateSessionNote({
        id: noteIdToUpdate,
        title: noteTitle,
        content: noteContent,
        appointment_id: data.appointmentId || null,
      }, {
        onSuccess: () => {
          setHasUnsavedChanges(false);
          setIsSaving(false);
          // Invalidar query do dashboard para atualizar notas pendentes
          void queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.summary });
        },
        onError: () => {
          setIsSaving(false);
        }
      });
    } else {
      console.log('✨ Criando nova nota (primeira vez)');
      // Primeira vez - criar nova nota
      createSessionNote({
        patient_id: data.patientId,
        appointment_id: data.appointmentId || null,
        title: noteTitle,
        content: noteContent,
      }, {
        onSuccess: (createdNote) => {
          console.log('✅ Nota criada com sucesso:', createdNote.id);
          // Salvar o ID da nota criada para próximos auto-saves
          setCreatedNoteId(createdNote.id);
          setHasUnsavedChanges(false);
          setIsSaving(false);
          // Invalidar query do dashboard para atualizar notas pendentes
          queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.summary });
        },
        onError: () => {
          setIsSaving(false);
        }
      });
    }
  };

  // Funções para salvar e cancelar
  const handleSave = () => {
    if (!noteTitle.trim() || !noteContent.trim()) {
      toast({
        variant: 'destructive',
        title: 'Campos obrigatórios',
        description: 'Título e conteúdo são obrigatórios.',
      });
      return;
    }

    setIsSaving(true);

    // Se já temos um ID de nota criada ou estamos em modo edit, atualizar
    const noteIdToUpdate = createdNoteId || (data.mode === 'edit' ? data.existingNoteId : null);

    if (noteIdToUpdate) {
      // Atualizar nota existente
      updateSessionNote({
        id: noteIdToUpdate,
        title: noteTitle,
        content: noteContent,
        appointment_id: data.appointmentId || null,
      }, {
        onSuccess: () => {
          toast({
            title: 'Nota atualizada',
            description: 'A nota de sessão foi atualizada com sucesso.',
          });
          setHasUnsavedChanges(false);
          setIsSaving(false);
          // Invalidar query do dashboard para atualizar notas pendentes
          queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.summary });
          onClose();
        },
        onError: () => {
          toast({
            variant: 'destructive',
            title: 'Erro ao atualizar',
            description: 'Ocorreu um erro ao atualizar a nota. Tente novamente.',
          });
          setIsSaving(false);
        }
      });
    } else {
      // Criar nova nota
      createSessionNote({
        patient_id: data.patientId,
        appointment_id: data.appointmentId || null,
        title: noteTitle,
        content: noteContent,
      }, {
        onSuccess: (createdNote) => {
          // Salvar o ID da nota criada
          setCreatedNoteId(createdNote.id);
          toast({
            title: 'Nota salva',
            description: 'A nota de sessão foi salva com sucesso.',
          });
          setHasUnsavedChanges(false);
          setIsSaving(false);
          // Invalidar query do dashboard para atualizar notas pendentes
          queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.summary });
          onClose();
        },
        onError: () => {
          toast({
            variant: 'destructive',
            title: 'Erro ao salvar',
            description: 'Ocorreu um erro ao salvar a nota. Tente novamente.',
          });
          setIsSaving(false);
        }
      });
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      setShowConfirmDialog(true);
    } else {
      onClose();
    }
  };

  const handleConfirmClose = () => {
    onClose();
  };

  // TODO: Preparação para integração futura com modelos
  // Esta função será implementada quando integrarmos com o sistema de modelos
  const handleUseTemplate = () => {
    // Futura integração:
    // 1. Abrir modal de seleção de modelos de sessão
    // 2. Permitir escolha de modelo específico para notas de sessão
    // 3. Processar placeholders do modelo com dados do paciente/sessão
    // 4. Inserir conteúdo processado no editor
    // 5. Manter funcionalidade de append vs replace
    console.log('Usar modelo - funcionalidade futura');
  };

  const headerContent = (
    <div className="flex items-center gap-3 w-full">
      <div className="flex-1">
        <Input
          value={noteTitle}
          onChange={(e) => { setNoteTitle(e.target.value); }}
          placeholder="Título da nota de sessão"
          className="border-0 shadow-none text-base font-medium bg-transparent px-0 focus-visible:ring-0"
        />
      </div>
      <div className="flex items-center gap-3 text-xs text-muted-foreground">
        <span className="flex items-center gap-1">
          <User className="h-3 w-3" />
          Sessão #{data.sessionNumber}
        </span>
        <span className="flex items-center gap-1">
          <Calendar className="h-3 w-3" />
          {data.appointmentId ? formatDate(data.scheduledAt || new Date().toISOString()) : formatDate(new Date().toISOString())}
        </span>
        <span className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          {data.duration || mockSessionContext.appointmentDuration} min
        </span>
        {data.appointmentId && (
          <span className="flex items-center gap-1 text-amber-600">
            <div className="w-2 h-2 bg-amber-600 rounded-full" />
            Pós-consulta
          </span>
        )}
        {data.appointmentId && (
          <span className="flex items-center gap-1 text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
            ID: {data.appointmentId.slice(-8)}
          </span>
        )}
        {isSaving && (
          <span className="flex items-center gap-1 text-blue-600">
            <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse" />
            Salvando...
          </span>
        )}
        <Button
          variant="ghost"
          size="sm"
          className="h-7 w-7 p-0"
          title="Histórico"
        >
          <History className="h-3 w-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleUseTemplate}
          className="h-7 px-2 text-xs"
          title="Usar modelo de sessão"
        >
          Modelo
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-7 w-7 p-0"
          title="Configurações"
        >
          <Settings className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );

  const minimizedContent = (
    <div className="flex items-center gap-2 w-full">
      <Avatar className="h-6 w-6 flex-shrink-0">
        <AvatarImage src={data.patientAvatar} />
        <AvatarFallback className="bg-blue-500 text-white text-xs">
          {data.patientInitials}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0 overflow-hidden">
        <div className="text-sm font-medium truncate leading-tight">{data.patientName}</div>
        <div className="text-xs opacity-90 truncate leading-tight">Sessão #{data.sessionNumber}</div>
      </div>
    </div>
  );

  const footerContent = (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="text-xs">
          {data.context === 'post_appointment' ? 'Pós-consulta' : 
           data.context === 'patient_view' ? 'Visualização do paciente' : 
           'Nota rápida'}
        </Badge>
        
        {data.appointmentId && (
          <Badge variant="outline" className="text-xs">
            Vinculada ao agendamento
          </Badge>
        )}
      </div>
      
      <div className="flex items-center gap-2">
        <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
          Cancelar
        </Button>
        <Button
          onClick={handleSave}
          disabled={!noteContent.trim() || !noteTitle.trim() || isSaving}
        >
          {isSaving ? 'Salvando...' : (data.mode === 'edit' ? 'Atualizar Sessão' : 'Salvar Sessão')}
        </Button>
      </div>
    </div>
  );

  const titleContent = (
    <div className="flex items-center gap-3">
      <Avatar className="h-8 w-8">
        <AvatarImage src={data.patientAvatar} />
        <AvatarFallback className="bg-blue-100 text-blue-700">
          {data.patientInitials}
        </AvatarFallback>
      </Avatar>
      <span className="flex items-center gap-2">
        {data.patientName}
        {hasUnsavedChanges && (
          <div className="w-2 h-2 bg-orange-500 rounded-full" title="Alterações não salvas" />
        )}
      </span>
    </div>
  );

  return (
    <>
      <FloatingWindow
      id={data.id}
      title={titleContent}
      isMinimized={isMinimized}
      isFullscreen={isFullscreen}
      zIndex={zIndex}
      minimizedIndex={minimizedIndex}
      windowIndex={windowIndex}
      onMinimize={onMinimize}
      onMaximize={onMaximize}
      onFullscreen={onFullscreen}
      onClose={onClose}
      onFocus={onFocus}
      headerContent={headerContent}
      footerContent={footerContent}
      minimizedContent={minimizedContent}
    >
      <div className="flex h-full">
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="space-y-4">
            <SimpleRichTextEditor
              value={noteContent}
              onChange={setNoteContent}
              placeholder="Digite as notas da sessão aqui..."
              minHeight="400px"
              className="border-0 shadow-none"
            />
          </div>
        </div>
        
        {isFullscreen && (
          <div className="w-72 border-l border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-800/50 p-3 overflow-y-auto">
            <div className="space-y-4">
              <div className="bg-white dark:bg-gray-900 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-sm mb-2 flex items-center gap-2">
                  <History className="h-4 w-4" />
                  Última Sessão
                </h3>
                <div className="text-xs text-muted-foreground mb-2">
                  {formatDate(mockSessionContext.lastSessionDate)}
                </div>
                <p className="text-xs leading-relaxed">
                  {mockSessionContext.lastSessionSummary}
                </p>
              </div>
              
              <div className="bg-white dark:bg-gray-900 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-sm mb-2">Objetivos Pendentes</h3>
                <div className="space-y-2">
                  {mockSessionContext.pendingGoals.map((goal, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-orange-400 rounded-full" />
                      <span className="text-xs">{goal}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </FloatingWindow>

    <ConfirmationDialog
      isOpen={showConfirmDialog}
      onClose={() => { setShowConfirmDialog(false); }}
      onConfirm={handleConfirmClose}
      title="Alterações não salvas"
      description="Você tem alterações não salvas. Deseja realmente fechar sem salvar?"
      confirmText="Fechar sem salvar"
      cancelText="Continuar editando"
      variant="destructive"
    />
  </>
  );
}

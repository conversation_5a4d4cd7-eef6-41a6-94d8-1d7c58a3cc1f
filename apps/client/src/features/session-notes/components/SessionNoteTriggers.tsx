import { Button } from '@/shared/ui/button';
import { 
  Plus, 
  FileText, 
  Edit3, 
  Zap,
  Calendar,
  User
} from 'lucide-react';
import { useSessionNoteModal } from '../hooks/useSessionNoteModal';

// Trigger para tela de paciente
export function PatientSessionNoteTrigger({ patientId }: { patientId: string }) {
  const { openSessionNote } = useSessionNoteModal();
  
  return (
    <Button 
      onClick={() => openSessionNote({
        patientId,
        context: 'patient_view'
      })}
      className="gap-2"
    >
      <Plus className="h-4 w-4" />
      Nova Nota de Sessão
    </Button>
  );
}

// Trigger para pós-consulta (calendário)
export function PostAppointmentTrigger({ 
  patientId, 
  appointmentId 
}: { 
  patientId: string; 
  appointmentId: string; 
}) {
  const { openPostAppointmentNote } = useSessionNoteModal();
  
  return (
    <Button 
      onClick={() => openPostAppointmentNote(patientId, appointmentId)}
      variant="outline"
      className="gap-2"
    >
      <FileText className="h-4 w-4" />
      <PERSON><PERSON><PERSON><PERSON>
    </Button>
  );
}

// Trigger para edição de nota existente
export function EditSessionNoteTrigger({ 
  patientId, 
  noteId 
}: { 
  patientId: string; 
  noteId: string; 
}) {
  const { editSessionNote } = useSessionNoteModal();
  
  return (
    <Button 
      onClick={() => editSessionNote(patientId, noteId)}
      variant="ghost"
      size="sm"
      className="gap-2"
    >
      <Edit3 className="h-3 w-3" />
      Editar
    </Button>
  );
}

// Trigger para nota rápida
export function QuickNoteTrigger({ patientId }: { patientId: string }) {
  const { openQuickNote } = useSessionNoteModal();
  
  return (
    <Button 
      onClick={() => openQuickNote(patientId)}
      variant="outline"
      size="sm"
      className="gap-2"
    >
      <Zap className="h-4 w-4" />
      Nota Rápida
    </Button>
  );
}

// Componente de demonstração com todos os triggers
export function SessionNoteTriggersDemo() {
  const mockPatientId = 'patient-123';
  const mockAppointmentId = 'appointment-456';
  const mockNoteId = 'note-789';
  
  return (
    <div className="space-y-6 p-6 bg-gray-50 rounded-lg">
      <div>
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <User className="h-5 w-5" />
          Triggers da Tela de Paciente
        </h3>
        <div className="flex gap-2">
          <PatientSessionNoteTrigger patientId={mockPatientId} />
          <QuickNoteTrigger patientId={mockPatientId} />
        </div>
      </div>
      
      <div>
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Triggers do Calendário
        </h3>
        <div className="flex gap-2">
          <PostAppointmentTrigger 
            patientId={mockPatientId} 
            appointmentId={mockAppointmentId} 
          />
        </div>
      </div>
      
      <div>
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Triggers de Lista de Notas
        </h3>
        <div className="flex gap-2">
          <EditSessionNoteTrigger 
            patientId={mockPatientId} 
            noteId={mockNoteId} 
          />
        </div>
      </div>
      
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Atalhos de Teclado</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <div><kbd className="bg-blue-200 px-2 py-1 rounded text-xs">Ctrl + N</kbd> - Nova nota (se estiver na tela de um paciente)</div>
          <div><kbd className="bg-blue-200 px-2 py-1 rounded text-xs">Esc</kbd> - Fechar modal</div>
        </div>
      </div>
    </div>
  );
}

import { z } from 'zod';

export const SessionNoteContextSchema = z.enum(['post_appointment', 'patient_view', 'quick_note']);
export const SessionNoteModeSchema = z.enum(['new', 'edit']);

export const SessionNoteDataSchema = z.object({
  id: z.string(),
  patientId: z.string(),
  patientName: z.string(),
  patientInitials: z.string(),
  patientAvatar: z.string().optional(),
  sessionNumber: z.number().positive(),
  appointmentId: z.string().optional(),
  scheduledAt: z.string().optional(),
  duration: z.number().positive().optional(),
  context: SessionNoteContextSchema,
  mode: SessionNoteModeSchema,
  existingNoteId: z.string().optional(),
  title: z.string(),
  content: z.string().optional()
});

export const PatientSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().optional()
});

export const AppointmentSchema = z.object({
  id: z.string(),
  patientId: z.string(),
  sessionNumber: z.number().positive(),
  scheduledAt: z.string(),
  duration: z.number().positive(),
  status: z.enum(['scheduled', 'completed', 'cancelled'])
});

export const SessionNoteSchema = z.object({
  id: z.string(),
  patientId: z.string(),
  appointmentId: z.string().optional(),
  sessionNumber: z.number().positive(),
  title: z.string(),
  content: z.string(),
  createdAt: z.string(),
  updatedAt: z.string()
});

export type SessionNoteContext = z.infer<typeof SessionNoteContextSchema>;
export type SessionNoteMode = z.infer<typeof SessionNoteModeSchema>;
export type SessionNoteData = z.infer<typeof SessionNoteDataSchema>;
export type Patient = z.infer<typeof PatientSchema>;
export type Appointment = z.infer<typeof AppointmentSchema>;
export type SessionNote = z.infer<typeof SessionNoteSchema>;

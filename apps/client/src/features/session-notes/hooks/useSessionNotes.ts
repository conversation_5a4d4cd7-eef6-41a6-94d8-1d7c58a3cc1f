import { useFloatingWindowsContext } from '@/shared/contexts/FloatingWindowsContext';
import { SessionNoteData, Patient, Appointment, SessionNote } from '../types/session-note.schema';
import { 
  createPostAppointmentSessionNote,
  createEditSessionNote,
  createPatientViewSessionNote,
  createQuickSessionNote
} from '../utils/sessionNoteHelpers';

export function useSessionNotes() {
  const { openWindow, getWindowsByType } = useFloatingWindowsContext();

  // Função para verificar se uma janela já existe e dar feedback
  const checkExistingWindow = (windowId: string, windowTitle: string) => {
    const existingWindow = getWindowsByType('session-note').find(w => w.id === windowId);

    if (existingWindow) {
      if (existingWindow.isMinimized) {
        // Se existe mas está minimizada, apenas maximiza
        return { exists: true, action: 'maximize' };
      } else {
        // Se já está aberta, apenas foca
        return { exists: true, action: 'focus' };
      }
    }

    return { exists: false, action: 'create' };
  };

  const openPostAppointmentNote = (patient: Patient, appointment: Appointment) => {
    const sessionNoteData = createPostAppointmentSessionNote(patient, appointment);
    const windowCheck = checkExistingWindow(sessionNoteData.id, sessionNoteData.title);

    // Se a janela já existe, o openWindow vai lidar com isso automaticamente
    // (maximizar se minimizada ou focar se já aberta)
    openWindow({
      id: sessionNoteData.id,
      type: 'session-note',
      title: sessionNoteData.title,
      data: sessionNoteData
    });
  };

  const openEditNote = (patient: Patient, sessionNote: SessionNote) => {
    const sessionNoteData = createEditSessionNote(patient, sessionNote);
    
    openWindow({
      id: sessionNoteData.id,
      type: 'session-note',
      title: sessionNoteData.title,
      data: sessionNoteData
    });
  };

  const openPatientViewNote = (patient: Patient, sessionNote: SessionNote) => {
    const sessionNoteData = createPatientViewSessionNote(patient, sessionNote);
    
    openWindow({
      id: sessionNoteData.id,
      type: 'session-note',
      title: sessionNoteData.title,
      data: sessionNoteData
    });
  };

  const openQuickNote = (patient: Patient, sessionNumber: number) => {
    const sessionNoteData = createQuickSessionNote(patient, sessionNumber);
    const windowCheck = checkExistingWindow(sessionNoteData.id, sessionNoteData.title);

    // Se a janela já existe, o openWindow vai lidar com isso automaticamente
    openWindow({
      id: sessionNoteData.id,
      type: 'session-note',
      title: sessionNoteData.title,
      data: sessionNoteData
    });
  };

  const getOpenSessionNotes = () => {
    return getWindowsByType('session-note');
  };

  return {
    openPostAppointmentNote,
    openEditNote,
    openPatientViewNote,
    openQuickNote,
    getOpenSessionNotes
  };
}

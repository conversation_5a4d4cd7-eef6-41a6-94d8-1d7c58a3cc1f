import { create } from 'zustand';

interface SessionNoteSlideOverState {
  isOpen: boolean;
  patientId?: string;
  appointmentId?: string;
  context?: 'post_appointment' | 'patient_view' | 'quick_note';
  mode?: 'new' | 'edit';
  existingNoteId?: string;
}

interface SessionNoteSlideOverStore extends SessionNoteSlideOverState {
  openSlideOver: (params: Omit<SessionNoteSlideOverState, 'isOpen'>) => void;
  closeSlideOver: () => void;
  setContext: (context: SessionNoteSlideOverState['context']) => void;
}

export const useSessionNoteSlideOverStore = create<SessionNoteSlideOverStore>((set) => ({
  isOpen: false,
  patientId: undefined,
  appointmentId: undefined,
  context: 'patient_view',
  mode: 'new',
  existingNoteId: undefined,

  openSlideOver: (params) => set({
    isOpen: true,
    ...params
  }),

  closeSlideOver: () => set({
    isOpen: false,
    patientId: undefined,
    appointmentId: undefined,
    existingNoteId: undefined
  }),

  setContext: (context) => set({ context })
}));

// Hook conveniente para usar o slide-over
export function useSessionNoteModal() {
  const store = useSessionNoteSlideOverStore();

  const openSessionNote = (params: {
    patientId: string;
    appointmentId?: string;
    context?: SessionNoteSlideOverState['context'];
    mode?: SessionNoteSlideOverState['mode'];
    existingNoteId?: string;
  }) => {
    store.openSlideOver({
      patientId: params.patientId,
      appointmentId: params.appointmentId,
      context: params.context ?? 'patient_view',
      mode: params.mode ?? 'new',
      existingNoteId: params.existingNoteId
    });
  };

  const openQuickNote = (patientId: string) => {
    store.openSlideOver({
      patientId,
      context: 'quick_note',
      mode: 'new'
    });
  };

  const openPostAppointmentNote = (patientId: string, appointmentId: string) => {
    store.openSlideOver({
      patientId,
      appointmentId,
      context: 'post_appointment',
      mode: 'new'
    });
  };

  const editSessionNote = (patientId: string, noteId: string) => {
    store.openSlideOver({
      patientId,
      existingNoteId: noteId,
      context: 'patient_view',
      mode: 'edit'
    });
  };

  return {
    ...store,
    openSessionNote,
    openQuickNote,
    openPostAppointmentNote,
    editSessionNote,
    closeModal: store.closeSlideOver
  };
}

// Componentes principais
export { SessionNoteSlideOver } from './components/SessionNoteSlideOver';
export { SessionNoteModalProvider } from './components/SessionNoteModalProvider';
export { SessionNoteWindow } from './components/SessionNoteWindow';

// Triggers
export {
  PatientSessionNoteTrigger,
  PostAppointmentTrigger,
  EditSessionNoteTrigger,
  QuickNoteTrigger,
  SessionNoteTriggersDemo
} from './components/SessionNoteTriggers';

// Hooks
export {
  useSessionNoteModal,
  useSessionNoteSlideOverStore
} from './hooks/useSessionNoteModal';
export { useSessionNotes } from './hooks/useSessionNotes';

// Types
export type {
  SessionNoteData,
  Patient,
  Appointment,
  SessionNote,
  SessionNoteContext,
  SessionNoteMode
} from './types/session-note.schema';

// Utils
export {
  createPostAppointmentSessionNote,
  createEditSessionNote,
  createPatientViewSessionNote,
  createQuickSessionNote,
  generateSessionTitle,
  getPatientInitials
} from './utils/sessionNoteHelpers';



import { SessionNoteData, Patient, Appointment, SessionNote } from '../types/session-note.schema';

export function getPatientInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

export function createPostAppointmentSessionNote(
  patient: Patient,
  appointment: Appointment
): SessionNoteData {
  // ID único baseado no appointment para evitar duplicatas
  const windowId = `post-appointment-${appointment.id}`;

  // Formatar data e hora da consulta para o título
  const appointmentDateTime = new Date(appointment.scheduledAt);
  const appointmentDate = appointmentDateTime.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
  const appointmentTime = appointmentDateTime.toLocaleTimeString('pt-BR', {
    hour: '2-digit',
    minute: '2-digit'
  });

  return {
    id: windowId,
    patientId: patient.id,
    patientName: patient.name,
    patientInitials: getPatientInitials(patient.name),
    patientAvatar: patient.avatar,
    sessionNumber: appointment.sessionNumber,
    appointmentId: appointment.id,
    scheduledAt: appointment.scheduledAt,
    duration: appointment.duration,
    context: 'post_appointment',
    mode: 'new',
    title: `Sessão ${appointmentDate} ${appointmentTime} - ${patient.name}`
  };
}

export function createEditSessionNote(
  patient: Patient,
  sessionNote: SessionNote
): SessionNoteData {
  const windowId = `session-note-edit-${sessionNote.id}`;

  return {
    id: windowId,
    patientId: patient.id,
    patientName: patient.name,
    patientInitials: getPatientInitials(patient.name),
    patientAvatar: patient.avatar,
    sessionNumber: sessionNote.sessionNumber,
    appointmentId: sessionNote.appointmentId,
    context: sessionNote.appointmentId ? 'post_appointment' : 'quick_note',
    mode: 'edit',
    existingNoteId: sessionNote.id,
    title: sessionNote.title,
    content: sessionNote.content
  };
}

export function createPatientViewSessionNote(
  patient: Patient,
  sessionNote: SessionNote
): SessionNoteData {
  const windowId = `session-note-view-${sessionNote.id}`;

  return {
    id: windowId,
    patientId: patient.id,
    patientName: patient.name,
    patientInitials: getPatientInitials(patient.name),
    patientAvatar: patient.avatar,
    sessionNumber: sessionNote.sessionNumber,
    appointmentId: sessionNote.appointmentId,
    context: 'patient_view',
    mode: 'edit',
    existingNoteId: sessionNote.id,
    title: sessionNote.title,
    content: sessionNote.content
  };
}

export function createQuickSessionNote(
  patient: Patient,
  sessionNumber: number
): SessionNoteData {
  // ID fixo para evitar múltiplas janelas do mesmo tipo para o mesmo paciente
  const windowId = `quick-note-${patient.id}`;

  return {
    id: windowId,
    patientId: patient.id,
    patientName: patient.name,
    patientInitials: getPatientInitials(patient.name),
    patientAvatar: patient.avatar,
    sessionNumber,
    context: 'quick_note',
    mode: 'new',
    title: `Nota Rápida - ${patient.name}`
  };
}

export function generateSessionTitle(
  patientName: string,
  sessionNumber: number,
  context: SessionNoteData['context']
): string {
  switch (context) {
    case 'post_appointment':
      return `Sessão #${sessionNumber} - ${patientName}`;
    case 'patient_view':
      return `Visualização: Sessão #${sessionNumber} - ${patientName}`;
    case 'quick_note':
      return `Nota Rápida - ${patientName}`;
    default:
      return `Sessão #${sessionNumber} - ${patientName}`;
  }
}

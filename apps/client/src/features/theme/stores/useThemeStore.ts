import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

type Theme = "dark" | "light" | "system";

interface ThemeState {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set) => ({
      theme: "system", // Default, será sobrescrito pelo persist
      setTheme: (theme) => {
        set({ theme });
        // Lógica para atualizar classe no <html>
        const root = window.document.documentElement;
        root.classList.remove("light", "dark");
        if (theme === "system") {
          const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
          root.classList.add(systemTheme);
        } else {
          root.classList.add(theme);
        }
      },
    }),
    {
      name: 'vite-ui-theme',
      storage: createJSONStorage(() => localStorage),
      onRehydrateStorage: () => (state) => {
        // Aplica o tema assim que ele é reidratado do storage
        if (state) {
          // Aplicar o tema diretamente sem chamar setTheme para evitar loops
          const theme = state.theme;
          const root = window.document.documentElement;
          root.classList.remove("light", "dark");
          if (theme === "system") {
            const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            root.classList.add(systemTheme);
          } else {
            root.classList.add(theme);
          }
        }
      },
      partialize: (state) => ({ theme: state.theme }),
    }
  )
);

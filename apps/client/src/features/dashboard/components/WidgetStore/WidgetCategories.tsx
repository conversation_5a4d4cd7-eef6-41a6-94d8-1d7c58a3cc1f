import { useState } from 'react';
import { widgetCategories } from '@/features/dashboard/widgetRegistry';
import { WidgetCategory } from '@/features/dashboard/types';
import { cn } from '@/shared/lib/utils';
import { Button } from '@/shared/ui/button';
import { Sparkles } from 'lucide-react';

interface WidgetCategoriesProps {
  selectedCategory: WidgetCategory | 'all';
  onSelectCategory: (category: WidgetCategory | 'all') => void;
}

export function WidgetCategories({ 
  selectedCategory, 
  onSelectCategory 
}: WidgetCategoriesProps) {
  return (
    <div className="flex flex-col space-y-1 w-full">
      <Button
        variant={selectedCategory === 'all' ? 'default' : 'ghost'}
        className={cn(
          "justify-start",
          selectedCategory === 'all' ? 'bg-primary text-primary-foreground' : ''
        )}
        onClick={() => onSelectCategory('all')}
      >
        <Sparkles className="mr-2 h-4 w-4" />
        Todos os Widgets
      </Button>
      
      {widgetCategories.map(category => (
        <Button
          key={category.id}
          variant={selectedCategory === category.id ? 'default' : 'ghost'}
          className={cn(
            "justify-start",
            selectedCategory === category.id ? 'bg-primary text-primary-foreground' : ''
          )}
          onClick={() => onSelectCategory(category.id)}
        >
          {category.label}
        </Button>
      ))}
    </div>
  );
}

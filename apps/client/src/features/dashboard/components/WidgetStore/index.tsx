import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  Di<PERSON>Title,
  DialogFooter
} from '@/shared/ui/dialog';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { Search, X, Info } from 'lucide-react';
import { WidgetCard } from './WidgetCard';
import { WidgetCategories } from './WidgetCategories';
import { 
  WidgetDefinition, 
  WidgetCategory, 
  PlanType 
} from '@/features/dashboard/types';
import { 
  availableWidgets, 
  getWidgetsByCategory,
  widgetCategories
} from '@/features/dashboard/widgetRegistry';

interface WidgetStoreProps {
  isOpen: boolean;
  onClose: () => void;
  onAddWidget: (widgetId: string) => void;
  userPlan?: PlanType;
}

export function WidgetStore({ 
  isOpen, 
  onClose, 
  onAddWidget,
  userPlan = 'free' // Plano padrão se não for fornecido
}: WidgetStoreProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<WidgetCategory | 'all'>('all');
  const [selectedWidget, setSelectedWidget] = useState<WidgetDefinition | null>(null);
  const [activeTab, setActiveTab] = useState<'browse' | 'featured'>('browse');

  // Filtrar widgets com base na pesquisa e categoria
  const filteredWidgets = availableWidgets.filter(widget => {
    const matchesSearch = searchQuery === '' || 
      widget.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      widget.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (widget.tags && widget.tags.some(tag => 
        tag.toLowerCase().includes(searchQuery.toLowerCase())
      ));
    
    const matchesCategory = selectedCategory === 'all' || widget.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Widgets em destaque (novos ou populares)
  const featuredWidgets = availableWidgets.filter(widget => 
    widget.isNew || widget.tags?.includes('popular')
  );

  // Adicionar um widget e fechar o diálogo
  const handleAddWidget = (widgetId: string) => {
    onAddWidget(widgetId);
    // Não fechar o diálogo para permitir adicionar múltiplos widgets
  };

  // Mostrar detalhes de um widget
  const handleShowWidgetInfo = (widget: WidgetDefinition) => {
    setSelectedWidget(widget);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Loja de Widgets</DialogTitle>
          <DialogDescription>
            Adicione widgets ao seu dashboard para personalizar sua experiência.
          </DialogDescription>
        </DialogHeader>

        {selectedWidget ? (
          // Detalhes do widget selecionado
          <div className="py-4 space-y-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className="h-12 w-12 rounded-md bg-primary/10 flex items-center justify-center">
                  <selectedWidget.icon className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">{selectedWidget.title}</h3>
                  <p className="text-sm text-muted-foreground">
                    Categoria: {
                      widgetCategories.find(c => c.id === selectedWidget.category)?.label || 
                      selectedWidget.category
                    }
                  </p>
                </div>
              </div>
              
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => setSelectedWidget(null)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-1">Descrição</h4>
                <p className="text-sm text-muted-foreground">{selectedWidget.description}</p>
              </div>
              
              {selectedWidget.tags && selectedWidget.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-1">Tags</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedWidget.tags.map(tag => (
                      <div key={tag} className="px-2 py-1 bg-muted rounded-md text-xs">
                        {tag}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div>
                <h4 className="text-sm font-medium mb-1">Plano Necessário</h4>
                <div className="px-2 py-1 bg-muted rounded-md text-xs inline-block">
                  {selectedWidget.requiredPlan === 'free' ? 'Gratuito' : 
                   selectedWidget.requiredPlan === 'basic' ? 'Básico' : 
                   selectedWidget.requiredPlan === 'premium' ? 'Premium' : 
                   'Enterprise'}
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium mb-1">Tamanho Padrão</h4>
                <div className="px-2 py-1 bg-muted rounded-md text-xs inline-block">
                  {selectedWidget.defaultSize.w} x {selectedWidget.defaultSize.h}
                </div>
              </div>
            </div>
            
            <div className="flex justify-end pt-4">
              <Button 
                variant="outline" 
                onClick={() => setSelectedWidget(null)}
                className="mr-2"
              >
                Voltar
              </Button>
              
              <Button
                onClick={() => {
                  handleAddWidget(selectedWidget.id);
                  setSelectedWidget(null);
                }}
                disabled={
                  selectedWidget.isComingSoon || 
                  planPriority[userPlan] < planPriority[selectedWidget.requiredPlan]
                }
              >
                {selectedWidget.isComingSoon ? 
                  "Em breve" : 
                  planPriority[userPlan] < planPriority[selectedWidget.requiredPlan] ?
                  "Upgrade Necessário" :
                  "Adicionar ao Dashboard"
                }
              </Button>
            </div>
          </div>
        ) : (
          // Lista de widgets
          <div className="flex flex-col h-full">
            <div className="flex items-center space-x-2 mb-4">
              <div className="relative flex-grow">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar widgets..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1 h-7 w-7"
                    onClick={() => setSearchQuery('')}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            
            <Tabs defaultValue="browse" value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
              <TabsList className="mb-4">
                <TabsTrigger value="browse">Navegar</TabsTrigger>
                <TabsTrigger value="featured">Destaques</TabsTrigger>
              </TabsList>
              
              <div className="flex-grow flex overflow-hidden">
                <TabsContent value="browse" className="flex flex-grow space-x-4 m-0 h-[50vh]">
                  {/* Categorias */}
                  <div className="w-48 border-r pr-2">
                    <WidgetCategories
                      selectedCategory={selectedCategory}
                      onSelectCategory={setSelectedCategory}
                    />
                  </div>
                  
                  {/* Lista de widgets */}
                  <ScrollArea className="flex-grow">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pr-4">
                      {filteredWidgets.length > 0 ? (
                        filteredWidgets.map(widget => (
                          <WidgetCard
                            key={widget.id}
                            widget={widget}
                            userPlan={userPlan}
                            onAdd={handleAddWidget}
                            onInfo={handleShowWidgetInfo}
                          />
                        ))
                      ) : (
                        <div className="col-span-2 flex flex-col items-center justify-center py-8 text-center">
                          <Info className="h-12 w-12 text-muted-foreground/50 mb-4" />
                          <h3 className="text-lg font-medium mb-2">Nenhum widget encontrado</h3>
                          <p className="text-muted-foreground max-w-md">
                            Não encontramos widgets que correspondam à sua pesquisa. 
                            Tente outros termos ou categorias.
                          </p>
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>
                
                <TabsContent value="featured" className="m-0 h-[50vh]">
                  <ScrollArea className="h-full">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pr-4">
                      {featuredWidgets.length > 0 ? (
                        featuredWidgets.map(widget => (
                          <WidgetCard
                            key={widget.id}
                            widget={widget}
                            userPlan={userPlan}
                            onAdd={handleAddWidget}
                            onInfo={handleShowWidgetInfo}
                          />
                        ))
                      ) : (
                        <div className="col-span-2 flex flex-col items-center justify-center py-8 text-center">
                          <Info className="h-12 w-12 text-muted-foreground/50 mb-4" />
                          <h3 className="text-lg font-medium mb-2">Nenhum destaque disponível</h3>
                          <p className="text-muted-foreground max-w-md">
                            Não há widgets em destaque no momento. Confira novamente em breve.
                          </p>
                        </div>
                      )}
                    </div>
                  </ScrollArea>
                </TabsContent>
              </div>
            </Tabs>
            
            <DialogFooter className="mt-4">
              <Button onClick={onClose}>Fechar</Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

// Prioridade dos planos para comparação
const planPriority: Record<PlanType, number> = {
  'free': 0,
  'basic': 1,
  'premium': 2,
  'enterprise': 3
};

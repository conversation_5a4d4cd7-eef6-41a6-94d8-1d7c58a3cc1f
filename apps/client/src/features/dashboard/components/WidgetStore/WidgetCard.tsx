import { WidgetDefinition, PlanType } from '@/features/dashboard/types';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/shared/ui/card';
import { Button } from '@/shared/ui/button';
import { Badge } from '@/shared/ui/badge';
import { Plus, Lock, Info } from 'lucide-react';
import { cn } from '@/shared/lib/utils';

interface WidgetCardProps {
  widget: WidgetDefinition;
  userPlan: PlanType;
  onAdd: (widgetId: string) => void;
  onInfo: (widget: WidgetDefinition) => void;
}

export function WidgetCard({ widget, userPlan, onAdd, onInfo }: WidgetCardProps) {
  // Verificar se o widget está disponível para o plano do usuário
  const planPriority: Record<PlanType, number> = {
    'free': 0,
    'basic': 1,
    'premium': 2,
    'enterprise': 3
  };
  
  const isAvailable = planPriority[userPlan] >= planPriority[widget.requiredPlan];
  const isComingSoon = widget.isComingSoon;
  
  return (
    <Card className={cn(
      "flex flex-col h-full transition-all duration-200",
      !isAvailable && "opacity-70",
      isComingSoon && "opacity-70"
    )}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            <div className="h-8 w-8 rounded-md bg-primary/10 flex items-center justify-center">
              <widget.icon className="h-5 w-5 text-primary" />
            </div>
            <CardTitle className="text-base">{widget.title}</CardTitle>
          </div>
          <div className="flex gap-1">
            {widget.isNew && (
              <Badge variant="default" className="bg-green-500 hover:bg-green-600">
                Novo
              </Badge>
            )}
            {widget.isComingSoon && (
              <Badge variant="outline" className="border-amber-500 text-amber-500">
                Em breve
              </Badge>
            )}
            {!isAvailable && !isComingSoon && (
              <Badge variant="outline" className="border-blue-500 text-blue-500">
                {widget.requiredPlan === 'basic' ? 'Básico' : 
                 widget.requiredPlan === 'premium' ? 'Premium' : 
                 widget.requiredPlan === 'enterprise' ? 'Enterprise' : ''}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-grow pb-2">
        <p className="text-sm text-muted-foreground">{widget.description}</p>
        
        {widget.tags && widget.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {widget.tags.map(tag => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-2">
        <div className="flex justify-between w-full">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => onInfo(widget)}
          >
            <Info className="h-4 w-4 mr-1" />
            Detalhes
          </Button>
          
          <Button
            variant={isAvailable && !isComingSoon ? "default" : "outline"}
            size="sm"
            onClick={() => isAvailable && !isComingSoon && onAdd(widget.id)}
            disabled={!isAvailable || isComingSoon}
          >
            {!isAvailable && !isComingSoon ? (
              <>
                <Lock className="h-4 w-4 mr-1" />
                Upgrade
              </>
            ) : isComingSoon ? (
              "Em breve"
            ) : (
              <>
                <Plus className="h-4 w-4 mr-1" />
                Adicionar
              </>
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}

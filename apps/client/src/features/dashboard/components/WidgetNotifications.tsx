import { useEffect } from 'react';
import { useWidgetsStore } from '@/features/dashboard/stores/useWidgetsStore';
import { Bell, X, AlarmClock } from 'lucide-react';
import { Button } from '@/shared/ui/button';
import { cn } from '@/shared/lib/utils';
import { useNavigate } from '@tanstack/react-router';

export function WidgetNotifications() {
  const { notifications, removeNotification } = useWidgetsStore();
  const navigate = useNavigate();

  // Remover notificações antigas (mais de 1 hora)
  useEffect(() => {
    const now = Date.now();
    notifications.forEach((notification: {id: string, timestamp: number}) => {
      if (now - notification.timestamp > 60 * 60 * 1000) {
        removeNotification(notification.id);
      }
    });
  }, [notifications, removeNotification]);

  // Se não houver notificações, não mostrar nada
  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2 max-w-md">
      {notifications.map((notification: {id: string, type: string, message: string, timestamp: number}) => (
        <div
          key={notification.id}
          className={cn(
            "bg-card border rounded-lg shadow-lg p-4 flex items-start gap-3 timer-notification",
            {
              "border-destructive": notification.type === 'timer',
              "border-primary": notification.type === 'note',
              "border-muted": notification.type === 'other'
            }
          )}
        >
          <div className={cn(
            "rounded-full p-2 flex-shrink-0",
            {
              "bg-destructive/10 text-destructive": notification.type === 'timer',
              "bg-primary/10 text-primary": notification.type === 'note',
              "bg-muted/10 text-muted-foreground": notification.type === 'other'
            }
          )}>
            {notification.type === 'timer' ? (
              <AlarmClock className="h-5 w-5 timer-icon-glow" />
            ) : notification.type === 'note' ? (
              <Bell className="h-5 w-5" />
            ) : (
              <Bell className="h-5 w-5" />
            )}
          </div>

          <div className="flex-1">
            <div className="flex justify-between items-start">
              <h4 className="font-medium">
                {notification.type === 'timer' ? 'Timer Finalizado' :
                 notification.type === 'note' ? 'Nova Nota' : 'Notificação'}
              </h4>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 -mt-1 -mr-1"
                onClick={() => removeNotification(notification.id)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>

            {notification.type === 'timer' && (
              <Button
                variant="outline"
                size="sm"
                className="mt-2 w-full"
                onClick={() => {
                  navigate({ to: '/dashboard' });
                  removeNotification(notification.id);
                }}
              >
                <AlarmClock className="mr-2 h-4 w-4" />
                Ir para o Dashboard
              </Button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

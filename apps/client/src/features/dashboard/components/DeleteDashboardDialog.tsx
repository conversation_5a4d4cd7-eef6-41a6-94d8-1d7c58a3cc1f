import { Button } from "@/shared/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import { AlertTriangle } from "lucide-react";

interface DeleteDashboardDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  dashboardName: string;
  isLoading?: boolean;
  isDefault?: boolean;
}

export function DeleteDashboardDialog({
  isOpen,
  onClose,
  onConfirm,
  dashboardName,
  isLoading = false,
  isDefault = false,
}: DeleteDashboardDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            Excluir Dashboard
          </DialogTitle>
          <DialogDescription>
            Tem certeza que deseja excluir o dashboard "{dashboardName}"?
            {isDefault && (
              <div className="mt-2 text-destructive font-medium">
                Este é seu dashboard padrão. Ao excluí-lo, outro dashboard será definido como padrão.
              </div>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancelar
          </Button>
          <Button 
            variant="destructive" 
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? "Excluindo..." : "Excluir Dashboard"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

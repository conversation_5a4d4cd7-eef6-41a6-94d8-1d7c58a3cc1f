import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/ui/dialog';
import { Button } from '@/shared/ui/button';
import { Switch } from '@/shared/ui/switch';
import { Label } from '@/shared/ui/label';
import { DashboardItem } from '@/features/dashboard/types';
import { AlertTriangle, Rows, Columns, LayoutGrid, Maximize, Minimize } from 'lucide-react';
import {
  availableWidgets,
  widgetCategories,
  getWidgetById
} from '@/features/dashboard/widgetRegistry';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/shared/ui/tabs';

interface DashboardSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  layout: DashboardItem[];
  onToggleWidget: (instanceId: string) => void;
  onResetLayout: () => void;
  compactType?: 'vertical' | 'horizontal' | null;
  setCompactType?: (type: 'vertical' | 'horizontal' | null) => void;
  preventCollision?: boolean;
  setPreventCollision?: (prevent: boolean) => void;
}

export function DashboardSettings({
  isOpen,
  onClose,
  layout,
  onToggleWidget,
  onResetLayout,
  compactType = 'vertical',
  setCompactType,
  preventCollision = false,
  setPreventCollision,
}: DashboardSettingsProps) {
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  // Agrupar widgets por instância
  const widgetInstances = layout.map(item => {
    const widgetDef = getWidgetById(item.widgetId);
    return {
      ...item,
      definition: widgetDef
    };
  });

  // Filtrar widgets por categoria
  const getWidgetsByCategory = (categoryId: string) => {
    if (categoryId === 'all') {
      return widgetInstances;
    }

    return widgetInstances.filter(
      item => item.definition?.category === categoryId
    );
  };

  // Ordenar widgets por título
  const sortedWidgetInstances = (categoryId: string) => {
    return getWidgetsByCategory(categoryId).sort((a, b) => {
      const titleA = a.definition?.title || '';
      const titleB = b.definition?.title || '';
      return titleA.localeCompare(titleB);
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Configurações do Dashboard</DialogTitle>
          <DialogDescription>
            Personalize seu dashboard ativando ou desativando widgets.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4 grid grid-cols-4">
            <TabsTrigger value="all">Todos</TabsTrigger>
            <TabsTrigger value="active">Ativos</TabsTrigger>
            <TabsTrigger value="inactive">Inativos</TabsTrigger>
            <TabsTrigger value="layout">Layout</TabsTrigger>
          </TabsList>

          <ScrollArea className="h-[50vh] pr-4">
            <div className="py-2 space-y-6">
              {/* Todos os widgets */}
              <TabsContent value="all" className="m-0">
                {widgetCategories.map(category => {
                  const categoryWidgets = sortedWidgetInstances(category.id);
                  if (categoryWidgets.length === 0) return null;

                  return (
                    <div key={category.id} className="mb-6">
                      <h3 className="text-sm font-medium mb-3">{category.label}</h3>
                      <div className="space-y-4">
                        {categoryWidgets.map(item => (
                          <div key={item.instanceId} className="flex items-start justify-between space-x-4">
                            <div className="flex-1">
                              <Label htmlFor={`widget-${item.instanceId}`} className="font-medium">
                                {item.definition?.title || 'Widget Desconhecido'}
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                {item.definition?.description || 'Sem descrição disponível'}
                              </p>
                            </div>
                            <Switch
                              id={`widget-${item.instanceId}`}
                              checked={item.visible}
                              onCheckedChange={() => onToggleWidget(item.instanceId)}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </TabsContent>

              {/* Widgets ativos */}
              <TabsContent value="active" className="m-0">
                <div className="space-y-4">
                  {widgetInstances
                    .filter(item => item.visible)
                    .sort((a, b) => (a.definition?.title || '').localeCompare(b.definition?.title || ''))
                    .map(item => (
                      <div key={item.instanceId} className="flex items-start justify-between space-x-4">
                        <div className="flex-1">
                          <Label htmlFor={`widget-active-${item.instanceId}`} className="font-medium">
                            {item.definition?.title || 'Widget Desconhecido'}
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            {item.definition?.description || 'Sem descrição disponível'}
                          </p>
                        </div>
                        <Switch
                          id={`widget-active-${item.instanceId}`}
                          checked={item.visible}
                          onCheckedChange={() => onToggleWidget(item.instanceId)}
                        />
                      </div>
                    ))}

                  {widgetInstances.filter(item => item.visible).length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">Nenhum widget ativo no momento.</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Widgets inativos */}
              <TabsContent value="inactive" className="m-0">
                <div className="space-y-4">
                  {widgetInstances
                    .filter(item => !item.visible)
                    .sort((a, b) => (a.definition?.title || '').localeCompare(b.definition?.title || ''))
                    .map(item => (
                      <div key={item.instanceId} className="flex items-start justify-between space-x-4">
                        <div className="flex-1">
                          <Label htmlFor={`widget-inactive-${item.instanceId}`} className="font-medium">
                            {item.definition?.title || 'Widget Desconhecido'}
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            {item.definition?.description || 'Sem descrição disponível'}
                          </p>
                        </div>
                        <Switch
                          id={`widget-inactive-${item.instanceId}`}
                          checked={item.visible}
                          onCheckedChange={() => onToggleWidget(item.instanceId)}
                        />
                      </div>
                    ))}

                  {widgetInstances.filter(item => !item.visible).length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">Todos os widgets estão ativos.</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Configurações de Layout */}
              <TabsContent value="layout" className="m-0">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-3">Tipo de Compactação</h3>
                    <div className="grid grid-cols-3 gap-2">
                      <Button
                        variant={compactType === 'vertical' ? "default" : "outline"}
                        className="flex flex-col items-center justify-center p-3 h-auto"
                        onClick={() => setCompactType?.('vertical')}
                      >
                        <Rows className="h-8 w-8 mb-2" />
                        <span className="text-xs">Vertical</span>
                      </Button>
                      <Button
                        variant={compactType === 'horizontal' ? "default" : "outline"}
                        className="flex flex-col items-center justify-center p-3 h-auto"
                        onClick={() => setCompactType?.('horizontal')}
                      >
                        <Columns className="h-8 w-8 mb-2" />
                        <span className="text-xs">Horizontal</span>
                      </Button>
                      <Button
                        variant={compactType === null ? "default" : "outline"}
                        className="flex flex-col items-center justify-center p-3 h-auto"
                        onClick={() => setCompactType?.(null)}
                      >
                        <LayoutGrid className="h-8 w-8 mb-2" />
                        <span className="text-xs">Nenhuma</span>
                      </Button>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-3">Opções de Layout</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label htmlFor="prevent-collision" className="font-medium">
                            Evitar Sobreposição
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            Impede que widgets se sobreponham ao arrastar
                          </p>
                        </div>
                        <Switch
                          id="prevent-collision"
                          checked={preventCollision}
                          onCheckedChange={(checked) => setPreventCollision?.(checked)}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-3">Dicas de Uso</h3>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p>• Use compactação vertical para empilhar widgets</p>
                      <p>• Use compactação horizontal para organizar widgets lado a lado</p>
                      <p>• Desative a compactação para posicionar widgets livremente</p>
                      <p>• Ative "Evitar Sobreposição" para impedir que widgets se sobreponham</p>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </div>
          </ScrollArea>
        </Tabs>

        <DialogFooter className="flex justify-between items-center mt-4">
          {!showResetConfirm ? (
            <Button variant="destructive" onClick={() => setShowResetConfirm(true)}>
              Restaurar Padrão
            </Button>
          ) : (
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-destructive" />
              <span className="text-sm text-destructive">Confirmar reset?</span>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => {
                  onResetLayout();
                  setShowResetConfirm(false);
                }}
              >
                Sim
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowResetConfirm(false)}
              >
                Não
              </Button>
            </div>
          )}

          <Button onClick={onClose}>Fechar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

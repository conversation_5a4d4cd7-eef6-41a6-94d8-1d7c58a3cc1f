import { useState } from "react";
import { But<PERSON> } from "@/shared/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Checkbox } from "@/shared/ui/checkbox";

interface CreateDashboardDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (name: string, makeDefault: boolean) => void;
  isLoading?: boolean;
}

export function CreateDashboardDialog({
  isOpen,
  onClose,
  onConfirm,
  isLoading = false,
}: CreateDashboardDialogProps) {
  const [name, setName] = useState("Novo Dashboard");
  const [makeDefault, setMakeDefault] = useState(false);

  const handleConfirm = () => {
    if (name.trim()) {
      onConfirm(name.trim(), makeDefault);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Criar Novo Dashboard</DialogTitle>
          <DialogDescription>
            Crie um novo dashboard personalizado para organizar seus widgets.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Nome
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="col-span-3"
              autoFocus
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <div className="col-span-4 flex items-center space-x-2">
              <Checkbox
                id="make-default"
                checked={makeDefault}
                onCheckedChange={(checked) => setMakeDefault(!!checked)}
              />
              <Label htmlFor="make-default">Definir como dashboard padrão</Label>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancelar
          </Button>
          <Button onClick={handleConfirm} disabled={!name.trim() || isLoading}>
            {isLoading ? "Criando..." : "Criar Dashboard"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

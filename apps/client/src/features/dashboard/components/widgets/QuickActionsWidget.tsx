import { WidgetWrapper } from './WidgetWrapper';
import { Button } from '@/shared/ui/button';
import {
  UserPlus,
  CalendarPlus,
  FileText,
  Search,
  MessageSquarePlus,
  FileBarChart,
  Zap
} from 'lucide-react';
import { useNavigate } from '@tanstack/react-router';
import { useToast } from '@/shared/hooks/use-toast';
import { useSessionNotes } from '@/features/session-notes/hooks/useSessionNotes';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery';

interface QuickActionsWidgetProps {
  isEditing?: boolean;
  onRemove?: () => void;
  dragHandleClassName?: string;
}

export function QuickActionsWidget({
  isEditing = false,
  onRemove,
  dragHandleClassName
}: QuickActionsWidgetProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { openQuickNote } = useSessionNotes();
  const { allPatients } = usePatientQuery();

  // Função para abrir nota rápida do primeiro paciente disponível
  const handleQuickNote = () => {
    if (allPatients.length === 0) {
      toast({
        title: 'Nenhum paciente encontrado',
        description: 'Cadastre um paciente primeiro para criar notas rápidas.'
      });
      return;
    }

    const firstPatient = {
      id: allPatients[0].id,
      name: allPatients[0].full_name,
      avatar: undefined
    };

    openQuickNote(firstPatient, 1);
  };

  const actions = [
    {
      icon: UserPlus,
      label: 'Novo Paciente',
      onClick: () => navigate({ to: '/people', search: { new: 'true' } }),
      color: 'text-green-500',
      bgColor: 'bg-green-500/10'
    },
    {
      icon: CalendarPlus,
      label: 'Nova Consulta',
      onClick: () => navigate({ to: '/calendar', search: { new: 'true' } }),
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10'
    },
    {
      icon: Zap,
      label: 'Nova Nota de Sessão',
      onClick: handleQuickNote,
      color: 'text-orange-500',
      bgColor: 'bg-orange-500/10'
    },
    {
      icon: FileText,
      label: 'Novo Modelo',
      onClick: () => navigate({ to: '/models', search: { new: 'true' } }),
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10'
    },
    {
      icon: Search,
      label: 'Buscar',
      onClick: () => toast({ title: 'Em breve', description: 'A funcionalidade de busca global estará disponível em breve.' }),
      color: 'text-amber-500',
      bgColor: 'bg-amber-500/10'
    },
    { 
      icon: MessageSquarePlus, 
      label: 'Mensagem', 
      onClick: () => toast({ title: 'Em breve', description: 'A funcionalidade de mensagens estará disponível em breve.' }),
      color: 'text-pink-500',
      bgColor: 'bg-pink-500/10'
    },
    { 
      icon: FileBarChart, 
      label: 'Relatório', 
      onClick: () => navigate({ to: '/financial', search: { tab: 'reports' } }),
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-500/10'
    },
  ];

  return (
    <>
      <WidgetWrapper 
        title="Ações Rápidas" 
        isEditing={isEditing}
        onRemove={onRemove}
        dragHandleClassName={dragHandleClassName}
      >
        <div className="grid grid-cols-3 gap-2">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant="ghost"
              className="h-auto flex flex-col items-center justify-center p-3 gap-2 hover:bg-accent/20"
              onClick={action.onClick}
            >
              <div className={`w-10 h-10 rounded-full ${action.bgColor} flex items-center justify-center`}>
                <action.icon className={`h-5 w-5 ${action.color}`} />
              </div>
              <span className="text-xs font-medium">{action.label}</span>
            </Button>
          ))}
        </div>
      </WidgetWrapper>
    </>
  );
}

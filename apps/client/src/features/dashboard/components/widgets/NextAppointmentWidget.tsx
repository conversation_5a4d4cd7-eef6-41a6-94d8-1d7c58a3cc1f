import { WidgetWrapper } from './WidgetWrapper';
import { useDashboardQuery } from '@/features/dashboard/hooks/useDashboardQuery';
import { Skeleton } from '@/shared/ui/skeleton';
import { Button } from '@/shared/ui/button';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useNavigate } from '@tanstack/react-router';
import { BaseWidgetProps } from '@/features/dashboard/types';
import { Clock } from 'lucide-react';
import { Card, CardContent } from '@/shared/ui/card';

export function NextAppointmentWidget({ 
  isEditing = false, 
  onRemove,
  dragHandleClassName,
  instanceId 
}: BaseWidgetProps) {
  const { dashboardData, isLoading } = useDashboardQuery();
  const navigate = useNavigate();

  const formatTime = (timeString: string) => {
    try {
      const date = parseISO(timeString);
      return format(date, 'HH:mm', { locale: ptBR });
    } catch (error) {
      return timeString;
    }
  };

  const formatDate = (timeString: string) => {
    try {
      const date = parseISO(timeString);
      return format(date, "dd 'de' MMMM", { locale: ptBR }).replace(/^\w/, c => c.toUpperCase());
    } catch (error) {
      return timeString;
    }
  };

  const handlePatientClick = (patientId: string) => {
    navigate({ to: '/person/$patientId', params: { patientId: patientId } });
  };

  const nextAppointment = dashboardData?.appointments.upcoming_list?.[0];

  return (
    <WidgetWrapper 
      title="Próximo Atendimento" 
      isEditing={isEditing}
      onRemove={onRemove}
      dragHandleClassName={dragHandleClassName}
    >
      {isLoading ? (
        <div className="flex items-center justify-center h-full">
          <Skeleton className="h-24 w-full" />
        </div>
      ) : nextAppointment ? (
        <Card className="border-none shadow-none bg-transparent">
          <CardContent className="p-0">
            <div className="p-4 rounded-lg border border-border bg-card hover:bg-accent/10 transition-colors">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 
                    className="font-medium text-foreground hover:text-primary cursor-pointer"
                    onClick={() => { handlePatientClick(nextAppointment.patient_id); }}
                  >
                    {nextAppointment.patient_name}
                  </h3>
                  <p className="text-sm text-muted-foreground">{nextAppointment.title}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{formatDate(nextAppointment.start_time)}</p>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Clock className="mr-1 h-3 w-3" />
                    <span>{formatTime(nextAppointment.start_time)} - {formatTime(nextAppointment.end_time)}</span>
                  </div>
                </div>
              </div>
              <div className="mt-4">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigate({ to: '/person/$patientId', params: { patientId: nextAppointment.patient_id } })}
                  className="mr-2"
                >
                  Ver Paciente
                </Button>
                <Button 
                  size="sm"
                  onClick={() => navigate({ to: '/calendar' })}
                >
                  Ver Agenda
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="flex flex-col items-center justify-center h-full text-center p-6">
          <p className="text-muted-foreground mb-4">Não há atendimentos agendados para os próximos dias.</p>
          <Button 
            size="sm"
            onClick={() => navigate({ to: '/calendar' })}
          >
            Agendar Consulta
          </Button>
        </div>
      )}
    </WidgetWrapper>
  );
}

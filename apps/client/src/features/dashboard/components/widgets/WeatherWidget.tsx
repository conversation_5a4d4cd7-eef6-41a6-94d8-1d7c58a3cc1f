import { useState, useEffect } from 'react';
import { WidgetWrapper } from './WidgetWrapper';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { 
  CloudSun, 
  Cloud, 
  Sun, 
  CloudRain, 
  CloudSnow, 
  CloudLightning, 
  CloudFog,
  RefreshCw,
  MapPin
} from 'lucide-react';
import { BaseWidgetProps } from '@/features/dashboard/types';

interface WeatherSettings {
  location: string;
  unit: 'celsius' | 'fahrenheit';
}

interface WeatherData {
  temperature: number;
  condition: string;
  location: string;
  humidity: number;
  windSpeed: number;
  icon: string;
}

interface WeatherWidgetProps extends BaseWidgetProps {}

export function WeatherWidget({ 
  instanceId,
  isEditing = false, 
  onRemove,
  onConfigure,
  dragHandleClassName,
  settings,
  onSettingsChange
}: WeatherWidgetProps) {
  // Configurações padrão
  const defaultSettings: WeatherSettings = {
    location: 'São Paulo',
    unit: 'celsius'
  };
  
  // Mesclar configurações padrão com as configurações salvas
  const widgetSettings = { ...defaultSettings, ...(settings as unknown as WeatherSettings) };
  
  // Estados
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingLocation, setEditingLocation] = useState(false);
  const [locationInput, setLocationInput] = useState(widgetSettings.location);
  
  // Buscar dados do clima (simulado)
  const fetchWeather = async (location: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulação de API - em um projeto real, você faria uma chamada à API de clima
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Dados simulados
      const conditions = [
        'clear', 'cloudy', 'partly-cloudy', 'rain', 'thunderstorm', 'snow', 'fog'
      ];
      const randomCondition = conditions[Math.floor(Math.random() * conditions.length)];
      const randomTemp = Math.floor(Math.random() * 30) + 5; // 5-35°C
      
      const weatherData: WeatherData = {
        temperature: randomTemp,
        condition: randomCondition,
        location: location,
        humidity: Math.floor(Math.random() * 60) + 30, // 30-90%
        windSpeed: Math.floor(Math.random() * 30) + 5, // 5-35 km/h
        icon: randomCondition
      };
      
      setWeather(weatherData);
    } catch (err) {
      setError('Erro ao buscar dados do clima');
      console.error('Erro ao buscar clima:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Buscar clima ao montar o componente
  useEffect(() => {
    fetchWeather(widgetSettings.location);
  }, [widgetSettings.location]);
  
  // Atualizar localização
  const updateLocation = () => {
    if (locationInput.trim() && locationInput !== widgetSettings.location) {
      if (onSettingsChange) {
        onSettingsChange({
          ...widgetSettings,
          location: locationInput
        });
      }
      fetchWeather(locationInput);
    }
    setEditingLocation(false);
  };
  
  // Alternar unidade de temperatura
  const toggleUnit = () => {
    const newUnit = widgetSettings.unit === 'celsius' ? 'fahrenheit' : 'celsius';
    if (onSettingsChange) {
      onSettingsChange({
        ...widgetSettings,
        unit: newUnit
      });
    }
  };
  
  // Converter temperatura
  const formatTemperature = (temp: number) => {
    if (widgetSettings.unit === 'fahrenheit') {
      return `${Math.round(temp * 9/5 + 32)}°F`;
    }
    return `${temp}°C`;
  };
  
  // Obter ícone com base na condição
  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'clear':
        return <Sun className="h-12 w-12 text-amber-400" />;
      case 'cloudy':
        return <Cloud className="h-12 w-12 text-gray-400" />;
      case 'partly-cloudy':
        return <CloudSun className="h-12 w-12 text-amber-300" />;
      case 'rain':
        return <CloudRain className="h-12 w-12 text-blue-400" />;
      case 'thunderstorm':
        return <CloudLightning className="h-12 w-12 text-purple-400" />;
      case 'snow':
        return <CloudSnow className="h-12 w-12 text-blue-200" />;
      case 'fog':
        return <CloudFog className="h-12 w-12 text-gray-300" />;
      default:
        return <CloudSun className="h-12 w-12 text-amber-300" />;
    }
  };
  
  // Obter descrição da condição
  const getConditionText = (condition: string) => {
    switch (condition) {
      case 'clear': return 'Céu limpo';
      case 'cloudy': return 'Nublado';
      case 'partly-cloudy': return 'Parcialmente nublado';
      case 'rain': return 'Chuva';
      case 'thunderstorm': return 'Tempestade';
      case 'snow': return 'Neve';
      case 'fog': return 'Neblina';
      default: return condition;
    }
  };
  
  return (
    <WidgetWrapper 
      title="Previsão do Tempo" 
      isEditing={isEditing}
      onRemove={onRemove}
      onConfigure={onConfigure}
      dragHandleClassName={dragHandleClassName}
    >
      <div className="flex flex-col h-full">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <p className="text-destructive mb-2">{error}</p>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => fetchWeather(widgetSettings.location)}
            >
              Tentar novamente
            </Button>
          </div>
        ) : weather ? (
          <div className="flex flex-col h-full">
            {/* Localização */}
            <div className="flex items-center justify-between mb-4">
              {editingLocation ? (
                <div className="flex items-center space-x-2 w-full">
                  <Input
                    value={locationInput}
                    onChange={(e) => setLocationInput(e.target.value)}
                    placeholder="Digite a cidade"
                    className="h-8 text-sm"
                    onKeyDown={(e) => e.key === 'Enter' && updateLocation()}
                  />
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={updateLocation}
                    className="h-8"
                  >
                    OK
                  </Button>
                </div>
              ) : (
                <>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1 text-muted-foreground" />
                    <span className="font-medium">{weather.location}</span>
                  </div>
                  <div className="flex space-x-1">
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => setEditingLocation(true)}
                      className="h-7 w-7"
                    >
                      <span className="sr-only">Editar localização</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-4 w-4"
                      >
                        <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                        <path d="m15 5 4 4" />
                      </svg>
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => fetchWeather(weather.location)}
                      className="h-7 w-7"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                </>
              )}
            </div>
            
            {/* Temperatura e condição */}
            <div className="flex items-center justify-between mb-4">
              <div>
                <div className="text-3xl font-bold">
                  {formatTemperature(weather.temperature)}
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={toggleUnit}
                  className="h-6 px-2 text-xs"
                >
                  Mudar para {widgetSettings.unit === 'celsius' ? '°F' : '°C'}
                </Button>
              </div>
              <div>
                {getWeatherIcon(weather.icon)}
              </div>
            </div>
            
            {/* Detalhes */}
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="p-2 bg-muted/30 rounded-md">
                <div className="text-muted-foreground">Condição</div>
                <div className="font-medium">{getConditionText(weather.condition)}</div>
              </div>
              <div className="p-2 bg-muted/30 rounded-md">
                <div className="text-muted-foreground">Umidade</div>
                <div className="font-medium">{weather.humidity}%</div>
              </div>
              <div className="col-span-2 p-2 bg-muted/30 rounded-md">
                <div className="text-muted-foreground">Vento</div>
                <div className="font-medium">{weather.windSpeed} km/h</div>
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </WidgetWrapper>
  );
}

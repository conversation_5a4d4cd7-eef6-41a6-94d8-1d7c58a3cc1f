import { WidgetWrapper } from './WidgetWrapper';
import { Skeleton } from '@/shared/ui/skeleton';
import { Button } from '@/shared/ui/button';
import { useNavigate } from '@tanstack/react-router';
import { useDashboardQuery } from '@/features/dashboard/hooks/useDashboardQuery';
import { RecentPatient } from '@/features/dashboard/types/dashboard.schema';

interface RecentPatientsWidgetProps {
  isEditing?: boolean;
  onRemove?: () => void;
  dragHandleClassName?: string;
}

export function RecentPatientsWidget({ 
  isEditing = false, 
  onRemove,
  dragHandleClassName 
}: RecentPatientsWidgetProps) {
  const { dashboardData, isLoading } = useDashboardQuery();
  const navigate = useNavigate();

  const handlePatientClick = (patientId: string) => {
    navigate({ to: '/_authenticated/person/$patientId', params: { patientId } });
  };
  
  const patients = dashboardData?.patients.recent_list || [];

  return (
    <WidgetWrapper 
      title="Pacientes Recentes" 
      isEditing={isEditing}
      onRemove={onRemove}
      dragHandleClassName={dragHandleClassName}
    >
      {isLoading ? (
        <div className="space-y-3">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      ) : patients.length > 0 ? (
        <div className="space-y-2">
          {patients.map((patient: RecentPatient) => (
            <div 
              key={patient.id}
              className="p-2 rounded-md hover:bg-accent/10 cursor-pointer transition-colors"
              onClick={() => handlePatientClick(patient.id)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{patient.full_name}</p>
                  <p className="text-xs text-muted-foreground">
                    {patient.phone || patient.email || 'Sem contato'}
                  </p>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-8 w-8 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate({ 
                      to: '/calendar', 
                      search: { patientId: patient.id, new: 'true' } 
                    });
                  }}
                >
                  <span className="sr-only">Agendar</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4"
                  >
                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                    <line x1="16" x2="16" y1="2" y2="6" />
                    <line x1="8" x2="8" y1="2" y2="6" />
                    <line x1="3" x2="21" y1="10" y2="10" />
                    <path d="M8 14h.01" />
                    <path d="M12 14h.01" />
                    <path d="M16 14h.01" />
                    <path d="M8 18h.01" />
                    <path d="M12 18h.01" />
                    <path d="M16 18h.01" />
                  </svg>
                </Button>
              </div>
            </div>
          ))}
          
          <div className="flex justify-end mt-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => navigate({ to: '/_authenticated/people' })}
            >
              Ver Todos
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-full text-center p-4">
          <p className="text-muted-foreground mb-3">Nenhum paciente recente encontrado.</p>
          <Button 
            size="sm"
            onClick={() => navigate({ to: '/_authenticated/people', search: { new: 'true' } })}
          >
            Adicionar Paciente
          </Button>
        </div>
      )}
    </WidgetWrapper>
  );
}

 import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/shared/ui/card';
import { Button } from '@/shared/ui/button';
import { Grip, X, Settings, Loader2 } from 'lucide-react'; // Adicionado Loader2
import { cn } from '@/shared/lib/utils';

interface WidgetWrapperProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  isEditing?: boolean;
  onRemove?: () => void;
  onConfigure?: () => void;
  dragHandleClassName?: string;
  isLoading?: boolean; // Nova prop
}

export function WidgetWrapper({
  title,
  children,
  className,
  isEditing = false,
  onRemove,
  onConfigure,
  dragHandleClassName,
  isLoading = false // Valor padrão
}: WidgetWrapperProps) {
  return (
    <Card className={cn('h-full flex flex-col overflow-hidden relative', className)}> {/* Adicionado relative */}
      {/* Overlay de Loading */}
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 flex items-center justify-center z-10">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
        </div>
      )}
      <CardHeader className="p-2 flex-shrink-0">
        <div className="flex items-center justify-between">
          {isEditing && dragHandleClassName && (
            <div className={cn(dragHandleClassName, "cursor-move")}>
              <Grip className="h-4 w-4 text-muted-foreground" />
            </div>
          )}

          <CardTitle className="text-sm sm:text-base font-medium truncate">{title}</CardTitle>

          <div className="flex items-center gap-1 flex-shrink-0">
            {isEditing && onConfigure && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={onConfigure}
              >
                <Settings className="h-3.5 w-3.5" />
              </Button>
            )}

            {isEditing && onRemove && (
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-destructive hover:text-destructive"
                onClick={onRemove}
              >
                <X className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-2 flex-grow overflow-auto">
        <div className="h-full">
          {children}
        </div>
      </CardContent>
    </Card>
  );
}

import { WidgetWrapper } from './WidgetWrapper';
import { useDashboardQuery } from '@/features/dashboard/hooks/useDashboardQuery';
import { Skeleton } from '@/shared/ui/skeleton';
import { Button } from '@/shared/ui/button';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useNavigate } from '@tanstack/react-router';

interface UpcomingAppointmentsWidgetProps {
  isEditing?: boolean;
  onRemove?: () => void;
  dragHandleClassName?: string;
}

export function UpcomingAppointmentsWidget({ 
  isEditing = false, 
  onRemove,
  dragHandleClassName 
}: UpcomingAppointmentsWidgetProps) {
  const { dashboardData, isLoading } = useDashboardQuery();
  const navigate = useNavigate();

  const formatTime = (timeString: string) => {
    try {
      const date = parseISO(timeString);
      return format(date, 'HH:mm', { locale: ptBR });
    } catch (error) {
      return timeString;
    }
  };

  const formatDate = (timeString: string) => {
    try {
      const date = parseISO(timeString);
      return format(date, "dd 'de' MMMM", { locale: ptBR }).replace(/^\w/, c => c.toUpperCase());
    } catch (error) {
      return timeString;
    }
  };

  const handlePatientClick = (patientId: string) => {
    navigate({ to: '/person/$patientId', params: { patientId: patientId } });
  };

  return (
    <WidgetWrapper 
      title="Próximos Atendimentos" 
      isEditing={isEditing}
      onRemove={onRemove}
      dragHandleClassName={dragHandleClassName}
    >
      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-16 w-full" />
        </div>
      ) : dashboardData?.appointments.upcoming_list && dashboardData.appointments.upcoming_list.length > 0 ? (
        <div className="space-y-4">
          {dashboardData.appointments.upcoming_list.map((appointment) => (
            <div 
              key={appointment.id} 
              className="p-3 rounded-lg border border-border bg-card hover:bg-accent/10 transition-colors"
            >
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 
                    className="font-medium text-foreground hover:text-primary cursor-pointer"
                    onClick={() => { handlePatientClick(appointment.patient_id); }}
                  >
                    {appointment.patient_name}
                  </h4>
                  <p className="text-sm text-muted-foreground">{appointment.title}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{formatDate(appointment.start_time)}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatTime(appointment.start_time)} - {formatTime(appointment.end_time)}
                  </p>
                </div>
              </div>
            </div>
          ))}
          
          <div className="flex justify-end mt-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => navigate({ to: '/calendar' })}
            >
              Ver Todos
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-full text-center p-6">
          <p className="text-muted-foreground mb-4">Não há atendimentos agendados para os próximos dias.</p>
          <Button 
            size="sm"
            onClick={() => navigate({ to: '/calendar' })}
          >
            Agendar Consulta
          </Button>
        </div>
      )}
    </WidgetWrapper>
  );
}

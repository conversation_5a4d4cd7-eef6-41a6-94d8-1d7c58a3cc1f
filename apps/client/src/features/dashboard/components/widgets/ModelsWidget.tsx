import { useState } from "react";
import { useNavigate } from "@tanstack/react-router";
import { useModelsQuery } from "@/features/models/hooks/useModelsQuery";
import { WidgetWrapper } from "./WidgetWrapper";
import { But<PERSON> } from "@/shared/ui/button";
import { Card, CardContent } from "@/shared/ui/card";
import { Badge } from "@/shared/ui/badge";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/shared/ui/tabs";
import { FileText, Star, Clock, FileEdit, Plus } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { cn } from "@/shared/lib/utils";
import { SelectContextDialog } from "@/features/models/components/SelectContextDialog";
import { BaseWidgetProps } from '@/features/dashboard/types';

export function ModelsWidget({
  instanceId,
  isEditing = false,
  onRemove,
  onConfigure,
  dragHandleClassName,
  settings,
  onSettingsChange
}: BaseWidgetProps) {
  const [activeTab, setActiveTab] = useState<"favorites" | "recent">("favorites");
  const [modelForContext, setModelForContext] = useState<any>(null);
  const [isSelectContextOpen, setIsSelectContextOpen] = useState(false);
  const navigate = useNavigate();

  const { models, isLoading } = useModelsQuery();

  // Filtrar modelos com base na aba ativa
  const filteredModels = activeTab === "favorites"
    ? models.filter(model => model.isFavorite).slice(0, 5)
    : models.sort((a, b) => new Date(b.lastUsed || b.createdAt).getTime() - new Date(a.lastUsed || a.createdAt).getTime()).slice(0, 5);

  // Obter ícone para categoria
  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case "report":
        return <FileText className="h-3.5 w-3.5" />;
      case "evaluation":
        return <FileText className="h-3.5 w-3.5" />;
      case "form":
        return <FileText className="h-3.5 w-3.5" />;
      default:
        return <FileText className="h-3.5 w-3.5" />;
    }
  };

  // Obter nome da categoria
  const getCategoryName = (categoryId: string) => {
    const categories: Record<string, string> = {
      "report": "Relatório",
      "evaluation": "Avaliação",
      "form": "Formulário",
      "letter": "Carta",
      "exercise": "Exercício",
      "other": "Outro",
    };
    return categories[categoryId] || "Outro";
  };

  // Usar modelo
  const handleUseModel = (model: any) => {
    setModelForContext(model);
    setIsSelectContextOpen(true);
  };

  // Callback para quando o contexto é selecionado
  const handleContextSelected = (context: any) => {
    // O SelectContextDialog já lida com a navegação
    setIsSelectContextOpen(false);
  };

  // Navegar para a página de modelos
  const handleViewAllModels = () => {
    navigate({ to: "/models" });
  };

  // Navegar para criar novo modelo
  const handleCreateModel = () => {
    navigate({ to: "/models/new" });
  };

  // Renderizar item de modelo
  const renderModelItem = (model: any) => (
    <div
      key={model.id}
      className="flex items-center p-2 rounded-md hover:bg-accent/50 cursor-pointer"
      onClick={() => handleUseModel(model)}
    >
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-1 mb-0.5">
          {getCategoryIcon(model.category)}
          <span className="text-xs text-muted-foreground">{getCategoryName(model.category)}</span>
          {model.isFavorite && <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />}
        </div>
        <h4 className="text-sm font-medium line-clamp-1">{model.title}</h4>
      </div>
      <div className="flex items-center text-xs text-muted-foreground ml-2">
        <Clock className="h-3 w-3 mr-1" />
        <span>{format(new Date(model.lastUsed || model.createdAt), "dd/MM/yyyy", { locale: ptBR })}</span>
      </div>
    </div>
  );

  return (
    <WidgetWrapper
      title="Modelos"
      isEditing={isEditing}
      onRemove={onRemove}
      onConfigure={onConfigure}
      dragHandleClassName={dragHandleClassName}
    >
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-between mb-2">
          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as "favorites" | "recent")}>
            <TabsList className="h-7">
              <TabsTrigger value="favorites" className="text-xs px-2 py-0.5">
                Favoritos
              </TabsTrigger>
              <TabsTrigger value="recent" className="text-xs px-2 py-0.5">
                Recentes
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Button variant="ghost" size="icon" className="h-6 w-6" onClick={handleCreateModel}>
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-pulse">Carregando...</div>
            </div>
          ) : filteredModels.length > 0 ? (
            <div className="space-y-1">
              {filteredModels.map(renderModelItem)}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full p-4 text-center">
              <p className="text-sm text-muted-foreground mb-2">
                {activeTab === "favorites" ? "Nenhum modelo favorito" : "Nenhum modelo recente"}
              </p>
              <Button variant="outline" size="sm" onClick={handleViewAllModels}>
                Ver todos os modelos
              </Button>
            </div>
          )}
        </div>

        {filteredModels.length > 0 && (
          <div className="mt-2 pt-2 border-t">
            <Button variant="ghost" size="sm" className="w-full text-xs" onClick={handleViewAllModels}>
              Ver todos os modelos
            </Button>
          </div>
        )}
      </div>

      {/* Dialog de seleção de contexto */}
      {modelForContext && (
        <SelectContextDialog
          isOpen={isSelectContextOpen}
          onClose={() => setIsSelectContextOpen(false)}
          onSelect={handleContextSelected}
        />
      )}
    </WidgetWrapper>
  );
}

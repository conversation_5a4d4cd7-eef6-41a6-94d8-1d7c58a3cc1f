import { useState, useEffect, useRef, useCallback, useLayoutEffect } from 'react';
import { WidgetWrapper } from './WidgetWrapper';
import { Button } from '@/shared/ui/button';
import { Slider } from '@/shared/ui/slider';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/shared/ui/tooltip';
import { Play, Pause, RotateCcw, Volume2, VolumeX, ChevronUp, ChevronDown, Settings2 } from 'lucide-react';
import { BaseWidgetProps } from '@/features/dashboard/types';
import { useWidgetSettingsQuery } from '@/features/dashboard/hooks/useWidgetSettingsQuery';
import { cn } from '@/shared/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { Switch } from '@/shared/ui/switch';
import { Label } from '@/shared/ui/label';
import { useWidgetsStore } from '@/features/dashboard/stores/useWidgetsStore';


interface TimerSettings {
  defaultDuration: number;
  soundEnabled: boolean;
  soundVolume: number;
  soundType: string;
  autoRestart: boolean;
  timerColor: string;
  flashWhenComplete: boolean;
  continuousSound: boolean;
}

// Usando diretamente BaseWidgetProps
type TimerWidgetProps = BaseWidgetProps;

export function TimerWidget({
  instanceId,
  isEditing = false,
  onRemove,
  onConfigure,
  dragHandleClassName,
  settings,
  onSettingsChange
}: TimerWidgetProps) {
  // Configurações padrão
  const defaultSettings: TimerSettings = {
    defaultDuration: 26 * 60, // 26 minutos em segundos (valor par para compatibilidade com o step de 2 minutos)
    soundEnabled: true,
    soundVolume: 0.7,
    soundType: 'bell',
    autoRestart: false,
    timerColor: 'primary',
    flashWhenComplete: true,
    continuousSound: true
  };

  // Usar React Query para gerenciar as configurações
  const {
    settings: storedSettings,
    updateSettings,
    isLoading
  } = useWidgetSettingsQuery<Record<string, unknown>>(instanceId);

  // Mesclar configurações padrão com as configurações salvas
  // Prioridade: props > React Query > padrão
  // Converter configurações para o formato correto
  const widgetSettings: TimerSettings = {
    ...defaultSettings,
    ...(storedSettings as unknown as Partial<TimerSettings>),
    ...(settings as unknown as Partial<TimerSettings>)
  };

  // Estados do timer - apenas para configurações que não são parte do estado global
  const [soundEnabled, setSoundEnabled] = useState(widgetSettings.soundEnabled);
  const [customDuration, setCustomDuration] = useState(widgetSettings.defaultDuration / 60);
  const [soundVolume, setSoundVolume] = useState(widgetSettings.soundVolume);
  const [soundType, setSoundType] = useState(widgetSettings.soundType);
  const [autoRestart, setAutoRestart] = useState(widgetSettings.autoRestart);
  const [timerColor, setTimerColor] = useState(widgetSettings.timerColor);
  const [flashWhenComplete, setFlashWhenComplete] = useState(widgetSettings.flashWhenComplete);
  const [continuousSound, setContinuousSound] = useState(widgetSettings.continuousSound);
  const [isShaking, setIsShaking] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);
  const [sliderValue, setSliderValue] = useState<number>(customDuration);

  // Referências
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const notificationSentRef = useRef<boolean>(false);
  const timerCompletedRef = useRef<boolean>(false);

  // Usar o store global de widgets
  const { updateTimerState, addNotification, activeTimers, cleanupCompletedTimers } = useWidgetsStore();

  // Obter o estado do timer do store global ou usar valores padrão
  const timerState = activeTimers[instanceId] || {
    instanceId,
    isRunning: false,
    timeLeft: customDuration * 60,
    isComplete: false,
    soundEnabled,
    customDuration,
    notificationSent: false
  };

  // Sincronizar as configurações locais com o estado global quando o componente montar ou o instanceId mudar
  useLayoutEffect(() => {
    if (timerState) {
      console.log(`Sincronizando configurações do timer ${instanceId} com estado global:`, timerState);

      // Verificar se o timer já foi completado para evitar re-disparar notificações
      if (timerState.isComplete && timerState.timeLeft === 0) {
        timerCompletedRef.current = true;
        notificationSentRef.current = true;
      }

      // Sincronizar apenas as configurações que são mantidas localmente
      setSoundEnabled(timerState.soundEnabled);
      setCustomDuration(timerState.customDuration);
      setSliderValue(timerState.customDuration);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instanceId, timerState?.soundEnabled, timerState?.customDuration]);

  // Não precisamos mais do efeito para atualizar o timer localmente
  // O store Zustand agora cuida de toda a atualização do timer

  // Efeito para sincronizar o valor do slider com a duração personalizada
  useEffect(() => {
    setSliderValue(customDuration);
  }, [customDuration]);

  // Atualizar o estado global quando as configurações locais mudarem
  useEffect(() => {
    // Usar um timeout para evitar atualizações muito frequentes
    const timeoutId = setTimeout(() => {
      // Atualizar apenas as configurações que são mantidas localmente
      updateTimerState(instanceId, {
        soundEnabled,
        customDuration
      });
    }, 100); // Pequeno delay para evitar atualizações muito frequentes

    return () => clearTimeout(timeoutId);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instanceId, soundEnabled, customDuration]);

  // Formatar o tempo restante
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Iniciar ou pausar o timer
  const toggleTimer = () => {
    updateTimerState(instanceId, {
      isRunning: !timerState.isRunning
    });
  };

  // Resetar o timer (usando useCallback para evitar recriação a cada render)
  const resetTimer = useCallback(() => {
    // Atualizar o estado global
    updateTimerState(instanceId, {
      isRunning: false,
      timeLeft: customDuration * 60,
      isComplete: false,
      notificationSent: false
    });

    // Parar o som se estiver tocando
    if (isAudioPlaying && audioRef.current) {
      audioRef.current.pause();
      if (audioRef.current.currentTime) {
        audioRef.current.currentTime = 0;
      }
      setIsAudioPlaying(false);
    }

    // Parar o efeito de vibração
    setIsShaking(false);

    // Resetar as flags de notificação
    notificationSentRef.current = false;
    timerCompletedRef.current = false;
  }, [instanceId, customDuration, isAudioPlaying, updateTimerState]);

  // Inicializar o áudio
  useEffect(() => {
    const soundMap = {
      bell: '/resources/sounds/timer-end.mp3',
      alarm: '/resources/sounds/alarm.mp3',
      chime: '/resources/sounds/chime.mp3'
    };

    const soundPath = soundMap[soundType as keyof typeof soundMap] || soundMap.bell;
    audioRef.current = new Audio(soundPath);

    if (audioRef.current) {
      audioRef.current.volume = soundVolume;

      // Configurar para repetir o som continuamente se a opção estiver ativada
      if (continuousSound) {
        audioRef.current.loop = true;
      } else {
        audioRef.current.loop = false;
      }
    }

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, [soundType, soundVolume, continuousSound]);

  // Efeito para detectar quando o timer termina
  useEffect(() => {
    // Se o timer acabou de terminar e a notificação ainda não foi enviada
    if (timerState.isComplete && timerState.timeLeft === 0 && !notificationSentRef.current) {
      // Marcar que a notificação foi enviada para este término
      notificationSentRef.current = true;
      timerCompletedRef.current = true;

      // Tocar som quando o timer terminar
      if (soundEnabled && audioRef.current) {
        audioRef.current.play().catch(e => console.error('Erro ao tocar áudio:', e));
        setIsAudioPlaying(true);

        // Ativar efeito de vibração se o som contínuo estiver ativado
        if (continuousSound) {
          setIsShaking(true);
        }

        // Adicionar notificação
        addNotification('timer', `Timer "Cronômetro de Sessão" finalizado: ${customDuration} minutos`);
      }

      // Reiniciar automaticamente se configurado
      if (autoRestart) {
        setTimeout(() => {
          resetTimer();
          // Atualizar o estado global para iniciar o timer
          updateTimerState(instanceId, { isRunning: true });
          // Resetar a flag de notificação quando o timer for reiniciado
          notificationSentRef.current = false;
          timerCompletedRef.current = false;
        }, 3000);
      }
    }
  }, [timerState.isComplete, timerState.timeLeft, soundEnabled, continuousSound, autoRestart, resetTimer, addNotification, customDuration, instanceId, updateTimerState]);

  // Alternar som
  const toggleSound = () => {
    setSoundEnabled(prev => !prev);
    const newSettings = {
      ...widgetSettings,
      soundEnabled: !soundEnabled
    };

    // Atualizar configurações
    if (onSettingsChange) {
      onSettingsChange(newSettings);
    } else {
      updateSettings(newSettings);
    }
  };

  // Adiantar o tempo em minutos
  const advanceTime = (minutes: number) => {
    if (!timerState.isRunning) return;

    const newTime = Math.max(0, timerState.timeLeft - minutes * 60);
    updateTimerState(instanceId, { timeLeft: newTime });
  };

  // Retroceder o tempo em minutos
  const rewindTime = (minutes: number) => {
    if (!timerState.isRunning) return;

    const newTime = Math.min(customDuration * 60, timerState.timeLeft + minutes * 60);
    updateTimerState(instanceId, { timeLeft: newTime });
  };

  // Atualizar configurações
  const updateTimerSettings = (newPartialSettings: Partial<TimerSettings>) => {
    // Verificar se há mudanças reais antes de atualizar
    let hasChanges = false;

    if (newPartialSettings.soundEnabled !== undefined && newPartialSettings.soundEnabled !== soundEnabled) {
      setSoundEnabled(newPartialSettings.soundEnabled);
      hasChanges = true;
    }

    if (newPartialSettings.soundVolume !== undefined && newPartialSettings.soundVolume !== soundVolume) {
      setSoundVolume(newPartialSettings.soundVolume);
      hasChanges = true;
    }

    if (newPartialSettings.soundType !== undefined && newPartialSettings.soundType !== soundType) {
      setSoundType(newPartialSettings.soundType);
      hasChanges = true;
    }

    if (newPartialSettings.autoRestart !== undefined && newPartialSettings.autoRestart !== autoRestart) {
      setAutoRestart(newPartialSettings.autoRestart);
      hasChanges = true;
    }

    if (newPartialSettings.timerColor !== undefined && newPartialSettings.timerColor !== timerColor) {
      setTimerColor(newPartialSettings.timerColor);
      hasChanges = true;
    }

    if (newPartialSettings.flashWhenComplete !== undefined && newPartialSettings.flashWhenComplete !== flashWhenComplete) {
      setFlashWhenComplete(newPartialSettings.flashWhenComplete);
      hasChanges = true;
    }

    if (newPartialSettings.continuousSound !== undefined && newPartialSettings.continuousSound !== continuousSound) {
      setContinuousSound(newPartialSettings.continuousSound);
      hasChanges = true;
    }

    // Atualizar configurações apenas se houver mudanças
    if (hasChanges) {
      const newSettings = {
        ...widgetSettings,
        ...newPartialSettings
      };

      if (onSettingsChange) {
        onSettingsChange(newSettings);
      } else {
        updateSettings(newSettings);
      }
    }
  };

  // Atualizar duração personalizada
  const handleDurationChange = (value: number[]) => {
    // Garantir que o valor seja sempre um múltiplo de 2
    const newDuration = Math.round(value[0] / 2) * 2;

    // Verificar se o valor realmente mudou
    if (newDuration !== customDuration) {
      console.log(`Alterando duração do timer para ${newDuration} minutos`);
      setCustomDuration(newDuration);
      setSliderValue(newDuration); // Atualizar o valor do slider

      // Se o timer não estiver rodando, atualizar o tempo restante também
      if (!timerState.isRunning) {
        updateTimerState(instanceId, {
          customDuration: newDuration,
          timeLeft: newDuration * 60
        });
      } else {
        // Se estiver rodando, atualizar apenas a duração personalizada
        updateTimerState(instanceId, {
          customDuration: newDuration
        });
      }

      const newSettings = {
        ...widgetSettings,
        defaultDuration: newDuration * 60
      };

      // Atualizar configurações
      if (onSettingsChange) {
        onSettingsChange(newSettings);
      } else {
        updateSettings(newSettings);
      }
    }
  };

  // Calcular a porcentagem de progresso
  const progressPercent = (timerState.timeLeft / (customDuration * 60)) * 100;

  // Determinar as classes para o timer
  const timerColorClass = timerState.isComplete && flashWhenComplete
    ? cn(`text-${timerColor}`, { 'animate-pulse': timerState.isComplete })
    : `text-${timerColor}`;

  // Classe para o efeito de vibração
  const containerClass = cn('h-full flex flex-col items-center justify-between py-1', {
    '[animation:var(--animate-shake)]': isShaking
  });

  return (
    <WidgetWrapper
      title="Cronômetro de Sessão"
      isEditing={isEditing}
      onRemove={onRemove}
      onConfigure={onConfigure}
      dragHandleClassName={dragHandleClassName}
      isLoading={isLoading}
    >
      <div className={containerClass}>
        <div className="w-full flex-1 flex flex-col justify-between">
          {/* Círculo de progresso - Responsível e centralizado */}
          <div className="flex-1 flex items-center justify-center mb-2">
            <div className="relative w-24 h-24 sm:w-32 sm:h-32 mx-auto">
              <svg className="w-full h-full" viewBox="0 0 100 100">
                {/* Círculo de fundo */}
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="currentColor"
                  className="text-muted/20"
                  strokeWidth="8"
                />
                {/* Círculo de progresso */}
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="currentColor"
                  className={timerColorClass}
                  strokeWidth="8"
                  strokeDasharray="283"
                  strokeDashoffset={283 - (283 * progressPercent) / 100}
                  transform="rotate(-90 50 50)"
                />
                {/* Texto do tempo */}
                <text
                  x="50"
                  y="55"
                  textAnchor="middle"
                  className="text-2xl font-bold"
                  fill="currentColor"
                >
                  {formatTime(timerState.timeLeft)}
                </text>
              </svg>
            </div>
          </div>

          {/* Controles - Mais compactos e responsíveis */}
          <div className="flex justify-center space-x-2 mb-3">
            <Button
              variant="outline"
              size="icon"
              onClick={toggleTimer}
              className="h-8 w-8"
            >
              {timerState.isRunning ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={resetTimer}
              className="h-8 w-8"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={toggleSound}
              className="h-8 w-8"
            >
              {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            </Button>

            {/* Botões para adiantar/retroceder tempo (apenas visíveis quando o timer está rodando) */}
            {timerState.isRunning && (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => rewindTime(1)}
                  className="h-8 w-8"
                  title="Adicionar 1 minuto"
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => advanceTime(1)}
                  className="h-8 w-8"
                  title="Reduzir 1 minuto"
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </>
            )}

            {/* Configurações avançadas */}
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  title="Configurações"
                >
                  <Settings2 className="h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="grid gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium leading-none">Configurações do Timer</h4>
                    <p className="text-sm text-muted-foreground">Personalize o comportamento do timer</p>
                  </div>

                  <div className="grid gap-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="sound-type">Som de alerta</Label>
                      <Select
                        value={soundType}
                        onValueChange={(value) => updateTimerSettings({ soundType: value })}
                        disabled={!soundEnabled}
                      >
                        <SelectTrigger className="w-[120px]" id="sound-type">
                          <SelectValue placeholder="Tipo de som" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bell">Sino</SelectItem>
                          <SelectItem value="alarm">Alarme</SelectItem>
                          <SelectItem value="chime">Chime</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Botão para limpar timers completados */}
                    <div className="pt-4 border-t mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => {
                          cleanupCompletedTimers();
                          // Mostrar feedback
                          addNotification('other', 'Timers completados foram removidos');
                        }}
                      >
                        Limpar Timers Completados
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="sound-volume">Volume</Label>
                      <Slider
                        id="sound-volume"
                        defaultValue={[soundVolume * 100]}
                        min={0}
                        max={100}
                        step={10}
                        className="w-[120px]"
                        onValueCommit={(value) => updateTimerSettings({ soundVolume: value[0] / 100 })}
                        disabled={!soundEnabled}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="auto-restart">Reinício automático</Label>
                      <Switch
                        id="auto-restart"
                        checked={autoRestart}
                        onCheckedChange={(checked) => updateTimerSettings({ autoRestart: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="flash-complete">Piscar ao completar</Label>
                      <Switch
                        id="flash-complete"
                        checked={flashWhenComplete}
                        onCheckedChange={(checked) => updateTimerSettings({ flashWhenComplete: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="continuous-sound">Som contínuo até confirmação</Label>
                      <Switch
                        id="continuous-sound"
                        checked={continuousSound}
                        onCheckedChange={(checked) => updateTimerSettings({ continuousSound: checked })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="timer-color">Cor do timer</Label>
                      <Select
                        value={timerColor}
                        onValueChange={(value) => updateTimerSettings({ timerColor: value })}
                      >
                        <SelectTrigger className="w-[120px]" id="timer-color">
                          <SelectValue placeholder="Cor" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="primary">Padrão</SelectItem>
                          <SelectItem value="red-500">Vermelho</SelectItem>
                          <SelectItem value="green-500">Verde</SelectItem>
                          <SelectItem value="blue-500">Azul</SelectItem>
                          <SelectItem value="yellow-500">Amarelo</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Slider para ajustar a duração - Mais compacto */}
          <div className="px-1">
            <div className="flex justify-between text-xs text-muted-foreground mb-1">
              <span>2m</span>
              <span>{customDuration}m</span>
              <span>60m</span>
            </div>
            <div className="relative">
              <TooltipProvider>
                <Tooltip open={sliderValue !== customDuration}>
                  <TooltipTrigger asChild>
                    <Slider
                      defaultValue={[customDuration]}
                      value={[sliderValue]}
                      min={2}
                      max={60}
                      step={2}
                      onValueChange={(value) => setSliderValue(value[0])}
                      onValueCommit={handleDurationChange}
                      disabled={timerState.isRunning}
                    />
                  </TooltipTrigger>
                  <TooltipContent side="top" className="py-1 px-2">
                    <span className="text-xs">{Math.round(sliderValue / 2) * 2} minutos</span>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {/* Marcadores de 10 em 10 minutos para referência */}
              <div className="absolute top-1/2 left-0 right-0 -translate-y-1/2 flex justify-between pointer-events-none">
                {[10, 20, 30, 40, 50].map(value => (
                  <div
                    key={value}
                    className="h-1 w-0.5 bg-muted-foreground/30"
                    style={{ marginLeft: `${((value - 2) / (60 - 2)) * 100}%` }}
                  />
                ))}
              </div>
            </div>
            {/* Legenda dos marcadores */}
            <div className="flex justify-between text-[10px] text-muted-foreground/50 mt-1 px-1">
              <span>10</span>
              <span>20</span>
              <span>30</span>
              <span>40</span>
              <span>50</span>
            </div>
          </div>
        </div>
      </div>

      {/* Botão de parar alarme - aparece apenas quando o timer terminou e o som está tocando */}
      {timerState.isComplete && isAudioPlaying && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/70 z-10">
          <Button
            variant="destructive"
            size="lg"
            className="animate-pulse"
            onClick={() => {
              // Parar o som
              if (audioRef.current) {
                audioRef.current.pause();
                if (audioRef.current.currentTime) {
                  audioRef.current.currentTime = 0;
                }
                setIsAudioPlaying(false);
              }
              setIsShaking(false);
              // Não resetar notificationSentRef aqui para evitar que a notificação seja enviada novamente
            }}
          >
            Parar Alarme
          </Button>
        </div>
      )}
    </WidgetWrapper>
  );
}

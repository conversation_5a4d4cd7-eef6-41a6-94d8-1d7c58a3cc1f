import { WidgetWrapper } from './WidgetWrapper';
import { Calendar } from '@/features/calendar/components/Calendar';
import { Button } from '@/shared/ui/button';
import { Calendar as CalendarIcon } from 'lucide-react';
import { useNavigate } from '@tanstack/react-router';

interface CalendarWidgetProps {
  isEditing?: boolean;
  onRemove?: () => void;
  dragHandleClassName?: string;
}

export function CalendarWidget({ 
  isEditing = false, 
  onRemove,
  dragHandleClassName 
}: CalendarWidgetProps) {
  const navigate = useNavigate();

  return (
    <WidgetWrapper 
      title="Calendário" 
      isEditing={isEditing}
      onRemove={onRemove}
      dragHandleClassName={dragHandleClassName}
    >
      <div className="flex flex-col h-full">
        <Calendar />
        
        <div className="mt-3 flex justify-end">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => navigate({ to: '/calendar' })}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            Ver Agenda Completa
          </Button>
        </div>
      </div>
    </WidgetWrapper>
  );
}

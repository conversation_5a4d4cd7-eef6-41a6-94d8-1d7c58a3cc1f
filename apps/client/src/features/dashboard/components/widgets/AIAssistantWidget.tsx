import { useState, useRef, useEffect } from 'react';
import { WidgetWrapper } from './WidgetWrapper';
import { Button } from '@/shared/ui/button';
import { Input } from '@/shared/ui/input';
import { Textarea } from '@/shared/ui/textarea';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { 
  Send, 
  Sparkles, 
  Mic, 
  MicOff,
  Loader2,
  Bot,
  User,
  Lightbulb
} from 'lucide-react';
import { BaseWidgetProps } from '@/features/dashboard/types';
import { useAIAssistant } from '@/features/dashboard/hooks/useAIAssistant';
import { useSpeechRecognition } from '@/shared/hooks/useSpeechRecognition';

interface AIAssistantSettings {
  model: 'basic' | 'advanced';
  suggestions: boolean;
}

interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

interface AIAssistantWidgetProps extends BaseWidgetProps {}

export function AIAssistantWidget({ 
  instanceId,
  isEditing = false, 
  onRemove,
  onConfigure,
  dragHandleClassName,
  settings,
  onSettingsChange
}: AIAssistantWidgetProps) {
  // Configurações padrão
  const defaultSettings: AIAssistantSettings = {
    model: 'basic',
    suggestions: true
  };
  
  // Mesclar configurações padrão com as configurações salvas
  const widgetSettings = { ...defaultSettings, ...(settings as unknown as AIAssistantSettings) };
  
  // Estados
  const [messages, setMessages] = useState<Message[]>([
    {
      role: 'system',
      content: 'Olá! Sou seu assistente de fonoaudiologia. Como posso ajudar hoje?',
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([
    'Como criar um relatório de evolução?',
    'Sugestões de exercícios para disfonia',
    'Agendar consulta para amanhã'
  ]);
  
  const { sendMessage: sendAIMessage, isPending: isProcessing, data: aiResponse } = useAIAssistant();
  
  const {
    isListening,
    startListening,
    stopListening,
  } = useSpeechRecognition({
    onResult: (transcript) => {
      setInput(transcript);
    },
    onError: (error) => {
      console.error('Speech recognition error in widget:', error);
    }
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Rolar para o final das mensagens quando uma nova mensagem é adicionada
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Adicionar resposta da IA quando ela chegar
  useEffect(() => {
    if (aiResponse?.response) {
      setMessages(prev => [
        ...prev,
        {
          role: 'assistant',
          content: aiResponse.response,
          timestamp: new Date()
        }
      ]);
    }
  }, [aiResponse]);
  
  // Enviar mensagem
  const sendMessage = async () => {
    if (!input.trim() || isProcessing) return;
    
    // Adicionar mensagem do usuário
    const userMessage = input.trim();
    setMessages(prev => [
      ...prev,
      {
        role: 'user',
        content: userMessage,
        timestamp: new Date()
      }
    ]);
    
    setInput('');
    
    sendAIMessage(userMessage);
  };
  
  // Alternar gravação de voz
  const toggleRecording = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };
  
  // Usar sugestão
  const useSuggestion = (suggestion: string) => {
    setInput(suggestion);
  };
  
  // Formatar timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  return (
    <WidgetWrapper 
      title="Assistente IA" 
      isEditing={isEditing}
      onRemove={onRemove}
      onConfigure={onConfigure}
      dragHandleClassName={dragHandleClassName}
    >
      <div className="flex flex-col h-full">
        {/* Área de mensagens */}
        <ScrollArea className="flex-grow mb-3 pr-2">
          <div className="space-y-3">
            {messages.map((message, index) => (
              <div 
                key={index} 
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div 
                  className={`
                    max-w-[85%] rounded-lg p-2.5 
                    ${message.role === 'user' 
                      ? 'bg-primary text-primary-foreground' 
                      : 'bg-muted'
                    }
                  `}
                >
                  <div className="flex items-center gap-2 mb-1">
                    {message.role === 'assistant' ? (
                      <Bot className="h-4 w-4" />
                    ) : message.role === 'user' ? (
                      <User className="h-4 w-4" />
                    ) : null}
                    <span className="text-xs opacity-70">
                      {message.role === 'assistant' ? 'Assistente' : 
                       message.role === 'user' ? 'Você' : 'Sistema'}
                    </span>
                    <span className="text-xs opacity-70 ml-auto">
                      {formatTime(message.timestamp)}
                    </span>
                  </div>
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                </div>
              </div>
            ))}
            {isProcessing && (
              <div className="flex justify-start">
                <div className="bg-muted rounded-lg p-3">
                  <Loader2 className="h-5 w-5 animate-spin" />
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
        
        {/* Sugestões */}
        {widgetSettings.suggestions && suggestions.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {suggestions.map((suggestion, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="text-xs h-7"
                onClick={() => useSuggestion(suggestion)}
              >
                <Lightbulb className="h-3 w-3 mr-1" />
                {suggestion}
              </Button>
            ))}
          </div>
        )}
        
        {/* Área de input */}
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            className={`h-9 w-9 ${isListening ? 'bg-red-100 text-red-500 animate-pulse' : ''}`}
            onClick={toggleRecording}
          >
            {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
          </Button>
          
          <div className="relative flex-grow">
            <Input
              placeholder="Digite sua mensagem..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && sendMessage()}
              className="pr-9"
            />
            <Sparkles className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          </div>
          
          <Button
            variant="default"
            size="icon"
            className="h-9 w-9"
            onClick={sendMessage}
            disabled={!input.trim() || isProcessing}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Nota de demonstração */}
        <div className="mt-2 text-xs text-center text-muted-foreground">
          <p>As respostas do assistente são geradas por IA.</p>
        </div>
      </div>
    </WidgetWrapper>
  );
}

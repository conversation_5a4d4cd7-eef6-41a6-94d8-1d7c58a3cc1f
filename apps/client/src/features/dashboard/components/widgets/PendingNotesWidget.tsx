import { WidgetWrapper } from './WidgetWrapper';
import { useDashboardQuery } from '@/features/dashboard/hooks/useDashboardQuery';
import { Skeleton } from '@/shared/ui/skeleton';
import { Button } from '@/shared/ui/button';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useNavigate } from '@tanstack/react-router';
import { BaseWidgetProps } from '@/features/dashboard/types';
import { FileEdit, AlertCircle } from 'lucide-react';
import { Badge } from '@/shared/ui/badge';
import { useSessionNotes } from '@/features/session-notes/hooks/useSessionNotes';
import { usePatientQuery } from '@/features/patients/hooks/usePatientQuery';

export function PendingNotesWidget({ 
  isEditing = false, 
  onRemove,
  dragHandleClassName,
  instanceId 
}: BaseWidgetProps) {
  const { dashboardData, isLoading } = useDashboardQuery();
  const navigate = useNavigate();
  const { openPostAppointmentNote } = useSessionNotes();
  const { allPatients } = usePatientQuery();

  const formatShortDate = (timeString: string) => {
    try {
      const date = parseISO(timeString);
      return format(date, 'dd/MM', { locale: ptBR });
    } catch (error) {
      return timeString;
    }
  };

  const handleCreateNote = (appointmentId: string, patientId: string) => {
    // Encontrar o paciente pelos dados
    const patient = allPatients.find(p => p.id === patientId);
    const appointment = dashboardData?.pending_session_notes.find(a => a.id === appointmentId);

    if (patient && appointment) {
      const patientData = {
        id: patient.id,
        name: patient.full_name,
        avatar: undefined
      };

      const appointmentData = {
        id: appointment.id,
        patientId: appointment.patient_id,
        sessionNumber: 1, // TODO: Calcular número da sessão baseado no histórico
        scheduledAt: appointment.start_time,
        duration: 50, // Duração padrão já que não temos esse campo no appointment
        status: 'completed' as const
      };

      openPostAppointmentNote(patientData, appointmentData);
      // A invalidação da query será feita automaticamente quando a nota for salva
    }
  };

  return (
    <WidgetWrapper 
      title="Notas Pendentes" 
      isEditing={isEditing}
      onRemove={onRemove}
      dragHandleClassName={dragHandleClassName}
    >
      {isLoading ? (
        <div className="space-y-3">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
        </div>
      ) : dashboardData?.pending_session_notes && dashboardData.pending_session_notes.length > 0 ? (
        <div className="space-y-2">
          <div className="flex items-center mb-3">
            <AlertCircle className="h-4 w-4 text-amber-500 mr-2" />
            <p className="text-sm text-muted-foreground">
              {dashboardData.pending_session_notes.length} consulta{dashboardData.pending_session_notes.length > 1 ? 's' : ''} realizada{dashboardData.pending_session_notes.length > 1 ? 's' : ''} sem evolução
            </p>
          </div>

          {dashboardData.pending_session_notes.map((appointment) => (
            <div 
              key={appointment.id} 
              className="p-3 rounded-lg border border-border bg-card hover:bg-accent/10 transition-colors"
            >
              <div className="flex justify-between items-center">
                <div className="flex-1">
                  <h4 className="font-medium text-foreground">{appointment.patient_name}</h4>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <span className="mr-2">{formatShortDate(appointment.start_time)}</span>
                    <Badge variant="outline" className="text-amber-500 border-amber-500">
                      Pendente
                    </Badge>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  onClick={() => { handleCreateNote(appointment.id, appointment.patient_id); }}
                  title="Criar Nota de Sessão"
                >
                  <FileEdit className="h-4 w-4" />
                  <span className="sr-only">Criar nota de sessão</span>
                </Button>
              </div>
            </div>
          ))}
          
          <div className="flex justify-end mt-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => navigate({ to: '/notes' })}
            >
              Ver Todas as Notas
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center h-full text-center p-6">
          <p className="text-muted-foreground">Nenhuma nota pendente!</p>
          <p className="text-sm text-muted-foreground mt-1">
            Todas as suas consultas realizadas possuem notas de evolução.
          </p>
        </div>
      )}
    </WidgetWrapper>
  );
}

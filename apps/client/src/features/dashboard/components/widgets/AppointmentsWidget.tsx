import { Calendar } from 'lucide-react';
import { WidgetWrapper } from './WidgetWrapper';
import { useDashboardQuery } from '@/features/dashboard/hooks/useDashboardQuery';
import { Skeleton } from '@/shared/ui/skeleton';

interface AppointmentsWidgetProps {
  isEditing?: boolean;
  onRemove?: () => void;
  dragHandleClassName?: string;
}

export function AppointmentsWidget({ 
  isEditing = false, 
  onRemove,
  dragHandleClassName 
}: AppointmentsWidgetProps) {
  const { dashboardData, isLoading } = useDashboardQuery();

  return (
    <WidgetWrapper 
      title="Consultas de Hoje" 
      isEditing={isEditing}
      onRemove={onRemove}
      dragHandleClassName={dragHandleClassName}
    >
      {isLoading ? (
        <Skeleton className="h-24 w-full" />
      ) : (
        <div className="flex flex-col h-full justify-center">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-3xl font-bold">
                {dashboardData?.appointments.today_count || 0}
              </p>
              <p className="text-sm text-muted-foreground">
                Agendamentos para hoje
              </p>
            </div>
            <div className="h-12 w-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <Calendar className="h-6 w-6 text-blue-500" />
            </div>
          </div>
          
          <div className="mt-4">
            <div className="flex justify-between text-sm">
              <span>Consultas concluídas</span>
              <span>
                {dashboardData?.appointments.today_progress.toFixed(0) || 0}%
              </span>
            </div>
            <div className="h-2 w-full bg-muted mt-1 rounded-full overflow-hidden">
              <div 
                className="h-full bg-blue-500 rounded-full" 
                style={{ 
                  width: `${dashboardData?.appointments.today_progress || 0}%`
                }}
              />
            </div>
          </div>
        </div>
      )}
    </WidgetWrapper>
  );
}

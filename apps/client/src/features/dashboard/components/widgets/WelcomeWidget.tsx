import { useState, useEffect } from 'react';
import { WidgetWrapper } from './WidgetWrapper';
import { BaseWidgetProps } from '@/features/dashboard/types';
import { useProfile } from '@/features/auth/hooks/useProfile';
import { Button } from '@/shared/ui/button';
import { Card, CardContent } from '@/shared/ui/card';
import { 
  Sparkles, 
  Sun, 
  Moon, 
  Sunrise,
  Sunset,
  Heart,
  Info,
  Lightbulb,
  Layout,
  Settings,
  Plus,
  Move,
  Eye,
  Palette
} from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { cn } from '@/shared/lib/utils';

export function WelcomeWidget({ 
  instanceId, 
  onRemove, 
  onConfigure, 
  isEditing, 
  dragHandleClassName 
}: BaseWidgetProps) {
  const { user } = useProfile();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showTips, setShowTips] = useState(true);

  // Atualizar hora a cada minuto
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => { clearInterval(timer); };
  }, []);

  // Função para determinar saudação baseada no horário
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 6) {
      return { text: 'Boa madrugada', icon: Moon, color: 'text-indigo-500', bg: 'from-indigo-500/10 to-purple-500/10' };
    } else if (hour < 12) {
      return { text: 'Bom dia', icon: Sunrise, color: 'text-amber-500', bg: 'from-amber-500/10 to-orange-500/10' };
    } else if (hour < 18) {
      return { text: 'Boa tarde', icon: Sun, color: 'text-orange-500', bg: 'from-orange-500/10 to-red-500/10' };
    } else {
      return { text: 'Boa noite', icon: Sunset, color: 'text-blue-500', bg: 'from-blue-500/10 to-indigo-500/10' };
    }
  };

  // Função para obter nome de exibição
  const getDisplayName = () => {
    if (!user) return 'Profissional';
    const firstName = user.first_name || '';
    return firstName ? `Dr(a). ${firstName}` : 'Profissional';
  };

  // Dicas sobre como usar o dashboard
  const dashboardTips = [
    {
      title: 'Personalize seu Dashboard',
      description: 'Clique em "Adicionar Widget" para incluir novos widgets ou "Editar Layout" para reorganizar',
      icon: Plus,
      color: 'bg-blue-500/10 text-blue-600'
    },
    {
      title: 'Reorganize os Widgets',
      description: 'Arraste e solte os widgets para criar o layout perfeito para seu fluxo de trabalho',
      icon: Move,
      color: 'bg-green-500/10 text-green-600'
    },
    {
      title: 'Configure cada Widget',
      description: 'Use o botão de configurações em cada widget para personalizá-lo conforme suas necessidades',
      icon: Settings,
      color: 'bg-purple-500/10 text-purple-600'
    }
  ];

  const greeting = getGreeting();
  const displayName = getDisplayName();

  return (
    <WidgetWrapper
      title=""
      onRemove={onRemove}
      onConfigure={onConfigure}
      isEditing={isEditing}
      dragHandleClassName={dragHandleClassName}
    >
      <div className="space-y-6 p-4">
        {/* Logo e Saudação */}
        <div className="text-center space-y-4">``
          <div className={cn(
            "p-6 rounded-xl bg-gradient-to-br shadow-lg border-2",
            greeting.bg,
            "border-primary/20 transform hover:scale-[1.02] transition-all duration-300"
          )}>
            <div className="flex items-center justify-center gap-3 mb-2">
              <greeting.icon className={cn("h-8 w-8 animate-bounce", greeting.color)} />
              <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                {greeting.text}, {displayName}!
              </h2>
            </div>
            <p className="text-base text-muted-foreground font-medium">
              {format(currentTime, "EEEE, d 'de' MMMM 'de' yyyy", { locale: ptBR }).replace(/^\w/, c => c.toUpperCase())}
            </p>
            <div className="flex items-center justify-center gap-3 mt-4">
              <Heart className="h-5 w-5 text-red-500 animate-pulse" />
              <p className="text-base font-semibold text-foreground">
                Seja bem-vindo(a) ao EvoluaCare!
              </p>
              <Sparkles className="h-5 w-5 text-yellow-500 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Sobre o Dashboard */}
        <Card className="border-primary/20 bg-gradient-to-r from-primary/10 to-blue-500/10 shadow-md hover:shadow-lg transition-shadow">
          <CardContent className="p-5">
            <div className="flex items-center gap-3 mb-3">
              <Layout className="h-6 w-6 text-primary flex-shrink-0" />
              <h3 className="font-bold text-lg text-foreground">Seu Dashboard Inteligente</h3>
            </div>
            <p className="text-sm text-muted-foreground leading-relaxed">
              Gerencie consultas, pacientes,
              crie modelos personalizados e otimize seu fluxo de trabalho em um único lugar!
            </p>
          </CardContent>
        </Card>

        {/* Dicas de Uso */}
        {showTips && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5 text-amber-500" />
                <h4 className="font-semibold text-base">Dicas Rápidas</h4>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-xs"
                onClick={() => { setShowTips(false); }}
              >
                <Eye className="h-4 w-4 mr-1" />
                Ocultar
              </Button>
            </div>
            
            <div className="grid gap-3">
              {dashboardTips.map((tip, index) => (
                <Card
                  key={index}
                  className={cn(
                    "border-none shadow-sm hover:shadow-md transition-all duration-200",
                    tip.color
                  )}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <div className="p-2 rounded-full bg-white/50">
                        <tip.icon className="h-4 w-4" />
                      </div>
                      <div>
                        <h5 className="font-semibold text-sm mb-1">{tip.title}</h5>
                        <p className="text-xs text-muted-foreground">{tip.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Botão para mostrar dicas */}
        {!showTips && (
          <Button
            variant="outline"
            size="sm"
            className="w-full text-sm border-primary/20 hover:bg-primary/5"
            onClick={() => { setShowTips(true); }}
          >
            <Lightbulb className="h-4 w-4 mr-2" />
            Mostrar Dicas
          </Button>
        )}

        {/* Rodapé Motivacional */}
        <div className="text-center p-4 rounded-xl bg-gradient-to-r from-emerald-500/10 to-teal-500/10 border border-emerald-500/20 shadow-sm">
          <div className="flex items-center justify-center gap-3 mb-2">
            <Palette className="h-5 w-5 text-emerald-600" />
            <Info className="h-5 w-5 text-emerald-600" />
          </div>
          <p className="text-sm font-medium text-muted-foreground">
            Personalize este espaço para otimizar seu dia a dia profissional
          </p>
        </div>
      </div>
    </WidgetWrapper>
  );
}

export default WelcomeWidget;
import { DollarSign, TrendingDown, TrendingUp, Wallet } from 'lucide-react';
import { useGetFinancialSummary } from '@/features/financial/hooks/useFinancialQuery';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/ui/card';
import { WidgetWrapper } from './WidgetWrapper';
import { BaseWidgetProps } from '../../types';
import { Skeleton } from '@/shared/ui/skeleton';

export function FinancialSummaryWidget({ instanceId, onRemove, onConfigure, isEditing, dragHandleClassName }: BaseWidgetProps) {
  const { data: summary, isLoading, error } = useGetFinancialSummary();

  const formatCurrency = (value: number | string | undefined) => {
    if (value === undefined) return 'R$ 0,00';
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(numericValue);
  };
  
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="space-y-4 p-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-8 w-1/2" />
          <Skeleton className="h-8 w-2/3" />
        </div>
      );
    }

    if (error) {
      return <p className="text-destructive p-4">Erro ao carregar dados.</p>;
    }

    if (!summary) {
        return <p className="text-muted-foreground p-4">Não há dados financeiros.</p>;
    }

    return (
      <div className="p-4 grid grid-cols-2 gap-4 text-sm">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-500/10 rounded-md">
            <TrendingUp className="h-5 w-5 text-green-500" />
          </div>
          <div>
            <p className="text-muted-foreground">Receita</p>
            <p className="font-bold">{formatCurrency(summary.income)}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-red-500/10 rounded-md">
            <TrendingDown className="h-5 w-5 text-red-500" />
          </div>
          <div>
            <p className="text-muted-foreground">Despesa</p>
            <p className="font-bold">{formatCurrency(summary.expenses)}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3 col-span-2">
           <div className="p-2 bg-blue-500/10 rounded-md">
            <Wallet className="h-5 w-5 text-blue-500" />
          </div>
          <div>
            <p className="text-muted-foreground">Saldo</p>
            <p className="font-bold">{formatCurrency(summary.balance)}</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <WidgetWrapper
      title="Resumo Financeiro"
      onRemove={onRemove}
      onConfigure={onConfigure}
      isEditing={isEditing}
      dragHandleClassName={dragHandleClassName}
      isLoading={isLoading}
    >
      {renderContent()}
    </WidgetWrapper>
  );
} 
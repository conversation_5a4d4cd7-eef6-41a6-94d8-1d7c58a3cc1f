import { useState } from 'react';
import { WidgetWrapper } from './WidgetWrapper';
import { Textarea } from '@/shared/ui/textarea';
import { Button } from '@/shared/ui/button';
import { Plus, Trash2, Loader2, Edit, X, Check } from 'lucide-react';
import { BaseWidgetProps } from '@/features/dashboard/types';
import { NoteItem } from '@/features/dashboard/types/dashboard.schema';
import { useDashboardQuery } from '@/features/dashboard/hooks/useDashboardQuery';
import { useNotesMutations } from '@/features/notes/hooks/useNotesMutations';
import { Input } from '@/shared/ui/input';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useQueryClient } from '@tanstack/react-query';
import { DASHBOARD_QUERY_KEYS } from '@/features/dashboard/hooks/useDashboardQuery';

export function NotesWidget({
  isEditing = false,
  onRemove,
  onConfigure,
  dragHandleClassName,
}: BaseWidgetProps) {
  const queryClient = useQueryClient();
  const { dashboardData, isLoading, isError } = useDashboardQuery();
  
  const {
    createNote,
    updateNote,
    deleteNote,
    isCreating,
    isUpdating,
    isDeleting,
  } = useNotesMutations();

  const [newNoteContent, setNewNoteContent] = useState('');
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);
  const [editingNoteContent, setEditingNoteContent] = useState('');

  const notes = dashboardData?.notes.notes_list || [];

  const invalidateDashboardQuery = () => {
    queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.summary });
  };

  const handleAddNote = () => {
    if (newNoteContent.trim()) {
      createNote({ content: newNoteContent.trim() }, {
        onSuccess: () => {
          setNewNoteContent('');
          invalidateDashboardQuery();
        }
      });
    }
  };

  const handleDeleteNote = (noteId: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta nota?')) {
      deleteNote(noteId, {
        onSuccess: () => {
          if (editingNoteId === noteId) {
            setEditingNoteId(null);
            setEditingNoteContent('');
          }
          invalidateDashboardQuery();
        }
      });
    }
  };

  const startEditing = (note: NoteItem) => {
    setEditingNoteId(note.id);
    setEditingNoteContent(note.content);
  };

  const cancelEditing = () => {
    setEditingNoteId(null);
    setEditingNoteContent('');
  };

  const handleUpdateNote = () => {
    if (editingNoteId && editingNoteContent.trim()) {
      updateNote({
        id: editingNoteId,
        data: { content: editingNoteContent.trim() },
      }, {
        onSuccess: () => {
          cancelEditing();
          invalidateDashboardQuery();
        }
      });
    }
  };

  const formatRelativeDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true,
        locale: ptBR,
      });
    } catch {
      return dateString;
    }
  };

  const anyMutationLoading = isCreating || isUpdating || isDeleting;

  return (
    <WidgetWrapper
      title="Notas Rápidas"
      isEditing={isEditing}
      onRemove={onRemove}
      onConfigure={onConfigure}
      dragHandleClassName={dragHandleClassName}
      isLoading={isLoading}
    >
      <div className="flex flex-col h-full p-4 space-y-3">
        <div className="flex space-x-2">
          <Input
            placeholder="Nova nota..."
            value={newNoteContent}
            onChange={(e) => setNewNoteContent(e.target.value)}
            disabled={isCreating}
            onKeyDown={(e) => e.key === 'Enter' && handleAddNote()}
          />
          <Button
            onClick={handleAddNote}
            size="icon"
            disabled={isCreating || !newNoteContent.trim()}
          >
            {isCreating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Plus className="h-4 w-4" />}
          </Button>
        </div>

        {isError && <p className="text-xs text-destructive">Erro ao carregar notas.</p>}
        <ScrollArea className="flex-grow pr-3">
          <div className="space-y-2">
            {notes.length === 0 && !isLoading && !isError && (
              <p className="text-sm text-muted-foreground text-center py-4">Nenhuma nota ainda.</p>
            )}
            {notes.map((note) => (
              <div key={note.id} className="text-sm p-2 border rounded bg-card group relative">
                {editingNoteId === note.id ? (
                  <div className="space-y-2">
                    <Textarea
                      value={editingNoteContent}
                      onChange={(e) => setEditingNoteContent(e.target.value)}
                      className="min-h-[60px]"
                      autoFocus
                    />
                    <div className="flex justify-end space-x-2">
                      <Button variant="ghost" size="sm" onClick={cancelEditing}>
                        <X className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleUpdateNote}
                        disabled={isUpdating || !editingNoteContent.trim()}
                      >
                        {isUpdating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Check className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <p className="whitespace-pre-wrap break-words">{note.content}</p>
                    <span className="text-xs text-muted-foreground block mt-1">
                      {formatRelativeDate(note.updated_at)}
                    </span>
                    <div className={`absolute top-1 right-1 flex space-x-1 opacity-0 group-hover:opacity-100 ${isEditing ? 'opacity-100' : ''} transition-opacity`}>
                      <Button variant="ghost" size="sm" onClick={() => startEditing(note)} disabled={anyMutationLoading}>
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleDeleteNote(note.id)} disabled={anyMutationLoading}>
                        {isDeleting && editingNoteId === note.id ? <Loader2 className="h-3 w-3 animate-spin" /> : <Trash2 className="h-3 w-3 text-destructive" />}
                      </Button>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
    </WidgetWrapper>
  );
}

import { Users } from 'lucide-react';
import { WidgetWrapper } from './WidgetWrapper';
import { useDashboardQuery } from '@/features/dashboard/hooks/useDashboardQuery';
import { Skeleton } from '@/shared/ui/skeleton';

interface PatientStatsWidgetProps {
  isEditing?: boolean;
  onRemove?: () => void;
  dragHandleClassName?: string;
}

export function PatientStatsWidget({ 
  isEditing = false, 
  onRemove,
  dragHandleClassName 
}: PatientStatsWidgetProps) {
  const { dashboardData, isLoading } = useDashboardQuery();

  return (
    <WidgetWrapper 
      title="Estatísticas de Pacientes" 
      isEditing={isEditing}
      onRemove={onRemove}
      dragHandleClassName={dragHandleClassName}
    >
      {isLoading ? (
        <Skeleton className="h-24 w-full" />
      ) : (
        <div className="flex flex-col h-full justify-center">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-3xl font-bold">
                {dashboardData?.patients.total || 0}
              </p>
              <p className="text-sm text-muted-foreground">
                Total de Pacientes
              </p>
            </div>
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
              <Users className="h-6 w-6 text-primary" />
            </div>
          </div>
          
          <div className="mt-4">
            <p className="text-sm font-medium">
              {dashboardData?.patients.new_this_month || 0} novos este mês
            </p>
            <div className="h-2 w-full bg-muted mt-1 rounded-full overflow-hidden">
              <div 
                className="h-full bg-primary rounded-full" 
                style={{ 
                  width: `${Math.min(
                    (dashboardData?.patients.new_this_month || 0) / 
                    (dashboardData?.patients.total || 1) * 100, 
                    100
                  )}%` 
                }}
              />
            </div>
          </div>
        </div>
      )}
    </WidgetWrapper>
  );
}

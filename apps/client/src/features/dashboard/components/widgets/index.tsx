import { PatientStatsWidget } from './PatientStatsWidget';
import { AppointmentsWidget } from './AppointmentsWidget';
import { CalendarWidget } from './CalendarWidget';
import { UpcomingAppointmentsWidget } from './UpcomingAppointmentsWidget';
import { QuickActionsWidget } from './QuickActionsWidget';
import { NotesWidget } from './NotesWidget';
import { RecentPatientsWidget } from './RecentPatientsWidget';
import { TimerWidget } from './TimerWidget';
import { WeatherWidget } from './WeatherWidget';
import { AIAssistantWidget } from './AIAssistantWidget';
import { ModelsWidget } from './ModelsWidget';
import { NextAppointmentWidget } from './NextAppointmentWidget';
import { PendingNotesWidget } from './PendingNotesWidget';
import WelcomeWidget from './WelcomeWidget';
import { getWidgetById } from '@/features/dashboard/widgetRegistry';
import { BaseWidgetProps } from '@/features/dashboard/types';
import { useWidgetSettingsQuery } from '@/features/dashboard/hooks/useWidgetSettingsQuery';
import { FinancialSummaryWidget } from './FinancialSummaryWidget';

interface WidgetProps extends BaseWidgetProps {
  widgetId: string;
}

export function DashboardWidget({
  widgetId,
  instanceId,
  isEditing = false,
  onRemove,
  onConfigure,
  dragHandleClassName,
  settings,
  onSettingsChange
}: WidgetProps) {
  // Obter definição do widget
  const widgetDef = getWidgetById(widgetId);

  // Obter configurações do widget usando React Query
  const {
    settings: widgetSettings,
    updateSettings,
    isLoading: isLoadingSettings
  } = useWidgetSettingsQuery(instanceId);

  if (!widgetDef) {
    return (
      <div className="h-full flex items-center justify-center p-4 border rounded-lg border-dashed">
        <p className="text-muted-foreground text-center">
          Widget não encontrado: {widgetId}
        </p>
      </div>
    );
  }

  // Usar configurações do React Query ou as fornecidas via props
  const effectiveSettings = settings || widgetSettings;

  // Função para atualizar configurações
  const handleSettingsChange = (newSettings: Record<string, unknown>) => {
    if (onSettingsChange) {
      // Se o componente pai forneceu uma função de atualização, usá-la
      onSettingsChange(newSettings);
    } else {
      // Caso contrário, usar a função do React Query
      updateSettings(newSettings);
    }
  };

  // Propriedades comuns para todos os widgets
  const commonProps = {
    instanceId,
    isEditing,
    onRemove,
    onConfigure,
    dragHandleClassName,
    settings: effectiveSettings,
    onSettingsChange: handleSettingsChange
  };

  // Renderizar o widget apropriado com base no tipo
  switch (widgetId) {
    case 'welcome':
      return <WelcomeWidget {...commonProps} />;

    case 'patientStats':
      return <PatientStatsWidget {...commonProps} />;

    case 'appointmentsToday':
      return <AppointmentsWidget {...commonProps} />;

    case 'calendar':
      return <CalendarWidget {...commonProps} />;

    case 'upcomingAppointments':
      return <UpcomingAppointmentsWidget {...commonProps} />;

    case 'nextAppointment':
      return <NextAppointmentWidget {...commonProps} />;

    case 'pendingNotes':
      return <PendingNotesWidget {...commonProps} />;

    case 'quickActions':
      return <QuickActionsWidget {...commonProps} />;

    case 'notes':
      return <NotesWidget {...commonProps} />;

    case 'recentPatients':
      return <RecentPatientsWidget {...commonProps} />;

    case 'timer':
      return <TimerWidget {...commonProps} />;

    case 'weather':
      return <WeatherWidget {...commonProps} />;

    case 'aiAssistant':
      return <AIAssistantWidget {...commonProps} />;

    case 'models':
      return <ModelsWidget {...commonProps} />;

    case 'financialSummary':
      return <FinancialSummaryWidget {...commonProps} />;

    // Widgets em desenvolvimento ou não implementados
    case 'treatmentPlans':
    case 'clinicalResources':
    case 'progressCharts':
    case 'messageCenter':
    case 'notifications':
      return (
        <div className="h-full flex flex-col items-center justify-center p-4 border rounded-lg border-dashed">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
            <widgetDef.icon className="h-6 w-6 text-primary" />
          </div>
          <h3 className="font-medium mb-1">{widgetDef.title}</h3>
          <p className="text-muted-foreground text-center text-sm">
            {widgetDef.isComingSoon
              ? "Este widget estará disponível em breve!"
              : "Widget em desenvolvimento"}
          </p>
        </div>
      );

    default:
      return (
        <div className="h-full flex items-center justify-center p-4 border rounded-lg border-dashed">
          <p className="text-muted-foreground text-center">
            Widget não implementado: {widgetId}
          </p>
        </div>
      );
  }
}

import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select"; // Importando componentes Select do shadcn/ui
import { Dashboard } from '@/features/dashboard/types'; // Importando o tipo Dashboard

interface DashboardSelectorProps {
  dashboards: Dashboard[];
  activeDashboardId: string | null;
  setActiveDashboardId: (id: string) => void;
  isLoading?: boolean; // Opcional para mostrar estado de carregamento
}

export function DashboardSelector({
  dashboards,
  activeDashboardId,
  setActiveDashboardId,
  isLoading = false,
}: DashboardSelectorProps) {

  const handleValueChange = (value: string) => {
    setActiveDashboardId(value);
  };

  const activeDashboardName = dashboards.find(d => d.id === activeDashboardId)?.name || "Selecionar...";

  return (
    <Select
      value={activeDashboardId || ''}
      onValueChange={handleValueChange}
      disabled={isLoading || dashboards.length === 0}
    >
      <SelectTrigger className="w-[180px] md:w-[250px]">
        <SelectValue placeholder="Selecionar Dashboard">
          {isLoading ? "Carregando..." : activeDashboardName}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {dashboards.length === 0 && !isLoading ? (
           <SelectItem value="nodashboards" disabled>Nenhum dashboard encontrado</SelectItem>
        ) : (
          dashboards.map((dashboard) => (
            <SelectItem key={dashboard.id} value={dashboard.id}>
              {dashboard.name} {dashboard.is_default ? "(Padrão)" : ""}
            </SelectItem>
          ))
        )}
        {/* Adicionar opção para criar novo dashboard aqui? */}
      </SelectContent>
    </Select>
  );
}

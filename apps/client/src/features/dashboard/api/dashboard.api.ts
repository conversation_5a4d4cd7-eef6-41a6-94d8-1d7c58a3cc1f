import { axiosInstance } from "@/shared/lib/api.client";
// Importar o tipo Dashboard definido localmente
import { Dashboard } from '@/features/dashboard/types';
// DashboardState, WidgetSettings e JsonValue não são mais usados diretamente aqui

// Chaves de query para dashboards
export const DASHBOARD_KEYS = {
  all: ['dashboards'],
  lists: () => [...DASHBOARD_KEYS.all, 'list'],
  details: () => [...DASHBOARD_KEYS.all, 'detail'],
  detail: (id: string) => [...DASHBOARD_KEYS.details(), id],
  default: ['dashboards', 'default'], // Chave para o dashboard padrão
  widgetSettings: (instanceId: string) => ['dashboard', 'widget', 'settings', instanceId], // Mantido por enquanto
  widgetData: (widgetId: string, instanceId: string) => ['dashboard', 'widget', 'data', widgetId, instanceId], // Mantido por enquanto
};

// Importar tipos de payload de types.ts
import { CreateDashboardPayload, UpdateDashboardPayload } from '@/features/dashboard/types';


// API para gerenciamento de dashboards
export const dashboardApi = {
  // Listar todos os dashboards do usuário
  listDashboards: async (): Promise<Dashboard[]> => { // Usar Dashboard
    try {
      console.log('Chamando API para listar dashboards');
      const response = await axiosInstance.get('/protected/dashboards');
      console.log('Resposta da API de listagem de dashboards:', response);

      // Verificar se a resposta contém dados válidos
      if (!response.data) {
        console.error('Resposta da API não contém dados:', response);
        return []; // Retorna array vazio em vez de lançar erro
      }

      return response.data;
    } catch (error) {
      console.error('Erro ao listar dashboards:', error);
      // Retorna array vazio em vez de lançar erro para evitar quebrar a UI
      return [];
    }
  },

  // Buscar o dashboard padrão do usuário
  getDefaultDashboard: async (): Promise<Dashboard> => { // Usar Dashboard
    try {
      const response = await axiosInstance.get('/protected/dashboards/default');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar dashboard padrão:', error);
      throw error;
    }
  },

  // Buscar um dashboard específico pelo ID
  getDashboard: async (id: string): Promise<Dashboard> => { // Usar Dashboard
    try {
      console.log(`Chamando API para buscar dashboard ${id}`);
      const response = await axiosInstance.get(`/protected/dashboards/${id}`);
      console.log(`Resposta da API para dashboard ${id}:`, response);

      // Verificar se a resposta contém dados válidos
      if (!response.data) {
        console.error(`Resposta da API para dashboard ${id} não contém dados:`, response);
        throw new Error(`Dashboard ${id} não encontrado ou resposta inválida`);
      }

      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar dashboard ${id}:`, error);
      throw error;
    }
  },

  // Criar um novo dashboard
  createDashboard: async (payload: CreateDashboardPayload): Promise<Dashboard> => { // Usar Dashboard
    try {
      const response = await axiosInstance.post('/protected/dashboards', payload);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar dashboard:', error);
      throw error;
    }
  },

  // Atualizar um dashboard existente
  updateDashboard: async (id: string, payload: UpdateDashboardPayload): Promise<Dashboard> => { // Usar Dashboard
    try {
      console.log(`Atualizando dashboard com ID ${id}...`);
      console.log('Payload:', payload);
      const response = await axiosInstance.put(`/protected/dashboards/${id}`, payload);
      console.log(`Dashboard com ID ${id} atualizado com sucesso:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar dashboard ${id}:`, error);
      throw error;
    }
  },

  // Deletar um dashboard
  deleteDashboard: async (id: string): Promise<void> => {
    try {
      await axiosInstance.delete(`/protected/dashboards/${id}`);
    } catch (error) {
      console.error(`Erro ao deletar dashboard ${id}:`, error);
      throw error;
    }
  },

   // Definir um dashboard como padrão
   setDefaultDashboard: async (id: string): Promise<Dashboard> => { // Usar Dashboard
    try {
      // Usamos POST para a ação de definir como padrão
      const response = await axiosInstance.post(`/protected/dashboards/${id}/set-default`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao definir dashboard ${id} como padrão:`, error);
      throw error;
    }
  },

  // --- APIs de Widgets (mantidas por enquanto) ---
  widgetSettings: {
    // Buscar configurações de um widget
    get: async <T extends Record<string, unknown>>(instanceId: string): Promise<T> => { // any -> unknown
      try {
        const response = await axiosInstance.get(`/protected/dashboard/widget/${instanceId}/settings`);
        return response.data;
      } catch (error) {
        console.error(`Erro ao buscar configurações do widget ${instanceId}:`, error);
        throw error;
      }
    },

    // Atualizar configurações de um widget
    update: async <T extends Record<string, unknown>>(instanceId: string, settings: T): Promise<T> => { // any -> unknown
      try {
        const response = await axiosInstance.put(`/protected/dashboard/widget/${instanceId}/settings`, settings);
        return response.data;
      } catch (error) {
        console.error(`Erro ao atualizar configurações do widget ${instanceId}:`, error);
        throw error;
      }
    },
  },

  // API para dados específicos de widgets
  widgetData: {
    // Buscar dados de um widget
    get: async <T>(widgetId: string, instanceId: string): Promise<T> => {
      try {
        const response = await axiosInstance.get(`/protected/dashboard/widget/${widgetId}/${instanceId}/data`);
        return response.data;
      } catch (error) {
        console.error(`Erro ao buscar dados do widget ${widgetId}/${instanceId}:`, error);
        throw error;
      }
    },

    // Atualizar dados de um widget
    update: async <T>(widgetId: string, instanceId: string, data: T): Promise<T> => {
      try {
        const response = await axiosInstance.put(`/protected/dashboard/widget/${widgetId}/${instanceId}/data`, data);
        return response.data;
      } catch (error) {
        console.error(`Erro ao atualizar dados do widget ${widgetId}/${instanceId}:`, error);
        throw error;
      }
    },
  },
};

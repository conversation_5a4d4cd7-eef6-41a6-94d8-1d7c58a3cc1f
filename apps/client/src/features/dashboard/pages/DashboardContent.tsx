import { But<PERSON> } from "@/shared/ui/button";
import { useDashboardQuery } from "@/features/dashboard/hooks/useDashboardQuery";
import { DashboardPage as CustomizableDashboard } from "./DashboardPage";

export function DashboardContent() {
  const { isError, refetch } = useDashboardQuery();

  if (isError) {
    return (
      <div className="container mx-auto p-6 flex flex-col items-center justify-center">
        <div className="max-w-md text-center p-6 rounded-lg border border-destructive/30 bg-destructive/10">
          <h2 className="text-lg font-medium text-destructive mb-2">Erro ao carregar dados</h2>
          <p className="text-muted-foreground mb-4">
            Ocorreu um problema ao carregar os dados do dashboard.
          </p>
          <Button onClick={() => refetch()}>Tentar novamente</Button>
        </div>
      </div>
    );
  }

  return (
    <main className="container max-w-full p-6 space-y-6">
      {/* Dashboard Personalizável */}
      <CustomizableDashboard />
    </main>
  );
}

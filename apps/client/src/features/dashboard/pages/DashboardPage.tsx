import { useState, useCallback } from 'react';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout'; // Importar Layout
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
// Importar DashboardItem para tipagem
import { DashboardItem, CreateDashboardPayload } from '@/features/dashboard/types';
import { DashboardWidget } from '@/features/dashboard/components/widgets';
// Corrigir nome do hook importado
import { useDashboardLayoutManager } from '@/features/dashboard/hooks/useDashboardLayoutQuery';
import { Button } from '@/shared/ui/button';
import { Settings, Plus, Loader2, Edit, X, Check, MoreVertical, LayoutGrid, Rows, Columns, RefreshCw } from 'lucide-react';
import { DashboardSettings } from '@/features/dashboard/components/DashboardSettings';
import { WidgetStore } from '@/features/dashboard/components/WidgetStore';
import { DashboardSelector } from '@/features/dashboard/components/DashboardSelector'; // Importar o novo componente
import { CreateDashboardDialog } from '@/features/dashboard/components/CreateDashboardDialog';
import { RenameDashboardDialog } from '@/features/dashboard/components/RenameDashboardDialog';
import { DeleteDashboardDialog } from '@/features/dashboard/components/DeleteDashboardDialog';
import { useToast } from '@/shared/hooks/use-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { dashboardApi, DASHBOARD_KEYS } from '@/features/dashboard/api/dashboard.api';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/shared/ui/dropdown-menu"; // Para o menu de gerenciamento

// Componente ResponsiveGridLayout com suporte a largura
const ResponsiveGridLayout = WidthProvider(Responsive);

export function DashboardPage() {
  // Usar o hook renomeado
  const {
    layout, // Usaremos 'layout' que agora reflete o draft ou o salvo
    visibleLayout, // Continua sendo o layout filtrado para exibição
    isLoading,
    isEditing,
    // Funções de controle de edição
    startEditing,
    commitLayout,
    discardDraftLayout,
    // Função para atualizar o rascunho
    updateLayout,
    // Funções de gerenciamento de widgets
    addWidget,
    removeWidget,
    toggleWidgetVisibility,
    updateWidgetSettings,
    resetLayout,
    optimizeLayout,
    // Adicionar dashboards e setActiveDashboardId se necessário para UI
    dashboards,
    activeDashboardId,
    setActiveDashboardId,
  } = useDashboardLayoutManager();

  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isWidgetStoreOpen, setIsWidgetStoreOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Estados para controle de layout
  const [compactType, setCompactType] = useState<'vertical' | 'horizontal' | null>('vertical');
  const [preventCollision, setPreventCollision] = useState(false);
  const [cols] = useState({ lg: 4, md: 3, sm: 2, xs: 1, xxs: 1 });
  const [rowHeight] = useState(150);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Mutação para criar um novo dashboard
  const createDashboardMutation = useMutation({
    mutationFn: (payload: CreateDashboardPayload) => {
      return dashboardApi.createDashboard(payload);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.lists() });
      setActiveDashboardId(data.id);
      toast({
        title: "Dashboard criado",
        description: `O dashboard "${data.name}" foi criado com sucesso.`,
        variant: "default"
      });
      setIsCreateDialogOpen(false);
    },
    onError: (error) => {
      console.error("Erro ao criar dashboard:", error);
      toast({
        title: "Erro ao criar dashboard",
        description: "Ocorreu um erro ao criar o dashboard. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  // Mutação para renomear um dashboard
  const renameDashboardMutation = useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) => {
      return dashboardApi.updateDashboard(id, { name });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.detail(data.id) });
      toast({
        title: "Dashboard renomeado",
        description: `O dashboard foi renomeado para "${data.name}".`,
        variant: "default"
      });
      setIsRenameDialogOpen(false);
    },
    onError: (error) => {
      console.error("Erro ao renomear dashboard:", error);
      toast({
        title: "Erro ao renomear dashboard",
        description: "Ocorreu um erro ao renomear o dashboard. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  // Mutação para definir um dashboard como padrão
  const setDefaultDashboardMutation = useMutation({
    mutationFn: (id: string) => {
      return dashboardApi.setDefaultDashboard(id);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.default });
      toast({
        title: "Dashboard padrão definido",
        description: `"${data.name}" agora é seu dashboard padrão.`,
        variant: "default"
      });
    },
    onError: (error) => {
      console.error("Erro ao definir dashboard padrão:", error);
      toast({
        title: "Erro ao definir dashboard padrão",
        description: "Ocorreu um erro ao definir o dashboard padrão. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  // Mutação para excluir um dashboard
  const deleteDashboardMutation = useMutation({
    mutationFn: async (id: string) => {
      // Verificar se o dashboard a ser excluído é o padrão
      const dashboardToDelete = dashboards.find(d => d.id === id);
      const isDefault = dashboardToDelete?.is_default === true;

      // Se for o padrão e existirem outros dashboards, primeiro definir outro como padrão
      if (isDefault && dashboards.length > 1) {
        // Encontrar outro dashboard para definir como padrão
        const otherDashboard = dashboards.find(d => d.id !== id);
        if (otherDashboard) {
          try {
            console.log(`Definindo dashboard ${otherDashboard.id} como padrão antes de excluir o atual...`);
            // Definir o outro dashboard como padrão ANTES de excluir o atual
            await dashboardApi.setDefaultDashboard(otherDashboard.id);

            // Agora podemos excluir o dashboard original com segurança
            await dashboardApi.deleteDashboard(id);

            return { wasDefault: true, newDefaultId: otherDashboard.id };
          } catch (error) {
            console.error("Erro ao definir novo dashboard padrão:", error);
            throw new Error("Não foi possível definir outro dashboard como padrão antes de excluir.");
          }
        }
      } else {
        // Se não for o padrão, podemos excluir diretamente
        await dashboardApi.deleteDashboard(id);
        return { wasDefault: false, newDefaultId: null };
      }
    },
    onSuccess: (result) => {
      if (!result) {
        console.warn('Resultado da exclusão de dashboard indefinido');
        return;
      }

      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.lists() });
      // Não precisamos invalidar o dashboard específico pois ele foi excluído

      if (result.wasDefault && result.newDefaultId) {
        // Se era o padrão e definimos um novo padrão
        const newDefaultDashboard = dashboards.find(d => d.id === result.newDefaultId);
        toast({
          title: "Dashboard excluído",
          description: `O dashboard foi excluído com sucesso. "${newDefaultDashboard?.name || 'Outro dashboard'}" foi definido como padrão.`,
          variant: "default"
        });
        // Definir o novo dashboard como ativo
        setActiveDashboardId(result.newDefaultId);
      } else {
        toast({
          title: "Dashboard excluído",
          description: "O dashboard foi excluído com sucesso.",
          variant: "default"
        });
      }

      setIsDeleteDialogOpen(false);
    },
    onError: (error) => {
      console.error("Erro ao excluir dashboard:", error);
      toast({
        title: "Erro ao excluir dashboard",
        description: "Ocorreu um erro ao excluir o dashboard. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  // Handlers para as ações de dashboard
  const handleCreateDashboard = (name: string, makeDefault: boolean) => {
    createDashboardMutation.mutate({ name, is_default: makeDefault });
  };

  const handleRenameDashboard = (name: string) => {
    if (activeDashboardId) {
      renameDashboardMutation.mutate({ id: activeDashboardId, name });
    }
  };

  const handleSetDefaultDashboard = () => {
    if (activeDashboardId) {
      setDefaultDashboardMutation.mutate(activeDashboardId);
    }
  };

  const handleDeleteDashboard = () => {
    if (activeDashboardId) {
      deleteDashboardMutation.mutate(activeDashboardId);
    }
  };

  // Callback para quando o layout é alterado pelo react-grid-layout
  const handleLayoutChange = useCallback((currentLayout: Layout[]) => {
    // Adicionamos um log para depuração
    console.log('handleLayoutChange called with layout:', currentLayout.length, 'items');

    // Verificamos se o layout está vazio, o que pode acontecer durante a inicialização
    if (currentLayout.length === 0) {
      console.log('handleLayoutChange: Layout vazio, ignorando');
      return;
    }

    // Passamos o layout atualizado para o hook atualizar o rascunho
    // O hook agora lida com o caso em que draftLayout é null
    updateLayout(currentLayout);
  }, [updateLayout]); // Depende apenas de updateDraftLayout

  // Adicionar um novo widget
  const handleAddWidget = (widgetId: string) => {
    addWidget(widgetId);
  };

  // Remover um widget
  const handleRemoveWidget = (instanceId: string) => {
    removeWidget(instanceId);
  };

  // Atualizar configurações de um widget
  const handleUpdateWidgetSettings = (instanceId: string, settings: Record<string, unknown>) => { // Alterado any para Record<string, unknown>
    updateWidgetSettings(instanceId, settings);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-sm text-muted-foreground">Carregando dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Barra de ações do dashboard */}
      <div className="flex flex-wrap gap-4 justify-between items-center"> {/* Adicionado flex-wrap e gap */}

        {/* Seletor de Dashboard e Botões de Gerenciamento */}
        <div className="flex items-center gap-2">
          <DashboardSelector
            dashboards={dashboards}
            activeDashboardId={activeDashboardId}
            setActiveDashboardId={setActiveDashboardId}
            isLoading={isLoading}
          />
          {/* Botões de Gerenciamento (Exemplo com Dropdown) */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" disabled={!activeDashboardId || isLoading}>
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setIsCreateDialogOpen(true)}>
                Novo Dashboard...
              </DropdownMenuItem>
              <DropdownMenuItem
                disabled={!activeDashboardId || isLoading}
                onClick={() => setIsRenameDialogOpen(true)}
              >
                Renomear Dashboard Atual...
              </DropdownMenuItem>
              <DropdownMenuItem
                disabled={!activeDashboardId || isLoading || (dashboards.find(d => d.id === activeDashboardId)?.is_default === true)}
                onClick={handleSetDefaultDashboard}
              >
                Definir como Padrão
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                disabled={!activeDashboardId || isLoading || dashboards.length <= 1}
                className="text-destructive focus:text-destructive"
                onClick={() => setIsDeleteDialogOpen(true)}
              >
                Excluir Dashboard Atual
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Botões de Ação do Layout */}
         <div className="flex flex-wrap items-center gap-2">
            {/* Controles de Layout - Visíveis apenas no modo de edição */}
            {isEditing && (
              <div className="flex items-center gap-1 mr-2 border-r pr-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <LayoutGrid className="mr-2 h-4 w-4" />
                      Layout
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setCompactType('vertical')} className={compactType === 'vertical' ? 'bg-accent' : ''}>
                      <Rows className="mr-2 h-4 w-4" />
                      Compactação Vertical
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setCompactType('horizontal')} className={compactType === 'horizontal' ? 'bg-accent' : ''}>
                      <Columns className="mr-2 h-4 w-4" />
                      Compactação Horizontal
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setCompactType(null)} className={compactType === null ? 'bg-accent' : ''}>
                      <LayoutGrid className="mr-2 h-4 w-4" />
                      Sem Compactação
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setPreventCollision(!preventCollision)}>
                      <input
                        type="checkbox"
                        checked={preventCollision}
                        onChange={() => {}}
                        className="mr-2"
                      />
                      Evitar Sobreposição
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={optimizeLayout}
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Otimizar
                </Button>
              </div>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsWidgetStoreOpen(true)}
              disabled={isLoading} // Desabilitar enquanto carrega
            >
              <Plus className="mr-2 h-4 w-4" />
             Adicionar Widget
           </Button>
           <Button
             variant="outline"
             size="sm"
             onClick={() => setIsSettingsOpen(true)}
           >
             <Settings className="mr-2 h-4 w-4" />
             Configurar Widgets
           </Button>
           {isEditing ? (
             <>
               <Button
                 variant="secondary"
                 size="sm"
                 onClick={discardDraftLayout}
               >
                 <X className="mr-2 h-4 w-4" />
                 Cancelar
               </Button>
               <Button
                 variant="default"
                 size="sm"
                 onClick={() => {
                   console.log('Botão Concluir Edição clicado');
                   commitLayout();
                 }}
               >
                 <Check className="mr-2 h-4 w-4" />
                 Concluir Edição
               </Button>
             </>
           ) : (
             <Button
              variant="outline"
              size="sm"
              onClick={startEditing}
              disabled={isLoading} // Desabilitar enquanto carrega
            >
              <Edit className="mr-2 h-4 w-4" />
               Editar Layout
             </Button>
           )}
         </div>
       </div>

      {/* Grid de widgets */}
      {/* Passa o layout atual (rascunho ou salvo) para o grid */}
      <ResponsiveGridLayout
        className="layout"
        layouts={{ lg: visibleLayout }} // Usa o visibleLayout que vem do hook (já filtrado e reflete o draft se editando)
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={cols} // Usa o estado de colunas
        rowHeight={rowHeight} // Usa o estado de altura de linha
        containerPadding={[5, 5]} // Padding menor para aproveitar melhor o espaço
        margin={[6, 6]} // Reduzido para diminuir o espaçamento entre widgets
        isDraggable={isEditing}
        isResizable={isEditing}
        onLayoutChange={handleLayoutChange}
        draggableHandle=".drag-handle"
        compactType={compactType} // Usa o estado de tipo de compactação
        preventCollision={preventCollision} // Usa o estado de prevenção de colisão
        useCSSTransforms={true} // Melhor performance
        autoSize={true} // Ajusta automaticamente o tamanho do container
      >
        {/* Mapeia sobre o visibleLayout para renderizar os widgets */}
        {visibleLayout.map((item: DashboardItem) => ( // Adicionado tipo DashboardItem
          <div key={item.i} className="shadow-sm">
            <DashboardWidget
              widgetId={item.widgetId}
              instanceId={item.instanceId}
              isEditing={isEditing}
              onRemove={() => handleRemoveWidget(item.instanceId)}
              onConfigure={() => {/* Implementar configuração específica do widget */}}
              dragHandleClassName="drag-handle"
              onSettingsChange={(settings) => handleUpdateWidgetSettings(item.instanceId, settings)}
            />
          </div>
        ))}
      </ResponsiveGridLayout>

      {/* Mensagem quando não há widgets */}
      {visibleLayout.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 text-center border border-dashed rounded-lg">
          <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <Plus className="h-8 w-8 text-primary" />
          </div>
          <h3 className="text-lg font-medium mb-2">Seu dashboard está vazio</h3>
          <p className="text-muted-foreground mb-6 max-w-md">
            Adicione widgets para personalizar seu dashboard de acordo com suas necessidades.
          </p>
          <Button onClick={() => setIsWidgetStoreOpen(true)}>
            Adicionar Widgets
          </Button>
        </div>
      )}

      {/* Dialog de configurações */}
      <DashboardSettings
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        layout={layout}
        onToggleWidget={toggleWidgetVisibility}
        onResetLayout={resetLayout}
        compactType={compactType}
        setCompactType={setCompactType}
        preventCollision={preventCollision}
        setPreventCollision={setPreventCollision}
      />

      {/* Loja de widgets */}
      <WidgetStore
        isOpen={isWidgetStoreOpen}
        onClose={() => setIsWidgetStoreOpen(false)}
        onAddWidget={handleAddWidget}
        userPlan="premium" // Temporariamente definido como premium para demonstração
      />

      {/* Diálogos de gerenciamento de dashboards */}
      <CreateDashboardDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onConfirm={handleCreateDashboard}
        isLoading={createDashboardMutation.isPending}
      />

      <RenameDashboardDialog
        isOpen={isRenameDialogOpen}
        onClose={() => setIsRenameDialogOpen(false)}
        onConfirm={handleRenameDashboard}
        currentName={dashboards.find(d => d.id === activeDashboardId)?.name || ""}
        isLoading={renameDashboardMutation.isPending}
      />

      <DeleteDashboardDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDeleteDashboard}
        dashboardName={dashboards.find(d => d.id === activeDashboardId)?.name || ""}
        isDefault={dashboards.find(d => d.id === activeDashboardId)?.is_default || false}
        isLoading={deleteDashboardMutation.isPending}
      />
    </div>
  );
}

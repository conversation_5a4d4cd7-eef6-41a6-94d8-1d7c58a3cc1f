import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// Tipos
export interface TimerState {
  instanceId: string;
  isRunning: boolean;
  timeLeft: number;
  isComplete: boolean;
  soundEnabled: boolean;
  customDuration: number;
  notificationSent?: boolean;
  lastCompletedAt?: number;
}

export interface NoteState {
  content: string;
  lastEdited: number;
}

export interface Notification {
  id: string;
  type: 'timer' | 'note' | 'other';
  message: string;
  timestamp: number;
}

interface WidgetsState {
  activeTimers: Record<string, TimerState>;
  notes: Record<string, NoteState>;
  notifications: Notification[];
  isInDashboard: boolean;

  // Ações internas (não expostas diretamente)
  _runTimerTick: () => void;
  _startStopTimerInterval: () => void;

  // Ações públicas
  setIsInDashboard: (value: boolean) => void;
  updateTimerState: (instanceId: string, timerState: Partial<TimerState>) => void;
  removeTimerState: (instanceId: string) => void;
  updateNoteState: (noteId: string, noteState: NoteState) => void;
  removeNoteState: (noteId: string) => void;
  addNotification: (type: 'timer' | 'note' | 'other', message: string) => string;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
  clearTimerNotifications: () => void;
  cleanupCompletedTimers: () => void;
}

// Intervalo global para o timer
let timerInterval: any = null;

export const useWidgetsStore = create<WidgetsState>()(
  persist(
    (set, get) => ({
      // Estado inicial
      activeTimers: {},
      notes: {},
      notifications: [],
      isInDashboard: false,

      // Ação interna para executar o tick do timer
      _runTimerTick: () => {
        set((state) => {
          // Verificar se há timers ativos
          const hasActiveTimers = Object.values(state.activeTimers).some(
            timer => timer.isRunning && timer.timeLeft > 0
          );

          // Se não há timers ativos, não fazer nada
          if (!hasActiveTimers) return {};

          // Atualizar cada timer ativo
          const updatedTimers = { ...state.activeTimers };
          let hasChanges = false;
          const newNotifications = [...state.notifications];
          const now = Date.now();

          Object.entries(updatedTimers).forEach(([id, timer]) => {
            // Atualizar todos os timers ativos, independente de estar no dashboard ou não
            if (timer.isRunning && timer.timeLeft > 0) {
              // Decrementar o tempo restante
              updatedTimers[id] = {
                ...timer,
                timeLeft: timer.timeLeft - 1
              };
              hasChanges = true;

              // Log para debug
              console.log(`Atualizando timer ${id}: ${timer.timeLeft} -> ${timer.timeLeft - 1}`);

              // Verificar se o timer acabou de chegar a zero após o decremento
              if (updatedTimers[id].timeLeft === 0) {
                // Timer acabou de chegar a zero, marcar como completo
                updatedTimers[id] = {
                  ...updatedTimers[id],
                  isRunning: false,
                  isComplete: true,
                  lastCompletedAt: now,
                  // Resetar notificationSent para false inicialmente
                  notificationSent: false
                };

                // Adicionar notificação apenas se não estiver no dashboard
                if (!state.isInDashboard) {
                  newNotifications.push({
                    id: `timer-${now}`,
                    type: 'timer',
                    message: `Timer "${id}" finalizado: ${timer.customDuration} minutos`,
                    timestamp: now
                  });

                  // Marcar que a notificação foi enviada
                  updatedTimers[id].notificationSent = true;
                }
              }
            } else if (timer.isRunning && timer.timeLeft === 0 && !timer.notificationSent) {
              // Caso especial: timer já está em zero mas ainda não foi marcado como completo
              updatedTimers[id] = {
                ...timer,
                isRunning: false,
                isComplete: true,
                lastCompletedAt: now
              };
              hasChanges = true;

              // Adicionar notificação apenas se não estiver no dashboard
              if (!state.isInDashboard) {
                newNotifications.push({
                  id: `timer-${now}`,
                  type: 'timer',
                  message: `Timer "${id}" finalizado: ${timer.customDuration} minutos`,
                  timestamp: now
                });

                // Marcar que a notificação foi enviada
                updatedTimers[id].notificationSent = true;
              }
            }
          });

          // Retornar o novo estado apenas se houver mudanças
          return hasChanges ? {
            activeTimers: updatedTimers,
            notifications: newNotifications
          } : {};
        });

        // Verificar se o intervalo ainda é necessário
        get()._startStopTimerInterval();
      },

      // Ação interna para iniciar/parar o intervalo do timer
      _startStopTimerInterval: () => {
        const hasRunningTimers = Object.values(get().activeTimers).some(
          t => t.isRunning && t.timeLeft > 0
        );

        if (hasRunningTimers && !timerInterval) {
          timerInterval = setInterval(() => {
            useWidgetsStore.getState()._runTimerTick();
          }, 1000);
        } else if (!hasRunningTimers && timerInterval) {
          clearInterval(timerInterval);
          timerInterval = null;
        }
      },

      // Ações públicas
      setIsInDashboard: (value) => set({ isInDashboard: value }),

      updateTimerState: (instanceId, timerState) => {
        console.log(`updateTimerState chamado para ${instanceId}:`, timerState);

        set((state) => {
          // Verificar se o timer já existe
          const existingTimer = state.activeTimers[instanceId];

          // Se o timer não existir, criar um novo
          if (!existingTimer) {
            console.log(`Criando novo timer para ${instanceId}:`, timerState);
            return {
              activeTimers: {
                ...state.activeTimers,
                [instanceId]: {
                  instanceId,
                  isRunning: false,
                  timeLeft: 0,
                  isComplete: false,
                  soundEnabled: true,
                  customDuration: 25,
                  notificationSent: false,
                  ...timerState
                } as TimerState
              }
            };
          }

          // Verificar se há mudanças reais
          let hasChanges = false;
          const newTimer = { ...existingTimer };

          // Verificar cada propriedade
          if (timerState.isRunning !== undefined && existingTimer.isRunning !== timerState.isRunning) {
            hasChanges = true;
            newTimer.isRunning = timerState.isRunning;

            // Se o timer estiver sendo reiniciado, resetar a flag de notificação
            if (timerState.isRunning && existingTimer.isComplete) {
              newTimer.isComplete = false;
              newTimer.notificationSent = false;
              newTimer.lastCompletedAt = undefined;
            }
          }

          if (timerState.timeLeft !== undefined && existingTimer.timeLeft !== timerState.timeLeft) {
            hasChanges = true;
            newTimer.timeLeft = timerState.timeLeft;

            // Se o tempo foi alterado para um valor maior que zero e o timer estava completo,
            // resetar o estado de completo e a flag de notificação
            if (timerState.timeLeft > 0 && existingTimer.isComplete) {
              newTimer.isComplete = false;
              newTimer.notificationSent = false;
              newTimer.lastCompletedAt = undefined;
            }
          }

          if (timerState.isComplete !== undefined && existingTimer.isComplete !== timerState.isComplete) {
            hasChanges = true;
            newTimer.isComplete = timerState.isComplete;

            // Se o timer acabou de ser completado, registrar o timestamp
            if (timerState.isComplete && !existingTimer.isComplete) {
              newTimer.lastCompletedAt = Date.now();
              newTimer.notificationSent = true; // Marcar que a notificação já foi enviada
            }
          }

          if (timerState.soundEnabled !== undefined && existingTimer.soundEnabled !== timerState.soundEnabled) {
            hasChanges = true;
            newTimer.soundEnabled = timerState.soundEnabled;
          }

          if (timerState.customDuration !== undefined && existingTimer.customDuration !== timerState.customDuration) {
            hasChanges = true;
            newTimer.customDuration = timerState.customDuration;
          }

          // Atualizar explicitamente a flag de notificação se fornecida
          if (timerState.notificationSent !== undefined && existingTimer.notificationSent !== timerState.notificationSent) {
            hasChanges = true;
            newTimer.notificationSent = timerState.notificationSent;
          }

          // Se não houver mudanças, retornar o estado atual
          if (!hasChanges) {
            console.log(`Nenhuma mudança detectada para o timer ${instanceId}`);
            return {};
          }

          console.log(`Atualizando timer ${instanceId} com novas informações:`, newTimer);

          // Retornar o novo estado
          return {
            activeTimers: {
              ...state.activeTimers,
              [instanceId]: newTimer
            }
          };
        });

        // Iniciar/parar o intervalo do timer se necessário
        get()._startStopTimerInterval();
      },

      removeTimerState: (instanceId) => {
        set((state) => {
          const newActiveTimers = { ...state.activeTimers };
          delete newActiveTimers[instanceId];

          return {
            activeTimers: newActiveTimers
          };
        });

        // Verificar se o intervalo ainda é necessário
        get()._startStopTimerInterval();
      },

      updateNoteState: (noteId, noteState) => set((state) => ({
        notes: {
          ...state.notes,
          [noteId]: noteState
        }
      })),

      removeNoteState: (noteId) => set((state) => {
        const newNotes = { ...state.notes };
        delete newNotes[noteId];

        return {
          notes: newNotes
        };
      }),

      addNotification: (type, message) => {
        const id = `${type}-${Date.now()}`;
        set((state) => ({
          notifications: [
            ...state.notifications,
            {
              id,
              type,
              message,
              timestamp: Date.now()
            }
          ]
        }));

        return id;
      },

      removeNotification: (id) => set((state) => ({
        notifications: state.notifications.filter(notification => notification.id !== id)
      })),

      clearAllNotifications: () => set({ notifications: [] }),

      clearTimerNotifications: () => set((state) => ({
        notifications: state.notifications.filter(notification => notification.type !== 'timer')
      })),

      cleanupCompletedTimers: () => set((state) => {
        const cleanedTimers = { ...state.activeTimers };
        let hasChanges = false;

        Object.entries(cleanedTimers).forEach(([id, timer]) => {
          // Remover timers completados
          if (timer.isComplete) {
            delete cleanedTimers[id];
            hasChanges = true;
          }
        });

        if (!hasChanges) return {};

        return {
          activeTimers: cleanedTimers
        };
      })
    }),
    {
      name: 'widgets-state',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        activeTimers: state.activeTimers,
        notes: state.notes
      }),
      onRehydrateStorage: () => (state) => {
        // Limpar timers antigos aqui
        if (state) {
          const now = Date.now();
          const cleanedTimers = { ...state.activeTimers };
          let changed = false;

          Object.entries(cleanedTimers).forEach(([id, timer]) => {
            // Remover timers completados há mais de 1 hora
            if (timer.isComplete && timer.lastCompletedAt &&
                now - timer.lastCompletedAt > 60 * 60 * 1000) {
              delete cleanedTimers[id];
              changed = true;
            }
          });

          if (changed) {
            state.activeTimers = cleanedTimers;
          }

          // Reiniciar o intervalo se necessário após reidratação
          // Usar setTimeout para garantir que o estado foi completamente reidratado
          setTimeout(() => {
            if (typeof state._startStopTimerInterval === 'function') {
              state._startStopTimerInterval();
            }
          }, 100);
        }
      },
    }
  )
);

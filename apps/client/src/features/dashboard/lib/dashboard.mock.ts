import { axiosInstance } from "@/shared/lib/api.client";
// Importar tipos necessários
import { Dashboard, DashboardItem, JsonValue, CreateDashboardPayload, UpdateDashboardPayload } from '@/features/dashboard/types';
import { AxiosRequestConfig } from 'axios';

// Layout padrão para NOVOS dashboards criados no mock
const newDashboardMockDefaultLayout: DashboardItem[] = [
    { i: 'next-appointment-mock-1', x: 0, y: 0, w: 2, h: 2, widgetId: 'next-appointment', instanceId: 'next-appointment-mock-1', visible: true },
    { i: 'pending-notes-mock-1', x: 0, y: 2, w: 2, h: 3, widgetId: 'pending-notes', instanceId: 'pending-notes-mock-1', visible: true },
    { i: 'quick-actions-mock-1', x: 2, y: 0, w: 2, h: 2, widgetId: 'quick-actions', instanceId: 'quick-actions-mock-1', visible: true },
    { i: 'upcoming-appointments-mock-1', x: 2, y: 2, w: 2, h: 3, widgetId: 'upcoming-appointments', instanceId: 'upcoming-appointments-mock-1', visible: true },
];

// Função para gerar UUIDs mockados
const generateMockUUID = () => `mock-uuid-${Math.random().toString(36).substring(2, 15)}`;

// Armazenamento em memória para os dados mockados
const mockStorage = {
  // Agora armazena uma lista de dashboards
  dashboards: [] as Dashboard[],
  widgetSettings: {} as Record<string, Record<string, unknown>>, // Usar unknown
  widgetData: {} as Record<string, Record<string, unknown>>, // Usar unknown
};

// Função para carregar/inicializar dados mockados
const loadInitialData = () => {
  try {
    // Tentar carregar dashboards salvos
    const savedDashboards = localStorage.getItem('mockDashboards');
    if (savedDashboards) {
      mockStorage.dashboards = JSON.parse(savedDashboards);
    } else {
      // Se não houver dashboards salvos, criar um padrão
      const defaultLayout: DashboardItem[] = [
          { i: 'next-appointment-1', x: 0, y: 0, w: 2, h: 2, widgetId: 'next-appointment', instanceId: 'next-appointment-1', visible: true },
          { i: 'pending-notes-1', x: 0, y: 2, w: 2, h: 3, widgetId: 'pending-notes', instanceId: 'pending-notes-1', visible: true },
          { i: 'quick-actions-1', x: 2, y: 0, w: 2, h: 2, widgetId: 'quick-actions', instanceId: 'quick-actions-1', visible: true },
          { i: 'upcoming-appointments-1', x: 2, y: 2, w: 2, h: 3, widgetId: 'upcoming-appointments', instanceId: 'upcoming-appointments-1', visible: true },
      ];
      const defaultDashboard: Dashboard = {
        id: generateMockUUID(),
        user_id: 'mock-user-1',
        name: "Meu Primeiro Dashboard",
        layout: defaultLayout as unknown as JsonValue, // Cast explícito
        is_default: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      mockStorage.dashboards.push(defaultDashboard);
      localStorage.setItem('mockDashboards', JSON.stringify(mockStorage.dashboards));
    }

    // Carregar configurações de widgets (mantido)
    const savedSettings = localStorage.getItem('mockWidgetSettings'); // Nome diferente para evitar conflito
    if (savedSettings) {
      mockStorage.widgetSettings = JSON.parse(savedSettings);
    }

    // Carregar dados específicos de widgets (mantido)
    Object.keys(localStorage).forEach(key => {
        if (key.startsWith('widget_data_')) {
            const [, , widgetId, instanceId] = key.split('_');
            if (!mockStorage.widgetData[widgetId]) {
                mockStorage.widgetData[widgetId] = {};
            }
            mockStorage.widgetData[widgetId][instanceId] = JSON.parse(localStorage.getItem(key) || '{}');
        }
    });
  } catch (error) {
    console.error('Erro ao carregar dados mockados do localStorage:', error);
  }
};

// Configurar interceptors para simular API
export function setupDashboardMockApi() {
  // Carregar dados iniciais
  loadInitialData();

  // Interceptar respostas
  axiosInstance.interceptors.response.use(
    response => response,
    async (error) => {
      const { config, response } = error;

      // Se for um erro real da API (não um erro de rede), continuar com o erro
      if (response) return Promise.reject(error);

      // Simular latência de rede (entre 200-500ms)
      await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 200));

      // Processar requisições mockadas
      return handleMockRequest(config);
    }
  );
}

// Função para lidar com requisições mockadas
async function handleMockRequest(config: AxiosRequestConfig): Promise<any> { // Retorno any aqui é aceitável para mock
  const { method, url } = config;

  if (!url) return Promise.reject(new Error('URL não definida'));

  // --- Novas Rotas Mockadas para Múltiplos Dashboards ---

  // GET /protected/dashboards (Listar)
  if (method?.toLowerCase() === 'get' && url === '/protected/dashboards') {
    return createSuccessResponse(mockStorage.dashboards);
  }

  // GET /protected/dashboards/default (Padrão)
  if (method?.toLowerCase() === 'get' && url === '/protected/dashboards/default') {
    const defaultDashboard = mockStorage.dashboards.find(d => d.is_default) || mockStorage.dashboards[0];
    if (defaultDashboard) {
      return createSuccessResponse(defaultDashboard);
    } else {
      return createErrorResponse(404, 'Nenhum dashboard padrão encontrado');
    }
  }

  // GET /protected/dashboards/{id} (Buscar por ID)
  const getMatch = url.match(/\/protected\/dashboards\/([^/]+)$/);
  if (method?.toLowerCase() === 'get' && getMatch) {
    const id = getMatch[1];
    const dashboard = mockStorage.dashboards.find(d => d.id === id);
    if (dashboard) {
      return createSuccessResponse(dashboard);
    } else {
      return createErrorResponse(404, 'Dashboard não encontrado');
    }
  }

  // POST /protected/dashboards (Criar)
  if (method?.toLowerCase() === 'post' && url === '/protected/dashboards') {
    const payload: CreateDashboardPayload = JSON.parse(config.data);
    const newDashboard: Dashboard = {
      id: generateMockUUID(),
      user_id: 'mock-user-1',
      name: payload.name,
      layout: payload.layout || newDashboardMockDefaultLayout as unknown as JsonValue,
      is_default: payload.is_default ?? false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    if (newDashboard.is_default) {
      mockStorage.dashboards.forEach(d => d.is_default = false);
    }
    mockStorage.dashboards.push(newDashboard);
    localStorage.setItem('mockDashboards', JSON.stringify(mockStorage.dashboards));
    return createSuccessResponse(newDashboard);
  }

  // PUT /protected/dashboards/{id} (Atualizar)
  const updateMatch = url.match(/\/protected\/dashboards\/([^/]+)$/);
  if (method?.toLowerCase() === 'put' && updateMatch) {
    const id = updateMatch[1];
    const payload: UpdateDashboardPayload = JSON.parse(config.data);
    const dashboardIndex = mockStorage.dashboards.findIndex(d => d.id === id);
    if (dashboardIndex !== -1) {
      if (payload.is_default === true) {
         mockStorage.dashboards.forEach(d => { if(d.id !== id) d.is_default = false; });
      }
      mockStorage.dashboards[dashboardIndex] = {
        ...mockStorage.dashboards[dashboardIndex],
        ...(payload.name && { name: payload.name }),
        ...(payload.layout && { layout: payload.layout }),
        ...(payload.is_default !== undefined && { is_default: payload.is_default }),
        updated_at: new Date().toISOString(),
      };
      localStorage.setItem('mockDashboards', JSON.stringify(mockStorage.dashboards));
      return createSuccessResponse(mockStorage.dashboards[dashboardIndex]);
    } else {
      return createErrorResponse(404, 'Dashboard não encontrado para atualizar');
    }
  }

   // DELETE /protected/dashboards/{id} (Deletar)
   const deleteMatch = url.match(/\/protected\/dashboards\/([^/]+)$/);
   if (method?.toLowerCase() === 'delete' && deleteMatch) {
     const id = deleteMatch[1];
     const dashboardIndex = mockStorage.dashboards.findIndex(d => d.id === id);
     if (dashboardIndex !== -1) {
       if (mockStorage.dashboards[dashboardIndex].is_default) {
         return createErrorResponse(400, 'Não é possível deletar o dashboard padrão.');
       }
       if (mockStorage.dashboards.length <= 1) {
          return createErrorResponse(400, 'Não é possível deletar o último dashboard.');
       }
       mockStorage.dashboards.splice(dashboardIndex, 1);
       localStorage.setItem('mockDashboards', JSON.stringify(mockStorage.dashboards));
       return createSuccessResponse({});
     } else {
       return createErrorResponse(404, 'Dashboard não encontrado para deletar');
     }
   }

   // POST /protected/dashboards/{id}/set-default (Definir Padrão)
   const setDefaultMatch = url.match(/\/protected\/dashboards\/([^/]+)\/set-default$/);
   if (method?.toLowerCase() === 'post' && setDefaultMatch) {
     const id = setDefaultMatch[1];
     let updatedDashboard: Dashboard | null = null;
     mockStorage.dashboards.forEach(d => {
       d.is_default = (d.id === id);
       if (d.id === id) {
         d.updated_at = new Date().toISOString();
         updatedDashboard = d;
       }
     });
     if (updatedDashboard) {
        localStorage.setItem('mockDashboards', JSON.stringify(mockStorage.dashboards));
        return createSuccessResponse(updatedDashboard);
     } else {
        return createErrorResponse(404, 'Dashboard não encontrado para definir como padrão');
     }
   }


  // --- Rotas Mockadas para Widgets (Mantidas) ---
  const settingsMatch = url.match(/\/protected\/dashboard\/widget\/(.+)\/settings/);
  if (settingsMatch) {
    const instanceId = settingsMatch[1];

    // GET /protected/dashboard/widget/:instanceId/settings
    if (method?.toLowerCase() === 'get') {
      return createSuccessResponse(mockStorage.widgetSettings[instanceId] || {});
    }

    // PUT /protected/dashboard/widget/:instanceId/settings
    if (method?.toLowerCase() === 'put') {
      const newSettings = JSON.parse(config.data);
      mockStorage.widgetSettings[instanceId] = newSettings;
      localStorage.setItem('mockWidgetSettings', JSON.stringify(mockStorage.widgetSettings));
      return createSuccessResponse(newSettings);
    }
  }

  // Dados de widgets
  const dataMatch = url.match(/\/protected\/dashboard\/widget\/(.+)\/(.+)\/data/);
  if (dataMatch) {
    const widgetId = dataMatch[1];
    const instanceId = dataMatch[2];
    const storageKey = `widget_data_${widgetId}_${instanceId}`;

    if (!mockStorage.widgetData[widgetId]) {
      mockStorage.widgetData[widgetId] = {};
    }

    // GET /protected/dashboard/widget/:widgetId/:instanceId/data
    if (method?.toLowerCase() === 'get') {
      return createSuccessResponse(mockStorage.widgetData[widgetId][instanceId] || {});
    }

    // PUT /protected/dashboard/widget/:widgetId/:instanceId/data
    if (method?.toLowerCase() === 'put') {
      const newData = JSON.parse(config.data);
      mockStorage.widgetData[widgetId][instanceId] = newData;
      localStorage.setItem(storageKey, JSON.stringify(newData));
      return createSuccessResponse(newData);
    }
  }

  // Se não for uma rota mockada, retornar erro 404
  return createErrorResponse(404, 'Rota não encontrada');
}

// Função para criar resposta de sucesso
function createSuccessResponse(data: unknown) {
  return {
    data,
    status: 200,
    statusText: 'OK',
    headers: {},
    config: {},
  };
}

// Função para criar resposta de erro
function createErrorResponse(status: number, message: string): Promise<never> {
  return Promise.reject({
    response: {
      data: { message },
      status,
      statusText: status === 404 ? 'Not Found' : 'Error',
      headers: {},
      config: {},
    },
  });
}

// Estado global para controle de criação de dashboard padrão
import { queryClient } from '@/app/router';

// Chave para o estado de criação do dashboard padrão
export const DASHBOARD_STATE_KEYS = {
  isCreatingDefault: ['dashboard', 'state', 'isCreatingDefault'],
};

// Funções para gerenciar o estado de criação do dashboard padrão
export const dashboardState = {
  // Verifica se já está criando um dashboard padrão
  isCreatingDefaultDashboard: (): boolean => {
    return queryClient.getQueryData<boolean>(DASHBOARD_STATE_KEYS.isCreatingDefault) || false;
  },

  // Define que está criando um dashboard padrão
  setCreatingDefaultDashboard: (isCreating: boolean): void => {
    queryClient.setQueryData(DASHBOARD_STATE_KEYS.isCreatingDefault, isCreating);
  },
};

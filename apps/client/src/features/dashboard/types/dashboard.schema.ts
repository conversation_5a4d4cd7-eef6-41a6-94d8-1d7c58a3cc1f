import { z } from 'zod';

const patientMetricsSchema = z.object({
  total: z.number(),
  new_this_month: z.number()
});

const appointmentItemSchema = z.object({
  id: z.string().uuid(),
  patient_id: z.string().uuid(),
  patient_name: z.string(),
  title: z.string(),
  start_time: z.string(),
  end_time: z.string()
});

const appointmentSummarySchema = z.object({
  today_count: z.number(),
  upcoming_count: z.number(),
  today_progress: z.number(),
  upcoming_list: z.array(appointmentItemSchema)
});

const recentPatientSchema = z.object({
  id: z.string().uuid(),
  full_name: z.string(),
  email: z.string().email().nullable(),
  phone: z.string().nullable(),
});

const patientSummarySchema = z.object({
  total: z.number(),
  new_this_month: z.number(),
  recent_list: z.array(recentPatientSchema),
});

const noteItemSchema = z.object({
  id: z.string().uuid(),
  content: z.string(),
  updated_at: z.string(),
});

const notesSummarySchema = z.object({
  notes_list: z.array(noteItemSchema),
});

export const dashboardSummarySchema = z.object({
  patients: patientSummarySchema,
  appointments: appointmentSummarySchema,
  notes: notesSummarySchema,
  pending_session_notes: z.array(appointmentItemSchema)
});

export type PatientMetrics = z.infer<typeof patientMetricsSchema>;
export type AppointmentItem = z.infer<typeof appointmentItemSchema>;
export type AppointmentsSummary = z.infer<typeof appointmentSummarySchema>;
export type DashboardSummary = z.infer<typeof dashboardSummarySchema>;
export type RecentPatient = z.infer<typeof recentPatientSchema>;
export type PatientSummary = z.infer<typeof patientSummarySchema>;
export type NoteItem = z.infer<typeof noteItemSchema>;
export type NotesSummary = z.infer<typeof notesSummarySchema>;
import { Layout } from 'react-grid-layout';
import { LucideIcon } from 'lucide-react';

// Tipos de planos disponíveis
export type PlanType = 'free' | 'basic' | 'premium' | 'enterprise';

// Categorias de widgets
export type WidgetCategory =
  | 'statistics'
  | 'calendar'
  | 'patients'
  | 'tools'
  | 'ai'
  | 'integrations'
  | 'financial';

// Interface para os metadados de um widget
export interface WidgetDefinition {
  id: string;
  title: string;
  description: string;
  icon: LucideIcon;
  category: WidgetCategory;
  requiredPlan: PlanType;
  defaultSize: { w: number; h: number };
  minSize?: { w: number; h: number };
  maxSize?: { w: number; h: number };
  isNew?: boolean;
  isComingSoon?: boolean;
  tags?: string[];
}

// Interface para um item de layout do dashboard
export interface DashboardItem extends Layout {
  widgetId: string;
  instanceId: string; // Para permitir múltiplas instâncias do mesmo widget
  visible: boolean;
  settings?: Record<string, unknown>; // Configurações específicas da instância (any -> unknown)
}

// Interface para o estado do dashboard
export interface DashboardState {
  layout: DashboardItem[];
  lastModified: string;
}

// Tipo genérico para valores JSON
export type JsonValue =
  | string
  | number
  | boolean
  | null
  | { [key: string]: JsonValue }
  | JsonValue[];

// Interface para representar um Dashboard como vem do Backend
export interface Dashboard {
  id: string;
  user_id: string;
  name: string;
  layout: JsonValue; // O layout em si é JSON
  is_default: boolean;
  created_at: string; // Ou Date, dependendo da deserialização
  updated_at: string; // Ou Date
}

// Interface para as configurações do widget
export interface WidgetSettings<T = Record<string, unknown>> { // (any -> unknown)
  instanceId: string;
  settings: T;
}

// Propriedades comuns para todos os widgets
export interface BaseWidgetProps {
  instanceId: string;
  isEditing?: boolean;
  onRemove?: () => void;
  onConfigure?: () => void;
  dragHandleClassName?: string;
  settings?: Record<string, unknown>; // (any -> unknown)
  onSettingsChange?: (settings: Record<string, unknown>) => void; // (any -> unknown)
}

// --- Payloads para API de Múltiplos Dashboards ---

export interface CreateDashboardPayload {
  name: string;
  layout?: JsonValue;
  is_default?: boolean;
}

export interface UpdateDashboardPayload {
  name?: string;
  layout?: JsonValue;
  is_default?: boolean;
}

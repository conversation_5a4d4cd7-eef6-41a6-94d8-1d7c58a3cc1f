import {
  Users,
  Calendar,
  Clock,
  DollarSign,
  FileText,
  MessageSquare,
  CloudSun,
  Brain,
  BarChart3,
  Stethoscope,
  Sparkles,
  NotebookPen,
  Timer,
  Zap,
  Bell,
  Lightbulb,
  Mic,
  Gauge,
  Clipboard,
  Layers,
  FileText as FileTextIcon,
  AlertCircle,
  Hand
} from 'lucide-react';

import { WidgetDefinition, WidgetCategory, PlanType } from './types';

// Função para registrar um widget
function registerWidget(widget: WidgetDefinition): WidgetDefinition {
  return widget;
}

// Registro de widgets disponíveis
export const availableWidgets: WidgetDefinition[] = [
  // Widget de boas vindas
  registerWidget({
    id: 'welcome',
    title: 'Boas Vindas',
    description: 'Widget de boas vindas personalizado com dicas e ações rápidas',
    icon: Hand,
    category: 'tools',
    requiredPlan: 'free',
    defaultSize: { w: 2, h: 2 },
    isNew: true,
  }),

  // Widgets de estatísticas
  registerWidget({
    id: 'patientStats',
    title: 'Estatísticas de Pacientes',
    description: 'Mostra o total de pacientes e novos pacientes do mês',
    icon: Users,
    category: 'statistics',
    requiredPlan: 'free',
    defaultSize: { w: 1, h: 1 },
  }),

  registerWidget({
    id: 'appointmentsToday',
    title: 'Consultas de Hoje',
    description: 'Exibe o número de consultas agendadas para hoje',
    icon: Calendar,
    category: 'statistics',
    requiredPlan: 'free',
    defaultSize: { w: 1, h: 1 },
  }),

  registerWidget({
    id: 'upcomingAppointments',
    title: 'Próximos Atendimentos',
    description: 'Lista das próximas consultas agendadas',
    icon: Clock,
    category: 'calendar',
    requiredPlan: 'free',
    defaultSize: { w: 3, h: 2 },
  }),

  // Widgets de calendário
  registerWidget({
    id: 'calendar',
    title: 'Calendário',
    description: 'Calendário mensal com visão geral dos agendamentos',
    icon: Calendar,
    category: 'calendar',
    requiredPlan: 'free',
    defaultSize: { w: 1, h: 2 },
  }),

  // Novos widgets de foco do dia
  registerWidget({
    id: 'nextAppointment',
    title: 'Próximo Atendimento',
    description: 'Mostra detalhes do próximo paciente a ser atendido',
    icon: Clock,
    category: 'calendar',
    requiredPlan: 'free',
    defaultSize: { w: 2, h: 1 },
    isNew: true,
  }),

  registerWidget({
    id: 'pendingNotes',
    title: 'Notas Pendentes',
    description: 'Lista consultas realizadas que precisam de evolução',
    icon: AlertCircle,
    category: 'tools',
    requiredPlan: 'free',
    defaultSize: { w: 2, h: 2 },
    isNew: true,
  }),

  // Widgets de pacientes
  registerWidget({
    id: 'recentPatients',
    title: 'Pacientes Recentes',
    description: 'Lista dos pacientes vistos recentemente',
    icon: Users,
    category: 'patients',
    requiredPlan: 'free',
    defaultSize: { w: 2, h: 1 },
  }),

  // Widgets de ferramentas
  registerWidget({
    id: 'quickActions',
    title: 'Ações Rápidas',
    description: 'Botões de acesso rápido para ações comuns',
    icon: Zap,
    category: 'tools',
    requiredPlan: 'free',
    defaultSize: { w: 1, h: 1 },
  }),

  registerWidget({
    id: 'notes',
    title: 'Notas Rápidas',
    description: 'Área para anotações rápidas e lembretes',
    icon: NotebookPen,
    category: 'tools',
    requiredPlan: 'free',
    defaultSize: { w: 2, h: 1 },
  }),

  registerWidget({
    id: 'timer',
    title: 'Cronômetro de Sessão',
    description: 'Cronômetro para controlar o tempo das sessões',
    icon: Timer,
    category: 'tools',
    requiredPlan: 'free',
    defaultSize: { w: 1, h: 2 },
    minSize: { w: 1, h: 1 },
    isNew: true,
  }),

  registerWidget({
    id: 'models',
    title: 'Modelos',
    description: 'Acesso rápido aos seus modelos favoritos e recentes',
    icon: FileTextIcon,
    category: 'tools',
    requiredPlan: 'free',
    defaultSize: { w: 2, h: 2 },
    minSize: { w: 2, h: 1 },
  }),

  registerWidget({
    id: 'notifications',
    title: 'Notificações',
    description: 'Visualize suas notificações recentes',
    icon: Bell,
    category: 'tools',
    requiredPlan: 'free',
    defaultSize: { w: 1, h: 2 },
  }),

  registerWidget({
    id: 'clinicalResources',
    title: 'Recursos Clínicos',
    description: 'Acesse recursos e materiais para terapia',
    icon: Layers,
    category: 'tools',
    requiredPlan: 'basic',
    defaultSize: { w: 1, h: 1 },
    isComingSoon: true,
  }),

  registerWidget({
    id: 'treatmentPlans',
    title: 'Planos de Tratamento',
    description: 'Visualize e gerencie planos de tratamento ativos',
    icon: Clipboard,
    category: 'patients',
    requiredPlan: 'basic',
    defaultSize: { w: 2, h: 1 },
    isComingSoon: true,
  }),

  // Widgets financeiros
  registerWidget({
    id: 'financialSummary',
    title: 'Resumo Financeiro',
    description: 'Resumo das informações financeiras',
    icon: DollarSign,
    category: 'financial',
    requiredPlan: 'basic',
    defaultSize: { w: 1, h: 1 },
  }),







  // Widgets de IA
  registerWidget({
    id: 'aiAssistant',
    title: 'Assistente IA',
    description: 'Assistente de IA para ajudar com tarefas comuns',
    icon: Brain,
    category: 'ai',
    requiredPlan: 'premium',
    defaultSize: { w: 2, h: 2 },
    isComingSoon: true,
    isNew: true,
  }),





  registerWidget({
    id: 'patientInsights',
    title: 'Insights de Pacientes',
    description: 'Análises e tendências sobre seus pacientes',
    icon: Lightbulb,
    category: 'ai',
    requiredPlan: 'premium',
    defaultSize: { w: 2, h: 1 },
    isComingSoon: true,
  }),

  // Widgets de integrações
  registerWidget({
    id: 'weather',
    title: 'Previsão do Tempo',
    description: 'Mostra a previsão do tempo para sua localização',
    icon: CloudSun,
    category: 'integrations',
    requiredPlan: 'free',
    defaultSize: { w: 1, h: 1 },
  }),
];

// Informações das categorias de widgets
export const widgetCategories = [
  {
    id: 'statistics' as WidgetCategory,
    label: 'Estatísticas',
    description: 'Widgets com dados e métricas importantes'
  },
  {
    id: 'calendar' as WidgetCategory,
    label: 'Calendário',
    description: 'Widgets relacionados a agendamentos e tempo'
  },
  {
    id: 'patients' as WidgetCategory,
    label: 'Pacientes',
    description: 'Widgets para gerenciamento e visualização de pacientes'
  },
  {
    id: 'tools' as WidgetCategory,
    label: 'Ferramentas',
    description: 'Ferramentas úteis para o dia a dia'
  },
  {
    id: 'ai' as WidgetCategory,
    label: 'Inteligência Artificial',
    description: 'Widgets com recursos de IA para otimizar seu trabalho'
  },
  {
    id: 'financial' as WidgetCategory,
    label: 'Financeiro',
    description: 'Widgets para controle financeiro e faturamento'
  },
  {
    id: 'integrations' as WidgetCategory,
    label: 'Integrações',
    description: 'Widgets que se integram com serviços externos'
  },
];

// Função para filtrar widgets por categoria
export function getWidgetsByCategory(category: WidgetCategory): WidgetDefinition[] {
  return availableWidgets.filter(widget => widget.category === category);
}

// Função para buscar widget por ID
export function getWidgetById(id: string): WidgetDefinition | undefined {
  return availableWidgets.find(widget => widget.id === id);
}

// Função para verificar se um widget está disponível para um plano
export function isWidgetAvailableForPlan(widget: WidgetDefinition, userPlan: PlanType): boolean {
  const planHierarchy: Record<PlanType, number> = {
    free: 0,
    basic: 1,
    premium: 2,
    enterprise: 3,
  };

  return planHierarchy[userPlan] >= planHierarchy[widget.requiredPlan];
}

import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/shared/hooks/use-toast';
import api from '@/shared/lib/api.client';

async function getAIResponse(prompt: string): Promise<{ response: string }> {
  // This is a hypothetical endpoint.
  // In a real application, this would call our backend, which then calls the AI provider.
  const { data } = await api.post('/protected/ai/assistant', { prompt });
  return data;
}

export function useAIAssistant() {
  const { toast } = useToast();

  const mutation = useMutation({
    mutationFn: getAIResponse,
    onError: (error) => {
      toast({
        title: 'Erro no Assistente de IA',
        description: 'Não foi possível obter uma resposta do assistente. Tente novamente mais tarde.',
        variant: 'destructive',
      });
      console.error(error);
    },
  });

  return {
    sendMessage: mutation.mutate,
    isPending: mutation.isPending,
    isError: mutation.isError,
    data: mutation.data,
  };
} 
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// Corrigir importações da API
import { dashboardApi, DASHBOARD_KEYS } from '@/features/dashboard/api/dashboard.api';
import { useToast } from '@/shared/hooks/use-toast';

export function useWidgetDataQuery<T>(widgetId: string, instanceId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  const storageKey = `widget_data_${widgetId}_${instanceId}`;
  
  // Query para buscar dados do widget
  const {
    data, // Usar 'data' como nome da variável desestruturada
    isLoading,
    error
  } = useQuery({
    queryKey: DASHBOARD_KEYS.widgetData(widgetId, instanceId),
    queryFn: () => dashboardApi.widgetData.get<T>(widgetId, instanceId), // Usar dashboardApi
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 60, // cacheTime renomeado para gcTime
    // onError removido
    initialData: () => { // Manter initialData para carregamento inicial rápido
      try {
        const savedData = localStorage.getItem(storageKey);
        if (savedData) {
          return JSON.parse(savedData) as T;
        }
      } catch (e) {
        console.error('Erro ao ler dados iniciais do localStorage:', e);
      }
      return undefined; // Retorna undefined se não encontrar no localStorage
    },
    retry: false, // Evitar retentativas automáticas
  });

   // Lógica de fallback para localStorage SE a query falhar
   const finalData = error ? (() => {
       console.warn(`Query de dados falhou para ${widgetId}/${instanceId}, tentando fallback do localStorage.`);
       try {
           const savedData = localStorage.getItem(storageKey);
           if (savedData) {
               return JSON.parse(savedData) as T;
           }
       } catch (e) {
           console.error('Erro ao ler fallback do localStorage:', e);
       }
       return undefined; // Retorna undefined se fallback falhar
   })() : data;


  // Mutation para atualizar dados do widget
  const updateDataMutation = useMutation({
    mutationFn: (newData: T) => dashboardApi.widgetData.update(widgetId, instanceId, newData), // Usar dashboardApi
    onMutate: async (newData) => {
      const queryKey = DASHBOARD_KEYS.widgetData(widgetId, instanceId); // Usar DASHBOARD_KEYS
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey });

      // Salvar estado anterior
      const previousData = queryClient.getQueryData(queryKey);

      // Atualizar cache otimisticamente
      queryClient.setQueryData(queryKey, newData);

      // Salvar no localStorage como fallback
      try {
        localStorage.setItem(storageKey, JSON.stringify(newData));
      } catch (e) {
        console.error('Erro ao salvar no localStorage:', e);
      }

      return { previousData, queryKey }; // Retornar queryKey no contexto
    },
    onError: (err, newData, context) => {
      // Reverter para estado anterior em caso de erro
      if (context?.previousData) {
        queryClient.setQueryData(context.queryKey, context.previousData);
      }

      toast({
        title: 'Erro ao salvar dados',
        description: `Não foi possível salvar os dados do widget: ${err instanceof Error ? err.message : 'Erro desconhecido'}`,
        variant: 'destructive',
      });
    },
     onSuccess: () => {
       // Toast de sucesso opcional
       // toast({ title: "Dados do widget salvos", variant: "default" });
    },
    onSettled: (data, error, variables, context) => {
      // Revalidar dados
       if (context?.queryKey) {
        queryClient.invalidateQueries({ queryKey: context.queryKey });
      }
    },
  });

  // Função para atualizar dados
  const updateData = (newData: T) => {
    updateDataMutation.mutate(newData);
  };

  return {
    data: finalData, // Usar finalData que inclui o fallback
    isLoading,
    isError: !!error, // Mantém isError para indicar falha da query original
    updateData,
    isUpdating: updateDataMutation.isPending
  };
}

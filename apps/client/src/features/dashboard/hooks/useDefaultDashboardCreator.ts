import { useEffect, useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { dashboardApi, DASHBOARD_KEYS } from '@/features/dashboard/api/dashboard.api';
import { JsonValue } from '@/features/dashboard/types';
import { useToast } from '@/shared/hooks/use-toast';

// Layout padrão para o dashboard padrão
const defaultDashboardLayout = [
  // Widgets de foco do dia na primeira linha
  { i: 'nextAppointment-1', x: 0, y: 0, w: 2, h: 1, widgetId: 'nextAppointment', instanceId: 'nextAppointment-1', visible: true },
  { i: 'pendingNotes-1', x: 2, y: 0, w: 2, h: 2, widgetId: 'pendingNotes', instanceId: 'pendingNotes-1', visible: true },
  { i: 'quickActions-1', x: 4, y: 0, w: 1, h: 1, widgetId: 'quickActions', instanceId: 'quickActions-1', visible: true },
  // Widgets na segunda linha
  { i: 'calendar-1', x: 0, y: 1, w: 2, h: 2, widgetId: 'calendar', instanceId: 'calendar-1', visible: true },
  // Terceira linha
  { i: 'upcomingAppointments-1', x: 0, y: 3, w: 3, h: 2, widgetId: 'upcomingAppointments', instanceId: 'upcomingAppointments-1', visible: true },
  { i: 'patientStats-1', x: 3, y: 3, w: 1, h: 1, widgetId: 'patientStats', instanceId: 'patientStats-1', visible: true },
  { i: 'appointmentsToday-1', x: 4, y: 3, w: 1, h: 1, widgetId: 'appointmentsToday', instanceId: 'appointmentsToday-1', visible: true },
];

// Chave para o localStorage
const DASHBOARD_CREATION_LOCK = 'dashboard_creation_lock';
const DASHBOARD_CREATION_TIMESTAMP = 'dashboard_creation_timestamp';

/**
 * Hook para gerenciar a criação do dashboard padrão.
 * Este hook deve ser usado apenas uma vez na aplicação, no nível mais alto possível.
 */
export function useDefaultDashboardCreator() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [isCreating, setIsCreating] = useState(false);

  // Mutação para criar o dashboard padrão
  const createDefaultDashboardMutation = useMutation({
    mutationFn: async () => {
      // Verificar se já existe um bloqueio
      if (localStorage.getItem(DASHBOARD_CREATION_LOCK) === 'true') {
        console.log('Bloqueio de criação de dashboard padrão ativo. Abortando.');
        return null;
      }

      // Verificar se foi criado recentemente (nos últimos 10 segundos)
      const lastCreationTime = localStorage.getItem(DASHBOARD_CREATION_TIMESTAMP);
      if (lastCreationTime) {
        const timeSinceLastCreation = Date.now() - parseInt(lastCreationTime, 10);
        if (timeSinceLastCreation < 10000) { // 10 segundos
          console.log(`Dashboard padrão foi criado recentemente (${timeSinceLastCreation}ms atrás). Abortando.`);
          return null;
        }
      }

      // Definir bloqueio
      localStorage.setItem(DASHBOARD_CREATION_LOCK, 'true');
      setIsCreating(true);

      try {
        console.log('Criando dashboard padrão...');
        
        // Adicionar um atraso deliberado para evitar condições de corrida
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const defaultPayload = {
          name: "Dashboard Padrão",
          is_default: true,
          layout: defaultDashboardLayout as unknown as JsonValue
        };
        
        const result = await dashboardApi.createDashboard(defaultPayload);
        
        // Registrar timestamp da criação
        localStorage.setItem(DASHBOARD_CREATION_TIMESTAMP, Date.now().toString());
        
        return result;
      } finally {
        // Liberar bloqueio
        localStorage.setItem(DASHBOARD_CREATION_LOCK, 'false');
        setIsCreating(false);
      }
    },
    onSuccess: (data) => {
      if (data) {
        console.log('Dashboard padrão criado com sucesso:', data);
        queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.lists() });
        queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.default });
        
        toast({
          title: "Dashboard padrão criado",
          description: "Um dashboard padrão foi criado para você.",
          variant: "default"
        });
      }
    },
    onError: (error) => {
      console.error('Erro ao criar dashboard padrão:', error);
      localStorage.setItem(DASHBOARD_CREATION_LOCK, 'false');
      setIsCreating(false);
      
      toast({
        title: "Erro",
        description: "Não foi possível criar um dashboard padrão.",
        variant: "destructive"
      });
    }
  });

  // Verificar se é necessário criar um dashboard padrão
  const checkAndCreateDefaultDashboard = async () => {
    try {
      // Verificar se já existe um bloqueio
      if (localStorage.getItem(DASHBOARD_CREATION_LOCK) === 'true' || isCreating) {
        return;
      }

      // Buscar a lista de dashboards
      const dashboards = await dashboardApi.listDashboards();
      
      // Se não houver dashboards, criar um padrão
      if (!dashboards || dashboards.length === 0) {
        createDefaultDashboardMutation.mutate();
      }
    } catch (error) {
      console.error('Erro ao verificar dashboards:', error);
    }
  };

  // Efeito para verificar e criar o dashboard padrão quando o componente for montado
  useEffect(() => {
    // Limpar bloqueio antigo ao iniciar a aplicação
    const lastCreationTime = localStorage.getItem(DASHBOARD_CREATION_TIMESTAMP);
    if (lastCreationTime) {
      const timeSinceLastCreation = Date.now() - parseInt(lastCreationTime, 10);
      if (timeSinceLastCreation > 60000) { // 1 minuto
        localStorage.setItem(DASHBOARD_CREATION_LOCK, 'false');
      }
    } else {
      localStorage.setItem(DASHBOARD_CREATION_LOCK, 'false');
    }

    // Verificar após um pequeno atraso para evitar múltiplas chamadas
    const timer = setTimeout(() => {
      checkAndCreateDefaultDashboard();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return {
    isCreating,
    createDefaultDashboard: () => createDefaultDashboardMutation.mutate()
  };
}

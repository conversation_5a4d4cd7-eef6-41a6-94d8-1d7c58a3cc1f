import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// Corrigir importações da API
import { dashboardApi, DASHBOARD_KEYS } from '@/features/dashboard/api/dashboard.api';
import { useToast } from '@/shared/hooks/use-toast';

export function useWidgetSettingsQuery<T extends Record<string, unknown>>(instanceId: string) { // any -> unknown
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  // Query para buscar configurações do widget
  const {
    data: settings, // Usar 'settings' como nome da variável desestruturada
    isLoading,
    error
  } = useQuery({
    queryKey: DASHBOARD_KEYS.widgetSettings(instanceId),
    queryFn: () => dashboardApi.widgetSettings.get<T>(instanceId),
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 60,
    // onError removido
    initialData: () => { // Manter initialData para carregamento inicial rápido
      try {
        const savedSettings = localStorage.getItem('mockWidgetSettings'); // Usar a chave correta do mock
        if (savedSettings) {
          const allSettings = JSON.parse(savedSettings);
          return allSettings[instanceId] || {} as T;
        }
      } catch (e) {
        console.error('Erro ao ler dados iniciais do localStorage:', e);
      }
      return {} as T; // Retorna objeto vazio se não encontrar no localStorage
    },
     // Adicionar retry: false pode ser útil para evitar retentativas automáticas e depender do fallback
     retry: false,
  });

   // Lógica de fallback para localStorage SE a query falhar
   const finalSettings = error ? (() => {
       console.warn(`Query falhou para ${instanceId}, tentando fallback do localStorage.`);
       try {
           const savedSettings = localStorage.getItem('mockWidgetSettings');
           if (savedSettings) {
               const allSettings = JSON.parse(savedSettings);
               return allSettings[instanceId] || {} as T;
           }
       } catch (e) {
           console.error('Erro ao ler fallback do localStorage:', e);
       }
       return {} as T; // Retorna objeto vazio se fallback falhar
   })() : settings;


  // Mutation para atualizar configurações do widget
  const updateSettingsMutation = useMutation({
    mutationFn: (newSettings: T) => dashboardApi.widgetSettings.update(instanceId, newSettings), // Usar dashboardApi
    onMutate: async (newSettings) => {
      const queryKey = DASHBOARD_KEYS.widgetSettings(instanceId); // Usar DASHBOARD_KEYS
      // Cancelar queries em andamento
      await queryClient.cancelQueries({ queryKey });

      // Salvar estado anterior
      const previousSettings = queryClient.getQueryData(queryKey);

      // Atualizar cache otimisticamente
      queryClient.setQueryData(queryKey, newSettings);

      // Salvar no localStorage como fallback (usando novo nome de chave)
      try {
        const savedSettings = localStorage.getItem('mockWidgetSettings');
        const allSettings = savedSettings ? JSON.parse(savedSettings) : {};
        allSettings[instanceId] = newSettings;
        localStorage.setItem('mockWidgetSettings', JSON.stringify(allSettings));
      } catch (e) {
        console.error('Erro ao salvar no localStorage:', e);
      }

      return { previousSettings, queryKey }; // Retornar queryKey no contexto
    },
    onError: (err, newSettings, context) => {
      // Reverter para estado anterior em caso de erro
      if (context?.previousSettings) {
         queryClient.setQueryData(context.queryKey, context.previousSettings);
      }
       toast({ // Adicionar toast de erro
         title: "Erro ao salvar configurações",
         description: `Não foi possível salvar as configurações do widget: ${err instanceof Error ? err.message : 'Erro desconhecido'}`,
         variant: "destructive",
       });
    },
    onSuccess: () => {
       // Toast de sucesso opcional
       // toast({ title: "Configurações salvas", variant: "default" });
    },
    onSettled: (data, error, variables, context) => {
      // Revalidar dados
      if (context?.queryKey) {
        queryClient.invalidateQueries({ queryKey: context.queryKey });
      }
    },
  });

  // Função para atualizar configurações
  const updateSettings = (newSettings: Partial<T>) => {
    const updatedSettings = { ...settings, ...newSettings } as T;
    updateSettingsMutation.mutate(updatedSettings);
  };

  return {
    settings: finalSettings as T, // Usar finalSettings que inclui o fallback
    isLoading,
    isError: !!error, // Mantém isError para indicar falha da query original
    updateSettings,
    isUpdating: updateSettingsMutation.isPending
  };
}

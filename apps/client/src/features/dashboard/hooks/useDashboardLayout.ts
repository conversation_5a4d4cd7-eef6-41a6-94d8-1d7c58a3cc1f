import { useState, useEffect } from 'react';
import { Layout } from 'react-grid-layout';
import { v4 as uuidv4 } from 'uuid';
import {
  DashboardItem,
  DashboardState,
  WidgetSettings
} from '@/features/dashboard/types';
import {
  availableWidgets,
  getWidgetById
} from '@/features/dashboard/widgetRegistry';

// Layout padrão para novos usuários
const defaultLayout: DashboardItem[] = [
  {
    i: 'patientStats-1',
    x: 0, y: 0, w: 1, h: 1,
    widgetId: 'patientStats',
    instanceId: 'patientStats-1',
    visible: true
  },
  {
    i: 'appointmentsToday-1',
    x: 1, y: 0, w: 1, h: 1,
    widgetId: 'appointmentsToday',
    instanceId: 'appointmentsToday-1',
    visible: true
  },
  {
    i: 'calendar-1',
    x: 2, y: 0, w: 1, h: 2,
    widgetId: 'calendar',
    instanceId: 'calendar-1',
    visible: true
  },
  {
    i: 'upcomingAppointments-1',
    x: 0, y: 1, w: 2, h: 2,
    widgetId: 'upcomingAppointments',
    instanceId: 'upcomingAppointments-1',
    visible: true
  },
  {
    i: 'quickActions-1',
    x: 0, y: 3, w: 1, h: 1,
    widgetId: 'quickActions',
    instanceId: 'quickActions-1',
    visible: true
  },
  {
    i: 'notes-1',
    x: 1, y: 3, w: 2, h: 1,
    widgetId: 'notes',
    instanceId: 'notes-1',
    visible: true
  },
  {
    i: 'recentPatients-1',
    x: 0, y: 4, w: 2, h: 1,
    widgetId: 'recentPatients',
    instanceId: 'recentPatients-1',
    visible: true
  },
];

// Função para criar um estado inicial do dashboard
const createInitialState = (): DashboardState => ({
  layout: defaultLayout,
  lastModified: new Date().toISOString(),
});

export function useDashboardLayout() {
  // Estado para armazenar o layout atual
  const [dashboardState, setDashboardState] = useState<DashboardState>(createInitialState());
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [widgetSettings, setWidgetSettings] = useState<Record<string, any>>({});

  // Carregar layout salvo ao inicializar
  useEffect(() => {
    const loadLayout = () => {
      try {
        const savedState = localStorage.getItem('dashboardState');
        const savedSettings = localStorage.getItem('widgetSettings');

        if (savedState) {
          setDashboardState(JSON.parse(savedState));
        }

        if (savedSettings) {
          setWidgetSettings(JSON.parse(savedSettings));
        }
      } catch (error) {
        console.error('Erro ao carregar estado do dashboard:', error);
        setDashboardState(createInitialState());
      } finally {
        setIsLoading(false);
      }
    };

    loadLayout();
  }, []);

  // Salvar layout quando for alterado
  const saveLayout = (newLayout: Layout[]) => {
    try {
      // Mesclar o novo layout com as propriedades existentes
      const updatedLayout = newLayout.map(item => {
        const existingItem = dashboardState.layout.find(i => i.i === item.i);
        if (!existingItem) {
          console.error(`Item não encontrado no layout: ${item.i}`);
          return null;
        }

        return {
          ...item,
          widgetId: existingItem.widgetId,
          instanceId: existingItem.instanceId,
          visible: existingItem.visible,
          settings: existingItem.settings
        };
      }).filter(Boolean) as DashboardItem[];

      const newState: DashboardState = {
        layout: updatedLayout,
        lastModified: new Date().toISOString()
      };

      setDashboardState(newState);
      localStorage.setItem('dashboardState', JSON.stringify(newState));
    } catch (error) {
      console.error('Erro ao salvar layout do dashboard:', error);
    }
  };

  // Adicionar um novo widget ao dashboard
  const addWidget = (widgetId: string) => {
    try {
      const widgetDef = getWidgetById(widgetId);
      if (!widgetDef) {
        throw new Error(`Widget não encontrado: ${widgetId}`);
      }

      // Gerar um ID único para a instância
      const instanceId = `${widgetId}-${uuidv4().substring(0, 8)}`;

      // Determinar a posição para o novo widget (no final do layout)
      const lastItem = [...dashboardState.layout].sort((a, b) => (b.y + b.h) - (a.y + a.h))[0];
      const y = lastItem ? lastItem.y + lastItem.h : 0;

      // Criar o novo item de layout
      const newItem: DashboardItem = {
        i: instanceId,
        x: 0,
        y,
        w: widgetDef.defaultSize.w,
        h: widgetDef.defaultSize.h,
        widgetId,
        instanceId,
        visible: true,
      };

      // Atualizar o estado
      const newState: DashboardState = {
        layout: [...dashboardState.layout, newItem],
        lastModified: new Date().toISOString()
      };

      setDashboardState(newState);
      localStorage.setItem('dashboardState', JSON.stringify(newState));

      return instanceId;
    } catch (error) {
      console.error('Erro ao adicionar widget:', error);
      return null;
    }
  };

  // Remover um widget do dashboard
  const removeWidget = (instanceId: string) => {
    try {
      const newLayout = dashboardState.layout.filter(item => item.instanceId !== instanceId);

      const newState: DashboardState = {
        layout: newLayout,
        lastModified: new Date().toISOString()
      };

      setDashboardState(newState);
      localStorage.setItem('dashboardState', JSON.stringify(newState));

      // Remover configurações do widget
      const newSettings = { ...widgetSettings };
      delete newSettings[instanceId];
      setWidgetSettings(newSettings);
      localStorage.setItem('widgetSettings', JSON.stringify(newSettings));
    } catch (error) {
      console.error('Erro ao remover widget:', error);
    }
  };

  // Alternar visibilidade de um widget
  const toggleWidgetVisibility = (instanceId: string) => {
    try {
      const updatedLayout = dashboardState.layout.map(item =>
        item.instanceId === instanceId ? { ...item, visible: !item.visible } : item
      );

      const newState: DashboardState = {
        layout: updatedLayout,
        lastModified: new Date().toISOString()
      };

      setDashboardState(newState);
      localStorage.setItem('dashboardState', JSON.stringify(newState));
    } catch (error) {
      console.error('Erro ao alternar visibilidade do widget:', error);
    }
  };

  // Atualizar configurações de um widget
  const updateWidgetSettings = <T extends Record<string, any>>(
    instanceId: string,
    settings: T
  ) => {
    try {
      // Atualizar configurações do widget
      const newSettings = {
        ...widgetSettings,
        [instanceId]: settings
      };

      setWidgetSettings(newSettings);
      localStorage.setItem('widgetSettings', JSON.stringify(newSettings));

      // Atualizar o layout se necessário
      const updatedLayout = dashboardState.layout.map(item =>
        item.instanceId === instanceId ? { ...item, settings } : item
      );

      const newState: DashboardState = {
        layout: updatedLayout,
        lastModified: new Date().toISOString()
      };

      setDashboardState(newState);
      localStorage.setItem('dashboardState', JSON.stringify(newState));
    } catch (error) {
      console.error('Erro ao atualizar configurações do widget:', error);
    }
  };

  // Obter configurações de um widget
  const getWidgetSettings = <T extends Record<string, any>>(instanceId: string): T => {
    return (widgetSettings[instanceId] || {}) as T;
  };

  // Resetar para o layout padrão
  const resetLayout = () => {
    const newState = createInitialState();
    setDashboardState(newState);
    localStorage.setItem('dashboardState', JSON.stringify(newState));
    setWidgetSettings({});
    localStorage.setItem('widgetSettings', JSON.stringify({}));
  };

  return {
    layout: dashboardState.layout,
    isLoading,
    isEditing,
    setIsEditing,
    saveLayout,
    addWidget,
    removeWidget,
    toggleWidgetVisibility,
    updateWidgetSettings,
    getWidgetSettings,
    resetLayout,
    // Filtrar apenas widgets visíveis para exibição
    visibleLayout: dashboardState.layout.filter(item => item.visible)
  };
}

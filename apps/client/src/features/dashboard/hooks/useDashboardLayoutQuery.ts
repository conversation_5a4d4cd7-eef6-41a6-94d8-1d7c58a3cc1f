import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useEffect, useCallback } from 'react';
import { Layout } from 'react-grid-layout';
import { dashboardApi, DASHBOARD_KEYS } from '@/features/dashboard/api/dashboard.api';
import {
  Dashboard,
  DashboardItem,
  JsonValue,
  UpdateDashboardPayload,
  WidgetDefinition,
} from '@/features/dashboard/types';
import { getWidgetById } from '@/features/dashboard/widgetRegistry';
import { useToast } from '@/shared/hooks/use-toast';
import { v4 as uuidv4 } from 'uuid';

// Layout padrão para NOVOS dashboards criados pelo usuário
const newDashboardDefaultLayout: DashboardItem[] = [
  { i: 'welcome-1', x: 0, y: 0, w: 1, h: 1, widgetId: 'welcome', instanceId: 'welcome-1', visible: true },
];

// --- Funções Auxiliares Puras (Testáveis) ---

/**
 * Faz o parse do layout, que pode ser uma string JSON ou um array.
 * Retorna um array de DashboardItem ou um array vazio em caso de erro.
 */
function parseLayout(layoutData: JsonValue | undefined): DashboardItem[] {
  if (!layoutData) return [];
  try {
    const parsed = typeof layoutData === 'string' ? JSON.parse(layoutData) : layoutData;
    return Array.isArray(parsed) ? (parsed as DashboardItem[]) : [];
  } catch (error) {
    console.error("Erro ao fazer parse do layout:", error);
    return [];
  }
}

/**
 * Encontra a próxima posição disponível no grid para um novo widget.
 */
function findNextAvailablePosition(layout: DashboardItem[], widgetDef: WidgetDefinition): { x: number; y: number } {
    const maxCols = 4;
    const { w, h } = widgetDef.defaultSize;

    const grid = new Array(100).fill(null).map(() => new Array(maxCols).fill(false));

    layout.forEach(item => {
        for (let y = item.y; y < item.y + item.h; y++) {
            for (let x = item.x; x < item.x + item.w; x++) {
                if (grid[y] !== undefined) {
                    grid[y][x] = true;
                }
            }
        }
    });

    for (let y = 0; y < grid.length; y++) {
        for (let x = 0; x <= maxCols - w; x++) {
            let canPlace = true;
            for (let yi = y; yi < y + h; yi++) {
                for (let xi = x; xi < x + w; xi++) {
                    if (grid[yi]?.[xi]) {
                        canPlace = false;
                        break;
                    }
                }
                if (!canPlace) break;
            }
            if (canPlace) {
                return { x, y };
            }
        }
    }

    return { x: 0, y: Math.max(0, ...layout.map(i => i.y + i.h)) };
}

// Hook refatorado para gerenciar múltiplos dashboards e seus layouts
export function useDashboardLayoutManager() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // --- Estados Locais ---
  const [isEditing, setIsEditing] = useState(false);
  const [draftLayout, setDraftLayout] = useState<DashboardItem[] | null>(null);
  const [activeDashboardId, setActiveDashboardId] = useState<string | null>(null);

  // --- Queries ---

  // Query para buscar a lista de dashboards do usuário
  const { data: dashboardsList = [], isLoading: isLoadingList } = useQuery({
    queryKey: DASHBOARD_KEYS.lists(),
    queryFn: dashboardApi.listDashboards,
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 30,
  });

  // Efeito para definir o dashboard ativo quando a lista carrega ou muda
  useEffect(() => {
    if (!activeDashboardId && !isLoadingList && dashboardsList.length > 0) {
      const defaultDashboard = dashboardsList.find(d => d.is_default);
      setActiveDashboardId(defaultDashboard ? defaultDashboard.id : dashboardsList[0].id);
    }
  }, [dashboardsList, activeDashboardId, isLoadingList]);

  // Query para buscar os dados do dashboard ATIVO
  const {
    data: activeDashboardData,
    isLoading: isLoadingActive,
    error: errorActive,
  } = useQuery({
    queryKey: DASHBOARD_KEYS.detail(activeDashboardId!),
    queryFn: () => {
        console.log(`[Query Active] Fetching dashboard with ID: ${activeDashboardId}`);
        return dashboardApi.getDashboard(activeDashboardId!);
    },
    enabled: !!activeDashboardId, // Só busca se houver um ID ativo
    staleTime: 1000 * 60 * 5,
    gcTime: 1000 * 60 * 30,
  });

  // --- Efeitos ---

  // Efeito para inicializar/limpar draftLayout baseado no modo de edição e dados ativos
  useEffect(() => {
    if (isEditing && activeDashboardData) {
      setDraftLayout(parseLayout(activeDashboardData.layout));
    } else {
      setDraftLayout(null);
    }
  }, [isEditing, activeDashboardData]);

  // --- Mutações ---

  // Mutação para ATUALIZAR um dashboard (layout, nome, is_default)
  const updateDashboardMutation = useMutation({
    mutationFn: (variables: { id: string; payload: UpdateDashboardPayload }) =>
      dashboardApi.updateDashboard(variables.id, variables.payload),
    onMutate: async ({ id, payload }) => {
      await queryClient.cancelQueries({ queryKey: DASHBOARD_KEYS.detail(id) });
      const previousDashboard = queryClient.getQueryData<Dashboard>(DASHBOARD_KEYS.detail(id));
      const previousList = queryClient.getQueryData<Dashboard[]>(DASHBOARD_KEYS.lists());

      if (previousDashboard) {
        const optimisticUpdate = { ...previousDashboard, ...payload };
        queryClient.setQueryData(DASHBOARD_KEYS.detail(id), optimisticUpdate);
        queryClient.setQueryData<Dashboard[]>(DASHBOARD_KEYS.lists(), (old = []) =>
          old.map(d => (d.id === id ? optimisticUpdate : d))
        );
      }
      return { previousList, previousDashboard, dashboardId: id };
    },
    onError: (err, __, context) => {
      if (context?.previousList) {
        queryClient.setQueryData(DASHBOARD_KEYS.lists(), context.previousList);
      }
      if (context?.previousDashboard) {
         queryClient.setQueryData(DASHBOARD_KEYS.detail(context.dashboardId), context.previousDashboard);
      }
      toast({ title: 'Erro ao salvar', description: `Não foi possível salvar as alterações.`, variant: 'destructive' });
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.lists() });
      if (variables.payload.is_default || data.is_default) {
        queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.default });
      }
      toast({ title: 'Dashboard salvo', description: `"${data.name}" foi salvo com sucesso.` });
    },
  });

  // --- Funções de Controle de Edição ---

  const startEditing = useCallback(() => {
    if (!activeDashboardId || !activeDashboardData) {
      toast({ title: "Aguarde", description: "Carregando dados do dashboard...", variant: "default" });
      return;
    }
    setIsEditing(true);
  }, [activeDashboardId, activeDashboardData, toast]);

  const updateDraftLayout = useCallback((newLayout: Layout[]) => {
    if (!isEditing || !draftLayout) return;
    const updatedDraft = newLayout.map(item => {
      const existingItem = draftLayout.find(d => d.i === item.i);
      return { ...existingItem!, ...item };
    });
    setDraftLayout(updatedDraft);
  }, [isEditing, draftLayout]);

  const commitDraftLayout = useCallback(() => {
    if (!isEditing || !draftLayout || !activeDashboardId) return;
    updateDashboardMutation.mutate(
      { id: activeDashboardId, payload: { layout: draftLayout as unknown as JsonValue } },
      { onSuccess: () => setIsEditing(false) }
    );
  }, [isEditing, draftLayout, activeDashboardId, updateDashboardMutation]);

  const discardDraftLayout = useCallback(() => {
    setIsEditing(false);
    toast({ title: 'Edição cancelada', variant: 'default' });
  }, [toast]);

  // --- Funções de Gerenciamento de Widgets ---

  const addWidget = useCallback((widgetId: string) => {
    if (!activeDashboardId) return;
    const widgetDef = getWidgetById(widgetId);
    if (!widgetDef) return;

    const currentLayout = isEditing ? draftLayout : parseLayout(activeDashboardData?.layout);
    if (currentLayout === null) return;

    const instanceId = `${widgetId}-${uuidv4().substring(0, 8)}`;
    const position = findNextAvailablePosition(currentLayout, widgetDef);
    const newItem: DashboardItem = { i: instanceId, ...position, ...widgetDef.defaultSize, widgetId, instanceId, visible: true };
    const newLayout = [...currentLayout, newItem];

    if (isEditing) {
      setDraftLayout(newLayout);
      toast({ title: "Widget Adicionado", description: "O widget será adicionado ao salvar o layout." });
    } else {
      updateDashboardMutation.mutate({ id: activeDashboardId, payload: { layout: newLayout as unknown as JsonValue } });
    }
  }, [isEditing, draftLayout, activeDashboardData, activeDashboardId, toast, updateDashboardMutation]);

  const removeWidget = useCallback((instanceId: string) => {
    const currentLayout = isEditing ? draftLayout : parseLayout(activeDashboardData?.layout);
    if (currentLayout === null || !activeDashboardId) return;

    const newLayout = currentLayout.filter(item => item.instanceId !== instanceId);

    if (isEditing) {
      setDraftLayout(newLayout);
      toast({ title: "Widget Removido", description: "O widget será removido ao salvar o layout." });
    } else {
      updateDashboardMutation.mutate({ id: activeDashboardId, payload: { layout: newLayout as unknown as JsonValue } });
    }
  }, [isEditing, draftLayout, activeDashboardId, activeDashboardData, toast, updateDashboardMutation]);

   const toggleWidgetVisibility = useCallback((instanceId: string) => {
      console.log('toggleWidgetVisibility called. instanceId:', instanceId, 'isEditing:', isEditing);
       if (isEditing) {
           if (draftLayout === null) return;
           console.log('toggleWidgetVisibility - toggling in draft');
           setDraftLayout((currentDraft) =>
               currentDraft?.map((item) =>
                   item.instanceId === instanceId ? { ...item, visible: !item.visible } : item
               ) ?? null
           );
       } else if (activeDashboardId && activeDashboardData) {
           try {
               const layoutToParse = activeDashboardData.layout;
               const parsedLayout = typeof layoutToParse === 'string' ? JSON.parse(layoutToParse) : layoutToParse;
               if (!Array.isArray(parsedLayout)) throw new Error("Layout inválido");

               const currentLayout = parsedLayout as DashboardItem[];
               const newLayout = currentLayout.map(item =>
                   item.instanceId === instanceId ? { ...item, visible: !item.visible } : item
               );
               const payload: UpdateDashboardPayload = { layout: newLayout as unknown as JsonValue };
               console.log('toggleWidgetVisibility - Saving directly with payload:', payload);
               updateDashboardMutation.mutate({ id: activeDashboardId, payload });
           } catch (e) {
                console.error("Erro ao alternar visibilidade diretamente:", e);
                toast({ title: "Erro", description: "Não foi possível alterar a visibilidade.", variant: "destructive"});
           }
       } else {
            toast({ title: "Aviso", description: "Não é possível alterar a visibilidade agora."});
       }
   }, [isEditing, draftLayout, activeDashboardId, activeDashboardData, toast, updateDashboardMutation]);

   const updateWidgetSettings = useCallback((instanceId: string, settings: Record<string, unknown>) => {
     console.log(`Hook: updateWidgetSettings chamado para ${instanceId}`, settings);
     // A lógica real está no useWidgetSettingsQuery
   }, []);

   const resetLayout = useCallback(() => {
      console.log('resetLayout called. isEditing:', isEditing);
      if (!isEditing) {
         toast({ title: "Aviso", description: "Entre em modo de edição para resetar o layout."});
         return;
      }
      console.log('resetLayout - Setting draftLayout to default.');
      setDraftLayout(newDashboardDefaultLayout);
      toast({ title: "Layout de Rascunho Resetado", description: "Layout restaurado para o padrão. Salve para aplicar." });
   }, [isEditing, toast]);

   // --- Seleção de Layout para Exibição ---
   // Determina qual layout usar para exibição (rascunho ou o ativo) e garante que seja um array
   const layoutForDisplaySource = isEditing ? draftLayout : activeDashboardData?.layout;
   let parsedDisplayLayout: DashboardItem[] = [];
   if (layoutForDisplaySource) {
       try {
           const parsed = typeof layoutForDisplaySource === 'string'
             ? JSON.parse(layoutForDisplaySource)
             : layoutForDisplaySource;
           if (Array.isArray(parsed)) {
               // TODO: Adicionar validação mais robusta para garantir que são DashboardItems?
               parsedDisplayLayout = parsed as DashboardItem[];
           } else { console.error("Layout para exibição não é um array:", parsed); }
       } catch(e) { console.error("Erro ao parsear layout para exibição:", e); }
   }
   // console.log('Final parsedDisplayLayout:', parsedDisplayLayout);

  // Função para otimizar o layout automaticamente
  const optimizeLayout = useCallback(() => {
    if (!isEditing || !draftLayout) {
      toast({
        title: "Erro",
        description: "Você precisa estar no modo de edição para otimizar o layout.",
        variant: "destructive"
      });
      return;
    }

    // Ordenar widgets por tamanho (do menor para o maior)
    const sortedWidgets = [...draftLayout]
      .filter(item => item.visible)
      .sort((a, b) => (a.w * a.h) - (b.w * b.h));

    // Definir o número máximo de colunas
    const maxCols = 4;

    // Criar uma matriz para representar o grid
    const grid: boolean[][] = [];
    for (let y = 0; y < 100; y++) { // Limite arbitrário de altura
      grid[y] = [];
      for (let x = 0; x < maxCols; x++) {
        grid[y][x] = false; // false = célula vazia
      }
    }

    // Função para verificar se um espaço está disponível
    const isSpaceAvailable = (x: number, y: number, w: number, h: number) => {
      if (x + w > maxCols) return false;

      for (let i = y; i < y + h; i++) {
        for (let j = x; j < x + w; j++) {
          if (grid[i][j]) return false;
        }
      }
      return true;
    };

    // Função para marcar um espaço como ocupado
    const occupySpace = (x: number, y: number, w: number, h: number) => {
      for (let i = y; i < y + h; i++) {
        for (let j = x; j < x + w; j++) {
          grid[i][j] = true;
        }
      }
    };

    // Posicionar cada widget no melhor local disponível
    const optimizedLayout = sortedWidgets.map(widget => {
      const { w, h } = widget;
      let bestX = 0;
      let bestY = 0;
      let bestScore = Infinity;

      // Procurar a melhor posição (mais próxima do topo e da esquerda)
      for (let y = 0; y < 50; y++) { // Limite razoável de busca
        for (let x = 0; x <= maxCols - w; x++) {
          if (isSpaceAvailable(x, y, w, h)) {
            const score = y * 100 + x; // Priorizar posições no topo e à esquerda
            if (score < bestScore) {
              bestScore = score;
              bestX = x;
              bestY = y;
            }
          }
        }
        // Se encontramos uma posição na linha atual, não precisamos procurar mais
        if (bestScore < Infinity) break;
      }

      // Marcar o espaço como ocupado
      occupySpace(bestX, bestY, w, h);

      // Retornar o widget com a nova posição
      return { ...widget, x: bestX, y: bestY };
    });

    // Adicionar widgets invisíveis de volta ao layout
    const invisibleWidgets = draftLayout.filter(item => !item.visible);
    const newLayout = [...optimizedLayout, ...invisibleWidgets];

    // Atualizar o layout
    setDraftLayout(newLayout);

    toast({
      title: "Layout Otimizado",
      description: "Os widgets foram reorganizados para melhor aproveitamento do espaço."
    });
  }, [isEditing, draftLayout, toast]);

  // --- Retorno do Hook ---
  return {
    // Dados e Estados
    dashboards: dashboardsList,
    activeDashboard: activeDashboardData,
    layout: parsedDisplayLayout, // Layout parseado e validado (ou array vazio)
    isLoading: isLoadingList || (!!activeDashboardId && isLoadingActive),
    isError: !!errorActive,
    isEditing,
    activeDashboardId,

    // Funções de Controle de Edição
    startEditing,
    commitLayout: commitDraftLayout,
    discardDraftLayout,
    updateLayout: updateDraftLayout,

    // Funções de Gerenciamento de Dashboards
    setActiveDashboardId,

    // Funções de Gerenciamento de Widgets
    addWidget,
    removeWidget,
    toggleWidgetVisibility,
    updateWidgetSettings,
    resetLayout,
    optimizeLayout, // Nova função para otimizar o layout

    // Layout filtrado para renderização no Grid
    visibleLayout: parsedDisplayLayout?.filter((item: DashboardItem) => item.visible) || []
  };
}

// src/hooks/useDashboardQuery.ts
import { useQuery } from '@tanstack/react-query';
import { axiosInstance } from '@/shared/lib/api.client';
import { useToast } from '@/shared/hooks/use-toast';
import { DashboardSummary } from '@/features/dashboard/types/dashboard.schema';

export const DASHBOARD_QUERY_KEYS = {
  summary: ['dashboard', 'summary'],
};

export function useDashboardQuery() {
  const { toast } = useToast();

  const dashboardQuery = useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.summary,
    queryFn: async (): Promise<DashboardSummary> => {
      try {
        const response = await axiosInstance.get('/protected/dashboard/summary');
        return response.data;
      } catch (error) {
        toast({
          title: "Erro ao carregar dashboard",
          description: "Não foi possível carregar os dados do dashboard",
          variant: "destructive"
        });
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutos
  });

  return {
    dashboardData: dashboardQuery.data,
    isLoading: dashboardQuery.isLoading,
    isError: dashboardQuery.isError,
    refetch: dashboardQuery.refetch
  };
}
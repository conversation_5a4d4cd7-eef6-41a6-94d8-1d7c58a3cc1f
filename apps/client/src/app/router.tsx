import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { RouterProvider as TanStackRouterProvider, createRouter } from "@tanstack/react-router";
import { routeTree } from "./routeTree.gen";
import { isProduction } from "@/shared/lib/env";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutos
      retry: (failureCount, error) => {
        const errorWithResponse = error as Error & { response?: { status?: number } };
        // Não repetir em erros de autenticação (401/403)
        if (errorWithResponse?.response?.status === 401 || errorWithResponse?.response?.status === 403) {
          return false;
        }
        // Limite de 2 tentativas para outros erros
        return failureCount < 2;
      },
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: false,
    },
  },
});

export const router = createRouter({
  routeTree,
  context: {
    queryClient,
  },
  defaultPreload: "intent",
  defaultPreloadStaleTime: 1000 * 60 * 5,
  basepath: isProduction ? "/react-vite-boilerplate/" : "/",
});
export function RouterProvider() {
  return (
    <QueryClientProvider client={queryClient}>
      <TanStackRouterProvider router={router} />
    </QueryClientProvider>
  );
}



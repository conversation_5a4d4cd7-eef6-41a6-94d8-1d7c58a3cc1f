import { Bell, ChevronDown } from "lucide-react";
import { Button } from "@/shared/ui/button";
import { useAuthQuery } from "@/features/auth/hooks";

interface HeaderProps {
  title?: string;
}

export function Header({ title = "Dashboard" }: HeaderProps) {
  const { user, isLoading } = useAuthQuery();

  if (isLoading || !user) {
    return (
      <header className="sticky top-0 z-40 border-b border-border bg-card">
        <div className="flex h-16 items-center justify-between px-6">
          <h1 className="text-2xl font-semibold text-foreground">{title}</h1>
        </div>
      </header>
    );
  }

  return (
    <header className="sticky top-0 z-40 border-b border-border bg-card">
      <div className="flex h-16 items-center justify-between px-6">
        <h1 className="text-2xl font-semibold text-foreground">{title}</h1>

        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            {/* Badge de notificações */}
            <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-destructive text-[10px] font-medium text-destructive-foreground">
              3
            </span>
          </Button>

          <div className="flex items-center gap-2">
            {/* Avatar */}
            <div className="h-8 w-8 rounded-full bg-secondary flex items-center justify-center overflow-hidden">
              {user.profile_picture ? (
                <img
                  src={user.profile_picture}
                  alt={user.first_name}
                  width={32}
                  height={32}
                  className="rounded-full object-cover"
                />
              ) : (
                <span className="text-sm font-medium text-secondary-foreground">
                  {user.first_name?.[0] ?? "U"}
                </span>
              )}
            </div>

            <div className="hidden md:block">
              <p className="text-sm font-medium text-foreground">{user.first_name}</p>
              <p className="text-xs text-muted-foreground">{user.email}</p>
            </div>

            <Button variant="ghost" size="icon">
              <ChevronDown className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
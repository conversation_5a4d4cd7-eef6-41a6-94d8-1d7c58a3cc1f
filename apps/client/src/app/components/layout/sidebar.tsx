import { useState, useEffect } from "react";
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  DollarSign,
  FileText,
  HelpCircle,
  Home,
  Settings,
  Users,
  AlarmClock,
  Files,
} from "lucide-react";
import { Button } from "@/shared/ui/button";
import { cn } from "@/shared/lib/utils";
import { Link, useRouterState } from "@tanstack/react-router";
import { LogoutButton } from "@/features/auth/components/logoutButton";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/shared/ui/tooltip";
import { Badge } from "@/shared/ui/badge";
import { useWidgetsStore } from "@/features/dashboard/stores/useWidgetsStore";

const navigationItems = [
  { name: "Dashboard", to: "/dashboard", icon: Home },
  { name: "Calendário", to: "/calendar", icon: Calendar },
  { name: "<PERSON><PERSON><PERSON><PERSON>", to: "/people", icon: Users },
  { name: "Financeiro", to: "/financial", icon: DollarSign },
  { name: "Documentos", to: "/documents", icon: Files },
  { name: "Modelos", to: "/models", icon: FileText },
  { name: "Ajustes", to: "/settings", icon: Settings },
  { name: "Suporte", to: "/suporte", icon: HelpCircle },
];

export function Sidebar() {
  const routerState = useRouterState();
  const currentPath = routerState.location.pathname;
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [showClearedIndicator, setShowClearedIndicator] = useState(false);
  const { notifications, activeTimers, isInDashboard } = useWidgetsStore();

  // Verificar se há notificações
  const dashboardNotifications = notifications.filter((n: {type: string}) => n.type === 'timer').length;

  // Verificar se há timers completados
  const hasCompletedTimers = Object.values(activeTimers).some((timer: {isComplete?: boolean}) => !!timer.isComplete);

  // Efeito para mostrar indicador de notificações limpas quando entrar no dashboard
  useEffect(() => {
    // Se entrou no dashboard e tinha notificações de timer antes
    if (isInDashboard && dashboardNotifications === 0 && hasCompletedTimers) {
      setShowClearedIndicator(true);

      // Esconder o indicador após 3 segundos
      const timer = setTimeout(() => {
        setShowClearedIndicator(false);
      }, 3000);

      return () => { clearTimeout(timer); };
    }
  }, [isInDashboard, dashboardNotifications, hasCompletedTimers]);

  return (
    <div
      className={cn(
        "flex flex-col border-r border-border bg-card text-card-foreground transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      <div className="flex items-center p-3 border-b border-border">
        <div className="flex items-center justify-center w-15 h-10 rounded-full">
          <img src="/logo.svg" alt="EvoluaCare Logo" />
        </div>
        {!isCollapsed && (
          <span className="ml-2 text-xl font-bold">
            EvoluaCare
          </span>
        )}
      </div>

      <Button
        variant="ghost"
        size="sm"
        className="self-end m-2"
        onClick={() => { setIsCollapsed(!isCollapsed); }}
      >
        {isCollapsed ? (
          <ChevronRight className="h-4 w-4" />
        ) : (
          <ChevronLeft className="h-4 w-4" />
        )}
      </Button>

      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-1 px-2">
          {navigationItems.map((item) => (
            <li key={item.name}>
              <Link
                to={item.to}
                className={cn(
                  "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors relative",
                  currentPath === item.to
                    ? "bg-accent text-accent-foreground"
                    : "text-foreground hover:bg-muted hover:text-foreground"
                )}
              >
                <div className="relative">
                  <item.icon className="h-5 w-5" />
                  {/* Indicador de notificação para o Dashboard */}
                  {item.to === "/dashboard" && (dashboardNotifications > 0 || hasCompletedTimers) && (
                    <div className="absolute -top-1 -right-1">
                      <span className="relative flex h-4 w-4 items-center justify-center rounded-full bg-destructive text-[10px] font-bold text-destructive-foreground">
                        {dashboardNotifications > 0 ? dashboardNotifications : <AlarmClock className="h-3 w-3" />}
                      </span>
                      {/* Efeito de ping animado */}
                      <span className="absolute -top-0 -right-0 h-4 w-4 timer-badge-ping rounded-full bg-destructive opacity-75"></span>
                    </div>
                  )}
                </div>
                {!isCollapsed && (
                  <span className="ml-3">{item.name}</span>
                )}
                {!isCollapsed && item.to === "/dashboard" && (
                  (dashboardNotifications > 0 || hasCompletedTimers) ? (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="ml-auto relative">
                            <AlarmClock className="h-5 w-5 text-destructive timer-icon-glow" />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="right" className="w-64 p-4">
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Timer(s) Finalizado(s)</h4>
                            <p className="text-xs text-muted-foreground">
                              {dashboardNotifications > 0
                                ? `Você tem ${dashboardNotifications} notificações de timers finalizados.`
                                : `Você tem timers finalizados no dashboard.`}
                            </p>
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full text-xs mt-2"
                              asChild
                            >
                              <Link to="/dashboard">Ir para o Dashboard</Link>
                            </Button>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ) : showClearedIndicator && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="ml-auto relative">
                            <AlarmClock className="h-5 w-5 text-muted-foreground opacity-50" />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent side="right" className="w-64 p-4">
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Notificações Limpas</h4>
                            <p className="text-xs text-muted-foreground">
                              As notificações de timer foram limpas automaticamente.
                            </p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )
                )}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      <div className="flex flex-col overflow-y-auto">
        <LogoutButton/>
      </div>
    </div>
  );
}
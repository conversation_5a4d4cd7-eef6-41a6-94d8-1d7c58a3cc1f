import { useFloatingWindowsContext } from '@/shared/contexts/FloatingWindowsContext';
import { SessionNoteWindow } from '@/features/session-notes/components/SessionNoteWindow';
import { SessionNoteData } from '@/features/session-notes/types/session-note.schema';

export function FloatingWindowsManager() {
  const {
    windows,
    closeWindow,
    minimizeWindow,
    maximizeWindow,
    toggleFullscreen,
    focusWindow,
    getMinimizedWindows
  } = useFloatingWindowsContext();

  if (windows.length === 0) {
    return null;
  }

  const minimizedWindows = getMinimizedWindows();
  const activeWindows = windows.filter(w => !w.isMinimized);

  return (
    <>
      {windows.map((window) => {
        if (window.type === 'session-note') {
          // Calcular o índice da janela minimizada
          const minimizedIndex = window.isMinimized
            ? minimizedWindows.findIndex(w => w.id === window.id)
            : 0;

          // Calcular o índice da janela ativa (para empilhamento)
          const windowIndex = !window.isMinimized
            ? activeWindows.findIndex(w => w.id === window.id)
            : 0;

          return (
            <SessionNoteWindow
              key={window.id}
              data={window.data as SessionNoteData}
              isMinimized={window.isMinimized}
              isFullscreen={window.isFullscreen}
              zIndex={window.zIndex}
              minimizedIndex={minimizedIndex}
              windowIndex={windowIndex}
              onClose={() => closeWindow(window.id)}
              onMinimize={() => minimizeWindow(window.id)}
              onMaximize={() => maximizeWindow(window.id)}
              onFullscreen={() => toggleFullscreen(window.id)}
              onFocus={() => focusWindow(window.id)}
            />
          );
        }

        return null;
      })}
    </>
  );
}

import { createFileRoute, redirect } from '@tanstack/react-router'
import RegisterPage from '@/features/auth/pages/Register'
import { queryClient } from '@/app/router'
import { AUTH_QUERY_KEYS } from '@/shared/lib/api.client'

export const Route = createFileRoute('/register')({
  beforeLoad: () => {
    const sessionData = queryClient.getQueryData<{ isAuthenticated: boolean }>(AUTH_QUERY_KEYS.session);
    const isAuthenticated = sessionData?.isAuthenticated || false;

    if (isAuthenticated) {
      throw redirect({
        to: '/dashboard',
      });
    }
  },
  component: RegisterPage,
})
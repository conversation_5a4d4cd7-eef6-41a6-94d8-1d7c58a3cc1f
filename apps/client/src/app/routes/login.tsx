import { createFileRoute, redirect } from '@tanstack/react-router'
import LoginPage from '@/features/auth/pages/Login'
import {queryClient} from "@/app/router";
import { AUTH_QUERY_KEYS } from '@/shared/lib/api.client'

export const Route = createFileRoute('/login')({
  beforeLoad: () => {
    const sessionData = queryClient.getQueryData<{ isAuthenticated: boolean }>(AUTH_QUERY_KEYS.session);
    const isAuthenticated = sessionData?.isAuthenticated || false;

    if (isAuthenticated) {
      throw redirect({
        to: '/dashboard',
      });
    }
  },
  component: LoginPage,
})
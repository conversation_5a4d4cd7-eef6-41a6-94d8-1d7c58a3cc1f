import { createFileRoute, useParams, useNavigate } from '@tanstack/react-router';
import { SessionNoteEditor } from '@/features/patients/components/details/SessionNoteEditor';
import { useToast } from '@/shared/hooks/use-toast';
import { useSessionNotesMutations } from '@/features/notes/hooks/useSessionNotesMutations';
import { useSessionNotesQuery } from '@/features/notes/hooks/useSessionNotesQuery';

export const Route = createFileRoute('/_authenticated/person/$patientId/session-notes/$noteId/edit')({
  component: EditSessionNotePage,
});

function EditSessionNotePage() {
  const { patientId, noteId } = Route.useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { updateSessionNote, isUpdating } = useSessionNotesMutations(patientId);
  const { sessionNotes } = useSessionNotesQuery(patientId);
  
  const note = sessionNotes.find(n => n.id === noteId);

  const handleSave = (content: string, title: string) => {
    if (!note) return;
    
    updateSessionNote(
      {
        id: noteId,
        title,
        content,
        appointment_id: note.appointment_id,
      },
      {
        onSuccess: () => {
          toast({
            title: 'Nota de sessão atualizada',
            description: 'A nota de sessão foi atualizada com sucesso.',
          });
          navigate({ to: '/person/$patientId', params: { patientId }, search: { tab: 'notes' } });
        },
        onError: (error) => {
          toast({
            variant: 'destructive',
            title: 'Erro ao atualizar nota',
            description: error.message || 'Ocorreu um erro inesperado.',
          });
        },
      }
    );
  };

  const handleCancel = () => {
    navigate({ to: '/person/$patientId', params: { patientId } });
  };

  if (!note) {
    return <div className="p-4">Nota não encontrada.</div>;
  }

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Editar Nota de Sessão</h1>
      <SessionNoteEditor
        patientId={patientId}
        appointmentId={note.appointment_id || undefined}
        initialContent={note.content}
        onSave={handleSave}
        onCancel={handleCancel}
        isSaving={isUpdating}
      />
    </div>
  );
}

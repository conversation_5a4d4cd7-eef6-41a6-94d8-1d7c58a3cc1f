import { createFileRoute, redirect, Outlet } from "@tanstack/react-router";
import { Sidebar } from "@/app/components/layout/sidebar";
import { Header } from "@/app/components/layout/header";
import { useAuthQuery } from "@/features/auth/hooks";
import { Loader2 } from "lucide-react";
import { authClient } from "@/shared/lib/api.client";
import { EmailVerificationAlert } from "@/features/auth/components/emailVerificationAlert";
import { useRouterState } from "@tanstack/react-router";
import { WidgetNotifications } from "@/features/dashboard/components/WidgetNotifications";
import { useWidgetsStore } from "@/features/dashboard/stores/useWidgetsStore";
// import { AIAgentButton } from "@/features/ai-agent/components/AIAgentButton"; // Preparado para implementação futura
import { useEffect, useRef } from "react";

export const Route = createFileRoute("/_authenticated")({
  beforeLoad: async ({ location }) => {
    try {
      const token = authClient.getToken();
      const isValid = authClient.isTokenValid(token);

      if (!isValid) {
        const newToken = await authClient.refreshToken();
        if (!newToken) {
          return redirect({
            to: "/login",
            search: { redirect: location.pathname }
          });
        }
      }
    } catch {
      return redirect({
        to: "/login",
        search: { redirect: location.pathname }
      });
    }
  },
  component: AuthenticatedLayout,
});

function AuthenticatedLayout() {
  const { isLoading, user } = useAuthQuery();
  const routerState = useRouterState();
  const path = routerState.location.pathname;
  const { setIsInDashboard, cleanupCompletedTimers, clearTimerNotifications } = useWidgetsStore();

  // Referência para rastrear a rota anterior
  const prevPathRef = useRef(path);

  // Atualizar o estado isInDashboard com base na rota atual
  useEffect(() => {
    const isDashboard = path.includes('/dashboard');
    const wasDashboard = prevPathRef.current.includes('/dashboard');
    setIsInDashboard(isDashboard);

    // Se estiver entrando no dashboard (não estava no dashboard antes)
    if (isDashboard && !wasDashboard) {
      console.log('Entrando no dashboard, limpando notificações de timer');
      // Limpar notificações de timer
      clearTimerNotifications();

      // Limpar timers completados antigos
      setTimeout(() => {
        cleanupCompletedTimers();
      }, 1000);
    }

    // Atualizar a referência da rota anterior
    prevPathRef.current = path;
  }, [path, setIsInDashboard, cleanupCompletedTimers, clearTimerNotifications]);

  // Determinar o título da página com base na rota atual
  const getPageTitle = () => {
    if (path.includes('/dashboard')) return 'Dashboard';
    if (path.includes('/calendar')) return 'Calendário';
    if (path.includes('/people')) return 'Pessoas';
    if (path.includes('/financial')) return 'Financeiro';
    if (path.includes('/documents')) return 'Documentos';
    if (path.includes('/models')) return 'Modelos';
    if (path.includes('/settings')) return 'Configurações';
    if (path.includes('/support')) return 'Suporte';

    return '';
  };

  // Estado de carregamento
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-lg font-medium">Verificando sua sessão...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-background">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Header title={getPageTitle()} />

        {user && user.is_verified === false && (
          <div className="container mx-auto px-6 mt-4">
            <EmailVerificationAlert email={user.email} />
          </div>
        )}

        <Outlet />
      </div>

      {/* Componente de notificações - mostrar apenas quando não estiver no dashboard */}
      {!path.includes('/dashboard') && <WidgetNotifications />}

      {/* IA Agent - preparado para implementação futura */}
      {/* <AIAgentButton /> */}
    </div>
  );
}
import { createFileRoute, redirect } from '@tanstack/react-router'
import VerificationNeededPage from '@/features/auth/pages/VerificationNeeded'
import { queryClient } from '@/app/router'
import { AUTH_QUERY_KEYS, USER_QUERY_KEYS } from "@/features/auth/hooks";
import { User } from '@/features/auth/types/user.schema'

export const Route = createFileRoute('/verification-needed')({
  beforeLoad: () => {
    const sessionData = queryClient.getQueryData<{ isAuthenticated: boolean }>(AUTH_QUERY_KEYS.session);
    const userData = queryClient.getQueryData<User>(USER_QUERY_KEYS.profile);

    const isAuthenticated = sessionData?.isAuthenticated || false;

    if (!isAuthenticated) {
      throw redirect({
        to: '/login',
      });
    }

    if (userData?.is_verified) {
      throw redirect({
        to: '/dashboard',
      });
    }
  },
  component: VerificationNeededPage,
})
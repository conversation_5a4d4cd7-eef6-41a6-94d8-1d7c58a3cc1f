import { createFileRoute, useSearch, useParams, useNavigate } from '@tanstack/react-router';
import { SessionNoteEditor } from '@/features/patients/components/details/SessionNoteEditor';
import { useToast } from '@/shared/hooks/use-toast';
import { useSessionNotesMutations } from '@/features/notes/hooks/useSessionNotesMutations';

export const Route = createFileRoute('/_authenticated/person/$patientId/session-notes/new')({
  component: NewSessionNotePage,
  // Validação dos search-params se necessário (exemplo)
  validateSearch: (search: Record<string, unknown>): { appointmentId?: string } => {
    return {
      appointmentId: search.appointmentId as string | undefined,
    };
  },
});

function NewSessionNotePage() {
  const { patientId } = Route.useParams();
  const { appointmentId } = Route.useSearch();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { createSessionNote, isCreating } = useSessionNotesMutations(patientId);

  const handleSave = (content: string, title: string) => {
    createSessionNote(
      {
        patient_id: patientId,
        appointment_id: appointmentId || null,
        title,
        content,
      },
      {
        onSuccess: (createdNote) => {
          toast({
            title: 'Nota de sessão salva',
            description: 'A nota de sessão foi salva com sucesso.',
          });
          navigate({ to: '/person/$patientId', params: { patientId }, search: { tab: 'notes' } });
        },
        onError: (error) => {
          toast({
            variant: 'destructive',
            title: 'Erro ao salvar nota',
            description: error.message || 'Ocorreu um erro inesperado.',
          });
        },
      }
    );
  };

  const handleCancel = () => {
    // Navegar de volta para a página de detalhes do paciente
    navigate({ to: '/person/$patientId', params: { patientId } });
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Criar Nova Nota de Sessão</h1>
      <SessionNoteEditor
        patientId={patientId}
        appointmentId={appointmentId}
        onSave={handleSave}
        onCancel={handleCancel}
        isSaving={isCreating}
      />
    </div>
  );
}

import { createFileRoute, redirect } from '@tanstack/react-router'
import ResetPasswordPage from '@/features/auth/pages/ResetPassword'
import { queryClient } from '@/app/router'
import { AUTH_QUERY_KEYS } from '@/shared/lib/api.client'

export const Route = createFileRoute('/reset-password/$token')({
  beforeLoad: () => {
    const sessionData = queryClient.getQueryData<{ isAuthenticated: boolean }>(AUTH_QUERY_KEYS.session);
    const isAuthenticated = sessionData?.isAuthenticated || false;

    if (isAuthenticated) {
      throw redirect({
        to: '/dashboard',
      });
    }
  },
  component: ResetPasswordPage,
})
import { createFileRoute, redirect } from '@tanstack/react-router'
import ForgotPasswordPage from '@/features/auth/pages/ForgotPassword'
import { queryClient } from '@/app/router'
import { AUTH_QUERY_KEYS } from '@/shared/lib/api.client'

export const Route = createFileRoute('/forgot-password')({
  beforeLoad: () => {
    const sessionData = queryClient.getQueryData<{ isAuthenticated: boolean }>(AUTH_QUERY_KEYS.session);
    const isAuthenticated = sessionData?.isAuthenticated || false;

    if (isAuthenticated) {
      throw redirect({
        to: '/dashboard',
      });
    }
  },
  component: ForgotPasswordPage,
})
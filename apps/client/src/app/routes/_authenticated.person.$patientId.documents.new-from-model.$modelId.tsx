import { createFileRoute } from '@tanstack/react-router';
import { NewDocumentFromModel } from '@/features/patients/components/details/NewDocumentFromModel';

export const Route = createFileRoute(
  '/_authenticated/person/$patientId/documents/new-from-model/$modelId'
)({
  component: () => {
    const { patientId, modelId } = Route.useParams();
    return <NewDocumentFromModel patientId={patientId} modelId={modelId} />;
  }
});

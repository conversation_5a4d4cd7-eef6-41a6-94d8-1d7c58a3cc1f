import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useToast } from '@/shared/hooks/use-toast';
import { DocumentEditor } from '@/features/patients/components/details/DocumentEditor';

export const Route = createFileRoute('/_authenticated/person/$patientId/documents/new')({
  component: NewDocumentPage
});

function NewDocumentPage() {
  const { patientId } = Route.useParams();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSave = (content: string, title: string) => {
    // TODO: Implementar a lógica para salvar o documento
    console.log('Salvando novo documento', { patientId, title, content });
    toast({
      title: 'Documento salvo',
      description: 'O documento foi salvo com sucesso.'
    });

    // Navegar de volta para a página de detalhes do paciente
    navigate({ to: '/person/$patientId', params: { patientId } });
  };

  const handleCancel = () => {
    // Navegar de volta para a página de detalhes do paciente
    navigate({ to: '/person/$patientId', params: { patientId } });
  };

  return (
    <DocumentEditor
      patientId={patientId}
      onSave={handleSave}
      onCancel={handleCancel}
    />
  );
}

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as VerifyEmailRouteImport } from './routes/verify-email'
import { Route as VerificationNeededRouteImport } from './routes/verification-needed'
import { Route as ResetPasswordRouteImport } from './routes/reset-password'
import { Route as RegisterRouteImport } from './routes/register'
import { Route as LoginRouteImport } from './routes/login'
import { Route as ForgotPasswordRouteImport } from './routes/forgot-password'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as AuthenticatedIndexRouteImport } from './routes/_authenticated.index'
import { Route as VerifyEmailTokenRouteImport } from './routes/verify-email.$token'
import { Route as ResetPasswordTokenRouteImport } from './routes/reset-password.$token'
import { Route as AuthenticatedSettingsRouteImport } from './routes/_authenticated.settings'
import { Route as AuthenticatedPeopleRouteImport } from './routes/_authenticated.people'
import { Route as AuthenticatedModelsRouteImport } from './routes/_authenticated.models'
import { Route as AuthenticatedFinancialRouteImport } from './routes/_authenticated.financial'
import { Route as AuthenticatedDocumentsRouteImport } from './routes/_authenticated.documents'
import { Route as AuthenticatedDashboardRouteImport } from './routes/_authenticated.dashboard'
import { Route as AuthenticatedCalendarRouteImport } from './routes/_authenticated.calendar'
import { Route as AuthenticatedPersonPatientIdRouteImport } from './routes/_authenticated.person.$patientId'
import { Route as AuthenticatedModelsModelIdRouteImport } from './routes/_authenticated.models.$modelId'
import { Route as AuthenticatedPersonPatientIdSessionNotesNewRouteImport } from './routes/_authenticated.person.$patientId.session-notes.new'
import { Route as AuthenticatedPersonPatientIdDocumentsNewRouteImport } from './routes/_authenticated.person.$patientId.documents.new'
import { Route as AuthenticatedPersonPatientIdSessionNotesNoteIdEditRouteImport } from './routes/_authenticated.person.$patientId.session-notes.$noteId.edit'
import { Route as AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRouteImport } from './routes/_authenticated.person.$patientId.documents.new-from-model.$modelId'

const VerifyEmailRoute = VerifyEmailRouteImport.update({
  id: '/verify-email',
  path: '/verify-email',
  getParentRoute: () => rootRouteImport,
} as any)
const VerificationNeededRoute = VerificationNeededRouteImport.update({
  id: '/verification-needed',
  path: '/verification-needed',
  getParentRoute: () => rootRouteImport,
} as any)
const ResetPasswordRoute = ResetPasswordRouteImport.update({
  id: '/reset-password',
  path: '/reset-password',
  getParentRoute: () => rootRouteImport,
} as any)
const RegisterRoute = RegisterRouteImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const ForgotPasswordRoute = ForgotPasswordRouteImport.update({
  id: '/forgot-password',
  path: '/forgot-password',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedIndexRoute = AuthenticatedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const VerifyEmailTokenRoute = VerifyEmailTokenRouteImport.update({
  id: '/$token',
  path: '/$token',
  getParentRoute: () => VerifyEmailRoute,
} as any)
const ResetPasswordTokenRoute = ResetPasswordTokenRouteImport.update({
  id: '/$token',
  path: '/$token',
  getParentRoute: () => ResetPasswordRoute,
} as any)
const AuthenticatedSettingsRoute = AuthenticatedSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedPeopleRoute = AuthenticatedPeopleRouteImport.update({
  id: '/people',
  path: '/people',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedModelsRoute = AuthenticatedModelsRouteImport.update({
  id: '/models',
  path: '/models',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedFinancialRoute = AuthenticatedFinancialRouteImport.update({
  id: '/financial',
  path: '/financial',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedDocumentsRoute = AuthenticatedDocumentsRouteImport.update({
  id: '/documents',
  path: '/documents',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedDashboardRoute = AuthenticatedDashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedCalendarRoute = AuthenticatedCalendarRouteImport.update({
  id: '/calendar',
  path: '/calendar',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedPersonPatientIdRoute =
  AuthenticatedPersonPatientIdRouteImport.update({
    id: '/person/$patientId',
    path: '/person/$patientId',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedModelsModelIdRoute =
  AuthenticatedModelsModelIdRouteImport.update({
    id: '/$modelId',
    path: '/$modelId',
    getParentRoute: () => AuthenticatedModelsRoute,
  } as any)
const AuthenticatedPersonPatientIdSessionNotesNewRoute =
  AuthenticatedPersonPatientIdSessionNotesNewRouteImport.update({
    id: '/session-notes/new',
    path: '/session-notes/new',
    getParentRoute: () => AuthenticatedPersonPatientIdRoute,
  } as any)
const AuthenticatedPersonPatientIdDocumentsNewRoute =
  AuthenticatedPersonPatientIdDocumentsNewRouteImport.update({
    id: '/documents/new',
    path: '/documents/new',
    getParentRoute: () => AuthenticatedPersonPatientIdRoute,
  } as any)
const AuthenticatedPersonPatientIdSessionNotesNoteIdEditRoute =
  AuthenticatedPersonPatientIdSessionNotesNoteIdEditRouteImport.update({
    id: '/session-notes/$noteId/edit',
    path: '/session-notes/$noteId/edit',
    getParentRoute: () => AuthenticatedPersonPatientIdRoute,
  } as any)
const AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRoute =
  AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRouteImport.update({
    id: '/documents/new-from-model/$modelId',
    path: '/documents/new-from-model/$modelId',
    getParentRoute: () => AuthenticatedPersonPatientIdRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/forgot-password': typeof ForgotPasswordRoute
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRouteWithChildren
  '/verification-needed': typeof VerificationNeededRoute
  '/verify-email': typeof VerifyEmailRouteWithChildren
  '/calendar': typeof AuthenticatedCalendarRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/documents': typeof AuthenticatedDocumentsRoute
  '/financial': typeof AuthenticatedFinancialRoute
  '/models': typeof AuthenticatedModelsRouteWithChildren
  '/people': typeof AuthenticatedPeopleRoute
  '/settings': typeof AuthenticatedSettingsRoute
  '/reset-password/$token': typeof ResetPasswordTokenRoute
  '/verify-email/$token': typeof VerifyEmailTokenRoute
  '/': typeof AuthenticatedIndexRoute
  '/models/$modelId': typeof AuthenticatedModelsModelIdRoute
  '/person/$patientId': typeof AuthenticatedPersonPatientIdRouteWithChildren
  '/person/$patientId/documents/new': typeof AuthenticatedPersonPatientIdDocumentsNewRoute
  '/person/$patientId/session-notes/new': typeof AuthenticatedPersonPatientIdSessionNotesNewRoute
  '/person/$patientId/documents/new-from-model/$modelId': typeof AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRoute
  '/person/$patientId/session-notes/$noteId/edit': typeof AuthenticatedPersonPatientIdSessionNotesNoteIdEditRoute
}
export interface FileRoutesByTo {
  '/forgot-password': typeof ForgotPasswordRoute
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRouteWithChildren
  '/verification-needed': typeof VerificationNeededRoute
  '/verify-email': typeof VerifyEmailRouteWithChildren
  '/calendar': typeof AuthenticatedCalendarRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/documents': typeof AuthenticatedDocumentsRoute
  '/financial': typeof AuthenticatedFinancialRoute
  '/models': typeof AuthenticatedModelsRouteWithChildren
  '/people': typeof AuthenticatedPeopleRoute
  '/settings': typeof AuthenticatedSettingsRoute
  '/reset-password/$token': typeof ResetPasswordTokenRoute
  '/verify-email/$token': typeof VerifyEmailTokenRoute
  '/': typeof AuthenticatedIndexRoute
  '/models/$modelId': typeof AuthenticatedModelsModelIdRoute
  '/person/$patientId': typeof AuthenticatedPersonPatientIdRouteWithChildren
  '/person/$patientId/documents/new': typeof AuthenticatedPersonPatientIdDocumentsNewRoute
  '/person/$patientId/session-notes/new': typeof AuthenticatedPersonPatientIdSessionNotesNewRoute
  '/person/$patientId/documents/new-from-model/$modelId': typeof AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRoute
  '/person/$patientId/session-notes/$noteId/edit': typeof AuthenticatedPersonPatientIdSessionNotesNoteIdEditRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/forgot-password': typeof ForgotPasswordRoute
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/reset-password': typeof ResetPasswordRouteWithChildren
  '/verification-needed': typeof VerificationNeededRoute
  '/verify-email': typeof VerifyEmailRouteWithChildren
  '/_authenticated/calendar': typeof AuthenticatedCalendarRoute
  '/_authenticated/dashboard': typeof AuthenticatedDashboardRoute
  '/_authenticated/documents': typeof AuthenticatedDocumentsRoute
  '/_authenticated/financial': typeof AuthenticatedFinancialRoute
  '/_authenticated/models': typeof AuthenticatedModelsRouteWithChildren
  '/_authenticated/people': typeof AuthenticatedPeopleRoute
  '/_authenticated/settings': typeof AuthenticatedSettingsRoute
  '/reset-password/$token': typeof ResetPasswordTokenRoute
  '/verify-email/$token': typeof VerifyEmailTokenRoute
  '/_authenticated/': typeof AuthenticatedIndexRoute
  '/_authenticated/models/$modelId': typeof AuthenticatedModelsModelIdRoute
  '/_authenticated/person/$patientId': typeof AuthenticatedPersonPatientIdRouteWithChildren
  '/_authenticated/person/$patientId/documents/new': typeof AuthenticatedPersonPatientIdDocumentsNewRoute
  '/_authenticated/person/$patientId/session-notes/new': typeof AuthenticatedPersonPatientIdSessionNotesNewRoute
  '/_authenticated/person/$patientId/documents/new-from-model/$modelId': typeof AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRoute
  '/_authenticated/person/$patientId/session-notes/$noteId/edit': typeof AuthenticatedPersonPatientIdSessionNotesNoteIdEditRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/forgot-password'
    | '/login'
    | '/register'
    | '/reset-password'
    | '/verification-needed'
    | '/verify-email'
    | '/calendar'
    | '/dashboard'
    | '/documents'
    | '/financial'
    | '/models'
    | '/people'
    | '/settings'
    | '/reset-password/$token'
    | '/verify-email/$token'
    | '/'
    | '/models/$modelId'
    | '/person/$patientId'
    | '/person/$patientId/documents/new'
    | '/person/$patientId/session-notes/new'
    | '/person/$patientId/documents/new-from-model/$modelId'
    | '/person/$patientId/session-notes/$noteId/edit'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/forgot-password'
    | '/login'
    | '/register'
    | '/reset-password'
    | '/verification-needed'
    | '/verify-email'
    | '/calendar'
    | '/dashboard'
    | '/documents'
    | '/financial'
    | '/models'
    | '/people'
    | '/settings'
    | '/reset-password/$token'
    | '/verify-email/$token'
    | '/'
    | '/models/$modelId'
    | '/person/$patientId'
    | '/person/$patientId/documents/new'
    | '/person/$patientId/session-notes/new'
    | '/person/$patientId/documents/new-from-model/$modelId'
    | '/person/$patientId/session-notes/$noteId/edit'
  id:
    | '__root__'
    | '/_authenticated'
    | '/forgot-password'
    | '/login'
    | '/register'
    | '/reset-password'
    | '/verification-needed'
    | '/verify-email'
    | '/_authenticated/calendar'
    | '/_authenticated/dashboard'
    | '/_authenticated/documents'
    | '/_authenticated/financial'
    | '/_authenticated/models'
    | '/_authenticated/people'
    | '/_authenticated/settings'
    | '/reset-password/$token'
    | '/verify-email/$token'
    | '/_authenticated/'
    | '/_authenticated/models/$modelId'
    | '/_authenticated/person/$patientId'
    | '/_authenticated/person/$patientId/documents/new'
    | '/_authenticated/person/$patientId/session-notes/new'
    | '/_authenticated/person/$patientId/documents/new-from-model/$modelId'
    | '/_authenticated/person/$patientId/session-notes/$noteId/edit'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  ForgotPasswordRoute: typeof ForgotPasswordRoute
  LoginRoute: typeof LoginRoute
  RegisterRoute: typeof RegisterRoute
  ResetPasswordRoute: typeof ResetPasswordRouteWithChildren
  VerificationNeededRoute: typeof VerificationNeededRoute
  VerifyEmailRoute: typeof VerifyEmailRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/verify-email': {
      id: '/verify-email'
      path: '/verify-email'
      fullPath: '/verify-email'
      preLoaderRoute: typeof VerifyEmailRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/verification-needed': {
      id: '/verification-needed'
      path: '/verification-needed'
      fullPath: '/verification-needed'
      preLoaderRoute: typeof VerificationNeededRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/reset-password': {
      id: '/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof ResetPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/register': {
      id: '/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof RegisterRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/forgot-password': {
      id: '/forgot-password'
      path: '/forgot-password'
      fullPath: '/forgot-password'
      preLoaderRoute: typeof ForgotPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/': {
      id: '/_authenticated/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/verify-email/$token': {
      id: '/verify-email/$token'
      path: '/$token'
      fullPath: '/verify-email/$token'
      preLoaderRoute: typeof VerifyEmailTokenRouteImport
      parentRoute: typeof VerifyEmailRoute
    }
    '/reset-password/$token': {
      id: '/reset-password/$token'
      path: '/$token'
      fullPath: '/reset-password/$token'
      preLoaderRoute: typeof ResetPasswordTokenRouteImport
      parentRoute: typeof ResetPasswordRoute
    }
    '/_authenticated/settings': {
      id: '/_authenticated/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof AuthenticatedSettingsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/people': {
      id: '/_authenticated/people'
      path: '/people'
      fullPath: '/people'
      preLoaderRoute: typeof AuthenticatedPeopleRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/models': {
      id: '/_authenticated/models'
      path: '/models'
      fullPath: '/models'
      preLoaderRoute: typeof AuthenticatedModelsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/financial': {
      id: '/_authenticated/financial'
      path: '/financial'
      fullPath: '/financial'
      preLoaderRoute: typeof AuthenticatedFinancialRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/documents': {
      id: '/_authenticated/documents'
      path: '/documents'
      fullPath: '/documents'
      preLoaderRoute: typeof AuthenticatedDocumentsRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/dashboard': {
      id: '/_authenticated/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthenticatedDashboardRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/calendar': {
      id: '/_authenticated/calendar'
      path: '/calendar'
      fullPath: '/calendar'
      preLoaderRoute: typeof AuthenticatedCalendarRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/person/$patientId': {
      id: '/_authenticated/person/$patientId'
      path: '/person/$patientId'
      fullPath: '/person/$patientId'
      preLoaderRoute: typeof AuthenticatedPersonPatientIdRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/models/$modelId': {
      id: '/_authenticated/models/$modelId'
      path: '/$modelId'
      fullPath: '/models/$modelId'
      preLoaderRoute: typeof AuthenticatedModelsModelIdRouteImport
      parentRoute: typeof AuthenticatedModelsRoute
    }
    '/_authenticated/person/$patientId/session-notes/new': {
      id: '/_authenticated/person/$patientId/session-notes/new'
      path: '/session-notes/new'
      fullPath: '/person/$patientId/session-notes/new'
      preLoaderRoute: typeof AuthenticatedPersonPatientIdSessionNotesNewRouteImport
      parentRoute: typeof AuthenticatedPersonPatientIdRoute
    }
    '/_authenticated/person/$patientId/documents/new': {
      id: '/_authenticated/person/$patientId/documents/new'
      path: '/documents/new'
      fullPath: '/person/$patientId/documents/new'
      preLoaderRoute: typeof AuthenticatedPersonPatientIdDocumentsNewRouteImport
      parentRoute: typeof AuthenticatedPersonPatientIdRoute
    }
    '/_authenticated/person/$patientId/session-notes/$noteId/edit': {
      id: '/_authenticated/person/$patientId/session-notes/$noteId/edit'
      path: '/session-notes/$noteId/edit'
      fullPath: '/person/$patientId/session-notes/$noteId/edit'
      preLoaderRoute: typeof AuthenticatedPersonPatientIdSessionNotesNoteIdEditRouteImport
      parentRoute: typeof AuthenticatedPersonPatientIdRoute
    }
    '/_authenticated/person/$patientId/documents/new-from-model/$modelId': {
      id: '/_authenticated/person/$patientId/documents/new-from-model/$modelId'
      path: '/documents/new-from-model/$modelId'
      fullPath: '/person/$patientId/documents/new-from-model/$modelId'
      preLoaderRoute: typeof AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRouteImport
      parentRoute: typeof AuthenticatedPersonPatientIdRoute
    }
  }
}

interface AuthenticatedModelsRouteChildren {
  AuthenticatedModelsModelIdRoute: typeof AuthenticatedModelsModelIdRoute
}

const AuthenticatedModelsRouteChildren: AuthenticatedModelsRouteChildren = {
  AuthenticatedModelsModelIdRoute: AuthenticatedModelsModelIdRoute,
}

const AuthenticatedModelsRouteWithChildren =
  AuthenticatedModelsRoute._addFileChildren(AuthenticatedModelsRouteChildren)

interface AuthenticatedPersonPatientIdRouteChildren {
  AuthenticatedPersonPatientIdDocumentsNewRoute: typeof AuthenticatedPersonPatientIdDocumentsNewRoute
  AuthenticatedPersonPatientIdSessionNotesNewRoute: typeof AuthenticatedPersonPatientIdSessionNotesNewRoute
  AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRoute: typeof AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRoute
  AuthenticatedPersonPatientIdSessionNotesNoteIdEditRoute: typeof AuthenticatedPersonPatientIdSessionNotesNoteIdEditRoute
}

const AuthenticatedPersonPatientIdRouteChildren: AuthenticatedPersonPatientIdRouteChildren =
  {
    AuthenticatedPersonPatientIdDocumentsNewRoute:
      AuthenticatedPersonPatientIdDocumentsNewRoute,
    AuthenticatedPersonPatientIdSessionNotesNewRoute:
      AuthenticatedPersonPatientIdSessionNotesNewRoute,
    AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRoute:
      AuthenticatedPersonPatientIdDocumentsNewFromModelModelIdRoute,
    AuthenticatedPersonPatientIdSessionNotesNoteIdEditRoute:
      AuthenticatedPersonPatientIdSessionNotesNoteIdEditRoute,
  }

const AuthenticatedPersonPatientIdRouteWithChildren =
  AuthenticatedPersonPatientIdRoute._addFileChildren(
    AuthenticatedPersonPatientIdRouteChildren,
  )

interface AuthenticatedRouteChildren {
  AuthenticatedCalendarRoute: typeof AuthenticatedCalendarRoute
  AuthenticatedDashboardRoute: typeof AuthenticatedDashboardRoute
  AuthenticatedDocumentsRoute: typeof AuthenticatedDocumentsRoute
  AuthenticatedFinancialRoute: typeof AuthenticatedFinancialRoute
  AuthenticatedModelsRoute: typeof AuthenticatedModelsRouteWithChildren
  AuthenticatedPeopleRoute: typeof AuthenticatedPeopleRoute
  AuthenticatedSettingsRoute: typeof AuthenticatedSettingsRoute
  AuthenticatedIndexRoute: typeof AuthenticatedIndexRoute
  AuthenticatedPersonPatientIdRoute: typeof AuthenticatedPersonPatientIdRouteWithChildren
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedCalendarRoute: AuthenticatedCalendarRoute,
  AuthenticatedDashboardRoute: AuthenticatedDashboardRoute,
  AuthenticatedDocumentsRoute: AuthenticatedDocumentsRoute,
  AuthenticatedFinancialRoute: AuthenticatedFinancialRoute,
  AuthenticatedModelsRoute: AuthenticatedModelsRouteWithChildren,
  AuthenticatedPeopleRoute: AuthenticatedPeopleRoute,
  AuthenticatedSettingsRoute: AuthenticatedSettingsRoute,
  AuthenticatedIndexRoute: AuthenticatedIndexRoute,
  AuthenticatedPersonPatientIdRoute:
    AuthenticatedPersonPatientIdRouteWithChildren,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

interface ResetPasswordRouteChildren {
  ResetPasswordTokenRoute: typeof ResetPasswordTokenRoute
}

const ResetPasswordRouteChildren: ResetPasswordRouteChildren = {
  ResetPasswordTokenRoute: ResetPasswordTokenRoute,
}

const ResetPasswordRouteWithChildren = ResetPasswordRoute._addFileChildren(
  ResetPasswordRouteChildren,
)

interface VerifyEmailRouteChildren {
  VerifyEmailTokenRoute: typeof VerifyEmailTokenRoute
}

const VerifyEmailRouteChildren: VerifyEmailRouteChildren = {
  VerifyEmailTokenRoute: VerifyEmailTokenRoute,
}

const VerifyEmailRouteWithChildren = VerifyEmailRoute._addFileChildren(
  VerifyEmailRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  ForgotPasswordRoute: ForgotPasswordRoute,
  LoginRoute: LoginRoute,
  RegisterRoute: RegisterRoute,
  ResetPasswordRoute: ResetPasswordRouteWithChildren,
  VerificationNeededRoute: VerificationNeededRoute,
  VerifyEmailRoute: VerifyEmailRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

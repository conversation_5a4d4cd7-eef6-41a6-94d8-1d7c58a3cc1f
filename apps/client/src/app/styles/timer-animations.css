/* Animações para o timer */

@keyframes glow {
  0% {
    filter: drop-shadow(0 0 2px rgba(239, 68, 68, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 6px rgba(239, 68, 68, 0.6));
  }
  100% {
    filter: drop-shadow(0 0 2px rgba(239, 68, 68, 0.3));
  }
}

@keyframes shake {
  0% { transform: translateX(0); }
  10% { transform: translateX(-2px) rotate(-1deg); }
  20% { transform: translateX(2px) rotate(1deg); }
  30% { transform: translateX(-2px) rotate(-1deg); }
  40% { transform: translateX(2px) rotate(1deg); }
  50% { transform: translateX(-1px) rotate(-0.5deg); }
  60% { transform: translateX(1px) rotate(0.5deg); }
  70% { transform: translateX(-1px) rotate(-0.5deg); }
  80% { transform: translateX(1px) rotate(0.5deg); }
  90% { transform: translateX(-0.5px) rotate(-0.25deg); }
  100% { transform: translateX(0); }
}

.timer-icon-glow {
  animation: glow 2s ease-in-out infinite;
}

.timer-icon-shake {
  animation: shake 1.5s ease-in-out infinite;
}

.timer-badge-ping {
  animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

@keyframes ping {
  75%, 100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.timer-notification {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

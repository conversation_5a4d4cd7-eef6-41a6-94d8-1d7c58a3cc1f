@import "tailwindcss";

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@font-face {
    font-family: 'Geist';
    src: url('/resources/fonts/Geist-VariableFont_wgh.ttf');
    font-weight: normal;
    font-style: normal;
}

html {
    font-family: 'Geist', sans-serif;
}

@theme {
  --breakpoint-*: initial;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  --font-sans: Inter var, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-shadow-pulse: shadow-pulse infinite 4s linear alternate;
  --animate-shake: shake 0.5s cubic-bezier(.36,.07,.19,.97) infinite;
  --animate-fade-in: fade-in 0.8s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes shadow-pulse {
    from {
      background: hsl(var(--primary) / 10%);
    }
    to {
      background: hsl(var(--primary) / 90%);
    }
  }
  @keyframes shake {
    0%, 100% {
      transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
      transform: translateX(-4px);
    }
    20%, 40%, 60%, 80% {
      transform: translateX(4px);
    }
  }
  
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@utility icon {
  @apply h-[1.2rem] w-[1.2rem];
}

@layer base {
  :root {
    --gradient: linear-gradient(to top left, #231557, #44107A, #FF1361);
    --background: 258 100% 98%;
    --foreground: 258 8% 16%;
    --muted: 258 28% 90%;
    --muted-foreground: 258 10% 40%;
    --popover: 258 28% 96%;
    --popover-foreground: 258 8% 16%;
    --card: 258 28% 96%;
    --card-foreground: 258 8% 16%;
    --border: 258 10% 85%;
    --input: 258 10% 85%;
    --primary: 263 82% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 258 63% 85%;
    --secondary-foreground: 258 30% 14%;
    --accent: 258 63% 85%;
    --accent-foreground: 258 30% 14%;
    --destructive: 0 80% 60%;
    --destructive-foreground: 0 0% 100%;
    --ring: 263 82% 60%;
    --radius: 0.5rem;
    --z-navbar: 50;
    --navbar-height: 4rem;
    --footer-height: 3rem;
    --content-height: calc(100dvh - var(--navbar-height) - var(--footer-height));
  }

  .dark {
    --gradient: linear-gradient(to top left, #231557, #44107A, #FF1361);
    --background: 258 35% 14%;
    --foreground: 0 0% 100%;
    --muted: 258 30% 20%;
    --muted-foreground: 0 0% 90%;
    --popover: 258 30% 25%;
    --popover-foreground: 0 0% 95%;
    --card: 258 30% 25%;
    --card-foreground: 0 0% 95%;
    --border: 258 25% 35%;
    --input: 258 25% 35%;
    --primary: 263 82% 70%;
    --primary-foreground: 0 0% 100%;
    --secondary: 258 25% 40%;
    --secondary-foreground: 258 16% 90%;
    --accent: 258 25% 40%;
    --accent-foreground: 258 16% 90%;
    --destructive: 0 80% 60%;
    --destructive-foreground: 0 0% 100%;
    --ring: 263 82% 70%;
    --radius: 0.5rem;
    --z-navbar: 50;
    --navbar-height: 4rem;
    --footer-height: 3rem;
    --content-height: calc(100dvh - var(--navbar-height) - var(--footer-height));
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.ProseMirror {
  outline: none;
  min-height: 280px;
}

.ProseMirror h1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.ProseMirror p {
  margin-bottom: 0.5rem;
}

.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.ProseMirror ul li {
  list-style-type: disc;
}

.ProseMirror ol li {
  list-style-type: decimal;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  height: 0;
}

.ProseMirror table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1rem;
  table-layout: fixed;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid hsl(var(--border));
  padding: 0.5rem;
  position: relative;
}

.ProseMirror table th {
  background-color: hsl(var(--muted));
  font-weight: 600;
}

.ProseMirror .selectedCell:after {
  background: rgba(var(--primary) / 0.2);
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

.ProseMirror .column-resize-handle {
  background-color: hsl(var(--primary));
  bottom: 0;
  pointer-events: none;
  position: absolute;
  right: -1px;
  top: 0;
  width: 3px;
}

/* Adiciona um fundo sutil aos placeholders */
.ProseMirror [data-placeholder] {
  background-color: rgba(var(--primary) / 0.05);
  border-radius: 4px;
  padding: 0 4px;
  color: hsl(var(--primary));
}

/* Estilos para os elementos de formulário no editor */
.ProseMirror [data-form-element] {
  margin: 1rem 0;
  border-radius: 0.375rem;
  border: 1px dashed hsl(var(--border));
  padding: 0.5rem;
  transition: all 0.2s ease;
}

.ProseMirror [data-form-element]:hover {
  border-color: hsl(var(--primary) / 0.5);
  background-color: hsl(var(--primary) / 0.05);
}

.ProseMirror [data-form-element].ProseMirror-selectednode {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--primary) / 0.1);
}

@media (max-width: 640px) {
  .ProseMirror {
    min-height: 200px;
  }
}

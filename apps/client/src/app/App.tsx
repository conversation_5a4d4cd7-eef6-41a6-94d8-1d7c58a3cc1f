import { QueryClientProvider } from "@tanstack/react-query";
import { TanStackReactQueryDevelopmentTools } from "@/shared/components/developmentTools";
import { Toaster as ShadcnToaster } from "@/shared/ui/toaster";
import { Toaster as SonnerToaster } from "sonner";
import { TooltipProvider } from "@/shared/ui/tooltip";
import { queryClient, RouterProvider } from "./router";
import { isDevelopment } from "@/shared/lib/env";
import { useDefaultDashboardCreator } from "@/features/dashboard/hooks/useDefaultDashboardCreator";
import { useEffect } from "react";
import { useWidgetsStore } from "@/features/dashboard/stores/useWidgetsStore";
import { FloatingWindowsProvider } from "@/shared/contexts/FloatingWindowsContext";
import { FloatingWindowsManager } from "./components/FloatingWindowsManager";

// Componente interno que usa o hook após o QueryClientProvider estar disponível
const DashboardInitializer = () => {
  // Usar o hook para criar o dashboard padrão se necessário
  useDefaultDashboardCreator();
  return null; // Este componente não renderiza nada
};

// Componente para inicializar o tema
const ThemeInitializer = () => {
  // Efeito para aplicar o tema quando o componente é montado
  useEffect(() => {
    // Obter o tema atual do localStorage
    const savedTheme = localStorage.getItem('vite-ui-theme');
    if (savedTheme) {
      try {
        const parsedTheme = JSON.parse(savedTheme);
        if (parsedTheme.state?.theme) {
          // Aplicar o tema salvo
          const root = window.document.documentElement;
          root.classList.remove("light", "dark");

          const theme = parsedTheme.state.theme;
          if (theme === "system") {
            const systemTheme = window.matchMedia("(prefers-color-scheme: dark)").matches
              ? "dark"
              : "light";
            root.classList.add(systemTheme);
          } else {
            root.classList.add(theme);
          }
        }
      } catch (e) {
        console.error('Erro ao processar tema salvo:', e);
      }
    }
  }, []);

  return null; // Este componente não renderiza nada
};

// Componente para inicializar os widgets
const WidgetsInitializer = () => {
  // Efeito para inicializar o intervalo do timer quando o componente é montado
  useEffect(() => {
    // Usar setTimeout para garantir que o estado foi completamente inicializado
    const initTimer = setTimeout(() => {
      useWidgetsStore.getState()._startStopTimerInterval();
    }, 500);

    // Limpar o intervalo quando o componente é desmontado
    return () => {
      clearTimeout(initTimer);
      const state = useWidgetsStore.getState();
      if (typeof state._startStopTimerInterval === 'function') {
        state._startStopTimerInterval();
      }
    };
  }, []);

  return null; // Este componente não renderiza nada
};

export const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <FloatingWindowsProvider>
        {/* Inicializadores dentro do QueryClientProvider */}
        <DashboardInitializer />
        <ThemeInitializer />
        <WidgetsInitializer />

        {/* {isDevelopment && (
          <TanStackReactQueryDevelopmentTools
            initialIsOpen={false}
            buttonPosition="bottom-right"
          />
        )} */}

        <ShadcnToaster />
        <SonnerToaster
          position="top-right"
          theme={typeof document !== 'undefined' && document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
          closeButton={true}
          richColors={true}
        />
        <TooltipProvider>
          <RouterProvider />
        </TooltipProvider>

        {/* Gerenciador de janelas flutuantes */}
        <FloatingWindowsManager />
      </FloatingWindowsProvider>
    </QueryClientProvider>
  );
};
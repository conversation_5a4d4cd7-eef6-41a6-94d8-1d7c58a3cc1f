/**
 * Design System Configuration
 * Centraliza padrões visuais para garantir consistência em todo o projeto
 */

// Espaçamentos padronizados (baseado em múltiplos de 4px)
export const spacing = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '0.75rem',    // 12px
  lg: '1rem',       // 16px
  xl: '1.5rem',     // 24px
  '2xl': '2rem',    // 32px
  '3xl': '3rem',    // 48px
  '4xl': '4rem',    // 64px
} as const;

// Raios de borda padronizados
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  full: '9999px',
} as const;

// Sombras padronizadas
export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
} as const;

// Tipografia padronizada
export const typography = {
  // Tamanhos de fonte
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
  },
  // Pesos de fonte
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  // Altura de linha
  lineHeight: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75',
  },
} as const;

// Breakpoints responsivos
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Durações de animação padronizadas
export const animation = {
  duration: {
    fast: '150ms',
    normal: '200ms',
    slow: '300ms',
  },
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
} as const;

// Padrões de componentes
export const componentPatterns = {
  // Card padrão
  card: {
    base: 'rounded-lg border bg-card text-card-foreground shadow-sm',
    header: 'flex flex-col space-y-1.5 p-6',
    content: 'p-6 pt-0',
    footer: 'flex items-center p-6 pt-0',
  },
  // Button padrões
  button: {
    base: 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
    sizes: {
      sm: 'h-9 px-3',
      md: 'h-10 px-4 py-2',
      lg: 'h-11 px-8',
      icon: 'h-10 w-10',
    },
  },
  // Input padrões
  input: {
    base: 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  },
} as const;

// Estados visuais padronizados
export const states = {
  loading: 'opacity-50 cursor-not-allowed',
  disabled: 'opacity-50 pointer-events-none',
  error: 'border-destructive text-destructive',
  success: 'border-green-500 text-green-600',
  warning: 'border-yellow-500 text-yellow-600',
} as const;

// Cores semânticas (usando CSS variables do shadcn)
export const colors = {
  // Cores principais
  primary: 'hsl(var(--primary))',
  primaryForeground: 'hsl(var(--primary-foreground))',
  
  // Cores de fundo
  background: 'hsl(var(--background))',
  foreground: 'hsl(var(--foreground))',
  
  // Cores de card
  card: 'hsl(var(--card))',
  cardForeground: 'hsl(var(--card-foreground))',
  
  // Cores de borda
  border: 'hsl(var(--border))',
  input: 'hsl(var(--input))',
  
  // Cores de estado
  destructive: 'hsl(var(--destructive))',
  destructiveForeground: 'hsl(var(--destructive-foreground))',
  
  // Cores mutadas
  muted: 'hsl(var(--muted))',
  mutedForeground: 'hsl(var(--muted-foreground))',
  
  // Cores de acento
  accent: 'hsl(var(--accent))',
  accentForeground: 'hsl(var(--accent-foreground))',
} as const;

// Utilitários para aplicar padrões
export const applyPattern = {
  card: () => componentPatterns.card.base,
  cardHeader: () => componentPatterns.card.header,
  cardContent: () => componentPatterns.card.content,
  button: (size: keyof typeof componentPatterns.button.sizes = 'md') => 
    `${componentPatterns.button.base} ${componentPatterns.button.sizes[size]}`,
  input: () => componentPatterns.input.base,
  spacing: (size: keyof typeof spacing) => spacing[size],
  shadow: (size: keyof typeof shadows) => shadows[size],
} as const;

// Layout patterns
export const layouts = {
  container: 'container max-w-full px-6',
  section: 'space-y-6',
  grid: {
    responsive: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    auto: 'grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-6',
  },
  flex: {
    center: 'flex items-center justify-center',
    between: 'flex items-center justify-between',
    start: 'flex items-center justify-start',
  },
} as const;

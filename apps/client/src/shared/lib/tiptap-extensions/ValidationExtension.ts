import { Extension } from '@tiptap/core';
import { <PERSON>lug<PERSON>, Plugin<PERSON>ey } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';

// Helper functions
function validateAllFields(doc: any, formResponses: Record<string, any>): ValidationError[] {
  const errors: ValidationError[] = [];

  if (doc && typeof doc.descendants === 'function') {
    doc.descendants((node: any) => {
      if (node && node.attrs && node.attrs.fieldId && node.attrs.validation) {
        const fieldId = node.attrs.fieldId;
        const validation: FieldValidation = node.attrs.validation;
        const value = formResponses[fieldId];

        const fieldErrors = validateField(fieldId, value, validation);
        errors.push(...fieldErrors);
      }
    });
  }

  return errors;
}

function validateField(fieldId: string, value: any, validation: FieldValidation): ValidationError[] {
  const errors: ValidationError[] = [];

  // Required validation
  if (validation.required && (value === undefined || value === null || value === '')) {
    errors.push({
      fieldId,
      message: 'Este campo é obrigatório',
      type: 'required'
    });
    return errors; // If required and empty, don't check other validations
  }

  // Skip other validations if field is empty and not required
  if (value === undefined || value === null || value === '') {
    return errors;
  }

  const stringValue = String(value);

  // Min length validation
  if (validation.minLength && stringValue.length < validation.minLength) {
    errors.push({
      fieldId,
      message: `Deve ter pelo menos ${validation.minLength} caracteres`,
      type: 'minLength'
    });
  }

  // Max length validation
  if (validation.maxLength && stringValue.length > validation.maxLength) {
    errors.push({
      fieldId,
      message: `Deve ter no máximo ${validation.maxLength} caracteres`,
      type: 'maxLength'
    });
  }

  // Pattern validation
  if (validation.pattern) {
    const regex = new RegExp(validation.pattern);
    if (!regex.test(stringValue)) {
      errors.push({
        fieldId,
        message: 'Formato inválido',
        type: 'pattern'
      });
    }
  }

  // Min/Max value validation for numbers
  if (validation.min !== undefined || validation.max !== undefined) {
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      if (validation.min !== undefined && numValue < validation.min) {
        errors.push({
          fieldId,
          message: `Deve ser pelo menos ${validation.min}`,
          type: 'min'
        });
      }
      if (validation.max !== undefined && numValue > validation.max) {
        errors.push({
          fieldId,
          message: `Deve ser no máximo ${validation.max}`,
          type: 'max'
        });
      }
    }
  }

  return errors;
}

export interface FieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
  email?: boolean;
  url?: boolean;
  custom?: (value: any) => string | null; // Returns error message or null if valid
}

export interface ValidationError {
  fieldId: string;
  message: string;
  type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'min' | 'max' | 'email' | 'url' | 'custom';
}

export interface ValidationOptions {
  formResponses?: Record<string, any>;
  showInlineErrors?: boolean;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    validation: {
      validateForm: () => ReturnType,
      updateFormResponses: (responses: Record<string, any>) => ReturnType,
      getValidationErrors: () => ValidationError[],
      isFormValid: () => boolean,
    }
  }
}

export const ValidationExtension = Extension.create<ValidationOptions>({
  name: 'validation',

  addOptions() {
    return {
      formResponses: {},
      showInlineErrors: true,
    }
  },

  addStorage() {
    return {
      formResponses: this.options.formResponses || {},
      validationErrors: [] as ValidationError[],
    }
  },

  addCommands() {
    return {
      validateForm: () => ({ editor }) => {
        const errors = validateAllFields(editor.state.doc, (this.storage as any).formResponses || {});
        (this.storage as any).validationErrors = errors;
        
        // Trigger re-render to show validation errors
        const { state, view } = editor;
        const tr = state.tr;
        view.dispatch(tr);
        
        return true;
      },

      updateFormResponses: (responses: Record<string, any>) => ({ editor }) => {
        this.storage.formResponses = responses;
        
        // Auto-validate on response change
        editor.commands.validateForm();
        return true;
      },

      getValidationErrors: (() => ({ editor }: any) => {
        return (this.storage as any).validationErrors || [];
      }) as any,

      isFormValid: (() => () => {
        return this.storage.validationErrors.length === 0;
      }) as any,
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('validation'),
        props: {
          decorations: (state) => {
            if (!this.options.showInlineErrors) return DecorationSet.empty;

            const { doc } = state;
            const decorations: Decoration[] = [];
            const errors = this.storage.validationErrors || [];

            // Add error decorations to fields with validation errors
            errors.forEach((error: ValidationError) => {
              doc.descendants((node, pos) => {
                if (node.attrs && node.attrs.fieldId === error.fieldId) {
                  decorations.push(
                    Decoration.node(pos, pos + node.nodeSize, {
                      class: 'validation-error',
                      'data-error-message': error.message,
                    })
                  );
                }
              });
            });

            return DecorationSet.create(doc, decorations);
          },
        },
      }),
    ];
  },
});

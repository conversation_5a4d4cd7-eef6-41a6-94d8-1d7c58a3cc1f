import React, { useState } from 'react';
import { NodeViewProps, NodeViewWrapper } from '@tiptap/react';
import { Label } from '@/shared/ui/label';
import { Input } from '@/shared/ui/input';
import { Textarea } from '@/shared/ui/textarea';
import { Button } from '@/shared/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/shared/ui/select';
import { RadioGroup, RadioGroupItem } from '@/shared/ui/radio-group';
import { Checkbox } from '@/shared/ui/checkbox';
import { cn } from '@/shared/lib/utils';
import { Settings, Check, Plus, X } from 'lucide-react';
import { SelectOption, FieldValidation, ConditionalRule } from './DynamicFieldExtension';

export default function DynamicFieldNodeView({
  node,
  selected,
  updateAttributes,
}: NodeViewProps) {
  const attrs = node.attrs;
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    fieldType: attrs.fieldType || 'text',
    label: attrs.label || 'Campo dinâmico',
    fieldId: attrs.fieldId || '',
    placeholder: attrs.placeholder || '',
    required: attrs.required || false,
    options: attrs.options || [],
    validation: attrs.validation || {},
    conditional: attrs.conditional || null,
  });

  const handleSave = () => {
    updateAttributes({
      ...formData,
      fieldId: formData.fieldId || `field-${Date.now()}`,
    });
    setIsEditing(false);
  };

  const addOption = () => {
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, { value: '', label: '' }]
    }));
  };

  const updateOption = (index: number, field: 'value' | 'label', value: string) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt: SelectOption, i: number) => 
        i === index ? { ...opt, [field]: value } : opt
      )
    }));
  };

  const removeOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_: SelectOption, i: number) => i !== index)
    }));
  };

  const renderFieldPreview = () => {
    const id = attrs.fieldId || `field-${Math.random().toString(36).substring(2, 9)}`;
    
    switch (attrs.fieldType) {
      case 'text':
        return (
          <Input
            id={id}
            placeholder={attrs.placeholder || "Digite aqui..."}
            disabled
            className="bg-background/50"
          />
        );
      
      case 'textarea':
        return (
          <Textarea
            id={id}
            placeholder={attrs.placeholder || "Digite aqui..."}
            disabled
            className="bg-background/50 resize-none"
            rows={3}
          />
        );
      
      case 'select':
        return (
          <Select disabled>
            <SelectTrigger className="bg-background/50">
              <SelectValue placeholder={attrs.placeholder || "Selecione uma opção"} />
            </SelectTrigger>
            <SelectContent>
              {attrs.options?.map((option: SelectOption, index: number) => (
                <SelectItem key={index} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      
      case 'radio':
        return (
          <RadioGroup disabled className="space-y-2">
            {attrs.options?.map((option: SelectOption, index: number) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} disabled />
                <Label className="text-sm">{option.label}</Label>
              </div>
            ))}
          </RadioGroup>
        );
      
      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox disabled />
            <Label className="text-sm">{attrs.label}</Label>
          </div>
        );
      
      case 'date':
        return (
          <Input
            id={id}
            type="date"
            disabled
            className="bg-background/50"
          />
        );
      
      case 'number':
        return (
          <Input
            id={id}
            type="number"
            placeholder={attrs.placeholder || "Digite um número..."}
            disabled
            className="bg-background/50"
          />
        );
      
      default:
        return (
          <Input
            id={id}
            placeholder={attrs.placeholder || "Digite aqui..."}
            disabled
            className="bg-background/50"
          />
        );
    }
  };

  const getFieldTypeLabel = (type: string) => {
    const labels = {
      text: 'Texto',
      textarea: 'Texto longo',
      select: 'Lista suspensa',
      radio: 'Múltipla escolha',
      checkbox: 'Caixa de seleção',
      date: 'Data',
      number: 'Número'
    };
    return labels[type as keyof typeof labels] || 'Campo dinâmico';
  };

  return (
    <NodeViewWrapper
      className={cn(
        "my-2 p-3 rounded border border-dashed",
        selected ? "border-primary bg-primary/5" : "border-muted-foreground/20",
        isEditing ? "bg-muted/20" : ""
      )}
      data-form-element="dynamic-field"
    >
      {isEditing ? (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Editar Campo Dinâmico</h4>
            <Button size="sm" variant="ghost" onClick={handleSave}>
              <Check className="h-4 w-4 mr-1" /> Salvar
            </Button>
          </div>

          {/* Tipo do campo */}
          <div className="space-y-2">
            <Label className="text-xs">Tipo do Campo</Label>
            <Select
              value={formData.fieldType}
              onValueChange={(value) => { setFormData(prev => ({ ...prev, fieldType: value })); }}
            >
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="text">Texto</SelectItem>
                <SelectItem value="textarea">Texto longo</SelectItem>
                <SelectItem value="select">Lista suspensa</SelectItem>
                <SelectItem value="radio">Múltipla escolha</SelectItem>
                <SelectItem value="checkbox">Caixa de seleção</SelectItem>
                <SelectItem value="date">Data</SelectItem>
                <SelectItem value="number">Número</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Label */}
          <div className="space-y-2">
            <Label className="text-xs">Rótulo</Label>
            <Input
              value={formData.label}
              onChange={(e) => { setFormData(prev => ({ ...prev, label: e.target.value })); }}
              className="h-8 text-sm"
            />
          </div>

          {/* Field ID */}
          <div className="space-y-2">
            <Label className="text-xs">ID do Campo (opcional)</Label>
            <Input
              value={formData.fieldId}
              onChange={(e) => { setFormData(prev => ({ ...prev, fieldId: e.target.value })); }}
              className="h-8 text-sm"
              placeholder="Gerado automaticamente se vazio"
            />
          </div>

          {/* Placeholder (para campos que suportam) */}
          {['text', 'textarea', 'select', 'number'].includes(formData.fieldType) && (
            <div className="space-y-2">
              <Label className="text-xs">Placeholder</Label>
              <Input
                value={formData.placeholder}
                onChange={(e) => { setFormData(prev => ({ ...prev, placeholder: e.target.value })); }}
                className="h-8 text-sm"
              />
            </div>
          )}

          {/* Opções (para select e radio) */}
          {['select', 'radio'].includes(formData.fieldType) && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-xs">Opções</Label>
                <Button size="sm" variant="outline" onClick={addOption}>
                  <Plus className="h-3 w-3 mr-1" /> Adicionar
                </Button>
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {formData.options.map((option: SelectOption, index: number) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Input
                      placeholder="Valor"
                      value={option.value}
                      onChange={(e) => { updateOption(index, 'value', e.target.value); }}
                      className="h-7 text-xs flex-1"
                    />
                    <Input
                      placeholder="Rótulo"
                      value={option.label}
                      onChange={(e) => { updateOption(index, 'label', e.target.value); }}
                      className="h-7 text-xs flex-1"
                    />
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => { removeOption(index); }}
                      className="h-7 w-7 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Campo obrigatório */}
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={formData.required}
              onCheckedChange={(checked) => { setFormData(prev => ({ ...prev, required: !!checked })); }}
            />
            <Label className="text-xs cursor-pointer">Campo obrigatório</Label>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">
              {attrs.label}
              {attrs.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <div className="flex items-center space-x-1">
              <div className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                {getFieldTypeLabel(attrs.fieldType)}
              </div>
              {selected && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => { setIsEditing(true); }}
                  title="Editar campo"
                >
                  <Settings className="h-3.5 w-3.5" />
                </Button>
              )}
            </div>
          </div>
          {renderFieldPreview()}
        </div>
      )}
    </NodeViewWrapper>
  );
}

import { Extension } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from 'prosemirror-state';
import { Decoration, DecorationSet } from 'prosemirror-view';

// Helper function to evaluate a single condition
function evaluateCondition(rule: ConditionalRule, formResponses: Record<string, unknown>): boolean {
  const fieldValue = formResponses[rule.fieldId];
  const ruleValue = rule.value;

  switch (rule.operator) {
    case 'equals':
      return fieldValue === ruleValue;

    case 'not_equals':
      return fieldValue !== ruleValue;

    case 'contains':
      if (typeof fieldValue === 'string' && typeof ruleValue === 'string') {
        return fieldValue.toLowerCase().includes(ruleValue.toLowerCase());
      }
      if (Array.isArray(fieldValue)) {
        return fieldValue.includes(ruleValue);
      }
      return false;

    case 'not_contains':
      if (typeof fieldValue === 'string' && typeof ruleValue === 'string') {
        return !fieldValue.toLowerCase().includes(ruleValue.toLowerCase());
      }
      if (Array.isArray(fieldValue)) {
        return !fieldValue.includes(ruleValue);
      }
      return true;

    case 'greater_than':
      if (typeof fieldValue === 'number' && typeof ruleValue === 'number') {
        return fieldValue > ruleValue;
      }
      if (typeof fieldValue === 'string' && typeof ruleValue === 'string') {
        return fieldValue.length > parseInt(ruleValue);
      }
      return false;

    case 'less_than':
      if (typeof fieldValue === 'number' && typeof ruleValue === 'number') {
        return fieldValue < ruleValue;
      }
      if (typeof fieldValue === 'string' && typeof ruleValue === 'string') {
        return fieldValue.length < parseInt(ruleValue);
      }
      return false;

    default:
      return true;
  }
}

export interface ConditionalRule {
  fieldId: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
  value: unknown;
}

export interface ConditionalLogicOptions {
  formResponses?: Record<string, unknown>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    conditionalLogic: {
      updateFormResponses: (responses: Record<string, unknown>) => ReturnType,
      evaluateConditionals: () => ReturnType,
    }
  }
}

export const ConditionalLogicExtension = Extension.create<ConditionalLogicOptions>({
  name: 'conditionalLogic',

  addOptions() {
    return {
      formResponses: {},
    }
  },

  addStorage() {
    return {
      formResponses: this.options.formResponses || {},
    }
  },

  addCommands() {
    return {
      updateFormResponses: (responses: Record<string, unknown>) => ({ editor }) => {
        this.storage.formResponses = responses;
        
        // Trigger re-evaluation of conditionals
        editor.commands.evaluateConditionals();
        return true;
      },

      evaluateConditionals: () => ({ editor }) => {
        // Force a re-render to apply conditional visibility
        const { state, view } = editor;
        const tr = state.tr;
        view.dispatch(tr);
        return true;
      },
    }
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('conditionalLogic'),
        props: {
          decorations: (state) => {
            const { doc } = state;
            const decorations: Decoration[] = [];
            const formResponses = this.storage.formResponses || {};

            doc.descendants((node, pos) => {
              // Check if node has conditional logic
              if (node.attrs?.conditional) {
                const conditional: ConditionalRule = node.attrs.conditional;
                const shouldHide = !evaluateCondition(conditional, formResponses);

                if (shouldHide) {
                  // Add decoration to hide the node
                  decorations.push(
                    Decoration.node(pos, pos + node.nodeSize, {
                      class: 'conditional-hidden',
                      style: 'display: none;',
                    })
                  );
                }
              }
            });

            return DecorationSet.create(doc, decorations);
          },
        },
      }),
    ];
  },

  // Helper method to get visible fields based on current form responses
  getVisibleFields(doc: any, formResponses: Record<string, unknown>): string[] {
    const visibleFields: string[] = [];

    if (doc && typeof doc.descendants === 'function') {
      doc.descendants((node: any) => {
        if (node && node.attrs && node.attrs.fieldId) {
          const fieldId = node.attrs.fieldId;

          if (node.attrs.conditional) {
            const conditional: ConditionalRule = node.attrs.conditional;
            if (evaluateCondition(conditional, formResponses)) {
              visibleFields.push(fieldId);
            }
          } else {
            // No conditional logic, always visible
            visibleFields.push(fieldId);
          }
        }
      });
    }

    return visibleFields;
  },
});

// CSS for hiding conditional fields
export const conditionalLogicStyles = `
  .conditional-hidden {
    display: none !important;
  }
  
  .conditional-field {
    transition: opacity 0.2s ease-in-out;
  }
  
  .conditional-field.hidden {
    opacity: 0.3;
    pointer-events: none;
  }
`;

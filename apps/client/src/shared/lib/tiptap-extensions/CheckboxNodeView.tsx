import React, { useState } from 'react';
import { NodeViewProps, NodeViewWrapper } from '@tiptap/react';
import { Label } from '@/shared/ui/label';
import { Checkbox } from '@/shared/ui/checkbox';
import { Input } from '@/shared/ui/input';
import { Button } from '@/shared/ui/button';
import { cn } from '@/shared/lib/utils';
import { Settings, Check } from 'lucide-react';

export default function CheckboxNodeView({
  node,
  selected,
  updateAttributes,
}: NodeViewProps) {
  const attrs = node.attrs;
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(attrs.label || 'Caixa de seleção');
  const [fieldId, setFieldId] = useState(attrs.fieldId || '');
  const [defaultChecked, setDefaultChecked] = useState(attrs.defaultChecked || false);
  const [required, setRequired] = useState(attrs.required || false);

  const handleSave = () => {
    updateAttributes({
      label,
      fieldId: fieldId || `field-${Date.now()}`,
      defaultChecked,
      required,
    });
    setIsEditing(false);
  };

  const id = attrs.fieldId || `field-${Math.random().toString(36).substring(2, 9)}`;

  return (
    <NodeViewWrapper
      className={cn(
        "my-2 p-2 rounded border border-dashed",
        selected ? "border-primary bg-primary/5" : "border-muted-foreground/20",
        isEditing ? "bg-muted/20" : ""
      )}
      data-form-element="checkbox"
    >
      {isEditing ? (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Editar Caixa de Seleção</h4>
            <Button size="sm" variant="ghost" onClick={handleSave}>
              <Check className="h-4 w-4 mr-1" /> Salvar
            </Button>
          </div>

          <div className="space-y-2">
            <Label htmlFor={`edit-label-${id}`} className="text-xs">
              Rótulo
            </Label>
            <Input
              id={`edit-label-${id}`}
              value={label}
              onChange={(e) => setLabel(e.target.value)}
              className="h-8 text-sm"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`edit-id-${id}`} className="text-xs">
              ID do Campo (opcional)
            </Label>
            <Input
              id={`edit-id-${id}`}
              value={fieldId}
              onChange={(e) => setFieldId(e.target.value)}
              className="h-8 text-sm"
              placeholder="Gerado automaticamente se vazio"
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={`edit-default-checked-${id}`}
              checked={defaultChecked}
              onChange={(e) => setDefaultChecked(e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor={`edit-default-checked-${id}`} className="text-xs cursor-pointer">
              Marcado por padrão
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={`edit-required-${id}`}
              checked={required}
              onChange={(e) => setRequired(e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor={`edit-required-${id}`} className="text-xs cursor-pointer">
              Campo obrigatório
            </Label>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Checkbox
              id={id}
              disabled
              defaultChecked={attrs.defaultChecked}
            />
            <Label
              htmlFor={id}
              className="text-sm font-medium"
            >
              {attrs.label}
              {attrs.required && <span className="text-destructive ml-1">*</span>}
            </Label>
          </div>
          <div className="flex items-center space-x-1">
            <div className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
              Caixa de seleção
            </div>
            {selected && (
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0"
                onClick={() => setIsEditing(true)}
                title="Editar campo"
              >
                <Settings className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        </div>
      )}
    </NodeViewWrapper>
  );
}

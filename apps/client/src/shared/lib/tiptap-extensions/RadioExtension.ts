import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import RadioNodeView from './RadioNodeView';

export interface RadioOptions {
  HTMLAttributes: Record<string, any>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    radio: {
      /**
       * Add a radio group input
       */
      setRadio: (options?: { label?: string; fieldId?: string; options?: string; required?: boolean }) => ReturnType;
    };
  }
}

export const Radio = Node.create<RadioOptions>({
  name: 'radio',
  
  group: 'block',
  
  atom: true,
  
  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },
  
  addAttributes() {
    return {
      label: {
        default: 'Botões de opção',
      },
      fieldId: {
        default: null,
      },
      options: {
        default: 'Opção 1;Opção 2;Opção 3',
      },
      required: {
        default: false,
        parseHTML: (element) => element.getAttribute('required') === 'true',
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-type="radio"]',
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return ['div', mergeAttributes(HTMLAttributes, { 'data-type': 'radio' })];
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(RadioNodeView);
  },
  
  addCommands() {
    return {
      setRadio:
        (options = {}) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          });
        },
    };
  },
});

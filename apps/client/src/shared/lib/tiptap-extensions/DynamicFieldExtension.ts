import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import DynamicFieldNodeView from './DynamicFieldNodeView'

export interface DynamicFieldOptions {
  HTMLAttributes: {
    [key: string]: unknown
  },
}

export interface SelectOption {
  value: string;
  label: string;
}

export interface FieldValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
}

export interface ConditionalRule {
  fieldId: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
  value: unknown;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    dynamicField: {
      insertDynamicField: (options: { 
        fieldType: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'date' | 'number';
        label: string;
        fieldId?: string;
        placeholder?: string;
        required?: boolean;
        options?: SelectOption[];
        validation?: FieldValidation;
        conditional?: ConditionalRule;
      }) => ReturnType,
    }
  }
}

export const DynamicFieldExtension = Node.create<DynamicFieldOptions>({
  name: 'dynamicField',

  group: 'block',

  defining: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      fieldType: {
        default: 'text',
        parseHTML: element => element.getAttribute('data-field-type'),
        renderHTML: attributes => {
          return {
            'data-field-type': attributes.fieldType,
          }
        },
      },
      label: {
        default: 'Campo dinâmico',
        parseHTML: element => element.getAttribute('data-label'),
        renderHTML: attributes => {
          return {
            'data-label': attributes.label,
          }
        },
      },
      fieldId: {
        default: '',
        parseHTML: element => element.getAttribute('data-field-id'),
        renderHTML: attributes => {
          return {
            'data-field-id': attributes.fieldId,
          }
        },
      },
      placeholder: {
        default: '',
        parseHTML: element => element.getAttribute('data-placeholder'),
        renderHTML: attributes => {
          return {
            'data-placeholder': attributes.placeholder,
          }
        },
      },
      required: {
        default: false,
        parseHTML: element => element.getAttribute('data-required') === 'true',
        renderHTML: attributes => {
          return {
            'data-required': attributes.required ? 'true' : 'false',
          }
        },
      },
      options: {
        default: [],
        parseHTML: element => {
          const optionsStr = element.getAttribute('data-options');
          return optionsStr ? JSON.parse(optionsStr) : [];
        },
        renderHTML: attributes => {
          return {
            'data-options': JSON.stringify(attributes.options || []),
          }
        },
      },
      validation: {
        default: null,
        parseHTML: element => {
          const validationStr = element.getAttribute('data-validation');
          return validationStr ? JSON.parse(validationStr) : null;
        },
        renderHTML: attributes => {
          return {
            'data-validation': attributes.validation ? JSON.stringify(attributes.validation) : null,
          }
        },
      },
      conditional: {
        default: null,
        parseHTML: element => {
          const conditionalStr = element.getAttribute('data-conditional');
          return conditionalStr ? JSON.parse(conditionalStr) : null;
        },
        renderHTML: attributes => {
          return {
            'data-conditional': attributes.conditional ? JSON.stringify(attributes.conditional) : null,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'dynamic-field',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['dynamic-field', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(DynamicFieldNodeView)
  },

  addCommands() {
    return {
      insertDynamicField: options => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: {
            ...options,
            fieldId: options.fieldId || `field-${Date.now()}`,
          },
        })
      },
    }
  },
})

import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import CheckboxNodeView from './CheckboxNodeView'

export interface CheckboxOptions {
  HTMLAttributes: {
    [key: string]: any
  },
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    checkbox: {
      setCheckbox: (options: { 
        label: string, 
        fieldId?: string, 
        required?: boolean,
        defaultChecked?: boolean
      }) => ReturnType,
    }
  }
}

export const Checkbox = Node.create<CheckboxOptions>({
  name: 'checkbox',

  group: 'block',

  defining: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      label: {
        default: 'Opção',
        parseHTML: element => element.getAttribute('data-label'),
        renderHTML: attributes => {
          return {
            'data-label': attributes.label,
          }
        },
      },
      fieldId: {
        default: '',
        parseHTML: element => element.getAttribute('data-field-id'),
        renderHTML: attributes => {
          return {
            'data-field-id': attributes.fieldId,
          }
        },
      },
      required: {
        default: false,
        parseHTML: element => element.getAttribute('data-required') === 'true',
        renderHTML: attributes => {
          return {
            'data-required': attributes.required ? 'true' : 'false',
          }
        },
      },
      defaultChecked: {
        default: false,
        parseHTML: element => element.getAttribute('data-default-checked') === 'true',
        renderHTML: attributes => {
          return {
            'data-default-checked': attributes.defaultChecked ? 'true' : 'false',
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'form-checkbox',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['form-checkbox', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(CheckboxNodeView)
  },

  addCommands() {
    return {
      setCheckbox: options => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: options,
        })
      },
    }
  },
})

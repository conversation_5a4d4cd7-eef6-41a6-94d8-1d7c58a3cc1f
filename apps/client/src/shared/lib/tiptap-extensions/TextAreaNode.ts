import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import TextAreaNodeView from './TextAreaNodeView'

export interface TextAreaOptions {
  HTMLAttributes: {
    [key: string]: any
  },
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    textArea: {
      setTextArea: (options: { 
        label: string, 
        fieldId?: string, 
        placeholder?: string, 
        required?: boolean,
        rows?: number
      }) => ReturnType,
    }
  }
}

export const TextArea = Node.create<TextAreaOptions>({
  name: 'textArea',

  group: 'block',

  defining: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      label: {
        default: 'Área de texto',
        parseHTML: element => element.getAttribute('data-label'),
        renderHTML: attributes => {
          return {
            'data-label': attributes.label,
          }
        },
      },
      fieldId: {
        default: '',
        parseHTML: element => element.getAttribute('data-field-id'),
        renderHTML: attributes => {
          return {
            'data-field-id': attributes.fieldId,
          }
        },
      },
      placeholder: {
        default: '',
        parseHTML: element => element.getAttribute('data-placeholder'),
        renderHTML: attributes => {
          return {
            'data-placeholder': attributes.placeholder,
          }
        },
      },
      required: {
        default: false,
        parseHTML: element => element.getAttribute('data-required') === 'true',
        renderHTML: attributes => {
          return {
            'data-required': attributes.required ? 'true' : 'false',
          }
        },
      },
      rows: {
        default: 3,
        parseHTML: element => {
          const rows = element.getAttribute('data-rows');
          return rows ? parseInt(rows, 10) : 3;
        },
        renderHTML: attributes => {
          return {
            'data-rows': attributes.rows,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'form-textarea',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['form-textarea', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(TextAreaNodeView)
  },

  addCommands() {
    return {
      setTextArea: options => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: options,
        })
      },
    }
  },
})

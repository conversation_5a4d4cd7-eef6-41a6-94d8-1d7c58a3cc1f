import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import DateInputNodeView from './DateInputNodeView'

export interface DateInputOptions {
  HTMLAttributes: {
    [key: string]: any
  },
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    dateInput: {
      setDateInput: (options: { 
        label: string, 
        fieldId?: string, 
        required?: boolean
      }) => ReturnType,
    }
  }
}

export const DateInput = Node.create<DateInputOptions>({
  name: 'dateInput',

  group: 'block',

  defining: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      label: {
        default: 'Data',
        parseHTML: element => element.getAttribute('data-label'),
        renderHTML: attributes => {
          return {
            'data-label': attributes.label,
          }
        },
      },
      fieldId: {
        default: '',
        parseHTML: element => element.getAttribute('data-field-id'),
        renderHTML: attributes => {
          return {
            'data-field-id': attributes.fieldId,
          }
        },
      },
      required: {
        default: false,
        parseHTML: element => element.getAttribute('data-required') === 'true',
        renderHTML: attributes => {
          return {
            'data-required': attributes.required ? 'true' : 'false',
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'form-date-input',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['form-date-input', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(DateInputNodeView)
  },

  addCommands() {
    return {
      setDateInput: options => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: options,
        })
      },
    }
  },
})

import React, { useState } from 'react';
import { NodeViewProps, NodeViewWrapper } from '@tiptap/react';
import { Label } from '@/shared/ui/label';
import { Input } from '@/shared/ui/input';
import { Button } from '@/shared/ui/button';
import { Textarea } from '@/shared/ui/textarea';
import { cn } from '@/shared/lib/utils';
import { Settings, Check } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";

export default function SelectNodeView({
  node,
  selected,
  updateAttributes,
}: NodeViewProps) {
  const attrs = node.attrs;
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(attrs.label || 'Lista de seleção');
  const [fieldId, setFieldId] = useState(attrs.fieldId || '');
  const [optionsText, setOptionsText] = useState(attrs.options || 'Opção 1;Opção 2;Opção 3');
  const [required, setRequired] = useState(attrs.required || false);

  const handleSave = () => {
    updateAttributes({
      label,
      fieldId: fieldId || `field-${Date.now()}`,
      options: optionsText,
      required,
    });
    setIsEditing(false);
  };

  const id = attrs.fieldId || `field-${Math.random().toString(36).substring(2, 9)}`;

  // Converter string de opções em array
  const options = attrs.options ? attrs.options.split(';') : ['Opção 1', 'Opção 2', 'Opção 3'];

  return (
    <NodeViewWrapper
      className={cn(
        "my-2 p-2 rounded border border-dashed",
        selected ? "border-primary bg-primary/5" : "border-muted-foreground/20",
        isEditing ? "bg-muted/20" : ""
      )}
      data-form-element="select"
    >
      {isEditing ? (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Editar Lista de Seleção</h4>
            <Button size="sm" variant="ghost" onClick={handleSave}>
              <Check className="h-4 w-4 mr-1" /> Salvar
            </Button>
          </div>

          <div className="space-y-2">
            <Label htmlFor={`edit-label-${id}`} className="text-xs">
              Rótulo
            </Label>
            <Input
              id={`edit-label-${id}`}
              value={label}
              onChange={(e) => setLabel(e.target.value)}
              className="h-8 text-sm"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`edit-id-${id}`} className="text-xs">
              ID do Campo (opcional)
            </Label>
            <Input
              id={`edit-id-${id}`}
              value={fieldId}
              onChange={(e) => setFieldId(e.target.value)}
              className="h-8 text-sm"
              placeholder="Gerado automaticamente se vazio"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`edit-options-${id}`} className="text-xs">
              Opções
            </Label>
            <Textarea
              id={`edit-options-${id}`}
              value={optionsText}
              onChange={(e) => setOptionsText(e.target.value)}
              className="text-sm min-h-[80px]"
              placeholder="Opção 1;Opção 2;Opção 3"
            />
            <p className="text-xs text-muted-foreground">
              Separe as opções com ponto e vírgula (;)
            </p>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={`edit-required-${id}`}
              checked={required}
              onChange={(e) => setRequired(e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor={`edit-required-${id}`} className="text-xs cursor-pointer">
              Campo obrigatório
            </Label>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label
              htmlFor={id}
              className="text-sm font-medium"
            >
              {attrs.label}
              {attrs.required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <div className="flex items-center space-x-1">
              <div className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                Lista de seleção
              </div>
              {selected && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => setIsEditing(true)}
                  title="Editar campo"
                >
                  <Settings className="h-3.5 w-3.5" />
                </Button>
              )}
            </div>
          </div>

          <Select disabled defaultValue={options[0]}>
            <SelectTrigger id={id} className="bg-background/50">
              <SelectValue placeholder={attrs.label} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option: string, index: number) => (
                <SelectItem key={index} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </NodeViewWrapper>
  );
}

import { Node, mergeAttributes } from '@tiptap/core'
import { ReactNodeViewRenderer } from '@tiptap/react'
import SelectNodeView from './SelectNodeView'

export interface SelectOptions {
  HTMLAttributes: {
    [key: string]: any
  },
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    select: {
      setSelect: (options: { 
        label: string, 
        fieldId?: string, 
        required?: boolean,
        options: string // Opções separadas por ponto-e-vírgula
      }) => ReturnType,
    }
  }
}

export const Select = Node.create<SelectOptions>({
  name: 'select',

  group: 'block',

  defining: true,

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },

  addAttributes() {
    return {
      label: {
        default: 'Selecione uma opção',
        parseHTML: element => element.getAttribute('data-label'),
        renderHTML: attributes => {
          return {
            'data-label': attributes.label,
          }
        },
      },
      fieldId: {
        default: '',
        parseHTML: element => element.getAttribute('data-field-id'),
        renderHTML: attributes => {
          return {
            'data-field-id': attributes.fieldId,
          }
        },
      },
      required: {
        default: false,
        parseHTML: element => element.getAttribute('data-required') === 'true',
        renderHTML: attributes => {
          return {
            'data-required': attributes.required ? 'true' : 'false',
          }
        },
      },
      options: {
        default: 'Opção 1;Opção 2;Opção 3',
        parseHTML: element => element.getAttribute('data-options'),
        renderHTML: attributes => {
          return {
            'data-options': attributes.options,
          }
        },
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'form-select',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['form-select', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]
  },

  addNodeView() {
    return ReactNodeViewRenderer(SelectNodeView)
  },

  addCommands() {
    return {
      setSelect: options => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: options,
        })
      },
    }
  },
})

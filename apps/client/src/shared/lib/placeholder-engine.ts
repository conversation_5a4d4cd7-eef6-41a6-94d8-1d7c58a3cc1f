export interface PlaceholderContext {
  patient?: {
    id?: string; // ID do paciente para navegação
    full_name?: string;
    date_of_birth?: string;
    age?: number;
    phone?: string;
    email?: string;
    address?: string;
    city?: string;
    state?: string;
    cpf?: string;
    school?: string;
    notes?: string;
    gender?: string;
    marital_status?: string;
    rg?: string;
    ethnicity?: string;
    nationality?: string;
    naturalness?: string;
    occupation?: string;
  };
  therapist?: {
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
    state?: string;
    cpf?: string;
    occupation?: string;
  };
  appointment?: {
    id?: string; // ID do agendamento para navegação
    title?: string;
    start_time?: string;
    duration_minutes?: number;
    notes?: string;
  };
  system?: {
    currentDate?: Date;
  };
  // Valores de formulário preenchidos pelo usuário
  form?: Record<string, unknown>;
}

import { format as formatDate, isValid as isValidDate, parseISO } from "date-fns";

// Tipos de campos suportados para a nova sintaxe de formulários
export type FieldType = 'text' | 'textarea' | 'number' | 'date' | 'time' | 'datetime' | 'select' | 'checkbox' | 'radio';

// Interface para campos de formulário extraídos da nova sintaxe
export interface FormField {
  id: string;
  type: FieldType;
  label: string;
  required?: boolean;
  options?: string[];
  defaultValue?: unknown;
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
  rows?: number;
}

// Mapeamento entre os nomes dos placeholders e os campos do objeto de contexto
const placeholderMapping: Record<string, string[]> = {
  // Paciente
  'Paciente.Nome': ['patient', 'full_name'],
  'Paciente.DataNascimento': ['patient', 'date_of_birth'],
  'Paciente.Idade': ['patient', 'age'],
  'Paciente.Telefone': ['patient', 'phone'],
  'Paciente.Email': ['patient', 'email'],
  'Paciente.Endereco': ['patient', 'address'],
  'Paciente.Cidade': ['patient', 'city'],
  'Paciente.Estado': ['patient', 'state'],
  'Paciente.CPF': ['patient', 'cpf'],
  'Paciente.Escola': ['patient', 'school'],
  'Paciente.Observacoes': ['patient', 'notes'],
  'Paciente.Genero': ['patient', 'gender'],
  'Paciente.EstadoCivil': ['patient', 'marital_status'],
  'Paciente.RG': ['patient', 'rg'],
  'Paciente.Etnia': ['patient', 'ethnicity'],
  'Paciente.Nacionalidade': ['patient', 'nationality'],
  'Paciente.Naturalidade': ['patient', 'naturalness'],
  'Paciente.Ocupacao': ['patient', 'occupation'],

  // Aliases comuns para campos do paciente (para compatibilidade)
  'Paciente.NomeCompleto': ['patient', 'full_name'],
  'Paciente.Data_Nascimento': ['patient', 'date_of_birth'],
  'Paciente.DataDeNascimento': ['patient', 'date_of_birth'],
  'Paciente.Fone': ['patient', 'phone'],
  'Paciente.Celular': ['patient', 'phone'],
  'Paciente.Sexo': ['patient', 'gender'],
  'Paciente.Notas': ['patient', 'notes'],

  // Terapeuta
  'Terapeuta.Nome': ['therapist', 'first_name', 'last_name'], // Caso especial: concatenação
  'Terapeuta.PrimeiroNome': ['therapist', 'first_name'],
  'Terapeuta.Sobrenome': ['therapist', 'last_name'],
  'Terapeuta.Email': ['therapist', 'email'],
  'Terapeuta.Telefone': ['therapist', 'phone'],
  'Terapeuta.Endereco': ['therapist', 'address'],
  'Terapeuta.Cidade': ['therapist', 'city'],
  'Terapeuta.Estado': ['therapist', 'state'],
  'Terapeuta.CPF': ['therapist', 'cpf'],
  'Terapeuta.Ocupacao': ['therapist', 'occupation'],

  // Aliases comuns para campos do terapeuta (para compatibilidade)
  'Terapeuta.NomeCompleto': ['therapist', 'first_name', 'last_name'], // Caso especial: concatenação
  'Terapeuta.Nome_Completo': ['therapist', 'first_name', 'last_name'], // Caso especial: concatenação
  'Terapeuta.Fone': ['therapist', 'phone'],
  'Terapeuta.Celular': ['therapist', 'phone'],
  'Terapeuta.Profissao': ['therapist', 'occupation'],
  'Terapeuta.Especialidade': ['therapist', 'occupation'],

  // Sessão
  'Sessao.Titulo': ['appointment', 'title'],
  'Sessao.Data': ['appointment', 'start_time'],
  'Sessao.Duracao': ['appointment', 'duration_minutes'],
  'Sessao.Observacoes': ['appointment', 'notes'],

  // Aliases comuns para campos da sessão (para compatibilidade)
  'Sessao.Horario': ['appointment', 'start_time'],
  'Sessao.DataHora': ['appointment', 'start_time'],
  'Sessao.Inicio': ['appointment', 'start_time'],
  'Sessao.Tempo': ['appointment', 'duration_minutes'],
  'Sessao.Notas': ['appointment', 'notes'],
  'Agendamento.Titulo': ['appointment', 'title'],
  'Agendamento.Data': ['appointment', 'start_time'],
  'Agendamento.Duracao': ['appointment', 'duration_minutes'],
  'Agendamento.Observacoes': ['appointment', 'notes'],

  // Sistema
  'Sistema.DataAtual': ['system', 'currentDate'],
  'Sistema.Data': ['system', 'currentDate'],
  'Sistema.Hoje': ['system', 'currentDate'],
  'Sistema.HoraAtual': ['system', 'currentDate'] // Será formatado apenas com a hora
};

// Função auxiliar para encontrar o mapeamento de um placeholder de forma case-insensitive
function findPlaceholderMapping(fieldPath: string): string[] | null {
  // Primeiro, tentar encontrar o mapeamento exato
  if (fieldPath in placeholderMapping) {
    return placeholderMapping[fieldPath];
  }

  // Se não encontrar, tentar de forma case-insensitive
  const normalizedFieldPath = fieldPath.toLowerCase();

  for (const key in placeholderMapping) {
    if (key.toLowerCase() === normalizedFieldPath) {
      return placeholderMapping[key];
    }
  }

  return null;
}

export function processPlaceholders(content: string, context: PlaceholderContext): string {
  // Log para debug (remover em produção)
  console.debug('Processando placeholders com contexto:', context);

  // Suporta sintaxe [Campo.Subcampo|formato] e [Form.nome_campo:tipo_campo:opções]
  return content.replace(/\[(.*?)\]/g, (match, placeholder) => {
    // Verificar se é um campo de formulário (nova sintaxe)
    if (placeholder.toLowerCase().startsWith('form.')) {
      // Não substituir campos de formulário durante o processamento inicial
      // Eles serão tratados separadamente pelo ModelFormViewer
      return match;
    }

    // Sintaxe antiga: [Campo.Subcampo|formato]
    const [fieldPath, formatRaw] = placeholder.split('|');
    const format = formatRaw ? formatRaw.trim() : undefined;
    const trimmedFieldPath = fieldPath.trim();

    // Verificar se temos um mapeamento para este placeholder (case-insensitive)
    const mapping = findPlaceholderMapping(trimmedFieldPath);
    let value: unknown = null;

    if (mapping) {
      // Caso especial para nome completo do terapeuta (concatenação)
      if (trimmedFieldPath.toLowerCase() === 'terapeuta.nome' && context.therapist) {
        const firstName = context.therapist.first_name ?? '';
        const lastName = context.therapist.last_name ?? '';
        value = `${firstName} ${lastName}`.trim();
      } else {
        // Usar o mapeamento para acessar o valor no contexto
        value = context;
        for (const part of mapping) {
          if (value && typeof value === 'object' && part in value) {
            value = value[part as keyof typeof value];
          } else {
            value = null;
            break;
          }
        }
      }
    } else {
      // Fallback para o comportamento original (busca direta no contexto)
      const parts = fieldPath.split('.');
      value = context;

      for (const part of parts) {
        if (value && typeof value === 'object' && part in value) {
          value = value[part as keyof typeof value];
        } else {
          value = null;
          break;
        }
      }
    }

    // Log para debug (remover em produção)
    console.debug(`Processando placeholder: [${fieldPath}]`, { mapping, value });

    // Se valor for null/undefined, retorna traço
    if (value === null || value === undefined) {
      return '-';
    }

    // Se houver formato, aplicar
    if (format) {
      // Caso especial para Sistema.HoraAtual
      if (trimmedFieldPath.toLowerCase() === 'sistema.horaatual') {
        try {
          const date = value instanceof Date ? value : new Date(String(value));
          if (isValidDate(date)) {
            return formatDate(date, format || 'HH:mm');
          }
        } catch (e) {
          console.error('Erro ao formatar hora atual:', e);
        }
      }

      // Datas (usa date-fns)
      if ((typeof value === 'string' || value instanceof Date) &&
          (format.match(/y|M|d|H|m|s/) ||
           format.toLowerCase() === 'dd/mm/yyyy' ||
           format.toLowerCase() === 'dd/mm/yy')) {

        let dateObj: Date | null = null;

        // Converter para objeto Date
        if (typeof value === 'string') {
          try {
            // Tentar parse direto
            const parsed = new Date(value);
            if (isValidDate(parsed)) {
              dateObj = parsed;
            } else {
              // Tentar parse com parseISO
              const parsedISO = parseISO(value);
              if (isValidDate(parsedISO)) {
                dateObj = parsedISO;
              }
            }
          } catch (e) {
            console.error('Erro ao converter string para data:', e);
          }
        } else if (value instanceof Date) {
          dateObj = value;
        }

        // Formatar a data se temos um objeto Date válido
        if (dateObj && isValidDate(dateObj)) {
          try {
            // Mapear formatos comuns para formatos do date-fns
            let dateFormat = format;
            if (format.toLowerCase() === 'dd/mm/yyyy' || format.toUpperCase() === 'DD/MM/AAAA') {
              dateFormat = 'dd/MM/yyyy';
            } else if (format.toLowerCase() === 'dd/mm/yy' || format.toUpperCase() === 'DD/MM/AA') {
              dateFormat = 'dd/MM/yy';
            } else if (format.toLowerCase() === 'dd de mmmm de yyyy') {
              dateFormat = "dd 'de' MMMM 'de' yyyy";
            }

            return formatDate(dateObj, dateFormat);
          } catch (e) {
            console.error('Erro ao formatar data:', e, { value, format });
            return String(value);
          }
        }
      }

      // Números
      if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
        const numValue = typeof value === 'number' ? value : Number(value);

        // Suporte a |currency, |decimal:2, |percent, etc.
        if (format.toLowerCase().startsWith('currency')) {
          // Exemplo: |currency:BRL
          const [, currency] = format.split(':');
          try {
            return new Intl.NumberFormat('pt-BR', {
              style: 'currency',
              currency: currency || 'BRL',
            }).format(numValue);
          } catch (e) {
            console.error('Erro ao formatar moeda:', e);
            return String(value);
          }
        }

        if (format.toLowerCase().startsWith('decimal')) {
          // Exemplo: |decimal:2
          const [, digits] = format.split(':');
          const fractionDigits = digits ? parseInt(digits, 10) : 2;
          try {
            return numValue.toLocaleString('pt-BR', {
              minimumFractionDigits: fractionDigits,
              maximumFractionDigits: fractionDigits,
            });
          } catch (e) {
            console.error('Erro ao formatar decimal:', e);
            return String(value);
          }
        }

        if (format.toLowerCase() === 'percent') {
          try {
            return (numValue * 100).toLocaleString('pt-BR', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }) + '%';
          } catch (e) {
            console.error('Erro ao formatar percentual:', e);
            return String(value);
          }
        }

        // Formato com zeros à esquerda
        if (/^0+$/.test(format)) {
          try {
            return numValue.toString().padStart(format.length, '0');
          } catch (e) {
            console.error('Erro ao formatar com zeros à esquerda:', e);
            return String(value);
          }
        }
      }

      // Strings: UPPERCASE, lowercase, capitalize
      if (typeof value === 'string') {
        if (format.toUpperCase() === 'UPPERCASE') {
          return value.toUpperCase();
        }
        if (format.toLowerCase() === 'lowercase') {
          return value.toLowerCase();
        }
        if (format.toLowerCase() === 'capitalize') {
          return value.charAt(0).toUpperCase() + value.slice(1);
        }
      }
    }

    // Valor padrão
    return String(value);
  });
}

// Função para extrair campos de formulário da nova sintaxe
export function extractFormFields(content: string): FormField[] {
  const fields: FormField[] = [];

  // Log para debug (remover em produção)
  console.debug('Extraindo campos de formulário do conteúdo:', content);

  // Regex melhorada para capturar todos os formatos de campos de formulário
  // Suporta [Form.campo], [Form.campo:tipo] e [Form.campo:tipo:opções]
  // Tornamos a regex case-insensitive para capturar variações como [form.campo]
  const formFieldRegex = /\[(?:Form|form)\.([^:\]]+)(?::([^:\]]+))?(?::([^:\]]+))?\]/g;
  let match;

  // Conjunto para rastrear IDs de campos já processados (evitar duplicatas)
  const processedIds = new Set<string>();

  // Primeiro, vamos verificar se há campos de formulário no conteúdo
  const hasFormFields = /\[(?:Form|form)\./i.test(content);
  console.debug('O conteúdo contém campos de formulário?', hasFormFields);

  while ((match = formFieldRegex.exec(content)) !== null) {
    const [fullMatch, fieldId, fieldType = 'text', optionsStr] = match;
    console.debug('Match encontrado:', { fullMatch, fieldId, fieldType, optionsStr });

    const trimmedId = fieldId.trim();

    // Evitar duplicatas
    if (processedIds.has(trimmedId)) {
      console.debug(`Campo duplicado ignorado: ${trimmedId}`);
      continue;
    }

    processedIds.add(trimmedId);

    // Criar campo base
    const field: FormField = {
      id: trimmedId,
      type: (fieldType ? fieldType.trim() : 'text') as FieldType,
      label: trimmedId.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase()),
      required: false, // Por padrão, campos não são obrigatórios
    };

    // Verificar se o campo deve ser obrigatório (convenção: termina com *)
    if (field.label.endsWith('*')) {
      field.label = field.label.slice(0, -1).trim();
      field.required = true;
    }

    // Verificar se o ID do campo contém "obrigatorio" ou "required"
    if (trimmedId.toLowerCase().includes('obrigatorio') ||
        trimmedId.toLowerCase().includes('required')) {
      field.required = true;
    }

    // Processar opções adicionais se existirem
    if (optionsStr) {
      if (field.type === 'select' || field.type === 'radio') {
        field.options = optionsStr.split(',').map(opt => opt.trim());
      } else if (field.type === 'number') {
        // Formato esperado: min,max,step
        const [min, max, step] = optionsStr.split(',').map(v => parseFloat(v.trim()));
        if (!isNaN(min)) field.min = min;
        if (!isNaN(max)) field.max = max;
        if (!isNaN(step)) field.step = step;
      } else if (field.type === 'textarea') {
        // Opção para textarea é o número de linhas
        const rows = parseInt(optionsStr.trim(), 10);
        if (!isNaN(rows)) field.rows = rows;
      } else if (field.type === 'checkbox') {
        // Para checkbox, a opção pode ser o valor padrão (true/false)
        field.defaultValue = optionsStr.trim().toLowerCase() === 'true';
      }
    }

    // Adicionar à lista de campos
    fields.push(field);

    // Log para debug (remover em produção)
    console.debug(`Campo de formulário extraído: ${field.id} (${field.type})`, field);
  }

  return fields;
}

// Função para substituir campos de formulário pelos valores preenchidos
export function replaceFormFields(content: string, formValues: Record<string, unknown>): string {
  // Log para debug (remover em produção)
  console.debug('Substituindo campos de formulário no conteúdo:', content);
  console.debug('Valores disponíveis para substituição:', formValues);

  // Regex melhorada para capturar todos os formatos de campos de formulário
  // Tornamos a regex case-insensitive para capturar variações como [form.campo]
  return content.replace(/\[(?:Form|form)\.([^:\]]+)(?::([^:\]]+))?(?::([^:\]]+))?\]/g, (match, fieldId, fieldType, optionsStr) => {
    const id = fieldId.trim();
    const value = formValues[id];

    // Log para debug (remover em produção)
    console.debug(`Substituindo campo de formulário: ${id}`, { match, value, type: fieldType });

    if (value === undefined || value === null) {
      console.debug(`Valor não encontrado para o campo ${id}, retornando '-'`);
      return '-';
    }

    // Formatação específica por tipo
    if (fieldType) {
      const type = fieldType.trim();

      // Formatação para tipos específicos
      switch (type) {
        case 'date':
          // Tentar formatar data
          try {
            const date = new Date(String(value));
            if (!isNaN(date.getTime())) {
              return formatDate(date, 'dd/MM/yyyy');
            }
          } catch (e) {
            console.error('Erro ao formatar data:', e);
          }
          break;

        case 'time':
          // Tentar formatar hora
          try {
            const date = new Date(`1970-01-01T${value}`);
            if (!isNaN(date.getTime())) {
              return formatDate(date, 'HH:mm');
            }
          } catch (e) {
            console.error('Erro ao formatar hora:', e);
          }
          break;

        case 'datetime':
          // Tentar formatar data e hora
          try {
            const date = new Date(String(value));
            if (!isNaN(date.getTime())) {
              return formatDate(date, 'dd/MM/yyyy HH:mm');
            }
          } catch (e) {
            console.error('Erro ao formatar data e hora:', e);
          }
          break;

        case 'number':
          // Tentar formatar número
          if (typeof value === 'number' || !isNaN(Number(value))) {
            const num = typeof value === 'number' ? value : Number(value);
            return num.toLocaleString('pt-BR');
          }
          break;

        case 'checkbox':
          // Formatar booleano
          if (typeof value === 'boolean') {
            return value ? 'Sim' : 'Não';
          }
          break;
      }
    }

    // Formatação padrão para outros tipos
    if (typeof value === 'boolean') {
      return value ? 'Sim' : 'Não';
    }

    return String(value);
  });
}

import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { jwtDecode } from 'jwt-decode';
import { queryClient } from '@/app/router';
import { ForgotPasswordFormData, LoginFormData, RegisterFormData } from '@/features/auth/types/auth.schema';

export const AUTH_QUERY_KEYS = {
  session: ['auth', 'session'],
  user: ['auth', 'user'],
};

interface DecodedToken {
  exp: number;
  sub: number;
  jti: string;
}

let inMemoryToken: string | null = null;

export const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  withCredentials: true,
  timeout: 10000,
});

async function clearAuthState(): Promise<void> {
  inMemoryToken = null;

  await Promise.all([
    queryClient.invalidateQueries({ queryKey: AUTH_QUERY_KEYS.session }),
    queryClient.invalidateQueries({ queryKey: AUTH_QUERY_KEYS.user })
  ]);

  queryClient.setQueryData(AUTH_QUERY_KEYS.session, {
    isAuthenticated: false,
    token: null
  });
}

let isRefreshing = false;
let refreshPromise: Promise<string | null> | null = null;

export const authClient = {
  getToken: (): string | null => inMemoryToken,

  setToken: (token: string): void => {
    inMemoryToken = token;
  },

  isTokenValid: (token: string | null): boolean => {
    if (!token) return false;

    try {
      const decoded = jwtDecode<DecodedToken>(token);
      const currentTime = Date.now() / 1000;

      return decoded.exp > (currentTime + 30);
    } catch {
      return false;
    }
  },

  isTokenExpiring: (token: string | null, bufferSeconds = 300): boolean => {
    if (!token) return false;

    try {
      const decoded = jwtDecode<DecodedToken>(token);
      const currentTime = Date.now() / 1000;

      return decoded.exp < (currentTime + bufferSeconds);
    } catch {
      return false;
    }
  },

  getAuthHeader: (): Record<string, string> => {
    return inMemoryToken ? { Authorization: `Bearer ${inMemoryToken}` } : {};
  },

  refreshToken: async (): Promise<string | null> => {
    if (isRefreshing) {
      return refreshPromise;
    }

    isRefreshing = true;
    refreshPromise = (async () => {
      try {
        const response = await axiosInstance.post<{ access_token: string }>(
          `/auth/refresh`,
          {},
          { withCredentials: true }
        );

        if (response?.data?.access_token) {
          const newToken = response.data.access_token;
          inMemoryToken = newToken;
          return newToken;
        } else {
          await clearAuthState();
          return null;
        }
      } catch (_error) {
        await clearAuthState();
        return null;
      } finally {
        isRefreshing = false;
        refreshPromise = null;
      }
    })();

    return refreshPromise;
  },

  login: async (credentials: LoginFormData): Promise<any> => {
    const response = await axiosInstance.post('/auth/login', credentials);

    if (response.data?.access_token) {
        authClient.setToken(response.data.access_token);
        localStorage.setItem('wasAuthenticated', 'true');
    }

    return response.data;
},

  register: async (data: RegisterFormData): Promise<any> => {
    const response = await axiosInstance.post('/auth/register', data);

    if (response.data?.access_token) {
      authClient.setToken(response.data.access_token);
    }

    return response.data;
  },

  logout: async (): Promise<void> => {
    try {
        await axiosInstance.post('/protected/logout', {}, {
            headers: authClient.getAuthHeader()
        });
    } catch (_error) {
    } finally {
        localStorage.removeItem('wasAuthenticated');
        await clearAuthState();
    }
},

  forgotPassword: async (data: ForgotPasswordFormData): Promise<any> => {
    const response = await axiosInstance.post('/auth/forgot-password', data);
    return response.data;
  },

  resetPassword: async (token: string, password: string): Promise<any> => {
    const response = await axiosInstance.post('/auth/reset-password', {
      token,
      new_password: password
    });
    return response.data;
  },

  verifyEmail: async (token: string): Promise<any> => {
    const response = await axiosInstance.get(`/auth/verify-email/${token}`);
    return response.data;
  },

  resendVerificationEmail: async (email: string): Promise<any> => {
    const response = await axiosInstance.post('/auth/resend-verification-email', { email });
    return response.data;
  },

  checkSession: async (): Promise<boolean> => {
    const currentToken = authClient.getToken();


    if (!currentToken) {
        const wasAuthenticated = localStorage.getItem('wasAuthenticated') === 'true';
        if (!wasAuthenticated) {
            return false;
        }
    }

    if (authClient.isTokenValid(currentToken)) {
        return true;
    }

    const newToken = await authClient.refreshToken();
    return !!newToken;
}
};

// Interceptor para monitorar todas as requisições
axiosInstance.interceptors.request.use(async (config) => {
    console.log(`Requisição ${config.method?.toUpperCase()} para ${config.url}`, {
        headers: config.headers,
        data: config.data,
        params: config.params
    });

    const token = authClient.getToken();
    const wasAuthenticated = localStorage.getItem('wasAuthenticated') === 'true';

    if ((token || wasAuthenticated) && !config.url?.includes('/auth/refresh')) {
        if (token && authClient.isTokenExpiring(token)) {
            try {
                const newToken = await authClient.refreshToken();
                if (newToken) {
                    config.headers.Authorization = `Bearer ${newToken}`;
                }
            } catch (_error) {
                // Falha silenciosa
            }
        } else if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
    }

    return config;
}, (error: AxiosError) => Promise.reject(error));

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`Resposta ${response.status} de ${response.config.url}`, {
      data: response.data,
      headers: response.headers
    });
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };
    const isLoginRequest = originalRequest.url?.includes('/auth/login');
    const isAuthError = error.response?.status === 401 || error.response?.status === 403;

    if (originalRequest._retry || !isAuthError || isLoginRequest || originalRequest.url?.includes('/auth/refresh')) {
      return Promise.reject(error);
    }

    originalRequest._retry = true;

    try {
      const newToken = await authClient.refreshToken();

      if (newToken && originalRequest) {
        const newRequest = { ...originalRequest };
        newRequest.headers = {
          ...newRequest.headers,
          Authorization: `Bearer ${newToken}`
        };

        if (originalRequest.data) {
          newRequest.data = originalRequest.data;
        }

        return axiosInstance(newRequest);
      } else {
        await clearAuthState();

        queryClient.setQueryData(AUTH_QUERY_KEYS.session, {
          isAuthenticated: false,
          redirectTo: window.location.pathname
        });

        return Promise.reject(error);
      }
    } catch (_refreshError) {
      await clearAuthState();
      return Promise.reject(error);
    }
  }
);

(() => {
  const params = new URLSearchParams(window.location.search);
  const tokenParam = params.get('token');

  if (tokenParam) {
    inMemoryToken = tokenParam;
    window.history.replaceState({}, document.title, window.location.pathname);
  }
})();

export default axiosInstance;
import { format as formatDateFns } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Formata uma data para exibição
 * @param date Data a ser formatada
 * @param formatStr Formato opcional (padrão: dd/MM/yyyy)
 * @returns String formatada
 */
export function formatDate(date: Date | string | null | undefined, formatStr = 'dd/MM/yyyy'): string {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return formatDateFns(dateObj, formatStr, { locale: ptBR });
  } catch (error) {
    console.error('Erro ao formatar data:', error);
    return String(date);
  }
}

/**
 * Formata uma data e hora para exibição
 * @param date Data a ser formatada
 * @param formatStr Formato opcional (padrão: dd/MM/yyyy HH:mm)
 * @returns String formatada
 */
export function formatDateTime(date: Date | string | null | undefined, formatStr = 'dd/MM/yyyy HH:mm'): string {
  return formatDate(date, formatStr);
}

/**
 * Formata uma hora para exibição
 * @param date Data a ser formatada
 * @param formatStr Formato opcional (padrão: HH:mm)
 * @returns String formatada
 */
export function formatTime(date: Date | string | null | undefined, formatStr = 'HH:mm'): string {
  return formatDate(date, formatStr);
}

/**
 * Verifica se uma data é válida
 * @param date Data a ser verificada
 * @returns true se a data for válida, false caso contrário
 */
export function isValidDate(date: any): boolean {
  if (!date) return false;
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj instanceof Date && !isNaN(dateObj.getTime());
}

/**
 * Calcula a idade a partir de uma data de nascimento
 * @param birthDate Data de nascimento
 * @returns Idade em anos
 */
export function calculateAge(birthDate: Date | string | null | undefined): number | null {
  if (!birthDate) return null;
  
  try {
    const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
    const today = new Date();
    
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  } catch (error) {
    console.error('Erro ao calcular idade:', error);
    return null;
  }
}

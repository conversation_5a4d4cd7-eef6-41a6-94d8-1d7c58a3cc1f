import { useState, useCallback, useRef } from 'react';

export interface FloatingWindowData {
  id: string;
  type: string;
  title: string;
  data: Record<string, unknown>;
}

export interface FloatingWindow {
  id: string;
  type: string;
  title: string;
  data: Record<string, unknown>;
  isMinimized: boolean;
  isFullscreen: boolean;
  zIndex: number;
  createdAt: Date;
}

export function useFloatingWindows() {
  const [windows, setWindows] = useState<FloatingWindow[]>([]);
  const [focusedWindowId, setFocusedWindowId] = useState<string | null>(null);
  const nextZIndex = useRef(1000);

  const getNextZIndex = useCallback(() => {
    return nextZIndex.current++;
  }, []);

  const openWindow = useCallback((windowData: FloatingWindowData) => {
    const existingWindow = windows.find(w => w.id === windowData.id);

    if (existingWindow) {
      setWindows(prev => prev.map(w =>
        w.id === windowData.id
          ? { ...w, isMinimized: false, zIndex: getNextZIndex() }
          : w
      ));
      setFocusedWindowId(windowData.id);
      return;
    }

    const newWindow: FloatingWindow = {
      id: windowData.id,
      type: windowData.type,
      title: windowData.title,
      data: windowData.data,
      isMinimized: false,
      isFullscreen: false,
      zIndex: getNextZIndex(),
      createdAt: new Date()
    };

    setWindows(prev => {
      const activeWindows = prev.filter(w => !w.isMinimized);

      // Se já temos 2 janelas abertas, minimizar a mais antiga
      if (activeWindows.length >= 2) {
        const oldestWindow = activeWindows.reduce((oldest, current) =>
          current.createdAt < oldest.createdAt ? current : oldest
        );

        return prev.map(w =>
          w.id === oldestWindow.id
            ? { ...w, isMinimized: true }
            : w
        ).concat(newWindow);
      }

      return [...prev, newWindow];
    });

    setFocusedWindowId(windowData.id);
  }, [windows, getNextZIndex]);

  const closeWindow = useCallback((windowId: string) => {
    setWindows(prev => prev.filter(w => w.id !== windowId));
    setFocusedWindowId(prev => prev === windowId ? null : prev);
  }, []);

  const minimizeWindow = useCallback((windowId: string) => {
    setWindows(prev => prev.map(w => 
      w.id === windowId 
        ? { ...w, isMinimized: true }
        : w
    ));
    setFocusedWindowId(prev => prev === windowId ? null : prev);
  }, []);

  const maximizeWindow = useCallback((windowId: string) => {
    setWindows(prev => {
      const activeWindows = prev.filter(w => !w.isMinimized && w.id !== windowId);

      // Se já temos 2 janelas abertas, minimizar a mais antiga
      if (activeWindows.length >= 2) {
        const oldestWindow = activeWindows.reduce((oldest, current) =>
          current.createdAt < oldest.createdAt ? current : oldest
        );

        return prev.map(w => {
          if (w.id === oldestWindow.id) {
            return { ...w, isMinimized: true };
          }
          if (w.id === windowId) {
            return { ...w, isMinimized: false, isFullscreen: false, zIndex: getNextZIndex() };
          }
          return w;
        });
      }

      return prev.map(w =>
        w.id === windowId
          ? { ...w, isMinimized: false, isFullscreen: false, zIndex: getNextZIndex() }
          : w
      );
    });

    setFocusedWindowId(windowId);
  }, [getNextZIndex]);

  const toggleFullscreen = useCallback((windowId: string) => {
    setWindows(prev => prev.map(w => 
      w.id === windowId 
        ? { ...w, isFullscreen: !w.isFullscreen, zIndex: getNextZIndex() }
        : w
    ));
    setFocusedWindowId(windowId);
  }, [getNextZIndex]);

  const focusWindow = useCallback((windowId: string) => {
    setWindows(prev => prev.map(w => 
      w.id === windowId 
        ? { ...w, zIndex: getNextZIndex() }
        : w
    ));
    setFocusedWindowId(windowId);
  }, [getNextZIndex]);

  const closeAllWindows = useCallback(() => {
    setWindows([]);
    setFocusedWindowId(null);
  }, []);

  const minimizeAllWindows = useCallback(() => {
    setWindows(prev => prev.map(w => ({ ...w, isMinimized: true })));
    setFocusedWindowId(null);
  }, []);

  const getWindow = useCallback((windowId: string) => {
    return windows.find(w => w.id === windowId);
  }, [windows]);

  const getWindowsByType = useCallback((type: string) => {
    return windows.filter(w => w.type === type);
  }, [windows]);

  const getMinimizedWindows = useCallback(() => {
    return windows
      .filter(w => w.isMinimized)
      .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime()); // Ordenar por ordem de criação
  }, [windows]);

  const getActiveWindows = useCallback(() => {
    return windows.filter(w => !w.isMinimized);
  }, [windows]);

  const hasWindows = windows.length > 0;
  const hasActiveWindows = getActiveWindows().length > 0;
  const hasMinimizedWindows = getMinimizedWindows().length > 0;

  return {
    windows,
    focusedWindowId,
    hasWindows,
    hasActiveWindows,
    hasMinimizedWindows,
    
    openWindow,
    closeWindow,
    minimizeWindow,
    maximizeWindow,
    toggleFullscreen,
    focusWindow,
    closeAllWindows,
    minimizeAllWindows,
    
    getWindow,
    getWindowsByType,
    getMinimizedWindows,
    getActiveWindows
  };
}


import { useState, useEffect } from 'react';

export function useUserPreferences<T>(key: string, defaultValue: T) {
  const [preferences, setPreferences] = useState<T>(() => {
    // Recuperar do localStorage na inicialização
    try {
      const storedValue = localStorage.getItem(key);
      return storedValue ? JSON.parse(storedValue) : defaultValue;
    } catch (error) {
      console.error('Erro ao carregar preferências:', error);
      return defaultValue;
    }
  });

  // Persistir alterações no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(key, JSON.stringify(preferences));
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
    }
  }, [preferences, key]);

  return [preferences, setPreferences] as const;
}
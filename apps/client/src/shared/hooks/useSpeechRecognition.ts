import { useState, useEffect, useRef } from 'react';

interface SpeechRecognitionOptions {
  onResult: (transcript: string) => void;
  onError?: (error: any) => void;
  onEnd?: () => void;
}

const getSpeechRecognition = () => {
  const S = window.SpeechRecognition || window.webkitSpeechRecognition;
  if (S) {
    return new S();
  }
  return null;
};

export function useSpeechRecognition({ onResult, onError, onEnd }: SpeechRecognitionOptions) {
  const [isListening, setIsListening] = useState(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  useEffect(() => {
    const recognition = getSpeechRecognition();
    if (!recognition) {
      console.warn('Web Speech API is not supported in this browser.');
      return;
    }

    recognition.continuous = true;
    recognition.lang = 'pt-BR';
    recognition.interimResults = false;

    recognition.onresult = (event: SpeechRecognitionEvent) => {
      const transcript = Array.from(event.results)
        .map((result: SpeechRecognitionResult) => result[0])
        .map((result: SpeechRecognitionAlternative) => result.transcript)
        .join('');
      onResult(transcript);
    };

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error', event.error);
      if (onError) {
        onError(event.error);
      }
      setIsListening(false);
    };

    recognition.onend = () => {
      if (onEnd) {
        onEnd();
      }
      setIsListening(false);
    };

    recognitionRef.current = recognition;

    return () => {
      recognition.stop();
    };
  }, [onResult, onError, onEnd]);

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      try {
        recognitionRef.current.start();
        setIsListening(true);
      } catch (error) {
        console.error("Could not start speech recognition.", error);
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    }
  };

  return { isListening, startListening, stopListening };
} 
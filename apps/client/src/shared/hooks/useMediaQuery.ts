import { useState, useEffect } from 'react';

export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState<boolean>(() => {
    // Verificar se estamos no navegador
    if (typeof window === 'undefined') {
      return false;
    }

    return window.matchMedia(query).matches;
  });

  useEffect(() => {
    // Verificar se estamos no navegador
    if (typeof window === 'undefined') {
      return;
    }

    const mediaQuery = window.matchMedia(query);

    // Atualizar o estado quando a media query mudar
    const updateMatches = (e: MediaQueryListEvent) => {
      setMatches(e.matches);
    };

    // Adicionar event listener
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', updateMatches);
    } else {
      // Fallback para browsers mais antigos
      mediaQuery.addListener(updateMatches);
    }

    // Remover event listener
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', updateMatches);
      } else {
        // Fallback para browsers mais antigos
        mediaQuery.removeListener(updateMatches);
      }
    };
  }, [query]);

  return matches;
}
import { useState, useEffect, ReactNode } from 'react';
import { Button } from '@/shared/ui/button';
import {
  X,
  Minimize2,
  Maximize2,
  Square
} from 'lucide-react';
import { cn } from '@/shared/lib/utils';

export interface FloatingWindowProps {
  id: string;
  title: ReactNode;
  isMinimized: boolean;
  isFullscreen: boolean;
  zIndex: number;
  minimizedIndex?: number; // Posição na lista de minimizadas
  windowIndex?: number; // Posição na lista de janelas ativas (para empilhamento)
  onMinimize: () => void;
  onMaximize: () => void;
  onFullscreen: () => void;
  onClose: () => void;
  onFocus: () => void;
  children: ReactNode;
  headerContent?: ReactNode;
  footerContent?: ReactNode;
  minimizedContent?: ReactNode;
  className?: string;
}

export function FloatingWindow({
  id,
  title,
  isMinimized,
  isFullscreen,
  zIndex,
  minimizedIndex = 0,
  windowIndex = 0,
  onMinimize,
  onMaximize,
  onFullscreen,
  onClose,
  onFocus,
  children,
  headerContent,
  footerContent,
  minimizedContent,
  className
}: FloatingWindowProps) {
  
  const windowWidth = 800; 
  const windowHeight = 600; 
  const spacing = 8; 

  const fixedPosition = {
    bottom: 80, 
    right: 16 + (windowIndex * (windowWidth + spacing)),
    width: windowWidth,
    height: windowHeight
  };

  
  const minimizedTabWidth = 260; 
  const minimizedTabHeight = 60; 
  const minimizedTabSpacing = 8;
  const minimizedPosition = {
    right: 16 + (minimizedIndex * (minimizedTabWidth + minimizedTabSpacing)),
    bottom: 16,
    width: minimizedTabWidth,
    height: minimizedTabHeight
  };

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && !isMinimized) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => { document.removeEventListener('keydown', handleEscape); };
  }, [isMinimized, onClose]);

  if (isMinimized) {
    return (
      <div
        className="fixed bg-card border rounded-lg shadow-lg cursor-pointer hover:shadow-xl transition-shadow p-3 flex items-center"
        style={{
          zIndex,
          right: minimizedPosition.right,
          bottom: minimizedPosition.bottom,
          width: minimizedPosition.width,
          height: minimizedPosition.height
        }}
        onClick={onMaximize}
      >
        <div className="w-full overflow-hidden">
          {minimizedContent ?? (
            <div className="flex items-center gap-2">
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate text-card-foreground">
                  {typeof title === 'string' ? title : 'Janela'}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "fixed bg-background border shadow-lg overflow-hidden transition-all duration-200",
        isFullscreen ? "inset-0 rounded-none" : "rounded-lg",
        className
      )}
      style={{
        zIndex,
        ...(isFullscreen ? {
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          width: '100vw',
          height: '100vh'
        } : {
          bottom: fixedPosition.bottom,
          right: fixedPosition.right,
          width: fixedPosition.width,
          height: fixedPosition.height
        })
      }}
      onClick={onFocus}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {typeof title === 'string' ? (
            <h2 className="font-semibold text-base truncate text-foreground">{title}</h2>
          ) : (
            title
          )}
          {headerContent}
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={onMinimize}
            className="h-7 w-7 p-0"
            title="Minimizar"
          >
            <Minimize2 className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={isFullscreen ? onMaximize : onFullscreen}
            className="h-7 w-7 p-0"
            title={isFullscreen ? "Restaurar" : "Tela cheia"}
          >
            {isFullscreen ? <Square className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-7 w-7 p-0"
            title="Fechar"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Body */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>

      {/* Footer */}
      {footerContent && (
        <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 p-4">
          {footerContent}
        </div>
      )}
    </div>
  );
}

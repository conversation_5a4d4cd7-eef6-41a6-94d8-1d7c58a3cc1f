import React from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Highlight from '@tiptap/extension-highlight';
import { TextStyle } from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import { Button } from "@/shared/ui/button";
import { Toggle } from "@/shared/ui/toggle";
import { cn } from "@/shared/lib/utils";
import {
  Bold,
  Italic,
  UnderlineIcon,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Table as TableIcon,
  Undo,
  Redo,
  Heading1,
  Heading2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Type
} from 'lucide-react';

interface SimpleRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  onEditorReady?: (editor: unknown) => void;
}

export function SimpleRichTextEditor({
  value,
  onChange,
  placeholder = "Digite suas notas aqui...",
  className,
  minHeight = "300px",
  onEditorReady
}: SimpleRichTextEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      TextStyle,
      Color,
      Highlight.configure({
        multicolor: true,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: value || "",
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange(html);
    },
    onCreate: ({ editor }) => {
      if (onEditorReady) {
        onEditorReady({ editor });
      }
    },
  });

  if (!editor) {
    return null;
  }

  const isBold = editor.isActive('bold');
  const isItalic = editor.isActive('italic');
  const isUnderline = editor.isActive('underline');
  const isBulletList = editor.isActive('bulletList');
  const isOrderedList = editor.isActive('orderedList');
  const isAlignLeft = editor.isActive({ textAlign: 'left' });
  const isAlignCenter = editor.isActive({ textAlign: 'center' });
  const isAlignRight = editor.isActive({ textAlign: 'right' });
  const isH1 = editor.isActive('heading', { level: 1 });
  const isH2 = editor.isActive('heading', { level: 2 });
  const isH3 = editor.isActive('heading', { level: 3 });
  const isHighlight = editor.isActive('highlight');

  return (
    <div className={cn("border rounded-md overflow-hidden bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700", className)}>
      {/* Toolbar */}
      <div className="bg-muted/50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700 p-2 flex flex-wrap items-center gap-1">
        {/* Títulos */}
        <div className="flex items-center border-r pr-2 mr-2">
          <Toggle
            size="sm"
            pressed={isH1}
            onPressedChange={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            aria-label="Título 1"
          >
            <Heading1 className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isH2}
            onPressedChange={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            aria-label="Título 2"
          >
            <Heading2 className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isH3}
            onPressedChange={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
            aria-label="Título 3"
          >
            <Heading3 className="h-4 w-4" />
          </Toggle>
        </div>

        {/* Formatação de texto */}
        <div className="flex items-center border-r pr-2 mr-2">
          <Toggle
            size="sm"
            pressed={isBold}
            onPressedChange={() => editor.chain().focus().toggleBold().run()}
            aria-label="Negrito"
          >
            <Bold className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isItalic}
            onPressedChange={() => editor.chain().focus().toggleItalic().run()}
            aria-label="Itálico"
          >
            <Italic className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isUnderline}
            onPressedChange={() => editor.chain().focus().toggleUnderline().run()}
            aria-label="Sublinhado"
          >
            <UnderlineIcon className="h-4 w-4" />
          </Toggle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHighlight({ color: '#fef08a' }).run()}
            className="h-8 px-2"
            title="Destacar amarelo"
          >
            <Highlighter className="h-4 w-4 text-yellow-500" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHighlight({ color: '#fecaca' }).run()}
            className="h-8 px-2"
            title="Destacar vermelho"
          >
            <Highlighter className="h-4 w-4 text-red-300" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHighlight({ color: '#bfdbfe' }).run()}
            className="h-8 px-2"
            title="Destacar azul"
          >
            <Highlighter className="h-4 w-4 text-blue-300" />
          </Button>
        </div>

        {/* Cores */}
        <div className="flex items-center border-r pr-2 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setColor('#ef4444').run()}
            className="h-8 px-2"
            title="Texto vermelho"
          >
            <Type className="h-4 w-4 text-red-500" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setColor('#3b82f6').run()}
            className="h-8 px-2"
            title="Texto azul"
          >
            <Type className="h-4 w-4 text-blue-500" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setColor('#10b981').run()}
            className="h-8 px-2"
            title="Texto verde"
          >
            <Type className="h-4 w-4 text-green-500" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().unsetColor().run()}
            className="h-8 px-2"
            title="Remover cor"
          >
            <Palette className="h-4 w-4" />
          </Button>
        </div>

        {/* Listas */}
        <div className="flex items-center border-r pr-2 mr-2">
          <Toggle
            size="sm"
            pressed={isBulletList}
            onPressedChange={() => editor.chain().focus().toggleBulletList().run()}
            aria-label="Lista com marcadores"
          >
            <List className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isOrderedList}
            onPressedChange={() => editor.chain().focus().toggleOrderedList().run()}
            aria-label="Lista numerada"
          >
            <ListOrdered className="h-4 w-4" />
          </Toggle>
        </div>

        {/* Alinhamento */}
        <div className="flex items-center border-r pr-2 mr-2">
          <Toggle
            size="sm"
            pressed={isAlignLeft}
            onPressedChange={() => editor.chain().focus().setTextAlign('left').run()}
            aria-label="Alinhar à esquerda"
          >
            <AlignLeft className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isAlignCenter}
            onPressedChange={() => editor.chain().focus().setTextAlign('center').run()}
            aria-label="Centralizar"
          >
            <AlignCenter className="h-4 w-4" />
          </Toggle>
          <Toggle
            size="sm"
            pressed={isAlignRight}
            onPressedChange={() => editor.chain().focus().setTextAlign('right').run()}
            aria-label="Alinhar à direita"
          >
            <AlignRight className="h-4 w-4" />
          </Toggle>
        </div>

        {/* Tabela */}
        <div className="flex items-center border-r pr-2 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
            className="h-8 px-2"
            title="Inserir tabela"
          >
            <TableIcon className="h-4 w-4" />
          </Button>
        </div>

        {/* Undo/Redo */}
        <div className="flex items-center ml-auto">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            className="h-8 px-2"
            title="Desfazer"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            className="h-8 px-2"
            title="Refazer"
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Editor */}
      <EditorContent
        editor={editor}
        className="prose prose-sm dark:prose-invert max-w-none p-4 focus-visible:outline-none bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
        style={{ minHeight }}
      />
    </div>
  );
}

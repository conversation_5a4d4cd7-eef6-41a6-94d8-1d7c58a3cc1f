import { z } from 'zod';


export const contactRelationshipTypes = [
  'parent',
  'guardian',
  'relative',
  'school',
  'professional',
  'other'
] as const;

export type ContactRelationshipType = typeof contactRelationshipTypes[number];


// Schema base para Contact (reflete a tabela 'contacts' unificada)
export const contactSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid().optional().nullable(), // Adicionado, pode ser null se não pertencer a um usuário? (Verificar lógica backend)
  name: z.string().min(3, { message: "Nome deve ter pelo menos 3 caracteres" }),
  relationship_type: z.string(), // Usar string genérica ou enum mais amplo? Manter string por enquanto.
  phone: z.string().optional().nullable(),
  email: z.string().email({ message: "Email inválido" }).optional().nullable().or(z.literal("")),
  address: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  // Campos adicionados da fusão
  cpf: z.string().optional().nullable(), // Adicionar validação de CPF se necessário no frontend
  rg: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  date_of_birth: z.string().optional().nullable(), // String para input date 'YYYY-MM-DD'
  gender: z.string().optional().nullable(),
  marital_status: z.string().optional().nullable(),
  ethnicity: z.string().optional().nullable(),
  nationality: z.string().optional().nullable(),
  naturalness: z.string().optional().nullable(),
  occupation: z.string().optional().nullable(),
  is_active: z.boolean().optional().nullable(),
    // Campos de vínculo (adicionados pela query/serviço, não da tabela direta)
    patient_id: z.string().uuid().optional().nullable(),
    role: z.string().optional().nullable(), // 'guardian' ou 'other'
    // Campo adicionado pelo backend para contagem de vínculos secundários
    linked_patient_count: z.number().optional().default(0), // Adicionar contagem
});

// Schema para criar um novo contato (omitir campos gerados/de vínculo)
export const createContactSchema = contactSchema.omit({
  id: true,
  user_id: true,
  patient_id: true,
  role: true,
  linked_patient_count: true, // Omitir contagem na criação
});


export const updateContactSchema = createContactSchema.partial();


export type Contact = z.infer<typeof contactSchema>;
export type CreateContactData = z.infer<typeof createContactSchema>;
export type UpdateContactData = z.infer<typeof updateContactSchema>;

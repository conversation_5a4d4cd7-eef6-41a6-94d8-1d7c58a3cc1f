import { createContext, useContext, ReactNode } from 'react';
import { useFloatingWindows, FloatingWindowData } from '@/shared/hooks/useFloatingWindows';

interface FloatingWindowsContextType {
  openWindow: (data: FloatingWindowData) => void;
  closeWindow: (windowId: string) => void;
  minimizeWindow: (windowId: string) => void;
  maximizeWindow: (windowId: string) => void;
  toggleFullscreen: (windowId: string) => void;
  focusWindow: (windowId: string) => void;
  closeAllWindows: () => void;
  minimizeAllWindows: () => void;
  hasWindows: boolean;
  hasActiveWindows: boolean;
  hasMinimizedWindows: boolean;
  windows: ReturnType<typeof useFloatingWindows>['windows'];
  getWindow: ReturnType<typeof useFloatingWindows>['getWindow'];
  getWindowsByType: ReturnType<typeof useFloatingWindows>['getWindowsByType'];
  getMinimizedWindows: ReturnType<typeof useFloatingWindows>['getMinimizedWindows'];
}

const FloatingWindowsContext = createContext<FloatingWindowsContextType | null>(null);

export function FloatingWindowsProvider({ children }: { children: ReactNode }) {
  const windowsManager = useFloatingWindows();

  const contextValue: FloatingWindowsContextType = {
    openWindow: windowsManager.openWindow,
    closeWindow: windowsManager.closeWindow,
    minimizeWindow: windowsManager.minimizeWindow,
    maximizeWindow: windowsManager.maximizeWindow,
    toggleFullscreen: windowsManager.toggleFullscreen,
    focusWindow: windowsManager.focusWindow,
    closeAllWindows: windowsManager.closeAllWindows,
    minimizeAllWindows: windowsManager.minimizeAllWindows,
    hasWindows: windowsManager.hasWindows,
    hasActiveWindows: windowsManager.hasActiveWindows,
    hasMinimizedWindows: windowsManager.hasMinimizedWindows,
    windows: windowsManager.windows,
    getWindow: windowsManager.getWindow,
    getWindowsByType: windowsManager.getWindowsByType,
    getMinimizedWindows: windowsManager.getMinimizedWindows
  };

  return (
    <FloatingWindowsContext.Provider value={contextValue}>
      {children}
    </FloatingWindowsContext.Provider>
  );
}

export function useFloatingWindowsContext() {
  const context = useContext(FloatingWindowsContext);
  if (!context) {
    throw new Error('useFloatingWindowsContext must be used within FloatingWindowsProvider');
  }
  return context;
}

import { forwardRef, useState, useEffect } from 'react';
import { Input } from '@/shared/ui/input';
import { ComponentProps } from 'react';

interface CurrencyInputProps extends Omit<ComponentProps<"input">, 'value' | 'onChange'> {
  value: string | number;
  onChange: (value: string) => void;
  locale?: string;
  currency?: string;
}

export const CurrencyInput = forwardRef<HTMLInputElement, CurrencyInputProps>(
  ({ value, onChange, locale = 'pt-BR', currency = 'BRL', ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState('');

    // Formatador para exibição formatada com símbolo da moeda
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    });

    // Formatador apenas para valores numéricos (sem símbolo)
    const numberFormatter = new Intl.NumberFormat(locale, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    // Atualizar o valor de exibição quando o valor real mudar
    useEffect(() => {
      if (value !== undefined && value !== null && value !== '') {
        const numericValue = typeof value === 'string' ? parseFloat(value) : value;
        if (!isNaN(numericValue)) {
          setDisplayValue(numberFormatter.format(numericValue));
        } else {
          setDisplayValue('');
        }
      } else {
        setDisplayValue('');
      }
    }, [value, numberFormatter]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      
      // Remove todos os caracteres não numéricos (exceto o separador decimal)
      const decimalSeparator = Intl.NumberFormat(locale).format(1.1).replace(/\d/g, '');
      const numericString = inputValue
        .replace(new RegExp(`[^0-9${decimalSeparator}]`, 'g'), '')
        .replace(decimalSeparator, '.');
      
      // Se o valor estiver vazio após limpeza, defina como vazio
      if (!numericString || numericString === '.') {
        onChange('');
        return;
      }
      
      // Converter para número e formatá-lo corretamente
      const numericValue = parseFloat(numericString);
      if (!isNaN(numericValue)) {
        // Atualiza o estado interno
        setDisplayValue(numberFormatter.format(numericValue));
        // Notifica o componente pai com o valor numérico como string
        onChange(numericValue.toFixed(2));
      }
    };

    // Quando o input perde o foco, formata o valor corretamente
    const handleBlur = () => {
      if (displayValue) {
        try {
          // Extrair valor numérico do formato atual
          const decimalSeparator = Intl.NumberFormat(locale).format(1.1).replace(/\d/g, '');
          const numericString = displayValue.replace(new RegExp(`[^0-9${decimalSeparator}]`, 'g'), '').replace(decimalSeparator, '.');
          const numericValue = parseFloat(numericString);
          
          if (!isNaN(numericValue)) {
            // Formata e atualiza novamente para garantir consistência
            setDisplayValue(numberFormatter.format(numericValue));
            onChange(numericValue.toFixed(2));
          }
        } catch (e) {
          // Em caso de erro, não faz nada
        }
      }
    };

    return (
      <div className="relative">
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
          {currency === 'BRL' ? 'R$' : '$'}
        </div>
        <Input
          ref={ref}
          value={displayValue}
          onChange={handleChange}
          onBlur={handleBlur}
          className="pl-8"
          inputMode="decimal"
          {...props}
        />
      </div>
    );
  }
);

CurrencyInput.displayName = 'CurrencyInput';

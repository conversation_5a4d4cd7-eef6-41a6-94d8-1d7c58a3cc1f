import { Input } from '@/shared/ui/input';
import { useState, useEffect, forwardRef, ChangeEvent, ComponentPropsWithoutRef } from 'react';

interface MaskedInputProps extends Omit<ComponentPropsWithoutRef<typeof Input>, 'onChange'> {
  mask: string;
  onChange?: (value: string) => void;
  onChangeRaw?: (rawValue: string) => void;
  defaultValue?: string;
  value?: string;
}

/**
 * Componente de input com máscara para CPF, telefones, datas, etc.
 * 
 * @param mask String contendo a máscara. Use 9 para números, A para letras e * para alfanuméricos
 * @param onChange Função chamada com o valor formatado quando o input muda
 * @param onChangeRaw Função chamada com o valor sem formatação quando o input muda
 * @param defaultValue Valor inicial do input (não formatado)
 * @param value Valor controlado do input (não formatado)
 */
export const MaskedInput = forwardRef<HTMLInputElement, MaskedInputProps>(
  ({ mask, onChange, onChangeRaw, defaultValue, value, ...rest }, ref) => {
    const [inputValue, setInputValue] = useState('');
    
    // Função para aplicar máscara ao valor
    const applyMask = (value: string): string => {
      let maskedValue = '';
      let valueIndex = 0;
      
      // Remove caracteres que não são dígitos, letras ou alfanuméricos conforme a máscara
      let rawValue = value;
      
      // Para cada caractere na máscara
      for (let i = 0; i < mask.length && valueIndex < rawValue.length; i++) {
        const maskChar = mask[i];
        
        // Se o caractere da máscara for um caractere especial (9, A, *)
        if (['9', 'A', '*'].includes(maskChar)) {
          const valueChar = rawValue[valueIndex];
          
          // Verifica se o caractere do valor atende às regras da máscara
          const isValid = 
            (maskChar === '9' && /\d/.test(valueChar)) || // Apenas números
            (maskChar === 'A' && /[a-zA-Z]/.test(valueChar)) || // Apenas letras
            (maskChar === '*' && /[a-zA-Z0-9]/.test(valueChar)); // Alfanumérico
            
          if (isValid) {
            maskedValue += valueChar;
            valueIndex++;
          } else {
            // Se o caractere não é válido, tenta o próximo caractere do valor
            valueIndex++;
            i--; // Mantém a posição atual na máscara
            continue;
          }
        } else {
          // Se o caractere da máscara for um separador (ex: '.', '-', '/')
          maskedValue += maskChar;
        }
      }
      
      return maskedValue;
    };
    
    // Função para remover a máscara
    const removeMask = (value: string): string => {
      if (!value) return '';
      
      // Identifica quais caracteres na máscara são separadores (não 9, A ou *)
      const separators = Array.from(new Set(mask.split('').filter((char: string) => !['9', 'A', '*'].includes(char))));
      
      // Remove todos os separadores do valor
      let rawValue = value;
      separators.forEach((sep: string) => {
        rawValue = rawValue.split(sep).join('');
      });
      
      return rawValue;
    };
    
    // Efeito para aplicar a máscara quando o valor controlado muda externamente
    useEffect(() => {
      if (value !== undefined) {
        const maskedValue = applyMask(value);
        setInputValue(maskedValue);
      }
    }, [value, mask]);
    
    // Efeito para aplicar a máscara ao valor inicial
    useEffect(() => {
      if (defaultValue && !value) {
        const maskedValue = applyMask(defaultValue);
        setInputValue(maskedValue);
      }
    }, [defaultValue]);
    
    // Função para lidar com a mudança no input
    const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      const rawValue = removeMask(newValue);
      const maskedValue = applyMask(rawValue);
      
      setInputValue(maskedValue);
      
      // Chama os callbacks com os valores apropriados
      if (onChange) onChange(maskedValue);
      if (onChangeRaw) onChangeRaw(rawValue);
    };
    
    return (
      <Input 
        ref={ref}
        value={inputValue}
        onChange={handleChange}
        {...rest}
      />
    );
  }
);

MaskedInput.displayName = 'MaskedInput';

import { useState } from "react";
import { Plus, Users, Phone, Mail, UserPlus, Trash2, Link2 } from "lucide-react"; // Remover Calendar
import { Button } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Contact } from "@/shared/types/contact.schema";
import { EmptyState } from "@/shared/ui/empty-state";
import { cn } from "@/shared/lib/utils";

interface ContactListProps {
  contacts: Contact[];
  isCompact?: boolean;
  onSelect?: (contact: Contact) => void;
  onEdit?: (contact: Contact) => void;
  onDelete?: (contact: Contact) => void;
  onLinkToPatient?: (contact: Contact) => void; // Ação para vincular novo
  onManageLinks?: (contact: Contact) => void; // Nova ação para gerenciar links existentes
  onAddContact?: () => void;
  className?: string;
}

export function ContactList({
  contacts,
                              onSelect,
  onEdit,
  onDelete,
  onLinkToPatient,
  onManageLinks, // Receber nova prop
  onAddContact,
  className
}: ContactListProps) {
  const [selectedContactId, setSelectedContactId] = useState<string | null>(null);

  if (contacts.length === 0) {
    return (
      <EmptyState
        icon={<Users />}
        title="Nenhum contato encontrado"
        description="Você ainda não possui contatos cadastrados."
        action={
          <Button onClick={onAddContact}>
            <Plus className="mr-2 h-4 w-4" />
            Adicionar Contato
          </Button>
        }
      />
    );
  }

  function getRelationshipLabel(type: string): string {
    const relationshipTypes: Record<string, string> = {
      'parent': 'Mãe/Pai',
      'guardian': 'Responsável Legal',
      'relative': 'Familiar',
      'school': 'Escola',
      'professional': 'Profissional',
      'other': 'Outro'
    };
    return relationshipTypes[type] || type;
  }

  return (
    <div className={cn("space-y-3", className)}>
      {contacts.map((contact) => (
        <div
          key={contact.id}
          className={cn(
            "flex items-center justify-between p-4 rounded-lg border hover:bg-accent/30 transition-colors",
            contact.id === selectedContactId && "bg-accent/30"
          )}
          onClick={() => {
            setSelectedContactId(contact.id);
            if (onSelect) onSelect(contact);
          }}
        >
          <div className="flex-1 min-w-0">
            <h3 className="font-medium">{contact.name}</h3>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline">
                {getRelationshipLabel(contact.relationship_type)}
              </Badge>
            </div>
            <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
              {contact.phone && (
                <div className="flex items-center">
                  <Phone className="h-3.5 w-3.5 mr-1" />
                  <span>{contact.phone}</span>
                </div>
              )}
              {contact.email && (
                <div className="flex items-center">
                  <Mail className="h-3.5 w-3.5 mr-1" />
                  <span>{contact.email}</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center gap-1">
            {/* Botão Vincular/Gerenciar Vínculos */}
            {/* Usar linked_patient_count para decidir qual botão mostrar */}
            {(contact.linked_patient_count ?? 0) > 0 ? (
              // Se já vinculado a pacientes secundários, mostrar "Gerenciar Vínculos"
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onManageLinks) onManageLinks(contact);
                }}
                className="text-xs"
                title="Gerenciar pacientes vinculados a este contato"
              >
                <Link2 className="h-3.5 w-3.5 mr-1.5" />
                Gerenciar Vínculos ({contact.linked_patient_count})
              </Button>
            ) : (
              // Se não vinculado (ou apenas como responsável, que é filtrado antes), mostrar "Vincular"
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  if (onLinkToPatient) onLinkToPatient(contact);
                }}
                className="text-xs"
                title="Vincular este contato a um paciente"
              >
                <UserPlus className="h-3.5 w-3.5 mr-1.5" />
                Vincular
              </Button>
            )}

            {/* Botão Editar */}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                if (onEdit) onEdit(contact);
              }}
              className="text-xs"
            >
              Editar
            </Button>
            {/* Botão Excluir */}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                if (onDelete) onDelete(contact);
              }}
              disabled={contact.relationship_type === 'guardian'} // Desabilitar se for responsável legal
              className="text-xs text-destructive hover:text-destructive hover:bg-destructive/10" // Estilo de perigo
              title={contact.relationship_type === 'guardian' ? "Responsáveis legais não podem ser excluídos diretamente." : "Excluir contato"}
            >
              <Trash2 className="h-3.5 w-3.5 mr-1" />
              Excluir
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
}

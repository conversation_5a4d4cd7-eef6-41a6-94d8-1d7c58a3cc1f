import React, { LazyExoticComponent, ComponentType, useState, lazy, Suspense } from "react";
import { isDevelopment } from "@/shared/lib/env";
import { Button } from '@/shared/ui/button';
import { Settings } from 'lucide-react';

export const ReactHookFormDevelopmentTools = !isDevelopment
  ? (): null => null
  : React.lazy(() =>
      import("@hookform/devtools").then((result) => ({
        default: result.DevTool,
      }))
    );

const NullComponent = () => null;

type LazyComponent = LazyExoticComponent<ComponentType<object>>;

// Lazy load the TanStack Router devtools
const TanStackRouterDevtools =
  process.env.NODE_ENV === 'production'
    ? () => null // Render nothing in production
    : React.lazy(() =>
        import('@tanstack/router-devtools').then((res) => ({
          default: res.TanStackRouterDevtools,
        }))
      );

// Lazy load the TanStack Query devtools
const TanStackQueryDevtools =
  process.env.NODE_ENV === 'production'
    ? () => null // Render nothing in production
    : React.lazy(() =>
        import('@tanstack/react-query-devtools').then((res) => ({
          default: res.ReactQueryDevtools,
        }))
      );

export const TanStackRouterDevelopmentTools: LazyComponent | (() => null) = !isDevelopment
  ? NullComponent
  : TanStackRouterDevtools;

export const TanStackReactQueryDevelopmentTools = !isDevelopment
  ? (): null => null
  : TanStackQueryDevtools;

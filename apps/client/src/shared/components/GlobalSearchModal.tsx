import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/shared/ui/dialog';
import { Input } from '@/shared/ui/input';
import { Search } from 'lucide-react';

interface GlobalSearchModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export function GlobalSearchModal({ isOpen, onOpenChange }: GlobalSearchModalProps) {
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Busca Global</DialogTitle>
        </DialogHeader>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Pesquisar pacientes, documentos, anotações..."
            className="pl-10 h-12 text-lg"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="mt-4 min-h-[300px] bg-muted/50 rounded-md flex items-center justify-center">
          <p className="text-muted-foreground">Resultados da busca aparecerão aqui.</p>
        </div>
      </DialogContent>
    </Dialog>
  );
} 
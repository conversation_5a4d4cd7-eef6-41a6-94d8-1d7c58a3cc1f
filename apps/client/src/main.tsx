import { createRoot } from "react-dom/client";
import "@/app/styles/index.css";
import "@/app/styles/timer-animations.css";
import { StrictMode }from "react";
import { App } from "./app/App";
import { setupDashboardMockApi } from "./features/dashboard/lib/dashboard.mock";

// Configurar mock API para desenvolvimento
setupDashboardMockApi();

const rootElement = document.getElementById('root')!

if (!rootElement.innerHTML) {
  const root = createRoot(rootElement)
  root.render(
    <StrictMode>
      <App />
    </StrictMode>,
  )
}

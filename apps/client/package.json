{"name": "client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write .", "typecheck": "tsc --noEmit -p tsconfig.app.json", "clean": "rm -rf node_modules dist"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.7", "@tailwindcss/vite": "^4.0.0", "@tanstack/react-form": "^1.14.1", "@tanstack/react-query": "^5.64.2", "@tanstack/react-router": "^1.97.14", "@tiptap/core": "^2.11.5", "@tiptap/extension-color": "^2.26.1", "@tiptap/extension-highlight": "^2.26.1", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-cell": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.22.0", "@tiptap/extension-underline": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/react-grid-layout": "^1.3.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lucide-react": "0.474.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.38.1", "react": "19.0.0", "react-day-picker": "^9.7.0", "react-dom": "19.0.0", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.54.2", "react-hotkeys-hook": "^5.1.0", "react-resizable": "^3.0.5", "react-tag-input": "^6.10.6", "sonner": "^2.0.6", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.14.0", "@hookform/devtools": "^4.3.3", "@tailwindcss/postcss": "^4.0.0", "@tanstack/eslint-plugin-query": "^5.64.2", "@tanstack/react-query-devtools": "^5.64.2", "@tanstack/router-devtools": "^1.97.14", "@tanstack/router-plugin": "^1.97.14", "@types/node": "22.10.9", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "eslint": "^9.14.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.4.47", "tailwind-merge": "^2.5.4", "tailwindcss": "4.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3", "typescript-eslint": "^8.13.0", "vite": "6.0.11", "zustand": "^5.0.3"}}
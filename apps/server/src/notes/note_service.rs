// src/notes/note_service.rs
use crate::config::database::DbPool;
use crate::errors::{ApiError, ApiResult};
use crate::notes::note_dto::{CreateNoteRequest, UpdateNoteRequest};
use crate::notes::note_model::{NewNote, Note};
use crate::notes::note_repository::{self, UpdateNoteData};
use uuid::Uuid;

// Função para criar uma nova nota
pub async fn create_note_service(
    pool: &DbPool,
    user_id: Uuid,
    payload: CreateNoteRequest,
) -> ApiResult<Note> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Validações adicionais podem ser feitas aqui (ex: verificar se patient_id/appointment_id pertencem ao usuário)

    let new_note = NewNote {
        user_id,
        content: &payload.content,
        patient_id: payload.patient_id,
        appointment_id: payload.appointment_id,
    };

    note_repository::create_note(new_note, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao criar nota: {}", e)))
}

// Função para buscar notas recentes
pub async fn get_recent_notes_service(
    pool: &DbPool,
    user_id: Uuid,
    limit: i64,
) -> ApiResult<Vec<Note>> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    note_repository::find_recent_notes_by_user(user_id, limit, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao buscar notas: {}", e)))
}

// Função para atualizar uma nota
pub async fn update_note_service(
    pool: &DbPool,
    user_id: Uuid,
    note_id: Uuid,
    payload: UpdateNoteRequest,
) -> ApiResult<Note> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Verificar se a nota pertence ao usuário antes de atualizar (opcional, mas bom)
    let _note = note_repository::find_note_by_id(note_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Nota".to_string()))?; // Usar ResourceNotFound

    // Validações adicionais (ex: verificar patient_id/appointment_id)

    let update_data = UpdateNoteData {
        content: payload.content,
        patient_id: payload.patient_id,
        appointment_id: payload.appointment_id,
    };

    note_repository::update_note(note_id, user_id, &update_data, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao atualizar nota: {}", e)))
}

// Função para deletar uma nota
pub async fn delete_note_service(
    pool: &DbPool,
    user_id: Uuid,
    note_id: Uuid,
) -> ApiResult<()> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    let num_deleted = note_repository::delete_note(note_id, user_id, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao deletar nota: {}", e)))?;

    if num_deleted == 0 {
        // Se delete retornou 0, significa que a nota não foi encontrada (ou não pertencia ao usuário)
        Err(ApiError::ResourceNotFound("Nota".to_string()))
    } else {
        Ok(())
    }
}

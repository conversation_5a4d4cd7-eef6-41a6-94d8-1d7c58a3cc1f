// src/notes/note_dto.rs
use serde::Deserialize;
use uuid::Uuid;

#[derive(Deserialize, Debug)]
pub struct CreateNoteRequest {
    pub content: String,
    pub patient_id: Option<Uuid>, 
    pub appointment_id: Option<Uuid>, 
}

#[derive(Deserialize, Debug)]
pub struct UpdateNoteRequest {
    pub content: Option<String>, 
    pub patient_id: Option<Option<Uuid>>, 
    pub appointment_id: Option<Option<Uuid>>, 
}


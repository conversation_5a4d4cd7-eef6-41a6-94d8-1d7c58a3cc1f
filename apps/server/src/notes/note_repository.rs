use crate::notes::note_model::{NewNote, Note};
use crate::schema::notes;
use diesel::prelude::*;
use diesel::result::QueryResult;
use uuid::Uuid;

pub fn create_note(new_note: NewNote, conn: &mut PgConnection) -> QueryResult<Note> {
    diesel::insert_into(notes::table)
        .values(&new_note)
        .returning(Note::as_returning())
        .get_result(conn)
}


pub fn find_recent_notes_by_user(
    user_id_val: Uuid,
    limit: i64,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Note>> {
    notes::table
        .filter(notes::user_id.eq(user_id_val))
        .filter(notes::patient_id.is_null())
        .filter(notes::appointment_id.is_null())
        .order_by(notes::updated_at.desc())
        .limit(limit)
        .select(Note::as_select())
        .load(conn)
}


pub fn find_note_by_id(
    note_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Note> {
    notes::table
        .filter(notes::id.eq(note_id_val))
        .filter(notes::user_id.eq(user_id_val))
        .select(Note::as_select())
        .first(conn)
}


#[derive(AsChangeset, Debug)]
#[diesel(table_name = notes)]
pub struct UpdateNoteData {
    pub content: Option<String>,
    pub patient_id: Option<Option<Uuid>>,
    pub appointment_id: Option<Option<Uuid>>,
}


pub fn update_note(
    note_id_val: Uuid,
    user_id_val: Uuid,
    update_data: &UpdateNoteData,
    conn: &mut PgConnection,
) -> QueryResult<Note> {
    diesel::update(
        notes::table
            .filter(notes::id.eq(note_id_val))
            .filter(notes::user_id.eq(user_id_val)),
    )
    .set(update_data)
    .returning(Note::as_returning())
    .get_result(conn)
}


pub fn delete_note(note_id_val: Uuid, user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<usize> {
    diesel::delete(
        notes::table
            .filter(notes::id.eq(note_id_val))
            .filter(notes::user_id.eq(user_id_val)),
    )
    .execute(conn)
}

use crate::auth::auth_middleware::AuthUser;
use crate::config::database::DbPool;
use crate::errors::ApiResult;
use crate::notes::note_dto::{CreateNoteRequest, UpdateNoteRequest};
use crate::notes::note_service;
use axum::{
    extract::{Extension, Path, Query},
    response::IntoResponse,
    Json, http::StatusCode,
};
use serde::Deserialize;
use uuid::Uuid;

#[derive(Deserialize)]
pub struct ListNotesParams {
    #[serde(default = "default_limit")]
    limit: i64,
}

fn default_limit() -> i64 {
    10 
}

// Handler para criar uma nova nota
pub async fn create_note_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(payload): Json<CreateNoteRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let created_note = note_service::create_note_service(&pool, user_id, payload).await?;
    Ok((StatusCode::CREATED, <PERSON><PERSON>(created_note)))
}

// Handler para listar notas recentes
pub async fn get_recent_notes_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Query(params): Query<ListNotesParams>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let notes = note_service::get_recent_notes_service(&pool, user_id, params.limit).await?;
    Ok(Json(notes))
}

// Handler para atualizar uma nota
pub async fn update_note_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(note_id): Path<Uuid>, 
    Json(payload): Json<UpdateNoteRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let updated_note = note_service::update_note_service(&pool, user_id, note_id, payload).await?;
    Ok(Json(updated_note))
}

// Handler para deletar uma nota
pub async fn delete_note_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(note_id): Path<Uuid>, 
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    note_service::delete_note_service(&pool, user_id, note_id).await?;
    Ok(StatusCode::NO_CONTENT) 
}

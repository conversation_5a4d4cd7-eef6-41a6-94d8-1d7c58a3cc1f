// src/notes/note_model.rs
use crate::schema::notes;
use chrono::NaiveDateTime;
use diesel::{Identifiable, Insertable, Queryable, Selectable};
use serde::Serialize;
use uuid::Uuid;

#[derive(Queryable, Selectable, Identifiable, Serialize, Debug)]
#[diesel(table_name = notes)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct Note {
    pub id: Uuid, 
    pub user_id: Uuid, 
    pub content: String,
    pub patient_id: Option<Uuid>, 
    pub appointment_id: Option<Uuid>, 
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Insertable, Debug)]
#[diesel(table_name = notes)]
pub struct NewNote<'a> {
    pub user_id: Uuid, 
    pub content: &'a str,
    pub patient_id: Option<Uuid>, 
    pub appointment_id: Option<Uuid>, 
}

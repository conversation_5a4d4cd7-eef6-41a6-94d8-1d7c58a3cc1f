use crate::config::database::DbPool;
use crate::db::models::document::{Document, NewDocument, UpdateDocument};
use crate::document::document_dto::{
    DocumentFilterParams, DocumentListResponse, DocumentResponse, DownloadUrlResponse,
    UpdateDocumentContentRequest, UpdateDocumentRequest, UploadDocumentMetadata,
};
use crate::document::document_repository;
use crate::errors::{ApiError, ApiResult};
use crate::s3_client::S3Storage;
use bytes::Bytes;
use chrono::Utc;
use diesel::PgConnection;
use sanitize_filename::sanitize;
use std::path::Path;
use std::time::Duration;
use uuid::Uuid;

// Converter Document para DocumentResponse
async fn document_to_response(
    document: Document,
    conn: &mut PgConnection,
) -> DocumentResponse {
    // Buscar nome do paciente se patient_id estiver presente
    let patient_name = if let Some(pid) = document.patient_id {
        document_repository::find_patient_name_by_id(pid, conn).ok()
    } else {
        None
    };

    // Buscar título do modelo se model_id estiver presente
    let model_name = if let Some(mid) = document.model_id {
        document_repository::find_model_title_by_id(mid, conn).ok()
    } else {
        None
    };

    DocumentResponse {
        id: document.id,
        title: document.title,
        filename: document.filename,
        mime_type: document.mime_type,
        size_bytes: document.size_bytes,
        patient_id: document.patient_id,
        patient_name,
        model_id: document.model_id,
        model_name,
        created_at: document.created_at,
        updated_at: document.updated_at,
        created_by: document.created_by,
        updated_by: document.updated_by,
    }
}

// Gerar chave S3 para o documento
fn generate_s3_key(
    user_id: Uuid,
    patient_id: Option<Uuid>,
    filename: &str,
    document_id: &Uuid,
) -> String {
    let sanitized_filename = sanitize(filename);
    let extension = Path::new(&sanitized_filename)
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("");

    let patient_part = if let Some(pid) = patient_id {
        format!("patient_{}/", pid)
    } else {
        "".to_string()
    };

    format!(
        "user_{}/{}{}_{}.{}",
        user_id,
        patient_part,
        document_id,
        sanitized_filename.trim_end_matches(&format!(".{}", extension)),
        extension
    )
}

// Upload de documento
pub async fn upload_document(
    pool: &DbPool,
    user_id: Uuid,
    metadata: UploadDocumentMetadata,
    file_bytes: Bytes,
    filename: String,
    mime_type: String,
) -> ApiResult<DocumentResponse> {
    // Inicializar cliente S3
    let s3_client = S3Storage::new()
        .await
        .map_err(ApiError::InternalServerError)?;

    // Gerar UUID para o documento
    let document_id = Uuid::new_v4();

    // Gerar chave S3
    let s3_key = generate_s3_key(user_id, metadata.patient_id, &filename, &document_id);

    // Fazer upload para o S3
    s3_client
        .upload_object(&s3_key, file_bytes, &mime_type)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao fazer upload: {}", e)))?;

    // Obter conexão com o banco de dados
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Criar registro no banco de dados
    let new_document = NewDocument {
        user_id,
        patient_id: metadata.patient_id,
        model_id: metadata.model_id,
        title: metadata.title,
        filename,
        s3_key,
        mime_type: Some(mime_type),
        size_bytes: None, // Poderia ser calculado a partir de file_bytes.len()
        created_by: user_id,
        updated_by: user_id,
    };

    let document = document_repository::create_document(&new_document, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao salvar documento: {}", e)))?;

    // Converter para resposta
    let response = document_to_response(document, &mut conn).await;
    Ok(response)
}

// Listar documentos com filtros e paginação
pub async fn list_documents(
    pool: &DbPool,
    user_id: Uuid,
    filter_params: DocumentFilterParams,
) -> ApiResult<DocumentListResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Configurar paginação
    let page_size = filter_params.page_size.unwrap_or(20);
    let page = filter_params.page.unwrap_or(0);
    let limit = Some(page_size);
    let offset = Some(page * page_size);

    // Buscar documentos com base nos filtros
    let documents = if let Some(patient_id) = filter_params.patient_id {
        // Filtrar por paciente
        document_repository::find_documents_by_patient_paginated(
            user_id,
            patient_id,
            limit,
            offset,
            &mut conn,
        )
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao buscar documentos: {}", e)))?
    } else if let Some(title) = &filter_params.title {
        // Filtrar por título
        document_repository::find_documents_by_title_paginated(
            user_id,
            title,
            limit,
            offset,
            &mut conn,
        )
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao buscar documentos: {}", e)))?
    } else {
        // Sem filtros específicos
        document_repository::find_documents_by_user_paginated(user_id, limit, offset, &mut conn)
            .map_err(|e| ApiError::InternalServerError(format!("Erro ao buscar documentos: {}", e)))?
    };

    // Contar total de documentos
    let total_count = if let Some(patient_id) = filter_params.patient_id {
        document_repository::count_documents_by_patient(user_id, patient_id, &mut conn)
            .map_err(|e| ApiError::InternalServerError(format!("Erro ao contar documentos: {}", e)))?
    } else if let Some(title) = &filter_params.title {
        document_repository::count_documents_by_title(user_id, title, &mut conn)
            .map_err(|e| ApiError::InternalServerError(format!("Erro ao contar documentos: {}", e)))?
    } else {
        document_repository::count_documents_by_user(user_id, &mut conn)
            .map_err(|e| ApiError::InternalServerError(format!("Erro ao contar documentos: {}", e)))?
    };

    // Converter documentos para respostas
    let mut responses = Vec::new();
    for document in documents {
        responses.push(document_to_response(document, &mut conn).await);
    }

    Ok(DocumentListResponse {
        documents: responses,
        total_count,
    })
}

// Obter URL de download para um documento
pub async fn get_document_download_url(
    pool: &DbPool,
    user_id: Uuid,
    document_id: Uuid,
    duration_secs: u64,
) -> ApiResult<DownloadUrlResponse> {
    // Obter conexão com o banco de dados
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Buscar documento
    let document = document_repository::find_document_by_id(document_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Documento".to_string()))?;

    // Inicializar cliente S3
    let s3_client = S3Storage::new()
        .await
        .map_err(ApiError::InternalServerError)?;

    // Gerar URL pré-assinada
    let download_url = s3_client
        .get_presigned_url(&document.s3_key, Duration::from_secs(duration_secs))
        .await
        .map_err(ApiError::InternalServerError)?;

    Ok(DownloadUrlResponse {
        download_url,
        filename: document.filename,
        mime_type: document.mime_type,
        expires_in_seconds: duration_secs,
    })
}

// Atualizar metadados do documento
pub async fn update_document_metadata(
    pool: &DbPool,
    user_id: Uuid,
    document_id: Uuid,
    update_req: UpdateDocumentRequest,
) -> ApiResult<DocumentResponse> {
    // Obter conexão com o banco de dados
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Verificar se o documento existe e pertence ao usuário
    let _ = document_repository::find_document_by_id(document_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Documento".to_string()))?;

    // Preparar dados para atualização
    let update_data = UpdateDocument {
        title: update_req.title,
        patient_id: update_req.patient_id,
        model_id: update_req.model_id,
        updated_by: Some(user_id),
        updated_at: Some(Utc::now().naive_utc()),
    };

    // Atualizar documento
    let updated_document =
        document_repository::update_document(document_id, user_id, &update_data, &mut conn)
            .map_err(|e| {
                ApiError::InternalServerError(format!("Erro ao atualizar documento: {}", e))
            })?;

    // Converter para resposta
    let response = document_to_response(updated_document, &mut conn).await;
    Ok(response)
}

// Atualizar conteúdo do documento
pub async fn update_document_content(
    pool: &DbPool,
    user_id: Uuid,
    document_id: Uuid,
    update_req: UpdateDocumentContentRequest,
) -> ApiResult<DocumentResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // 1. Buscar o documento para garantir que ele existe e pertence ao usuário
    let document = document_repository::find_document_by_id(document_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Documento".to_string()))?;

    // 2. Opcional: Verificar se o tipo de documento permite edição de conteúdo (ex: HTML, texto)
    if document.mime_type.as_deref() != Some("text/html") {
        return Err(ApiError::BadRequest(
            "Este tipo de documento não pode ter seu conteúdo editado.".to_string(),
        ));
    }

    // 3. Inicializar cliente S3
    let s3_client = S3Storage::new()
        .await
        .map_err(ApiError::InternalServerError)?;

    // 4. Fazer upload do novo conteúdo, substituindo o objeto existente
    s3_client
        .upload_object(
            &document.s3_key,
            Bytes::from(update_req.content),
            document.mime_type.as_deref().unwrap_or("text/plain"),
        )
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao atualizar conteúdo: {}", e)))?;

    // 5. Atualizar o 'updated_at' no banco de dados
    let updated_document = document_repository::touch_document(document_id, &mut conn).map_err(
        |e| ApiError::InternalServerError(format!("Erro ao atualizar timestamp: {}", e)),
    )?;

    // 6. Converter para a resposta
    let response = document_to_response(updated_document, &mut conn).await;
    Ok(response)
}

// Obter conteúdo do documento
pub async fn get_document_content(
    pool: &DbPool,
    user_id: Uuid,
    document_id: Uuid,
) -> ApiResult<String> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // 1. Buscar o documento para garantir que ele existe e pertence ao usuário
    let document = document_repository::find_document_by_id(document_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Documento".to_string()))?;

    // 2. Inicializar cliente S3
    let s3_client = S3Storage::new()
        .await
        .map_err(ApiError::InternalServerError)?;

    // 3. Baixar o conteúdo do objeto do S3
    let object_output = s3_client
        .get_object(&document.s3_key)
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao buscar conteúdo do S3: {}", e)))?;

    let content_bytes = object_output
        .body
        .collect()
        .await
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao ler o corpo do objeto S3: {}", e)))?
        .into_bytes();

    // 4. Converter bytes para String
    let content = String::from_utf8(content_bytes.to_vec())
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao converter conteúdo para texto: {}", e)))?;

    Ok(content)
}

// Excluir documento
pub async fn delete_document(
    pool: &DbPool,
    user_id: Uuid,
    document_id: Uuid,
) -> ApiResult<()> {
    // Obter conexão com o banco de dados
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Buscar documento para obter a chave S3
    let document = document_repository::find_document_by_id(document_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Documento".to_string()))?;

    // Inicializar cliente S3
    let s3_client = S3Storage::new()
        .await
        .map_err(ApiError::InternalServerError)?;

    // Excluir objeto do S3
    if s3_client.object_exists(&document.s3_key).await {
        s3_client
            .delete_object(&document.s3_key)
            .await
            .map_err(|e| ApiError::InternalServerError(format!("Erro ao excluir arquivo: {}", e)))?;
    }

    // Excluir registro do banco de dados
    let deleted_count =
        document_repository::delete_document(document_id, user_id, &mut conn).map_err(|e| {
            ApiError::InternalServerError(format!("Erro ao excluir documento: {}", e))
        })?;

    if deleted_count == 0 {
        return Err(ApiError::ResourceNotFound("Documento".to_string()));
    }

    Ok(())
}

use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// Resposta para documentos
#[derive(Debug, Serialize)]
pub struct DocumentResponse {
    pub id: Uuid,
    pub title: String,
    pub filename: String,
    pub mime_type: Option<String>,
    pub size_bytes: Option<i64>,
    pub patient_id: Option<Uuid>, 
    pub patient_name: Option<String>, 
    pub model_id: Option<Uuid>,  
    pub model_name: Option<String>, 
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    pub created_by: Uuid, 
    pub updated_by: Uuid, 
}

// Resposta para lista de documentos com paginação
#[derive(Debug, Serialize)]
pub struct DocumentListResponse {
    pub documents: Vec<DocumentResponse>,
    pub total_count: i64,
}

// Parâmetros de filtro para documentos
#[derive(Debug, Deserialize)]
pub struct DocumentFilterParams {
    pub patient_id: Option<Uuid>, 
    pub title: Option<String>,
    pub page: Option<i64>,
    pub page_size: Option<i64>,
}

// Metadados para upload de documento
#[derive(Debug, Deserialize)]
pub struct UploadDocumentMetadata {
    pub title: String,
    pub patient_id: Option<Uuid>, 
    pub model_id: Option<Uuid>,  
}

// Requisição para atualização de documento
#[derive(Debug, Deserialize)]
pub struct UpdateDocumentRequest {
    pub title: Option<String>,
    pub patient_id: Option<Option<Uuid>>, 
    pub model_id: Option<Option<Uuid>>, 
}

#[derive(Debug, Deserialize)]
pub struct CreateDocumentFromHtmlRequest {
    pub title: String,
    pub content: String,
    pub patient_id: Option<Uuid>,
    pub model_id: Option<Uuid>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateDocumentContentRequest {
    pub content: String,
}

// Resposta para URL de download
#[derive(Debug, Serialize)]
pub struct DownloadUrlResponse {
    pub download_url: String,
    pub filename: String,
    pub mime_type: Option<String>,
    pub expires_in_seconds: u64,
}

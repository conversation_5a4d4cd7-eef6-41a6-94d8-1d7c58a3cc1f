use crate::auth::auth_middleware::AuthUser;
use crate::config::database::DbPool;
use crate::document::document_dto::{
    DocumentFilterParams, UpdateDocumentContentRequest,
    UpdateDocumentRequest, UploadDocumentMetadata,
};
use crate::document::document_service;
use crate::errors::{ApiError, ApiResult};
use axum::{
    extract::{Extension, Path, Query},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use axum_extra::extract::Multipart;
use bytes::Bytes;
use serde_json::json;
use uuid::Uuid;

// Handler para upload de documento
pub async fn upload_document_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    mut multipart: Multipart,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    
    // Variáveis para armazenar os dados do multipart
    let mut metadata: Option<UploadDocumentMetadata> = None;
    let mut file_bytes: Option<Bytes> = None;
    let mut filename: Option<String> = None;
    let mut mime_type: Option<String> = None;

    // Processar campos do multipart
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        ApiError::BadRequest(format!("Erro ao processar formulário multipart: {}", e))
    })? {
        let name = field.name().unwrap_or("").to_string();

        if name == "metadata" {
            let data = field.text().await.map_err(|e| {
                ApiError::BadRequest(format!("Erro ao ler metadados: {}", e))
            })?;
            
            metadata = Some(serde_json::from_str(&data).map_err(|e| {
                ApiError::BadRequest(format!("Metadados inválidos: {}", e))
            })?);
        } else if name == "file" {
            filename = Some(field.file_name().unwrap_or("arquivo.bin").to_string());
            mime_type = Some(field.content_type().unwrap_or("application/octet-stream").to_string());
            file_bytes = Some(field.bytes().await.map_err(|e| {
                ApiError::BadRequest(format!("Erro ao ler arquivo: {}", e))
            })?);
        }
    }

    // Verificar se todos os campos necessários foram fornecidos
    let metadata = metadata.ok_or_else(|| ApiError::BadRequest("Metadados não fornecidos".to_string()))?;
    let file_bytes = file_bytes.ok_or_else(|| ApiError::BadRequest("Arquivo não fornecido".to_string()))?;
    let filename = filename.ok_or_else(|| ApiError::BadRequest("Nome do arquivo não fornecido".to_string()))?;
    let mime_type = mime_type.ok_or_else(|| ApiError::BadRequest("Tipo MIME não fornecido".to_string()))?;

    // Fazer upload do documento
    let document = document_service::upload_document(
        &pool,
        user_id,
        metadata,
        file_bytes,
        filename,
        mime_type,
    )
    .await?;

    Ok((StatusCode::CREATED, Json(document)))
}

/*
// Handler para criar documento a partir de HTML
pub async fn create_document_from_html_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(req): Json<CreateDocumentFromHtmlRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;

    let document = document_service::create_document_from_html(&pool, user_id, req).await?;

    Ok((StatusCode::CREATED, Json(document)))
}
*/

// Handler para listar documentos
pub async fn list_documents_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Query(params): Query<DocumentFilterParams>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    
    let documents = document_service::list_documents(&pool, user_id, params).await?;
    
    Ok(Json(documents))
}

// Handler para obter URL de download
pub async fn get_document_download_url_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(document_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    
    // Duração padrão da URL: 1 hora
    let duration_secs = 3600;
    
    let download_url = document_service::get_document_download_url(
        &pool,
        user_id,
        document_id,
        duration_secs,
    )
    .await?;
    
    Ok(Json(download_url))
}

// Handler para obter conteúdo do documento
pub async fn get_document_content_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(document_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    
    let content = document_service::get_document_content(
        &pool,
        user_id,
        document_id,
    )
    .await?;
    
    Ok(Json(json!({ "content": content })))
}

// Handler para atualizar metadados do documento
pub async fn update_document_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(document_id): Path<Uuid>,
    Json(update_req): Json<UpdateDocumentRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    
    let updated_document = document_service::update_document_metadata(
        &pool,
        user_id,
        document_id,
        update_req,
    )
    .await?;
    
    Ok(Json(updated_document))
}

// Handler para atualizar conteúdo do documento
pub async fn update_document_content_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(document_id): Path<Uuid>,
    Json(update_req): Json<UpdateDocumentContentRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;

    let updated_document = document_service::update_document_content(
        &pool,
        user_id,
        document_id,
        update_req,
    )
    .await?;

    Ok(Json(updated_document))
}

// Handler para excluir documento
pub async fn delete_document_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(document_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    
    document_service::delete_document(&pool, user_id, document_id).await?;
    
    Ok((StatusCode::NO_CONTENT, Json(json!({"message": "Documento excluído com sucesso"}))))
}

use crate::db::models::document::{Document, NewDocument, UpdateDocument};
use crate::schema::{documents, patients, models};
use diesel::prelude::*;
use diesel::PgConnection;
use diesel::result::QueryResult;
use uuid::Uuid;

// Criar um novo documento
pub fn create_document(
    new_document: &NewDocument,
    conn: &mut PgConnection,
) -> QueryResult<Document> {
    diesel::insert_into(documents::table)
        .values(new_document)
        .get_result(conn)
}

// Buscar documento por ID e user_id (para garantir acesso apenas aos próprios documentos)
pub fn find_document_by_id(
    document_id: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Document> {
    documents::table
        .filter(documents::id.eq(document_id))
        .filter(documents::user_id.eq(user_id_val))
        .first(conn)
}

// Buscar documentos por user_id com paginação
pub fn find_documents_by_user_paginated(
    user_id_val: Uuid,
    limit_val: Option<i64>,
    offset_val: Option<i64>,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Document>> {
    let mut query = documents::table
        .filter(documents::user_id.eq(user_id_val))
        .order_by(documents::created_at.desc())
        .into_boxed();

    if let Some(limit) = limit_val {
        query = query.limit(limit);
    }

    if let Some(offset) = offset_val {
        query = query.offset(offset);
    }

    query.load::<Document>(conn)
}

// Buscar documentos por patient_id e user_id com paginação
pub fn find_documents_by_patient_paginated(
    user_id_val: Uuid,
    patient_id_val: Uuid,
    limit_val: Option<i64>,
    offset_val: Option<i64>,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Document>> {
    let mut query = documents::table
        .filter(documents::user_id.eq(user_id_val))
        .filter(documents::patient_id.eq(patient_id_val))
        .order_by(documents::created_at.desc())
        .into_boxed();

    if let Some(limit) = limit_val {
        query = query.limit(limit);
    }

    if let Some(offset) = offset_val {
        query = query.offset(offset);
    }

    query.load::<Document>(conn)
}

// Buscar documentos por título (parcial) e user_id com paginação
pub fn find_documents_by_title_paginated(
    user_id_val: Uuid,
    title_val: &str,
    limit_val: Option<i64>,
    offset_val: Option<i64>,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Document>> {
    let mut query = documents::table
        .filter(documents::user_id.eq(user_id_val))
        .filter(documents::title.ilike(format!("%{}%", title_val)))
        .order_by(documents::created_at.desc())
        .into_boxed();

    if let Some(limit) = limit_val {
        query = query.limit(limit);
    }

    if let Some(offset) = offset_val {
        query = query.offset(offset);
    }

    query.load::<Document>(conn)
}

// Contar total de documentos por user_id
pub fn count_documents_by_user(
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<i64> {
    documents::table
        .filter(documents::user_id.eq(user_id_val))
        .count()
        .get_result(conn)
}

// Contar total de documentos por patient_id e user_id
pub fn count_documents_by_patient(
    user_id_val: Uuid,
    patient_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<i64> {
    documents::table
        .filter(documents::user_id.eq(user_id_val))
        .filter(documents::patient_id.eq(patient_id_val))
        .count()
        .get_result(conn)
}

// Contar total de documentos por título (parcial) e user_id
pub fn count_documents_by_title(
    user_id_val: Uuid,
    title_val: &str,
    conn: &mut PgConnection,
) -> QueryResult<i64> {
    documents::table
        .filter(documents::user_id.eq(user_id_val))
        .filter(documents::title.ilike(format!("%{}%", title_val)))
        .count()
        .get_result(conn)
}

// Atualizar documento
pub fn update_document(
    document_id: Uuid,
    user_id_val: Uuid,
    update_data: &UpdateDocument,
    conn: &mut PgConnection,
) -> QueryResult<Document> {
    diesel::update(
        documents::table
            .filter(documents::id.eq(document_id))
            .filter(documents::user_id.eq(user_id_val)),
    )
    .set(update_data)
    .get_result(conn)
}

// Atualiza o timestamp 'updated_at' de um documento
pub fn touch_document(
    document_id: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Document> {
    diesel::update(documents::table.find(document_id))
        .set(documents::updated_at.eq(diesel::dsl::now))
        .get_result(conn)
}

// Excluir documento
pub fn delete_document(
    document_id: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    diesel::delete(
        documents::table
            .filter(documents::id.eq(document_id))
            .filter(documents::user_id.eq(user_id_val)),
    )
    .execute(conn)
}

// Buscar nome do paciente pelo ID
pub fn find_patient_name_by_id(
    patient_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<String> {
    patients::table
        .select(patients::full_name)
        .filter(patients::id.eq(patient_id_val))
        .first(conn)
}

// Buscar título do modelo pelo ID
pub fn find_model_title_by_id(
    model_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<String> {
    models::table
        .select(models::title)
        .filter(models::id.eq(model_id_val))
        .first(conn)
}

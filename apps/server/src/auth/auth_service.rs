use crate::auth::auth_dto::{
    ChangePasswordRequest, ChangePasswordResponse, ForgotPasswordRequest, ForgotPasswordResponse,
    LoginRequest, LoginResponse, RefreshTokenResponse, RegisterRequest, RegisterResponse,
    ResendVerificationEmailRequest, ResendVerificationEmailResponse, ResetPasswordRequest,
    ResetPasswordResponse,
};
use crate::auth::auth_repository::{
    find_refresh_token, remove_old_refresh_tokens, remove_refresh_token, store_refresh_token,
};
use crate::auth::auth_tokens::{
    generate_access_token, generate_refresh_token, validate_refresh_token,
};
use crate::auth::{auth_hashing, auth_repository};
use crate::db::models::user::NewUser;
use crate::email::email_service::EmailService;
use crate::user::user_repository;
use crate::user::user_repository::save_user;
use axum::http::StatusCode;
use axum::response::{IntoResponse, Response};
use axum::Json;
use chrono::Utc;
use diesel::pg::PgConnection;
use diesel::Connection;
use rand::Rng;
use rand_distr::Alphanumeric;
use serde_json::json;
use std::{char, env};
use time::{Duration, OffsetDateTime};
use tower_cookies::{Cookie, Cookies};
use uuid::Uuid;

const REFRESH_TOKEN_COOKIE_NAME: &str = "refresh_token";
const REFRESH_TOKEN_EXPIRY_DAYS: i64 = 7;

pub async fn login(
    payload: LoginRequest,
    conn: &mut PgConnection,
    cookies: &Cookies,
) -> Result<Response, String> {
    let user = user_repository::find_user_by_email(&payload.email, conn)
        .map_err(|_| "Credenciais inválidas".to_string())?;

    if !auth_hashing::verify_password(&payload.password, &user.password_hash) {
        return Err("Credenciais inválidas".to_string());
    }

    let access_token = generate_access_token(user.id);
    let refresh_token = generate_refresh_token(user.id);

    // Transação para armazenar novo token e limpar tokens antigos
    conn.transaction::<_, diesel::result::Error, _>(|conn| {
        // Armazena o novo token
        store_refresh_token(user.id, &refresh_token, conn)?;

        // Remove tokens antigos para o mesmo usuário
        let count = remove_old_refresh_tokens(user.id, &refresh_token, conn)?;
        if count > 0 {
            println!("Removidos {} tokens antigos do usuário {}", count, user.id);
        }

        Ok(())
    })
    .map_err(|e| format!("Falha ao gerenciar refresh tokens: {}", e))?;

    set_refresh_token_cookie(cookies, &refresh_token);

    let response_body = LoginResponse {
        access_token,
        refresh_token: "".to_string(),
        user_id: user.id,
        is_verified: user.is_verified.unwrap_or(false),
    };

    Ok((StatusCode::OK, Json(response_body)).into_response())
}

pub async fn logout(cookies: &Cookies) -> Response {
    // Remover o cookie do refresh token
    clear_refresh_token_cookie(cookies);

    // Retornar resposta de sucesso
    (
        StatusCode::OK,
        Json(json!({"message": "Logout efetuado com sucesso"})),
    )
        .into_response()
}

pub async fn register(
    payload: RegisterRequest,
    conn: &mut PgConnection,
    email_service: &EmailService,
    cookies: &Cookies,
) -> Result<Response, String> {
    println!("[SERVICE] Serviço de registro iniciado para: {}", payload.email);

    let hashed_password = auth_hashing::hash_password(&payload.password);
    println!("[SERVICE] Senha hasheada com sucesso.");

    let email_token: String = (0..32)
        .map(|_| rand::rng().sample(Alphanumeric) as char)
        .collect();
    println!("[SERVICE] Token de verificação de email gerado.");

    let verification_expires = Utc::now().naive_utc() + chrono::Duration::hours(24);

    let new_user = NewUser {
        first_name: payload.first_name,
        last_name: payload.last_name,
        email: payload.email.clone(),
        occupation: payload.occupation,
        password_hash: hashed_password,

        date_of_birth: None,
        cpf: None,
        phone: None,
        address: None,
        city: None,
        state: None,
        profile_picture: None,
        
        is_active: Some(true),
        is_verified: Some(false),
        verification_token: Some(email_token.clone()),
        verification_token_expires: Some(verification_expires),
        password_reset_token: None,
        password_reset_expires: None,
    };
    println!("[SERVICE] Estrutura NewUser criada, tentando salvar no banco de dados...");

    if let Ok(saved_user) = save_user(&new_user, conn) {
        println!("[SERVICE] Usuário salvo com sucesso no banco de dados com ID: {}", saved_user.id);
        let access_token = generate_access_token(saved_user.id);
        let refresh_token = generate_refresh_token(saved_user.id);
        println!("[SERVICE] Tokens de acesso e atualização gerados.");

        let verification_link = format!(
            "{}/verify-email/{}",
            env::var("DOMAIN").unwrap_or("http://localhost:3000".to_string()),
            email_token
        );
        println!("[SERVICE] Link de verificação gerado: {}", verification_link);

        // Envia o e-mail de verificação em uma nova thread
        let email_service_clone = email_service.clone();
        let email = payload.email.clone();

        tokio::spawn(async move {
            match email_service_clone
                .send_verification_email(email, &verification_link)
                .await
            {
                Ok(_) => tracing::info!("E-mail de verificação enviado com sucesso"),
                Err(e) => tracing::error!("Erro ao enviar e-mail de verificação: {}", e),
            }
        });

        println!("[SERVICE] Tentando armazenar o refresh token...");
        store_refresh_token(saved_user.id, &refresh_token, conn)
            .map_err(|e| {
                let err_msg = format!("[SERVICE] Falha ao armazenar o refresh token: {}", e);
                println!("{}", err_msg);
                err_msg
            })?;
        println!("[SERVICE] Refresh token armazenado com sucesso.");

        // Configurar cookie HTTP-only para o refresh token
        set_refresh_token_cookie(cookies, &refresh_token);
        println!("[SERVICE] Cookie de refresh token configurado.");

        // Retornar apenas o access token no corpo da resposta
        let response_body = RegisterResponse {
            access_token,
            refresh_token: "".to_string(), // Não enviamos o refresh token no corpo
            user_id: saved_user.id,
        };

        println!("[SERVICE] Resposta de registro pronta para ser enviada.");
        return Ok((StatusCode::OK, Json(response_body)).into_response());
    }

    println!("[SERVICE] Falha ao salvar o usuário no banco de dados. A função save_user retornou um erro.");
    Err("Failed to register user".to_string())
}

pub async fn forgot_password(
    payload: ForgotPasswordRequest,
    conn: &mut PgConnection,
    email_service: &EmailService,
) -> Result<ForgotPasswordResponse, String> {
    let _user = match user_repository::find_user_by_email(&payload.email, conn) {
        Ok(user) => user,
        Err(_) => {
            return Ok(ForgotPasswordResponse {
                message: "Se o email estiver cadastrado, enviaremos instruções para redefinição de senha."
                    .to_string(),
            });
        }
    };

    // Gerar token aleatório para redefinição
    let reset_token: String = (0..32)
        .map(|_| rand::rng().sample(Alphanumeric) as char)
        .collect();

    let expires_at = Utc::now().naive_utc() + chrono::Duration::hours(1);

    if let Err(e) =
        user_repository::update_password_reset_token(&payload.email, &reset_token, expires_at, conn)
    {
        return Err(format!("Erro ao atualizar token de redefinição: {}", e));
    }

    let reset_link = format!(
        "{}/reset-password/{}",
        env::var("DOMAIN").unwrap_or_else(|_| "http://localhost:3000".to_string()),
        reset_token
    );

    let email_service_clone = email_service.clone();
    let email = payload.email.clone();
    let reset_link_clone = reset_link.clone();

    tokio::spawn(async move {
        match email_service_clone
            .send_reset_password_email(email, &reset_link_clone)
            .await
        {
            Ok(_) => tracing::info!("Email de redefinição enviado com sucesso"),
            Err(e) => tracing::error!("Erro ao enviar email de redefinição: {}", e),
        }
    });

    Ok(ForgotPasswordResponse {
        message: "Se o email estiver cadastrado, enviaremos instruções para redefinição de senha."
            .to_string(),
    })
}

pub async fn reset_password(
    payload: ResetPasswordRequest,
    conn: &mut PgConnection,
) -> Result<ResetPasswordResponse, String> {
    let user = user_repository::find_user_by_reset_token(&payload.token, conn)
        .map_err(|_| "Token de redefinição inválido ou expirado".to_string())?;

    if let Some(expires_at) = user.password_reset_expires {
        if expires_at < Utc::now().naive_utc() {
            return Err("Token de redefinição expirado. Solicite um novo.".to_string());
        }
    } else {
        return Err("Token de redefinição inválido".to_string());
    }

    if payload.new_password.len() < 8 {
        return Err("A senha deve ter pelo menos 8 caracteres".to_string());
    }

    let tem_maiuscula = payload.new_password.chars().any(|c| c.is_uppercase());
    let tem_minuscula = payload.new_password.chars().any(|c| c.is_lowercase());
    let tem_numero = payload.new_password.chars().any(|c| c.is_ascii_digit());
    let tem_especial = payload.new_password.chars().any(|c| !c.is_alphanumeric());

    if !tem_maiuscula || !tem_minuscula || !tem_numero || !tem_especial {
        return Err(
            "A senha deve conter letras maiúsculas, minúsculas, números e caracteres especiais"
                .to_string(),
        );
    }

    let new_password_hash = auth_hashing::hash_password(&payload.new_password);

    if let Err(e) = user_repository::update_password(user.id, &new_password_hash, conn) {
        return Err(format!("Erro ao atualizar senha: {}", e));
    }

    if let Err(e) = auth_repository::remove_all_refresh_tokens_for_user(user.id, conn) {
        tracing::warn!(
            "Erro ao revogar tokens de atualização após redefinição de senha: {}",
            e
        );
    }

    Ok(ResetPasswordResponse {
        message: "Senha atualizada com sucesso. Você já pode fazer login com sua nova senha."
            .to_string(),
    })
}

pub async fn refresh_tokens(
    conn: &mut PgConnection,
    cookies: &Cookies,
) -> Result<Response, String> {
    // Remover log
    // println!("Tentando refresh: Cookies recebidos: {:?}", cookies.list());

    let refresh_token = cookies
        .get(REFRESH_TOKEN_COOKIE_NAME)
        .map(|c| c.value().to_string())
        .ok_or_else(|| {
            println!("Refresh token não encontrado nos cookies!");
            "Refresh token não encontrado".to_string()
        })?;

    // Remover log
    // println!("Refresh Token encontrado: {}", refresh_token);
    let claims = validate_refresh_token(&refresh_token)
        .map_err(|e| format!("Refresh token inválido: {}", e))?;

    // Verificar se o token existe no banco de dados
    let token = find_refresh_token(&refresh_token, conn)
        .map_err(|e| format!("Refresh token não encontrado no banco: {}", e))?;

    // Adiciona verificação de expiração
    let now = Utc::now().naive_utc();
    if token.expires_at < now {
        return Err("Refresh token expirado".to_string());
    }

    // Token válido: gera novos tokens
    let user_id = claims.sub;
    let new_access_token = generate_access_token(user_id);
    let new_refresh_token = generate_refresh_token(user_id);

    // Transação para garantir que as operações sejam atômicas
    conn.transaction::<_, diesel::result::Error, _>(|conn| {
        // 1. Remover o token antigo específico
        let _ = remove_refresh_token(&refresh_token, conn)?;

        // 2. Armazenar o novo token
        let _ = store_refresh_token(user_id, &new_refresh_token, conn)?;

        // 3. Remover todos os outros tokens antigos deste usuário
        // (isso evita acumulação de tokens e melhora a segurança)
        let count = remove_old_refresh_tokens(user_id, &new_refresh_token, conn)?;
        if count > 0 {
            println!("Removidos {} tokens antigos do usuário {}", count, user_id);
        }

        Ok(())
    })
    .map_err(|e| format!("Erro na transação: {}", e))?;

    // Atualizar o cookie do refresh token
    set_refresh_token_cookie(cookies, &new_refresh_token);

    // Retornar apenas o access token no corpo da resposta
    let response_body = RefreshTokenResponse {
        access_token: new_access_token,
        refresh_token: "".to_string(), // Não enviamos o refresh token no corpo
    };

    Ok((StatusCode::OK, Json(response_body)).into_response())
}
pub async fn verify_email(token: String, conn: &mut PgConnection) -> Result<(), String> {
    // Buscar usuário pelo token de verificação
    if let Ok(user) = user_repository::find_user_by_verification_token(&token, conn) {
        // Verificar se o token expirou
        if let Some(expires_at) = user.verification_token_expires {
            if expires_at < Utc::now().naive_utc() {
                return Err("Token de verificação expirado".to_string());
            }
        }

        // Atualizar o usuário para confirmado
        if user_repository::update_user_verification_status(user.id, true, conn).is_ok() {
            return Ok(());
        }
    }

    Err("Token de verificação inválido".to_string())
}

pub async fn resend_verification_email(
    payload: ResendVerificationEmailRequest,
    conn: &mut PgConnection,
    email_service: &EmailService,
) -> Result<ResendVerificationEmailResponse, String> {
    let user = user_repository::find_user_by_email(&payload.email, conn)
        .map_err(|_| "E-mail não encontrado".to_string())?;

    if user.is_verified.unwrap_or(false) {
        return Err("E-mail já verificado".to_string());
    }

    let verification_token: String = (0..32)
        .map(|_| rand::rng().sample(Alphanumeric) as char)
        .collect();

    // Definir nova data de expiração
    let verification_expires = Utc::now().naive_utc() + chrono::Duration::hours(24);

    // Atualizar token de verificação no banco de dados
    user_repository::update_verification_token(
        user.id,
        &verification_token,
        verification_expires,
        conn,
    )
    .map_err(|e| format!("Falha ao atualizar token: {}", e))?;

    // Enviar e-mail
    let verification_link = format!(
        "{}/verify-email/{}",
        env::var("DOMAIN").unwrap_or("localhost:3000".to_string()),
        verification_token
    );

    // Enviar e-mail de forma assíncrona
    let email_service_clone = email_service.clone();
    let email = payload.email.clone();

    tokio::spawn(async move {
        if let Err(e) = email_service_clone
            .send_verification_email(email, &verification_link)
            .await
        {
            eprintln!("Erro ao reenviar e-mail de verificação: {}", e);
        }
    });

    Ok(ResendVerificationEmailResponse {
        message: "E-mail de verificação reenviado com sucesso".to_string(),
    })
}

pub async fn change_password(
    user_id: Uuid,
    payload: ChangePasswordRequest,
    conn: &mut PgConnection,
) -> Result<ChangePasswordResponse, String> {
    let user = user_repository::find_user_by_id(user_id, conn)
        .map_err(|_| "Usuário não encontrado".to_string())?;

    if !auth_hashing::verify_password(&payload.current_password, &user.password_hash) {
        return Err("Senha atual incorreta".to_string());
    }

    if payload.current_password == payload.new_password {
        return Err("A nova senha deve ser diferente da senha atual".to_string());
    }

    if payload.new_password.len() < 8 {
        return Err("A nova senha deve ter pelo menos 8 caracteres".to_string());
    }

    let new_password_hash = auth_hashing::hash_password(&payload.new_password);

    match user_repository::update_password(user_id, &new_password_hash, conn) {
        Ok(_) => Ok(ChangePasswordResponse {
            message: "Senha alterada com sucesso".to_string(),
        }),
        Err(_) => Err("Falha ao atualizar a senha".to_string()),
    }
}

// Função auxiliar para definir o cookie do refresh token
fn set_refresh_token_cookie(cookies: &Cookies, refresh_token: &str) {
    let expires = OffsetDateTime::now_utc() + Duration::days(REFRESH_TOKEN_EXPIRY_DAYS);

    let cookie = Cookie::build((REFRESH_TOKEN_COOKIE_NAME, refresh_token.to_string()))
        .path("/")
        .http_only(true)
        .secure(true)
        .same_site(tower_cookies::cookie::SameSite::None)
        .expires(expires)
        // .partitioned(true)  // remova esta linha se não for suportada
        .build();

    cookies.add(cookie);
    // Remover log
    // println!("Refresh token cookie definido: {}", refresh_token)
}

// Função auxiliar para limpar o cookie do refresh token
fn clear_refresh_token_cookie(cookies: &Cookies) {
    let cookie = Cookie::build((REFRESH_TOKEN_COOKIE_NAME, ""))
        .path("/")
        .http_only(true)
        .secure(true)
        .same_site(tower_cookies::cookie::SameSite::Strict)
        .max_age(Duration::seconds(0))
        .build();

    cookies.add(cookie);
}

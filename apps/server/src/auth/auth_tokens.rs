use jsonwebtoken::{
    decode, encode, errors::<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Val<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::env;
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
pub struct AccessTokenClaims {
    pub sub: Uuid,  
    pub exp: usize,
    pub jti: String,
}

fn get_access_secret() -> String {
    env::var("JWT_ACCESS_SECRET").unwrap_or_else(|_| "default_access_secret_key".to_string())
}

pub fn generate_access_token(user_id: Uuid) -> String { 
    let secret = get_access_secret();
    let jti = Uuid::new_v4().to_string();

    let expiration = chrono::Utc::now()
        .checked_add_signed(chrono::Duration::minutes(30))
        .expect("Failed to compute expiration")
        .timestamp() as usize;

    let claims = AccessTokenClaims {
        sub: user_id,
        exp: expiration,
        jti,
    };

    encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_ref()),
    )
    .expect("Failed to generate access token")
}

pub fn validate_access_token(token: &str) -> Result<AccessTokenClaims, String> {
    let secret = get_access_secret();
    match decode::<AccessTokenClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &Validation::default(),
    ) {
        Ok(TokenData { claims, .. }) => Ok(claims),
        Err(err) => match *err.kind() {
            ErrorKind::ExpiredSignature => Err("Access token expired".to_string()),
            ErrorKind::InvalidToken => Err("Invalid access token".to_string()),
            _ => Err("Failed to validate access token".to_string()),
        },
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenClaims {
    pub sub: Uuid, 
    pub exp: usize,
}

fn get_refresh_secret() -> String {
    env::var("JWT_REFRESH_SECRET").unwrap_or_else(|_| "default_refresh_secret_key".to_string())
}

pub fn generate_refresh_token(user_id: Uuid) -> String { 
    let secret = get_refresh_secret();
    let expiration = chrono::Utc::now()
        .checked_add_signed(chrono::Duration::days(30))
        .expect("Failed to compute expiration")
        .timestamp() as usize;

    let claims = RefreshTokenClaims {
        sub: user_id,
        exp: expiration,
    };

    encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_ref()),
    )
    .expect("Failed to generate refresh token")
}

pub fn validate_refresh_token(token: &str) -> Result<RefreshTokenClaims, String> {
    let secret = get_refresh_secret();

    match decode::<RefreshTokenClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &Validation::default(),
    ) {
        Ok(TokenData { claims, .. }) => Ok(claims),
        Err(err) => match *err.kind() {
            ErrorKind::ExpiredSignature => Err("Refresh token expired".to_string()),
            ErrorKind::InvalidToken => Err("Invalid refresh token".to_string()),
            _ => Err("Failed to validate refresh token".to_string()),
        },
    }
}

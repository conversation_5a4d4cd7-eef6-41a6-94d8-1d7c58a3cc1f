use crate::auth::auth_dto::{
    ChangePasswordRequest, ForgotPasswordRequest, LoginRequest, RegisterRequest,
    ResendVerificationEmailRequest, ResetPasswordRequest,
};
use crate::auth::auth_middleware::AuthUser;
use crate::auth::{auth_repository, auth_service, auth_tokens};
use crate::config::database::DbPool;
use crate::email::email_service::EmailService;
use crate::errors::{ApiError, ApiResult};
use axum::extract::{Extension, Path};
use axum::http::HeaderMap;
use axum::{http::StatusCode, response::IntoResponse, Json};
use chrono::DateTime;
use serde_json::json;
use tower_cookies::Cookies;
use validator::Validate;

pub async fn login_handler(
    Extension(pool): Extension<DbPool>,
    cookies: Cookies,
    Json(payload): Json<LoginRequest>,
) -> impl IntoResponse {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match auth_service::login(payload, &mut conn, &cookies).await {
        Ok(response) => Ok(response),
        Err(e) => {
            if e.contains("Credenciais inválidas") {
                Err(ApiError::InvalidCredentials)
            } else if e.contains("E-mail não verificado") {
                Err(ApiError::EmailNotVerified)
            } else {
                Err(ApiError::AuthenticationFailed)
            }
        }
    }
}

pub async fn logout_handler(
    Extension(pool): Extension<DbPool>,
    cookies: Cookies,
    headers: HeaderMap,
) -> ApiResult<impl IntoResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    if let Some(auth_header) = headers.get("Authorization") {
        if let Ok(token_str) = auth_header.to_str() {
            if let Some(token) = token_str.strip_prefix("Bearer ") {
                if let Ok(claims) = auth_tokens::validate_access_token(token) {
                    let token_expiry =
                        DateTime::from_timestamp(claims.exp as i64, 0)
                            .map(|dt| dt.naive_utc())
                            .unwrap_or_else(|| {
                                chrono::Utc::now().naive_utc() + chrono::Duration::hours(1)
                            });

                    let _ = auth_repository::revoke_access_token(
                        &claims.jti,
                        claims.sub,
                        token_expiry,
                        &mut conn,
                    );
                }
            }
        }
    }

    if let Some(cookie) = cookies.get("refresh_token") {
        let _ = auth_repository::remove_refresh_token(cookie.value(), &mut conn);
    }

    auth_service::logout(&cookies).await;

    Ok(StatusCode::OK)
}

pub async fn register_handler(
    Extension(pool): Extension<DbPool>,
    Extension(email_service): Extension<EmailService>,
    cookies: Cookies,
    Json(payload): Json<RegisterRequest>,
) -> ApiResult<impl IntoResponse> {
    if let Err(errors) = payload.validate() {
        return Err(ApiError::ValidationError(errors));
    }

    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match auth_service::register(payload, &mut conn, &email_service, &cookies).await {
        Ok(response) => Ok(response),
        Err(e) => {
            if e.contains("já existe") {
                Err(ApiError::ResourceAlreadyExists("Usuário/Email".to_string()))
            } else {
                Err(ApiError::BadRequest(e))
            }
        }
    }
}

pub async fn forgot_password_handler(
    Extension(pool): Extension<DbPool>,
    Extension(email_service): Extension<EmailService>,
    Json(payload): Json<ForgotPasswordRequest>,
) -> ApiResult<impl IntoResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match auth_service::forgot_password(payload, &mut conn, &email_service).await {
        Ok(response) => Ok(Json(response)),
        Err(e) => Err(ApiError::BadRequest(e)),
    }
}

pub async fn reset_password_handler(
    Extension(pool): Extension<DbPool>,
    Json(payload): Json<ResetPasswordRequest>,
) -> ApiResult<impl IntoResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match auth_service::reset_password(payload, &mut conn).await {
        Ok(response) => Ok(Json(response)),
        Err(e) => {
            if e.contains("expirado") {
                Err(ApiError::TokenExpired)
            } else if e.contains("inválido") {
                Err(ApiError::TokenInvalid)
            } else if e.contains("senha") {
                let mut errors = validator::ValidationErrors::new();
                let mut error = validator::ValidationError::new("password_error");
                error.message = Some(e.into());
                errors.add("password", error);
                Err(ApiError::ValidationError(errors))
            } else {
                Err(ApiError::BadRequest(e))
            }
        }
    }
}

pub async fn refresh_token_handler(
    Extension(pool): Extension<DbPool>,
    cookies: Cookies,
) -> ApiResult<impl IntoResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match auth_service::refresh_tokens(&mut conn, &cookies).await {
        Ok(response) => Ok(response),
        Err(e) => {
            if e.contains("expirado") {
                Err(ApiError::TokenExpired)
            } else if e.contains("inválido") {
                Err(ApiError::TokenInvalid)
            } else {
                Err(ApiError::AuthenticationFailed)
            }
        }
    }
}

pub async fn verify_email_handler(
    Path(token): Path<String>,
    Extension(pool): Extension<DbPool>,
) -> ApiResult<impl IntoResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match auth_service::verify_email(token, &mut conn).await {
        Ok(_) => Ok((
            StatusCode::OK,
            Json(json!({
                "message": "E-mail verificado com sucesso. Você já pode fazer login."
            })),
        )),
        Err(e) => {
            if e.contains("expirado") {
                Err(ApiError::TokenExpired)
            } else if e.contains("inválido") {
                Err(ApiError::TokenInvalid)
            } else {
                Err(ApiError::BadRequest(e))
            }
        }
    }
}

pub async fn resend_verification_email_handler(
    Extension(pool): Extension<DbPool>,
    Json(payload): Json<ResendVerificationEmailRequest>,
) -> ApiResult<impl IntoResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;
    let email_service = crate::email::email_service::EmailService::new();

    match auth_service::resend_verification_email(payload, &mut conn, &email_service).await {
        Ok(response) => Ok(Json(response)),
        Err(e) => {
            if e.contains("não encontrado") {
                Err(ApiError::ResourceNotFound("Usuário".to_string()))
            } else if e.contains("já verificado") {
                Err(ApiError::BadRequest("E-mail já verificado".to_string()))
            } else {
                Err(ApiError::BadRequest(e))
            }
        }
    }
}

pub async fn change_password_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(payload): Json<ChangePasswordRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match auth_service::change_password(user_id, payload, &mut conn).await {
        Ok(response) => Ok(Json(response)),
        Err(e) => {
            if e.contains("Senha atual incorreta") {
                Err(ApiError::InvalidCredentials)
            } else if e.contains("nova senha deve") {
                let mut errors = validator::ValidationErrors::new();
                let mut error = validator::ValidationError::new("password_error");
                error.message = Some(e.into());
                errors.add("new_password", error);
                Err(ApiError::ValidationError(errors))
            } else {
                Err(ApiError::BadRequest(e))
            }
        }
    }
}

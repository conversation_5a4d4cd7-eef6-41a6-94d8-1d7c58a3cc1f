use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::{Validate, ValidationError};

#[derive(Debug, Deserialize, Validate)]
pub struct RegisterRequest {
    #[validate(length(min = 2, message = "Nome muito curto"))]
    pub first_name: String,

    #[validate(length(min = 2, message = "Sobrenome muito curto"))]
    pub last_name: String,

    #[validate(email(message = "Email em formato inválido"))]
    pub email: String,
    
    // Campo opcional, como no frontend
    pub occupation: Option<String>,

    #[validate(custom(function = "validate_password"))]
    pub password: String,
}

fn validate_password(password: &str) -> Result<(), ValidationError> {
    if password.len() < 8 {
        return Err(ValidationError::new("senha_muito_curta"));
    }

    let tem_maiuscula = password.chars().any(|c| c.is_uppercase());
    let tem_minuscula = password.chars().any(|c| c.is_lowercase());
    let tem_numero = password.chars().any(|c| c.is_ascii_digit());
    let tem_especial = password.chars().any(|c| !c.is_alphanumeric());

    if !tem_maiuscula || !tem_minuscula || !tem_numero || !tem_especial {
        return Err(ValidationError::new("senha_fraca"));
    }

    Ok(())
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RegisterResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub user_id: Uuid,
}

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub email: String,
    pub password: String,
}
#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub user_id: Uuid,
    pub is_verified: bool,
}
#[derive(Debug, Deserialize)]
pub struct ForgotPasswordRequest {
    pub email: String,
}

#[derive(Debug, Serialize)]
pub struct ForgotPasswordResponse {
    pub message: String,
}

#[derive(Debug, Deserialize)]
pub struct ResetPasswordRequest {
    pub token: String,
    pub new_password: String,
}

#[derive(Debug, Serialize)]
pub struct ResetPasswordResponse {
    pub message: String,
}

#[derive(Debug, Deserialize)]
pub struct RefreshTokenRequest {
    pub refresh_token: String,
}

#[derive(Debug, Serialize)]
pub struct RefreshTokenResponse {
    pub access_token: String,
    pub refresh_token: String,
}

#[derive(Debug, Deserialize)]
pub struct ResendVerificationEmailRequest {
    pub email: String,
}
#[derive(Debug, Serialize)]
pub struct ResendVerificationEmailResponse {
    pub message: String,
}

#[derive(Debug, Deserialize)]
pub struct ChangePasswordRequest {
    pub current_password: String,
    pub new_password: String,
}

#[derive(Debug, Serialize)]
pub struct ChangePasswordResponse {
    pub message: String,
}

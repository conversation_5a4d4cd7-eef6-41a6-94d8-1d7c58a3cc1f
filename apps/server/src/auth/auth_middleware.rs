use crate::auth::{auth_repository, auth_tokens};
use crate::config::database::DbPool;
use axum::{
    extract::{Request, State},
    http::StatusCode,
    middleware::Next,
    response::IntoResponse,
};
use uuid::Uuid;

pub async fn auth_middleware(
    State(pool): State<DbPool>,
    mut req: Request,
    next: Next,
) -> impl IntoResponse {
    if let Some(auth_header) = req.headers().get("Authorization") {
        if let Ok(token_str) = auth_header.to_str() {
            let token_str = token_str.strip_prefix("Bearer ").unwrap_or(token_str);

            return match auth_tokens::validate_access_token(token_str) {
                Ok(claims) => {
                    // Verificar se o token está na blacklist
                    let mut conn = match pool.get() {
                        Ok(conn) => conn,
                        Err(_) => {
                            return (
                                StatusCode::INTERNAL_SERVER_ERROR,
                                "Falha ao conectar ao banco de dados",
                            )
                                .into_response()
                        }
                    };

                    match auth_repository::is_token_revoked(&claims.jti, &mut conn) {
                        Ok(true) => (StatusCode::UNAUTHORIZED, "Token revogado").into_response(),
                        Ok(false) => {
                            // Token válido e não revogado
                            req.extensions_mut().insert(AuthUser(claims.sub));
                            next.run(req).await
                        }
                        Err(_) => (StatusCode::INTERNAL_SERVER_ERROR, "Erro ao verificar token")
                            .into_response(),
                    }
                }
                Err(err) if err == "Access token expired" => {
                    (StatusCode::UNAUTHORIZED, "Access token expired").into_response()
                }
                Err(_) => (StatusCode::UNAUTHORIZED, "Invalid access token").into_response(),
            };
        }
    }

    (StatusCode::UNAUTHORIZED, "No valid access token").into_response()
}

#[derive(Clone, Debug)]
pub struct AuthUser(pub Uuid);

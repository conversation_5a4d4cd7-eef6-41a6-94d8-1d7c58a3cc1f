use crate::db::models::refresh_token::{NewRefreshToken, RefreshToken};
use crate::db::models::revoked_token::NewRevokedToken;
use crate::schema::refresh_tokens::dsl::{refresh_tokens, token as rt_token};
use crate::schema::refresh_tokens::expires_at;
use crate::schema::revoked_tokens::dsl::revoked_tokens;
use crate::schema::revoked_tokens::{expiry, token_jti};
use chrono::NaiveDateTime;
use diesel::pg::PgConnection;
use diesel::prelude::*;
use uuid::Uuid;
use diesel::{QueryResult, RunQueryDsl};

pub fn store_refresh_token(
    user_id_val: Uuid,
    token_val: &str,
    conn: &mut PgConnection,
) -> QueryResult<RefreshToken> {
    let _expires_at = chrono::Utc::now()
        .checked_add_signed(chrono::Duration::days(7))
        .expect("Falha ao calcular data de expiração do token")
        .naive_utc();

    let new_token = NewRefreshToken {
        user_id: user_id_val,
        token: token_val.to_string(),
        expires_at: _expires_at,
    };
    diesel::insert_into(refresh_tokens)
        .values(&new_token)
        .get_result(conn)
}

pub fn find_refresh_token(token_val: &str, conn: &mut PgConnection) -> QueryResult<RefreshToken> {
    refresh_tokens.filter(rt_token.eq(token_val)).first(conn)
}

pub fn remove_refresh_token(token_val: &str, conn: &mut PgConnection) -> QueryResult<usize> {
    diesel::delete(refresh_tokens.filter(rt_token.eq(token_val))).execute(conn)
}

pub fn clean_expired_refresh_tokens(conn: &mut PgConnection) -> QueryResult<usize> {
    let now = chrono::Utc::now().naive_utc();

    diesel::delete(refresh_tokens.filter(expires_at.lt(now))).execute(conn)
}

pub fn remove_all_refresh_tokens_for_user(
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    use crate::schema::refresh_tokens::dsl::{refresh_tokens, user_id};

    diesel::delete(refresh_tokens.filter(user_id.eq(user_id_val))).execute(conn)
}
pub fn remove_old_refresh_tokens(
    user_id_val: Uuid,
    except_token: &str,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    use crate::schema::refresh_tokens::dsl::{refresh_tokens, token, user_id};

    diesel::delete(refresh_tokens.filter(user_id.eq(user_id_val).and(token.ne(except_token))))
        .execute(conn)
}

pub fn revoke_access_token(
    jti: &str,
    user_id_val: Uuid,
    expiry_val: NaiveDateTime,
    conn: &mut PgConnection,
) -> QueryResult<()> {
    let new_token = NewRevokedToken {
        token_jti: jti.to_string(),
        expiry: expiry_val,
        user_id: user_id_val,
    };

    diesel::insert_into(revoked_tokens)
        .values(&new_token)
        .execute(conn)?;

    Ok(())
}

pub fn is_token_revoked(jti: &str, conn: &mut PgConnection) -> QueryResult<bool> {
    let count: i64 = revoked_tokens
        .filter(token_jti.eq(jti))
        .count()
        .get_result(conn)?;

    Ok(count > 0)
}

pub fn clean_expired_revoked_tokens(conn: &mut PgConnection) -> QueryResult<usize> {
    let now = chrono::Utc::now().naive_utc();

    diesel::delete(revoked_tokens.filter(expiry.lt(now))).execute(conn)
}

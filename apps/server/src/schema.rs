// @generated automatically by Diesel CLI.

diesel::table! {
    appointments (id) {
        id -> Uuid,
        user_id -> Uuid,
        patient_id -> Uuid,
        #[max_length = 255]
        title -> Varchar,
        #[max_length = 50]
        type_ -> Varchar,
        #[max_length = 50]
        status -> Varchar,
        start_time -> Timestamp,
        end_time -> Timestamp,
        notes -> Nullable<Text>,
        location -> Nullable<Text>,
        #[max_length = 20]
        color -> Nullable<Varchar>,
        is_recurring -> Nullable<Bool>,
        #[max_length = 36]
        series_id -> Nullable<Varchar>,
        #[max_length = 50]
        recurrence_pattern -> Nullable<Varchar>,
        recurrence_end_date -> Nullable<Timestamp>,
        recurrence_count -> Nullable<Int4>,
        created_at -> Timestamp,
        updated_at -> Timestamp,
    }
}

diesel::table! {
    assessments (id) {
        id -> Uuid,
        patient_id -> Uuid,
        #[max_length = 255]
        title -> Varchar,
        content -> Nullable<Text>,
        created_at -> Timestamptz,
        updated_at -> <PERSON>tamptz,
    }
}

diesel::table! {
    contacts (id) {
        id -> Uuid,
        user_id -> Nullable<Uuid>,
        #[max_length = 255]
        name -> Varchar,
        #[max_length = 50]
        relationship_type -> Varchar,
        #[max_length = 20]
        phone -> Nullable<Varchar>,
        #[max_length = 255]
        email -> Nullable<Varchar>,
        address -> Nullable<Text>,
        notes -> Nullable<Text>,
        #[max_length = 14]
        cpf -> Nullable<Varchar>,
        #[max_length = 20]
        rg -> Nullable<Varchar>,
        #[max_length = 100]
        city -> Nullable<Varchar>,
        #[max_length = 50]
        state -> Nullable<Varchar>,
        date_of_birth -> Nullable<Date>,
        #[max_length = 50]
        gender -> Nullable<Varchar>,
        #[max_length = 50]
        marital_status -> Nullable<Varchar>,
        #[max_length = 50]
        ethnicity -> Nullable<Varchar>,
        #[max_length = 100]
        nationality -> Nullable<Varchar>,
        #[max_length = 100]
        naturalness -> Nullable<Varchar>,
        #[max_length = 255]
        occupation -> Nullable<Varchar>,
        is_active -> Nullable<Bool>,
        created_at -> Timestamp,
        updated_at -> Timestamp,
    }
}

diesel::table! {
    dashboards (id) {
        id -> Uuid,
        user_id -> Uuid,
        name -> Varchar,
        layout -> Jsonb,
        is_default -> Bool,
        created_at -> Timestamp,
        updated_at -> Timestamp,
    }
}

diesel::table! {
    documents (id) {
        id -> Uuid,
        user_id -> Uuid,
        patient_id -> Nullable<Uuid>,
        model_id -> Nullable<Uuid>,
        #[max_length = 255]
        title -> Varchar,
        #[max_length = 255]
        filename -> Varchar,
        #[max_length = 1024]
        s3_key -> Varchar,
        #[max_length = 100]
        mime_type -> Nullable<Varchar>,
        size_bytes -> Nullable<Int8>,
        created_at -> Timestamp,
        updated_at -> Timestamp,
        created_by -> Uuid,
        updated_by -> Uuid,
    }
}

diesel::table! {
    models (id) {
        id -> Uuid,
        user_id -> Uuid,
        #[max_length = 255]
        title -> Varchar,
        #[max_length = 100]
        category -> Varchar,
        #[max_length = 50]
        type_ -> Varchar,
        description -> Nullable<Text>,
        content -> Text,
        is_favorite -> Nullable<Bool>,
        tags -> Nullable<Array<Nullable<Text>>>,
        last_used -> Nullable<Timestamp>,
        created_at -> Timestamp,
        updated_at -> Timestamp,
        template_content -> Nullable<Jsonb>,
        form_schema -> Nullable<Jsonb>,
        output_types -> Nullable<Array<Nullable<Text>>>,
        usage_count -> Nullable<Int4>,
    }
}

diesel::table! {
    notes (id) {
        id -> Uuid,
        user_id -> Uuid,
        content -> Text,
        patient_id -> Nullable<Uuid>,
        appointment_id -> Nullable<Uuid>,
        created_at -> Timestamp,
        updated_at -> Timestamp,
    }
}

diesel::table! {
    patient_contacts (id) {
        id -> Uuid,
        patient_id -> Uuid,
        contact_id -> Uuid,
        #[max_length = 50]
        role -> Varchar,
        is_primary -> Nullable<Bool>,
        created_at -> Timestamp,
    }
}

diesel::table! {
    patient_tags (id) {
        id -> Uuid,
        patient_id -> Uuid,
        tag_id -> Uuid,
        created_at -> Timestamp,
    }
}

diesel::table! {
    patients (id) {
        id -> Uuid,
        user_id -> Uuid,
        #[max_length = 255]
        full_name -> Varchar,
        date_of_birth -> Nullable<Date>,
        #[max_length = 50]
        gender -> Nullable<Varchar>,
        #[max_length = 50]
        marital_status -> Nullable<Varchar>,
        #[max_length = 14]
        cpf -> Nullable<Varchar>,
        #[max_length = 20]
        rg -> Nullable<Varchar>,
        #[max_length = 20]
        phone -> Nullable<Varchar>,
        #[max_length = 255]
        email -> Nullable<Varchar>,
        address -> Nullable<Text>,
        #[max_length = 100]
        city -> Nullable<Varchar>,
        #[max_length = 50]
        state -> Nullable<Varchar>,
        #[max_length = 50]
        ethnicity -> Nullable<Varchar>,
        #[max_length = 100]
        nationality -> Nullable<Varchar>,
        #[max_length = 100]
        naturalness -> Nullable<Varchar>,
        #[max_length = 255]
        occupation -> Nullable<Varchar>,
        notes -> Nullable<Text>,
        is_active -> Nullable<Bool>,
        created_at -> Timestamp,
        updated_at -> Timestamp,
    }
}

diesel::table! {
    refresh_tokens (id) {
        id -> Uuid,
        user_id -> Uuid,
        token -> Text,
        expires_at -> Timestamptz,
    }
}

diesel::table! {
    revoked_tokens (id) {
        id -> Uuid,
        #[max_length = 255]
        token_jti -> Varchar,
        expiry -> Timestamp,
        revoked_at -> Nullable<Timestamp>,
        user_id -> Nullable<Uuid>,
    }
}

diesel::table! {
    session_notes (id) {
        id -> Uuid,
        user_id -> Uuid,
        patient_id -> Uuid,
        appointment_id -> Nullable<Uuid>,
        #[max_length = 255]
        title -> Varchar,
        content -> Text,
        created_at -> Timestamp,
        updated_at -> Timestamp,
    }
}

diesel::table! {
    tags (id) {
        id -> Uuid,
        #[max_length = 100]
        name -> Varchar,
        #[max_length = 50]
        color -> Varchar,
        user_id -> Uuid,
        created_at -> Timestamp,
    }
}

diesel::table! {
    transactions (id) {
        id -> Uuid,
        user_id -> Uuid,
        patient_id -> Nullable<Uuid>,
        appointment_id -> Nullable<Uuid>,
        #[max_length = 255]
        description -> Varchar,
        amount -> Numeric,
        #[sql_name = "type"]
        #[max_length = 50]
        type_ -> Varchar,
        #[max_length = 50]
        status -> Varchar,
        transaction_date -> Timestamp,
        created_at -> Timestamp,
        updated_at -> Timestamp,
    }
}

diesel::table! {
    users (id) {
        id -> Uuid,
        #[max_length = 255]
        first_name -> Varchar,
        #[max_length = 255]
        last_name -> Varchar,
        #[max_length = 255]
        email -> Varchar,
        date_of_birth -> Nullable<Timestamp>,
        #[max_length = 14]
        cpf -> Nullable<Varchar>,
        #[max_length = 20]
        phone -> Nullable<Varchar>,
        address -> Nullable<Text>,
        #[max_length = 100]
        city -> Nullable<Varchar>,
        #[max_length = 50]
        state -> Nullable<Varchar>,
        #[max_length = 255]
        occupation -> Nullable<Varchar>,
        password_hash -> Text,
        is_active -> Nullable<Bool>,
        is_verified -> Nullable<Bool>,
        verification_token -> Nullable<Text>,
        verification_token_expires -> Nullable<Timestamp>,
        password_reset_token -> Nullable<Text>,
        password_reset_expires -> Nullable<Timestamp>,
        profile_picture -> Nullable<Text>,
        created_at -> Nullable<Timestamp>,
        updated_at -> Nullable<Timestamp>,
    }
}

diesel::joinable!(appointments -> patients (patient_id));
diesel::joinable!(appointments -> users (user_id));
diesel::joinable!(assessments -> patients (patient_id));
diesel::joinable!(contacts -> users (user_id));
diesel::joinable!(dashboards -> users (user_id));
diesel::joinable!(documents -> models (model_id));
diesel::joinable!(documents -> patients (patient_id));
diesel::joinable!(models -> users (user_id));
diesel::joinable!(notes -> appointments (appointment_id));
diesel::joinable!(notes -> patients (patient_id));
diesel::joinable!(notes -> users (user_id));
diesel::joinable!(patient_contacts -> contacts (contact_id));
diesel::joinable!(patient_contacts -> patients (patient_id));
diesel::joinable!(patient_tags -> patients (patient_id));
diesel::joinable!(patient_tags -> tags (tag_id));
diesel::joinable!(patients -> users (user_id));
diesel::joinable!(refresh_tokens -> users (user_id));
diesel::joinable!(revoked_tokens -> users (user_id));
diesel::joinable!(session_notes -> appointments (appointment_id));
diesel::joinable!(session_notes -> patients (patient_id));
diesel::joinable!(session_notes -> users (user_id));
diesel::joinable!(tags -> users (user_id));
diesel::joinable!(transactions -> appointments (appointment_id));
diesel::joinable!(transactions -> patients (patient_id));
diesel::joinable!(transactions -> users (user_id));

diesel::allow_tables_to_appear_in_same_query!(
    appointments,
    assessments,
    contacts,
    dashboards,
    documents,
    models,
    notes,
    patient_contacts,
    patient_tags,
    patients,
    refresh_tokens,
    revoked_tokens,
    session_notes,
    tags,
    transactions,
    users,
);

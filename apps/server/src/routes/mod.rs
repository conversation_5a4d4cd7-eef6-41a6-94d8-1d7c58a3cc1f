use crate::appointment::appointment_handler;
use crate::auth::{auth_handler, auth_middleware::auth_middleware};
use crate::app::AppState;
use crate::contact::contact_handler;
// Importar todos os handlers do dashboard
use crate::dashboard::dashboard_handler;
use crate::document::document_handler; // Importar handlers de documentos
use crate::financial::financial_handler;
// use crate::guardian::guardian_handler; // Removido
use crate::middleware::error_middleware::error_handling_middleware;
use crate::middleware::rate_limiter::{rate_limit_middleware, RateLimiter};
use crate::model::model_handler; // Importar handlers de modelos
use crate::notes::note_handler; // Importar handlers de notas
use crate::patient::patient_handler;
use crate::session_note::handler as session_note_handler; // Importar handlers de notas de sessão
use crate::tag::tag_handler;
use crate::user::user_handler;
use axum::http::{HeaderName, Method};
use axum::middleware::from_fn_with_state;
use axum::{middleware::from_fn, Router};
use tower_cookies::CookieManagerLayer;
use tower_http::cors::CorsLayer;
use crate::assessment::assessment_handler;

pub fn create_routes(app_state: AppState) -> Router {
    let cors = CorsLayer::new()
        .allow_origin(["http://localhost:5173".parse().unwrap()])
        .allow_methods([
            Method::GET,
            Method::POST,
            Method::PUT,
            Method::DELETE,
            Method::OPTIONS,
            Method::PATCH,
        ])
        .allow_headers([
            HeaderName::from_static("content-type"),
            HeaderName::from_static("authorization"),
            HeaderName::from_static("x-requested-with"),
        ])
        .allow_credentials(true);

    let strict_limiter = RateLimiter::new(100, 60); // 5 req/min
    let permissive_limiter = RateLimiter::new(500, 60); // 15 req/min

    let auth_routes = Router::new()
        .route("/login", axum::routing::post(auth_handler::login_handler))
        .route(
            "/register",
            axum::routing::post(auth_handler::register_handler),
        )
        .route(
            "/forgot-password",
            axum::routing::post(auth_handler::forgot_password_handler),
        )
        .layer(from_fn_with_state(strict_limiter, rate_limit_middleware));

    let user_routes = Router::new()
        .route(
            "/profile",
            axum::routing::get(user_handler::get_user_profile_handler),
        )
        .route(
            "/profile",
            axum::routing::put(user_handler::update_user_profile_handler),
        );

    let patient_routes = Router::new()
        .route(
            "/",
            axum::routing::get(patient_handler::list_patients_handler),
        )
        .route(
            "/search",
            axum::routing::get(patient_handler::search_patients_handler),
        )
        .route(
            "/{id}",
            axum::routing::get(patient_handler::get_patient_details_handler),
        )
        .route(
            "/",
            axum::routing::post(patient_handler::create_patient_handler),
        )
        .route(
            "/{id}",
            axum::routing::put(patient_handler::update_patient_handler),
        )
        .route(
            "/{id}",
            axum::routing::delete(patient_handler::delete_patient_handler),
        )
        // Aninhar rotas de assessment sob pacientes
        .nest(
            "/{id}/assessments",
            assessment_handler::create_assessment_routes(),
        )
        // Rotas de tags vinculadas a pacientes
        .route(
            "/{id}/tags",
            axum::routing::get(tag_handler::get_patient_tags_handler),
        )
        .route(
            "/{id}/tags",
            axum::routing::post(tag_handler::add_tag_to_patient_handler),
        )
        .route(
            "/{id}/tags/{tag_id}",
            axum::routing::delete(tag_handler::remove_tag_from_patient_handler),
        );

    let appointment_routes = Router::new()
        .route(
            "/",
            axum::routing::get(appointment_handler::get_appointments_handler),
        )
        .route(
            "/{id}",
            axum::routing::get(appointment_handler::get_appointment_by_id_handler),
        )
        .route(
            "/",
            axum::routing::post(appointment_handler::create_appointment_handler),
        )
        .route(
            "/{id}",
            axum::routing::put(appointment_handler::update_appointment_handler),
        )
        .route(
            "/{id}/recurring",
            axum::routing::put(appointment_handler::update_recurring_appointment_handler),
        )
        .route(
            "/{id}",
            axum::routing::delete(appointment_handler::delete_appointment_handler),
        )
        .route(
            "/slots",
            axum::routing::get(appointment_handler::get_available_slots_handler),
        )
        .route(
            "/by_patient/{patient_id}",
            axum::routing::get(appointment_handler::get_appointments_by_patient_handler),
        );

    let contact_routes = Router::new()
        .route(
            "/",
            axum::routing::get(contact_handler::get_contacts_list_handler),
        )
        .route(
            "/{id}",
            axum::routing::get(contact_handler::get_contact_details_handler),
        )
        .route(
            "/",
            axum::routing::post(contact_handler::create_contact_handler),
        )
        .route(
            "/{id}",
            axum::routing::put(contact_handler::update_contact_handler),
        )
        .route(
            "/{id}",
            axum::routing::delete(contact_handler::delete_contact_handler),
        )
        // Nova rota para buscar pacientes vinculados a um contato
        .route(
            "/{id}/linked-patients",
            axum::routing::get(contact_handler::get_linked_patients_handler),
        )
        // Nova rota para buscar o resumo de vínculos de um contato
        .route(
            "/{id}/links-summary",
            axum::routing::get(contact_handler::get_contact_links_summary_handler),
        );

    let patient_contact_routes = Router::new()
        .route(
            "/patients/{id}/contacts",
            axum::routing::get(contact_handler::get_patient_contacts_handler),
        )
        .route(
            "/patients/{id}/contacts",
            axum::routing::post(contact_handler::link_contact_to_patient_handler),
        )
        .route(
            "/patients/{id}/contacts/{contact_id}",
            axum::routing::delete(contact_handler::unlink_contact_from_patient_handler),
        );

    // Nova rota para tags
    let tag_routes = Router::new()
        .route("/", axum::routing::get(tag_handler::get_tags_list_handler))
        .route("/", axum::routing::post(tag_handler::create_tag_handler))
        .route("/{id}", axum::routing::put(tag_handler::update_tag_handler))
        .route(
            "/{id}",
            axum::routing::delete(tag_handler::delete_tag_handler),
        );

    // Rotas para notas
    let note_routes = Router::new()
        .route("/", axum::routing::post(note_handler::create_note_handler))
        .route("/", axum::routing::get(note_handler::get_recent_notes_handler))
        .route("/{note_id}", axum::routing::put(note_handler::update_note_handler))
        .route("/{note_id}", axum::routing::delete(note_handler::delete_note_handler));

    // Rotas para modelos
    let model_routes = Router::new()
        .route("/", axum::routing::get(model_handler::get_models_list_handler))
        .route("/{id}", axum::routing::get(model_handler::get_model_details_handler))
        .route("/", axum::routing::post(model_handler::create_model_handler))
        .route("/{id}", axum::routing::put(model_handler::update_model_handler))
        .route("/{id}", axum::routing::delete(model_handler::delete_model_handler))
        .route("/{id}/favorite", axum::routing::put(model_handler::toggle_favorite_handler))
        .route("/{id}/last-used", axum::routing::put(model_handler::update_last_used_handler))
        .route("/{id}/duplicate", axum::routing::post(model_handler::duplicate_model_handler))
        .route("/{id}/export", axum::routing::get(model_handler::export_model_handler))
        .route("/import", axum::routing::post(model_handler::import_model_handler))
        .route("/{id}/apply", axum::routing::post(model_handler::apply_model_handler)); // Nova rota para aplicar modelos dinâmicos

    // Rotas para notas de sessão
    let session_note_routes = Router::new()
        .route("/", axum::routing::post(session_note_handler::create_session_note_handler))
        .route("/", axum::routing::get(session_note_handler::list_session_notes_handler))
        .route("/{note_id}", axum::routing::get(session_note_handler::get_session_note_handler))
        .route("/{note_id}", axum::routing::put(session_note_handler::update_session_note_handler))
        .route("/{note_id}", axum::routing::delete(session_note_handler::delete_session_note_handler));

    // let guardian_routes = Router::new() // Removido
    //     .route("/", axum::routing::get(guardian_handler::list_guardians_handler)); // Removido

    let public_routes = Router::new()
        .merge(auth_routes)
        .route(
            "/reset-password",
            axum::routing::post(auth_handler::reset_password_handler),
        )
        .route(
            "/refresh",
            axum::routing::post(auth_handler::refresh_token_handler),
        )
        .route(
            "/verify-email/{token}",
            axum::routing::get(auth_handler::verify_email_handler),
        )
        .route(
            "/resend-verification-email",
            axum::routing::post(auth_handler::resend_verification_email_handler),
        )
        .layer(from_fn_with_state(
            permissive_limiter,
            rate_limit_middleware,
        ));

    // Rotas para documentos
    let document_routes = Router::new()
        .route("/upload", axum::routing::post(document_handler::upload_document_handler))
        // .route("/from-html", axum::routing::post(document_handler::create_document_from_html_handler))
        .route("/", axum::routing::get(document_handler::list_documents_handler))
        .route("/{document_id}/content", axum::routing::get(document_handler::get_document_content_handler))
        .route("/{document_id}/download", axum::routing::get(document_handler::get_document_download_url_handler))
        .route("/{document_id}", axum::routing::put(document_handler::update_document_handler))
        .route("/{document_id}/content", axum::routing::put(document_handler::update_document_content_handler))
        .route("/{document_id}", axum::routing::delete(document_handler::delete_document_handler));

    let financial_routes = Router::new()
        .route(
            "/",
            axum::routing::post(financial_handler::create_transaction_handler),
        )
        .route(
            "/",
            axum::routing::get(financial_handler::list_transactions_handler),
        )
        .route(
            "/summary",
            axum::routing::get(financial_handler::get_summary_handler),
        )
        .route(
            "/{id}",
            axum::routing::put(financial_handler::update_transaction_handler),
        )
        .route(
            "/{id}",
            axum::routing::delete(financial_handler::delete_transaction_handler),
        )
        .route(
            "/{id}/status",
            axum::routing::patch(financial_handler::update_transaction_status_handler),
        )
        .route(
            "/export",
            axum::routing::get(financial_handler::export_transactions_handler),
        );

    let protected_routes = Router::new()
        .nest("/user", user_routes)
        .nest("/patients", patient_routes)
        .nest("/appointments", appointment_routes)
        .nest("/contacts", contact_routes)
        .nest("/tags", tag_routes)
        .nest("/notes", note_routes) // Adicionar rotas de notas
        .nest("/models", model_routes) // Adicionar rotas de modelos
        .nest("/session-notes", session_note_routes) // Adicionar rotas de notas de sessão
        .nest("/documents", document_routes) // Adicionar rotas de documentos
        .nest("/financial", financial_routes)
        // .nest("/guardians", guardian_routes) // Removido
        .merge(patient_contact_routes)
        .route("/logout", axum::routing::post(auth_handler::logout_handler))
        .route(
            "/change-password",
            axum::routing::post(auth_handler::change_password_handler),
        )
        .route(
            "/dashboard/summary",
            axum::routing::get(dashboard_handler::get_dashboard_summary_handler), // Usar nome atualizado
        )
        // --- Novas Rotas para Múltiplos Dashboards ---
        .route(
            "/dashboards", // Listar todos os dashboards do usuário
            axum::routing::get(dashboard_handler::list_dashboards_handler),
        )
        .route(
            "/dashboards", // Criar um novo dashboard
            axum::routing::post(dashboard_handler::create_dashboard_handler),
        )
        .route(
            "/dashboards/default", // Obter o dashboard padrão
            axum::routing::get(dashboard_handler::get_default_dashboard_handler),
        )
        .route(
            "/dashboards/{dashboard_id}", // Obter um dashboard específico (Sintaxe corrigida)
            axum::routing::get(dashboard_handler::get_dashboard_handler),
        )
        .route(
            "/dashboards/{dashboard_id}", // Atualizar um dashboard específico (Sintaxe corrigida)
            axum::routing::put(dashboard_handler::update_dashboard_handler),
        )
        .route(
            "/dashboards/{dashboard_id}", // Deletar um dashboard específico (Sintaxe corrigida)
            axum::routing::delete(dashboard_handler::delete_dashboard_handler),
        )
        .route(
            "/dashboards/{dashboard_id}/set-default", // Definir como padrão (Sintaxe corrigida)
            axum::routing::post(dashboard_handler::set_default_dashboard_handler), // Usar POST para ação
        )
        // --- Rotas Antigas Removidas ---
        // .route(
        //     "/dashboard/layout/config",
        //     axum::routing::get(get_dashboard_layout_config_handler), // Removida
        // )
        // .route(
        //     "/dashboard/layout/config",
        //     axum::routing::put(update_dashboard_layout_config_handler), // Removida
        // )
        .layer(from_fn_with_state(app_state.pool.clone(), auth_middleware));

    Router::new()
        .nest("/auth", public_routes)
        .nest("/protected", protected_routes)
        .layer(from_fn(error_handling_middleware))
        .layer(CookieManagerLayer::new())
        .layer(cors)
        .with_state(app_state)
}

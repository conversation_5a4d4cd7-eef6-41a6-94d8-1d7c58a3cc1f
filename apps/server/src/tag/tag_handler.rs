use crate::auth::auth_middleware::AuthUser;
use crate::config::database::DbPool;
use crate::errors::{ApiError, ApiResult};
use crate::tag::tag_dto::{AddTagToPatientRequest, CreateTagRequest, UpdateTagRequest};
use crate::tag::tag_service;
use axum::{
    extract::{Extension, Path},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use uuid::Uuid;
use validator::ValidationErrors;

pub async fn get_tags_list_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match tag_service::get_all_tags(user_id, &mut conn).await {
        Ok(tags) => Ok(Json(tags)),
        Err(e) => Err(ApiError::InternalServerError(e)),
    }
}

pub async fn create_tag_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(payload): Json<CreateTagRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    if payload.name.trim().is_empty() {
        let mut errors = ValidationErrors::new();
        let mut error = validator::ValidationError::new("empty_name");
        error.message = Some("Nome da tag não pode estar vazio".into());
        errors.add("name", error);
        return Err(ApiError::ValidationError(errors));
    }

    match tag_service::create_new_tag(user_id, payload, &mut conn).await {
        Ok(tag) => Ok((StatusCode::CREATED, Json(tag))),
        Err(e) => Err(ApiError::InternalServerError(e)),
    }
}

pub async fn update_tag_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(tag_id): Path<Uuid>,
    Json(payload): Json<UpdateTagRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    if payload.name.is_none() && payload.color.is_none() {
        let mut errors = ValidationErrors::new();
        let mut error = validator::ValidationError::new("empty_update");
        error.message = Some("Pelo menos um campo deve ser fornecido para atualização".into());
        errors.add("update", error);
        return Err(ApiError::ValidationError(errors));
    }

    match tag_service::update_existing_tag(user_id, tag_id, payload, &mut conn).await {
        Ok(tag) => Ok(Json(tag)),
        Err(e) => {
            if e.contains("não encontrada") {
                Err(ApiError::ResourceNotFound("Tag".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn delete_tag_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(tag_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match tag_service::delete_tag(user_id, &tag_id, &mut conn).await {
        Ok(_) => Ok(StatusCode::NO_CONTENT),
        Err(e) => {
            if e.contains("não encontrada") {
                Err(ApiError::ResourceNotFound("Tag".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn get_patient_tags_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(patient_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match tag_service::get_patient_tags(user_id, patient_id, &mut conn).await {
        Ok(tags) => Ok(Json(tags)),
        Err(e) => {
            if e.contains("Paciente não encontrado") {
                Err(ApiError::ResourceNotFound("Paciente".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn add_tag_to_patient_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(patient_id): Path<Uuid>,
    Json(payload): Json<AddTagToPatientRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match tag_service::add_tag_to_patient(user_id, patient_id, payload.tag_id, &mut conn).await {
        Ok(_) => Ok(Json(serde_json::json!({
            "message": "Tag adicionada com sucesso"
        }))),
        Err(e) => {
            if e.contains("Paciente não encontrado") {
                Err(ApiError::ResourceNotFound("Paciente".to_string()))
            } else if e.contains("Tag não encontrada") {
                Err(ApiError::ResourceNotFound("Tag".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn remove_tag_from_patient_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path((patient_id, tag_id)): Path<(Uuid, Uuid)>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match tag_service::remove_tag_from_patient(user_id, patient_id, tag_id, &mut conn).await {
        Ok(_) => Ok(StatusCode::NO_CONTENT),
        Err(e) => {
            if e.contains("Paciente não encontrado") {
                Err(ApiError::ResourceNotFound("Paciente".to_string()))
            } else if e.contains("Tag não encontrada") {
                Err(ApiError::ResourceNotFound("Tag".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

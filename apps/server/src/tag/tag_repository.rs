use crate::db::models::patient_tag::NewPatientTag;
use crate::db::models::tag::{NewTag, Tag};
use crate::schema::patient_tags::dsl::{
    patient_id as pt_patient_id, patient_tags, tag_id as pt_tag_id,
};
use crate::schema::tags::dsl::*;
use diesel::prelude::*;
use diesel::result::QueryResult;
use diesel::PgConnection;
use uuid::Uuid;

// Operações para tags
pub fn find_tags_by_user(user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<Vec<Tag>> {
    tags.filter(user_id.eq(user_id_val))
        .order_by(name.asc())
        .load::<Tag>(conn)
}

pub fn find_tag_by_id(
    tag_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Tag> {
    tags.filter(id.eq(tag_id_val))
        .filter(user_id.eq(user_id_val))
        .first::<Tag>(conn)
}

pub fn create_tag(new_tag: &NewTag, conn: &mut PgConnection) -> QueryResult<Tag> {
    diesel::insert_into(tags).values(new_tag).get_result(conn)
}

pub fn update_tag(
    tag_id_val: Uuid,
    user_id_val: Uuid,
    tag_data: &Tag,
    conn: &mut PgConnection,
) -> QueryResult<Tag> {
    diesel::update(
        tags.filter(id.eq(tag_id_val))
            .filter(user_id.eq(user_id_val)),
    )
    .set((name.eq(&tag_data.name), color.eq(&tag_data.color)))
    .get_result(conn)
}

pub fn delete_tag(
    tag_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    diesel::delete(
        tags.filter(id.eq(tag_id_val))
            .filter(user_id.eq(user_id_val)),
    )
    .execute(conn)
}

// Operações para associações entre pacientes e tags
pub fn find_tags_by_patient(patient_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<Vec<Tag>> {
    use crate::schema::patient_tags;
    use crate::schema::tags;

    tags::table
        .inner_join(patient_tags::table.on(tags::id.eq(patient_tags::tag_id)))
        .filter(patient_tags::patient_id.eq(patient_id_val))
        .select(Tag::as_select())
        .order_by(name.asc())
        .load::<Tag>(conn)
}

pub fn add_tag_to_patient(
    patient_id_val: Uuid,
    tag_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    let new_patient_tag = NewPatientTag {
        patient_id: patient_id_val,
        tag_id: tag_id_val,
    };

    diesel::insert_into(patient_tags)
        .values(&new_patient_tag)
        .on_conflict((pt_patient_id, pt_tag_id))
        .do_nothing()
        .execute(conn)
}

pub fn remove_tag_from_patient(
    patient_id_val: Uuid,
    tag_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    diesel::delete(
        patient_tags
            .filter(pt_patient_id.eq(patient_id_val))
            .filter(pt_tag_id.eq(tag_id_val)),
    )
    .execute(conn)
}

pub fn generate_tag_id() -> Uuid {
    Uuid::new_v4()
}

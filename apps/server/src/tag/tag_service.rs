use crate::db::models::tag::{NewTag, Tag};
use crate::tag::tag_dto::{CreateTagRequest, TagResponse, UpdateTagRequest};
use crate::tag::tag_repository;
use diesel::PgConnection;
use std::collections::HashMap;
use uuid::Uuid;

fn tag_to_response(tag: Tag) -> TagResponse {
    TagResponse {
        id: tag.id,
        name: tag.name,
        color: tag.color,
    }
}

pub async fn get_all_tags(
    user_id: Uuid,
    conn: &mut PgConnection,
) -> Result<Vec<TagResponse>, String> {
    let tags = tag_repository::find_tags_by_user(user_id, conn)
        .map_err(|e| format!("Erro ao buscar tags: {}", e))?;
    let responses = tags.into_iter().map(tag_to_response).collect();
    Ok(responses)
}

pub async fn create_new_tag(
    user_id: Uuid,
    request: CreateTagRequest,
    conn: &mut PgConnection,
) -> Result<TagResponse, String> {
    let tag_id = tag_repository::generate_tag_id();
    let tag_color = request.color.unwrap_or_else(|| "blue".to_string());
    let new_tag = NewTag {
        id: tag_id,
        name: request.name,
        color: tag_color,
        user_id,
    };
    let tag = tag_repository::create_tag(&new_tag, conn)
        .map_err(|e| format!("Erro ao criar tag: {}", e))?;
    Ok(tag_to_response(tag))
}

pub async fn update_existing_tag(
    user_id: Uuid,
    tag_id: Uuid,
    request: UpdateTagRequest,
    conn: &mut PgConnection,
) -> Result<TagResponse, String> {
    let mut tag = tag_repository::find_tag_by_id(tag_id, user_id, conn)
        .map_err(|_| "Tag não encontrada".to_string())?;
    if let Some(name) = request.name {
        tag.name = name;
    }
    if let Some(color) = request.color {
        tag.color = color;
    }
    let updated_tag = tag_repository::update_tag(tag_id, user_id, &tag, conn)
        .map_err(|e| format!("Erro ao atualizar tag: {}", e))?;
    Ok(tag_to_response(updated_tag))
}

pub async fn delete_tag(user_id: Uuid, tag_id: &Uuid, conn: &mut PgConnection) -> Result<(), String> {
    let _ = tag_repository::find_tag_by_id(*tag_id, user_id, conn)
        .map_err(|_| "Tag não encontrada".to_string())?;
    tag_repository::delete_tag(*tag_id, user_id, conn)
        .map_err(|e| format!("Erro ao excluir tag: {}", e))?;
    Ok(())
}

pub async fn get_patient_tags(
    user_id: Uuid,
    patient_id: Uuid,
    conn: &mut PgConnection,
) -> Result<Vec<TagResponse>, String> {
    let _ = crate::patient::patient_repository::find_patient_by_id(patient_id, user_id, conn)
        .map_err(|_| "Paciente não encontrado".to_string())?;
    let tags = tag_repository::find_tags_by_patient(patient_id, conn)
        .map_err(|e| format!("Erro ao buscar tags do paciente: {}", e))?;
    let responses = tags.into_iter().map(tag_to_response).collect();
    Ok(responses)
}

pub async fn add_tag_to_patient(
    user_id: Uuid,
    patient_id: Uuid,
    tag_id: Uuid,
    conn: &mut PgConnection,
) -> Result<(), String> {
    let _ = crate::patient::patient_repository::find_patient_by_id(patient_id, user_id, conn)
        .map_err(|_| "Paciente não encontrado".to_string())?;
    let _ = tag_repository::find_tag_by_id(tag_id, user_id, conn)
        .map_err(|_| "Tag não encontrada".to_string())?;
    tag_repository::add_tag_to_patient(patient_id, tag_id, conn)
        .map_err(|e| format!("Erro ao adicionar tag ao paciente: {}", e))?;
    Ok(())
}

pub async fn remove_tag_from_patient(
    user_id: Uuid,
    patient_id: Uuid,
    tag_id: Uuid,
    conn: &mut PgConnection,
) -> Result<(), String> {
    let _ = crate::patient::patient_repository::find_patient_by_id(patient_id, user_id, conn)
        .map_err(|_| "Paciente não encontrado".to_string())?;
    let _ = tag_repository::find_tag_by_id(tag_id, user_id, conn)
        .map_err(|_| "Tag não encontrada".to_string())?;
    tag_repository::remove_tag_from_patient(patient_id, tag_id, conn)
        .map_err(|e| format!("Erro ao remover tag do paciente: {}", e))?;
    Ok(())
}

pub fn extract_tags_from_notes(notes: &str) -> Vec<String> {
    let mut tags = Vec::new();
    for line in notes.lines() {
        let line = line.trim();
        if line.starts_with("tag:") {
            if let Some(tag_id) = line.strip_prefix("tag:") {
                tags.push(tag_id.trim().to_string());
            }
        }
    }
    tags
}

pub fn process_special_fields(notes: &str) -> HashMap<String, String> {
    let mut fields = HashMap::new();
    for line in notes.lines() {
        let line = line.trim();
        if let Some(idx) = line.find(':') {
            let (key, value) = line.split_at(idx);
            let key = key.trim();
            let value = value[1..].trim();
            if !key.is_empty() && !value.is_empty() && key != "tag" {
                fields.insert(key.to_string(), value.to_string());
            }
        }
    }
    fields
}

// use once_cell::sync::Lazy;
// use regex::Regex;

// static CPF_REGEX: Lazy<Regex> =
//     Lazy::new(|| Regex::new(r"^(\d{3}\.?\d{3}\.?\d{3}-?\d{2})$").unwrap());

pub fn sanitize_cpf(cpf: &str) -> String {
    cpf.chars().filter(|c| c.is_ascii_digit()).collect()
}

// pub fn format_cpf(cpf: &str) -> String {
//     let digits = sanitize_cpf(cpf);
//     if digits.len() != 11 {
//         return digits;
//     }

//     format!(
//         "{}.{}.{}-{}",
//         &digits[0..3],
//         &digits[3..6],
//         &digits[6..9],
//         &digits[9..11]
//     )
// }

pub fn is_valid_cpf(cpf: &str) -> bool {
    if cpf.trim().is_empty() {
        return false;
    }

    let digits = sanitize_cpf(cpf);

    if digits.len() != 11 {
        return false;
    }

    if digits.chars().all(|c| c == digits.chars().next().unwrap()) {
        return false;
    }

    let digits: Vec<u32> = digits.chars().map(|c| c.to_digit(10).unwrap()).collect();

    let sum: u32 = digits[0] * 10
        + digits[1] * 9
        + digits[2] * 8
        + digits[3] * 7
        + digits[4] * 6
        + digits[5] * 5
        + digits[6] * 4
        + digits[7] * 3
        + digits[8] * 2;

    let remainder = (sum * 10) % 11;
    let first_check = if remainder == 10 { 0 } else { remainder };

    if first_check != digits[9] {
        return false;
    }

    let sum: u32 = digits[0] * 11
        + digits[1] * 10
        + digits[2] * 9
        + digits[3] * 8
        + digits[4] * 7
        + digits[5] * 6
        + digits[6] * 5
        + digits[7] * 4
        + digits[8] * 3
        + digits[9] * 2;

    let remainder = (sum * 10) % 11;
    let second_check = if remainder == 10 { 0 } else { remainder };

    second_check == digits[10]
}

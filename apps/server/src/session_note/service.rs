use crate::config::database::DbPool;
use crate::db::models::session_note::{NewSessionNote, UpdateSessionNoteData};
use crate::errors::{ApiError, ApiResult};
use crate::patient::patient_repository;
use crate::session_note::dto::{CreateSessionNoteRequest, SessionNoteResponse, UpdateSessionNoteRequest};
use crate::session_note::repository;
use uuid::Uuid;

// Função para criar uma nova nota de sessão
pub async fn create_session_note_service(
    pool: &DbPool,
    user_id: Uuid,
    payload: CreateSessionNoteRequest,
) -> ApiResult<SessionNoteResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    let patient = patient_repository::find_patient_by_id(payload.patient_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Paciente".to_string()))?;


    let new_note = NewSessionNote {
        user_id,
        patient_id: payload.patient_id,
        appointment_id: payload.appointment_id,
        title: payload.title,
        content: payload.content,
    };

    let created_note = repository::create_session_note(new_note, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao criar nota de sessão: {}", e)))?;

    // Construir resposta com informações adicionais
    let response = SessionNoteResponse {
        id: created_note.id,
        patient_id: created_note.patient_id,
        appointment_id: created_note.appointment_id,
        title: created_note.title,
        content: created_note.content,
        created_at: created_note.created_at,
        updated_at: created_note.updated_at,
        patient_name: Some(patient.full_name),
        appointment_date: None, // Poderia buscar a data do agendamento se necessário
    };

    Ok(response)
}

// Função para buscar notas de sessão por paciente
pub async fn get_session_notes_by_patient_service(
    pool: &DbPool,
    user_id: Uuid,
    patient_id: Uuid,
    limit: i64,
) -> ApiResult<Vec<SessionNoteResponse>> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Verificar se o paciente existe e pertence ao usuário
    let patient = patient_repository::find_patient_by_id(patient_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Paciente".to_string()))?;

    let notes = repository::find_session_notes_by_patient(user_id, patient_id, limit, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao buscar notas de sessão: {}", e)))?;

    // Construir respostas com informações adicionais
    let responses = notes
        .into_iter()
        .map(|note| SessionNoteResponse {
            id: note.id,
            patient_id: note.patient_id,
            appointment_id: note.appointment_id,
            title: note.title,
            content: note.content,
            created_at: note.created_at,
            updated_at: note.updated_at,
            patient_name: Some(patient.full_name.clone()),
            appointment_date: None, // Poderia buscar a data do agendamento se necessário
        })
        .collect();

    Ok(responses)
}

// Função para buscar notas de sessão por agendamento
pub async fn get_session_notes_by_appointment_service(
    pool: &DbPool,
    user_id: Uuid,
    appointment_id: Uuid,
) -> ApiResult<Vec<SessionNoteResponse>> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Verificar se o agendamento existe e pertence ao usuário
    // (implementação omitida por brevidade)

    let notes = repository::find_session_notes_by_appointment(user_id, appointment_id, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao buscar notas de sessão: {}", e)))?;

    // Construir respostas com informações adicionais
    let responses = notes
        .into_iter()
        .map(|note| {
            // Buscar informações do paciente
            let patient_name = patient_repository::find_patient_by_id(note.patient_id, user_id, &mut conn)
                .ok()
                .map(|p| p.full_name);

            SessionNoteResponse {
                id: note.id,
                patient_id: note.patient_id,
                appointment_id: note.appointment_id,
                title: note.title,
                content: note.content,
                created_at: note.created_at,
                updated_at: note.updated_at,
                patient_name,
                appointment_date: None, // Poderia buscar a data do agendamento se necessário
            }
        })
        .collect();

    Ok(responses)
}

// Função para buscar uma nota de sessão por ID
pub async fn get_session_note_by_id_service(
    pool: &DbPool,
    user_id: Uuid,
    note_id: Uuid,
) -> ApiResult<SessionNoteResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    let note = repository::find_session_note_by_id(note_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Nota de sessão".to_string()))?;

    // Buscar informações do paciente
    let patient_name = patient_repository::find_patient_by_id(note.patient_id, user_id, &mut conn)
        .ok()
        .map(|p| p.full_name);

    // Construir resposta com informações adicionais
    let response = SessionNoteResponse {
        id: note.id,
        patient_id: note.patient_id,
        appointment_id: note.appointment_id,
        title: note.title,
        content: note.content,
        created_at: note.created_at,
        updated_at: note.updated_at,
        patient_name,
        appointment_date: None, // Poderia buscar a data do agendamento se necessário
    };

    Ok(response)
}

// Função para atualizar uma nota de sessão
pub async fn update_session_note_service(
    pool: &DbPool,
    user_id: Uuid,
    note_id: Uuid,
    payload: UpdateSessionNoteRequest,
) -> ApiResult<SessionNoteResponse> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Verificar se a nota existe e pertence ao usuário
    let _ = repository::find_session_note_by_id(note_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Nota de sessão".to_string()))?;

    let update_data = UpdateSessionNoteData {
        title: payload.title,
        content: payload.content,
        appointment_id: payload.appointment_id,
    };

    let updated_note = repository::update_session_note(note_id, user_id, update_data, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao atualizar nota de sessão: {}", e)))?;

    // Buscar informações do paciente
    let patient_name = patient_repository::find_patient_by_id(updated_note.patient_id, user_id, &mut conn)
        .ok()
        .map(|p| p.full_name);

    // Construir resposta com informações adicionais
    let response = SessionNoteResponse {
        id: updated_note.id,
        patient_id: updated_note.patient_id,
        appointment_id: updated_note.appointment_id,
        title: updated_note.title,
        content: updated_note.content,
        created_at: updated_note.created_at,
        updated_at: updated_note.updated_at,
        patient_name,
        appointment_date: None, // Poderia buscar a data do agendamento se necessário
    };

    Ok(response)
}

// Função para excluir uma nota de sessão
pub async fn delete_session_note_service(
    pool: &DbPool,
    user_id: Uuid,
    note_id: Uuid,
) -> ApiResult<()> {
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Verificar se a nota existe e pertence ao usuário
    let _ = repository::find_session_note_by_id(note_id, user_id, &mut conn)
        .map_err(|_| ApiError::ResourceNotFound("Nota de sessão".to_string()))?;

    repository::delete_session_note(note_id, user_id, &mut conn)
        .map_err(|e| ApiError::InternalServerError(format!("Erro ao excluir nota de sessão: {}", e)))?;

    Ok(())
}

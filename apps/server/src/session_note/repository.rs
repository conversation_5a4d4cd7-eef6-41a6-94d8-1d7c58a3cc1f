use crate::db::models::session_note::{NewSessionNote, SessionNote, UpdateSessionNoteData};
use crate::schema::session_notes;
use diesel::prelude::*;
use diesel::result::QueryResult;
use uuid::Uuid;

pub fn create_session_note(new_note: NewSessionNote, conn: &mut PgConnection) -> QueryResult<SessionNote> {
    diesel::insert_into(session_notes::table)
        .values(&new_note)
        .returning(SessionNote::as_returning())
        .get_result(conn)
}

pub fn find_session_notes_by_patient(
    user_id_val: Uuid,
    patient_id_val: Uuid,
    limit: i64,
    conn: &mut PgConnection,
) -> QueryResult<Vec<SessionNote>> {
    session_notes::table
        .filter(session_notes::user_id.eq(user_id_val))
        .filter(session_notes::patient_id.eq(patient_id_val))
        .order_by(session_notes::updated_at.desc())
        .limit(limit)
        .select(SessionNote::as_select())
        .load(conn)
}

pub fn find_session_notes_by_appointment(
    user_id_val: Uuid,
    appointment_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Vec<SessionNote>> {
    session_notes::table
        .filter(session_notes::user_id.eq(user_id_val))
        .filter(session_notes::appointment_id.eq(appointment_id_val))
        .order_by(session_notes::updated_at.desc())
        .select(SessionNote::as_select())
        .load(conn)
}

pub fn find_session_note_by_id(
    note_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<SessionNote> {
    session_notes::table
        .filter(session_notes::id.eq(note_id_val))
        .filter(session_notes::user_id.eq(user_id_val))
        .select(SessionNote::as_select())
        .first(conn)
}

pub fn update_session_note(
    note_id_val: Uuid,
    user_id_val: Uuid,
    update_data: UpdateSessionNoteData,
    conn: &mut PgConnection,
) -> QueryResult<SessionNote> {
    diesel::update(
        session_notes::table
            .filter(session_notes::id.eq(note_id_val))
            .filter(session_notes::user_id.eq(user_id_val)),
    )
    .set(update_data)
    .returning(SessionNote::as_returning())
    .get_result(conn)
}

pub fn delete_session_note(
    note_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    diesel::delete(
        session_notes::table
            .filter(session_notes::id.eq(note_id_val))
            .filter(session_notes::user_id.eq(user_id_val)),
    )
    .execute(conn)
}

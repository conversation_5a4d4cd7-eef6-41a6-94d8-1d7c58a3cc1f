use crate::auth::auth_middleware::AuthUser;
use crate::config::database::DbPool;
use crate::errors::{ApiError, ApiResult};
use crate::session_note::dto::{CreateSessionNoteRequest, ListSessionNotesParams, UpdateSessionNoteRequest};
use crate::session_note::service;
use axum::{
    extract::{Extension, Path, Query},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use uuid::Uuid;
use validator;

// Handler para criar uma nova nota de sessão
pub async fn create_session_note_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(payload): Json<CreateSessionNoteRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let created_note = service::create_session_note_service(&pool, user_id, payload).await?;
    Ok((StatusCode::CREATED, <PERSON><PERSON>(created_note)))
}

// Handler para listar notas de sessão (com filtros)
pub async fn list_session_notes_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Query(params): Query<ListSessionNotesParams>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;

    // Se patient_id for fornecido, buscar notas por paciente
    if let Some(patient_id) = params.patient_id {
        let notes = service::get_session_notes_by_patient_service(&pool, user_id, patient_id, params.limit).await?;
        return Ok(Json(notes));
    }

    // Se appointment_id for fornecido, buscar notas por agendamento
    if let Some(appointment_id) = params.appointment_id {
        let notes = service::get_session_notes_by_appointment_service(&pool, user_id, appointment_id).await?;
        return Ok(Json(notes));
    }

    // Se nenhum filtro for fornecido, retornar erro
    let mut errors = validator::ValidationErrors::new();
    let mut error = validator::ValidationError::new("missing_filter");
    error.message = Some("É necessário fornecer patient_id ou appointment_id".into());
    errors.add("filters", error);
    Err(ApiError::ValidationError(errors))
}

// Handler para buscar uma nota de sessão por ID
pub async fn get_session_note_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(note_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let note = service::get_session_note_by_id_service(&pool, user_id, note_id).await?;
    Ok(Json(note))
}

// Handler para atualizar uma nota de sessão
pub async fn update_session_note_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(note_id): Path<Uuid>,
    Json(payload): Json<UpdateSessionNoteRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let updated_note = service::update_session_note_service(&pool, user_id, note_id, payload).await?;
    Ok(Json(updated_note))
}

// Handler para excluir uma nota de sessão
pub async fn delete_session_note_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(note_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    service::delete_session_note_service(&pool, user_id, note_id).await?;
    Ok(StatusCode::NO_CONTENT)
}

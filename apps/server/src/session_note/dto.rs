use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Serialize, Debug)]
pub struct SessionNoteResponse {
    pub id: Uuid, 
    pub patient_id: Uuid, 
    pub appointment_id: Option<Uuid>, 
    pub title: String,
    pub content: String,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    pub patient_name: Option<String>,
    pub appointment_date: Option<NaiveDateTime>,
}

#[derive(Deserialize, Debug)]
pub struct CreateSessionNoteRequest {
    pub patient_id: Uuid, 
    pub appointment_id: Option<Uuid>, 
    pub title: String,
    pub content: String,
}

#[derive(Deserialize, Debug)]
pub struct UpdateSessionNoteRequest {
    pub title: Option<String>,
    pub content: Option<String>,
    pub appointment_id: Option<Option<Uuid>>, 
}

// Parâmetros para listar notas de sessão
#[derive(Deserialize, Debug)]
pub struct ListSessionNotesParams {
    pub patient_id: Option<Uuid>, 
    pub appointment_id: Option<Uuid>, 
    #[serde(default = "default_limit")]
    pub limit: i64,
}

fn default_limit() -> i64 {
    20 
}

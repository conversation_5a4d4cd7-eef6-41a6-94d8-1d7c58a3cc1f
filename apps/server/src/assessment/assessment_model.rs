use crate::schema::assessments;
use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Queryable, Identifiable, Serialize, Deserialize, Debug)]
#[diesel(table_name = assessments)]
pub struct Assessment {
    pub id: Uuid,
    pub patient_id: Uuid,
    pub title: String,
    pub content: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = assessments)]
pub struct NewAssessment {
    pub patient_id: Uuid,
    pub title: String,
    pub content: Option<String>,
} 
use crate::{
    app::AppState,
    assessment::{
        assessment_dto::CreateAssessmentRequest, assessment_service::AssessmentService,
    },
    auth::auth_middleware::AuthUser,
    errors::ApiResult,
};
use axum::{
    extract::{Extension, Path, State},
    response::IntoResponse,
    routing::post,
    Json, Router,
};
use uuid::Uuid;
use validator::Validate;

async fn create_assessment_handler(
    State(app_state): State<AppState>,
    Extension(user): Extension<AuthUser>,
    Path(patient_id): Path<Uuid>,
    Json(payload): Json<CreateAssessmentRequest>,
) -> ApiResult<impl IntoResponse> {
    payload.validate()?;
    let assessment =
        AssessmentService::create_assessment(&app_state.pool, patient_id, user.0, payload).await?;
    Ok((axum::http::StatusCode::CREATED, Json(assessment)))
}

async fn get_assessments_by_patient_handler(
    State(app_state): State<AppState>,
    Path(patient_id): Path<Uuid>,
    Extension(_user): Extension<AuthUser>,
) -> ApiResult<impl IntoResponse> {
    let assessments =
        AssessmentService::get_assessments_for_patient(&app_state.pool, patient_id).await?;
    Ok(Json(assessments))
}

pub fn create_assessment_routes() -> Router<AppState> {
    Router::new().route(
        "/",
        post(create_assessment_handler).get(get_assessments_by_patient_handler),
    )
} 
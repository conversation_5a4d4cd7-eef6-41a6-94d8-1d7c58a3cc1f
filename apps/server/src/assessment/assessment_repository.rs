use crate::assessment::assessment_model::{Assessment, NewAssessment};
use crate::schema::assessments;
use diesel::prelude::*;
use diesel::r2d2::{ConnectionManager, PooledConnection};
use diesel::PgConnection;
use uuid::Uuid;

pub struct AssessmentRepository;

impl AssessmentRepository {
    pub fn create(
        conn: &mut PooledConnection<ConnectionManager<PgConnection>>,
        new_assessment: NewAssessment,
    ) -> Result<Assessment, diesel::result::Error> {
        diesel::insert_into(assessments::table)
            .values(&new_assessment)
            .get_result(conn)
    }

    pub fn find_by_patient_id(
        conn: &mut PooledConnection<ConnectionManager<PgConnection>>,
        p_id: Uuid,
    ) -> Result<Vec<Assessment>, diesel::result::Error> {
        assessments::table
            .filter(assessments::patient_id.eq(p_id))
            .order(assessments::created_at.desc())
            .load::<Assessment>(conn)
    }

    pub fn find_by_id(
        conn: &mut PooledConnection<ConnectionManager<PgConnection>>,
        assessment_id: Uuid,
    ) -> Result<Option<Assessment>, diesel::result::Error> {
        assessments::table
            .find(assessment_id)
            .first(conn)
            .optional()
    }
} 
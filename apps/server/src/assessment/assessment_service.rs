use crate::assessment::assessment_dto::{AssessmentR<PERSON>ponse, CreateAssessmentRequest};
use crate::assessment::assessment_model::NewAssessment;
use crate::assessment::assessment_repository::AssessmentRepository;
use crate::config::database::DbPool;
use tokio::task;
use uuid::Uuid;

pub struct AssessmentService;

impl AssessmentService {
    pub async fn create_assessment(
        pool: &DbPool,
        patient_id: Uuid,
        _user_id: Uuid,
        req: CreateAssessmentRequest,
    ) -> Result<AssessmentResponse, String> {
        let mut conn = pool.get().expect("couldn't get db connection from pool");

        let new_assessment = NewAssessment {
            patient_id,
            title: req.title,
            content: req.content,
        };

        let assessment = task::spawn_blocking(move || {
            AssessmentRepository::create(&mut conn, new_assessment)
        })
        .await
        .map_err(|e| e.to_string())?
        .map_err(|e| e.to_string())?;

        Ok(AssessmentResponse {
            id: assessment.id,
            patient_id: assessment.patient_id,
            title: assessment.title,
            content: assessment.content,
            created_at: assessment.created_at,
            updated_at: assessment.updated_at,
        })
    }

    pub async fn get_assessments_for_patient(
        pool: &DbPool,
        patient_id: Uuid,
    ) -> Result<Vec<AssessmentResponse>, String> {
        let mut conn = pool.get().expect("couldn't get db connection from pool");

        let assessments = task::spawn_blocking(move || {
            AssessmentRepository::find_by_patient_id(&mut conn, patient_id)
        })
        .await
        .map_err(|e| e.to_string())?
        .map_err(|e| e.to_string())?;

        let response = assessments
            .into_iter()
            .map(|a| AssessmentResponse {
                id: a.id,
                patient_id: a.patient_id,
                title: a.title,
                content: a.content,
                created_at: a.created_at,
                updated_at: a.updated_at,
            })
            .collect();

        Ok(response)
    }
} 
use crate::schema::refresh_tokens;
use chrono::NaiveDateTime;
use diesel::{Insertable, Queryable};
use uuid::Uuid;

#[derive(Debug, Queryable)]
pub struct RefreshToken {
    pub id: Uuid,
    pub user_id: Uuid,
    pub token: String,
    pub expires_at: NaiveDateTime,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = refresh_tokens)]
pub struct NewRefreshToken {
    pub user_id: Uuid,
    pub token: String,
    pub expires_at: NaiveDateTime,
}

use crate::schema::revoked_tokens;
use chrono::NaiveDateTime;
use diesel::{Insertable, Queryable};
use uuid::Uuid;

#[derive(Debug, Queryable)]
pub struct RevokedToken {
    pub id: Uuid,
    pub token_jti: String,
    pub expiry: NaiveDateTime,
    pub revoked_at: Option<NaiveDateTime>,
    pub user_id: Option<Uuid>,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = revoked_tokens)]
pub struct NewRevokedToken {
    pub token_jti: String,
    pub expiry: NaiveDateTime,
    pub user_id: Uuid, 
}

use crate::schema::appointments;
use chrono::{DateTime, NaiveDateTime, Utc};
use diesel::{AsChangeset, Identifiable, Insertable, Queryable};
use serde::Serialize;
use uuid::Uuid;

#[derive(Debug, Queryable, Identifiable, AsChangeset, Clone, Serialize)]
pub struct Appointment {
    pub id: Uuid,
    pub user_id: Uuid,
    pub patient_id: Uuid,
    pub title: String,
    pub type_: String,
    pub status: String,
    pub start_time: NaiveDateTime,
    pub end_time: NaiveDateTime,
    pub notes: Option<String>,
    pub location: Option<String>,
    pub color: Option<String>,
    pub is_recurring: Option<bool>,
    pub series_id: Option<String>,
    pub recurrence_pattern: Option<String>,
    pub recurrence_end_date: Option<NaiveDateTime>,
    pub recurrence_count: Option<i32>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = appointments)]
pub struct NewAppointment {
    // pub id: Uuid, // Opcional se gerado pelo BD
    pub user_id: Uuid,
    pub patient_id: Uuid,
    pub title: String,
    pub type_: String,
    pub status: String,
    pub start_time: NaiveDateTime,
    pub end_time: NaiveDateTime,
    pub notes: Option<String>,
    pub location: Option<String>,
    pub color: Option<String>,
    pub is_recurring: Option<bool>,
    pub series_id: Option<String>,
    pub recurrence_pattern: Option<String>,
    pub recurrence_end_date: Option<NaiveDateTime>,
    pub recurrence_count: Option<i32>,
}

impl From<Appointment> for crate::appointment::appointment_dto::AppointmentResponse {
    fn from(appointment: Appointment) -> Self {
        let duration = appointment
            .end_time
            .signed_duration_since(appointment.start_time);
        let duration_minutes = duration.num_minutes();

        Self {
            id: appointment.id,
            user_id: appointment.user_id,
            patient_id: appointment.patient_id,
            patient_name: None,
            title: appointment.title,
            notes: appointment.notes,
            start_time: DateTime::from_naive_utc_and_offset(appointment.start_time, Utc),
            end_time: DateTime::from_naive_utc_and_offset(appointment.end_time, Utc),
            duration_minutes,
            status: appointment.status,
            type_: appointment.type_,
            color: appointment.color,
            location: appointment.location,
            series_id: appointment.series_id,
            is_recurring: appointment.is_recurring.unwrap_or(false),
            recurrence_pattern: appointment.recurrence_pattern,
            recurrence_end_date: appointment.recurrence_end_date.map(|dt| DateTime::from_naive_utc_and_offset(dt, Utc)),
            recurrence_count: appointment.recurrence_count,
        }
    }
}

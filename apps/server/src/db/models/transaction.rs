use crate::schema::transactions;
use bigdecimal::BigDecimal;
use chrono::NaiveDateTime;
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Queryable, Identifiable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = transactions)]
pub struct Transaction {
    pub id: Uuid,
    pub user_id: Uuid,
    pub patient_id: Option<Uuid>,
    pub appointment_id: Option<Uuid>,
    pub description: String,
    pub amount: BigDecimal,
    #[serde(rename = "type")]
    pub type_: String,
    pub status: String,
    pub transaction_date: NaiveDateTime,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Insertable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = transactions)]
pub struct NewTransaction {
    pub user_id: Uuid,
    pub patient_id: Option<Uuid>,
    pub appointment_id: Option<Uuid>,
    pub description: String,
    pub amount: BigDecimal,
    #[serde(rename = "type")]
    pub type_: String,
    pub status: String,
    pub transaction_date: NaiveDateTime,
}

#[derive(AsC<PERSON>eset, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = transactions)]
pub struct UpdateTransaction {
    pub patient_id: Option<Uuid>,
    pub appointment_id: Option<Uuid>,
    pub description: Option<String>,
    pub amount: Option<BigDecimal>,
    #[serde(rename = "type")]
    pub type_: Option<String>,
    pub status: Option<String>,
    pub transaction_date: Option<NaiveDateTime>,
} 
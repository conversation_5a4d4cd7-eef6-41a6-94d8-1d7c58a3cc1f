use crate::schema::patient_contacts;
use chrono::NaiveDateTime;
use diesel::{Identifiable, Insertable, Queryable, Selectable};
use uuid::Uuid;

#[derive(Debug, Queryable, Selectable, Identifiable)]
#[diesel(table_name = patient_contacts)]
pub struct PatientContact {
    pub id: Uuid,
    pub patient_id: Uuid,
    pub contact_id: Uuid,
    pub is_primary: Option<bool>, 
    pub created_at: NaiveDateTime,
    pub role: String, 
}

#[derive(Debug, Insertable)]
#[diesel(table_name = patient_contacts)]
pub struct NewPatientContact {
    pub patient_id: Uuid,
    pub contact_id: Uuid,
    pub role: String,
}

use crate::schema::users;
use chrono::NaiveDateTime;
use diesel::{Insertable, Queryable};
use uuid::Uuid;

#[allow(dead_code)]
#[derive(Debug, Queryable)]
pub struct User {
    pub id: Uuid,
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub date_of_birth: Option<NaiveDateTime>,
    pub cpf: Option<String>,
    pub phone: Option<String>,
    pub address: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub occupation: Option<String>,
    pub password_hash: String,
    pub is_active: Option<bool>,
    pub is_verified: Option<bool>,
    pub verification_token: Option<String>,
    pub verification_token_expires: Option<NaiveDateTime>,
    pub password_reset_token: Option<String>,
    pub password_reset_expires: Option<NaiveDateTime>,
    pub profile_picture: Option<String>,
    pub created_at: Option<NaiveDateTime>,
    pub updated_at: Option<NaiveDateTime>,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = users)]
pub struct NewUser {
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub date_of_birth: Option<NaiveDateTime>,
    pub cpf: Option<String>,
    pub phone: Option<String>,
    pub address: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub occupation: Option<String>,
    pub password_hash: String,
    pub is_active: Option<bool>,
    pub is_verified: Option<bool>,
    pub verification_token: Option<String>,
    pub verification_token_expires: Option<NaiveDateTime>,
    pub password_reset_token: Option<String>,
    pub password_reset_expires: Option<NaiveDateTime>,
    pub profile_picture: Option<String>,
}

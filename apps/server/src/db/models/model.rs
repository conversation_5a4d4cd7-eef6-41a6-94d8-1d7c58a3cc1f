use crate::schema::models;
use chrono::NaiveDateTime;
use diesel::{AsChangeset, Identifiable, Insertable, Queryable, Selectable};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Queryable, Selectable, Identifiable, AsChangeset, Serialize, Deserialize)]
#[diesel(table_name = models)]
pub struct Model {
    pub id: Uuid,
    pub user_id: Uuid,
    pub title: String,
    pub category: String,
    pub type_: String,
    pub description: Option<String>,
    pub content: String,
    pub is_favorite: Option<bool>,
    pub tags: Option<Vec<Option<String>>>,
    pub last_used: Option<NaiveDateTime>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,

    // NOVOS CAMPOS PARA MODELOS DINÂMICOS
    pub template_content: Option<serde_json::Value>, // TipTap JSON
    pub form_schema: Option<serde_json::Value>,      // Formulário dinâmico
    pub output_types: Option<Vec<Option<String>>>,   // ['note', 'pdf', 'docx']
    pub usage_count: Option<i32>,                    // Contador de uso
}

#[derive(Debug, Insertable)]
#[diesel(table_name = models)]
pub struct NewModel {
    pub user_id: Uuid,
    pub title: String,
    pub category: String,
    pub type_: String,
    pub description: Option<String>,
    pub content: String,
    pub is_favorite: Option<bool>,
    pub tags: Option<Vec<Option<String>>>,
    pub last_used: Option<NaiveDateTime>,

    // NOVOS CAMPOS PARA MODELOS DINÂMICOS
    pub template_content: Option<serde_json::Value>,
    pub form_schema: Option<serde_json::Value>,
    pub output_types: Option<Vec<Option<String>>>,
    pub usage_count: Option<i32>,
}

#[derive(Debug, AsChangeset)]
#[diesel(table_name = models)]
pub struct UpdateModelData {
    pub title: Option<String>,
    pub category: Option<String>,
    pub description: Option<Option<String>>,
    pub content: Option<String>,
    pub is_favorite: Option<bool>,
    pub tags: Option<Option<Vec<Option<String>>>>,
    pub last_used: Option<Option<NaiveDateTime>>,

    // NOVOS CAMPOS PARA MODELOS DINÂMICOS
    pub template_content: Option<Option<serde_json::Value>>,
    pub form_schema: Option<Option<serde_json::Value>>,
    pub output_types: Option<Option<Vec<Option<String>>>>,
    pub usage_count: Option<Option<i32>>,
}

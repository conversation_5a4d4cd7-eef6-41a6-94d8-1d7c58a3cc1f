use crate::schema::session_notes;
use chrono::NaiveDateTime;
use diesel::{AsChangeset, Identifiable, Insertable, Queryable, Selectable};
use serde::Serialize;
use uuid::Uuid;

#[derive(Queryable, Selectable, Identifiable, Serialize, Debug)]
#[diesel(table_name = session_notes)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct SessionNote {
    pub id: Uuid,
    pub user_id: Uuid,
    pub patient_id: Uuid,
    pub appointment_id: Option<Uuid>,
    pub title: String,
    pub content: String,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Insertable, Debug)]
#[diesel(table_name = session_notes)]
pub struct NewSessionNote {
    pub user_id: Uuid,
    pub patient_id: Uuid,
    pub appointment_id: Option<Uuid>,
    pub title: String,
    pub content: String,
}

#[derive(AsChangeset, Debug)]
#[diesel(table_name = session_notes)]
pub struct UpdateSessionNoteData {
    pub title: Option<String>,
    pub content: Option<String>,
    pub appointment_id: Option<Option<Uuid>>,
}

use crate::schema::tags;
use chrono::NaiveDateTime;
use diesel::{AsChangeset, Insertable, Queryable, Selectable};
use uuid::Uuid;

#[derive(Debug, Queryable, Selectable, AsChangeset)]
pub struct Tag {
    pub id: Uuid,
    pub name: String,
    pub color: String,
    pub user_id: Uuid,
    pub created_at: NaiveDateTime,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = tags)]
pub struct NewTag {
    pub id: Uuid, 
    pub name: String,
    pub color: String,
    pub user_id: Uuid,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = tags)]
pub struct ImportTag {
    pub id: Option<Uuid>,
    pub name: String,
    pub color: String,
    pub user_id: Uuid,
}

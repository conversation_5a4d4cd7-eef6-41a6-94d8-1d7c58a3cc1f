use crate::schema::patients;
use chrono::{NaiveDate, NaiveDateTime};
use diesel::{AsChangeset, Insertable, Queryable, Selectable}; 
use uuid::Uuid;

#[derive(Debug, Queryable, Selectable, AsChangeset)]
#[diesel(table_name = patients)]
pub struct Patient {
    pub id: Uuid,
    pub user_id: Uuid,
   
    pub full_name: String,
    pub date_of_birth: Option<NaiveDate>,
    pub gender: Option<String>,
    pub marital_status: Option<String>,
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub ethnicity: Option<String>,
    pub nationality: Option<String>,
    pub naturalness: Option<String>,
    pub occupation: Option<String>,
    pub notes: Option<String>,
    pub is_active: Option<bool>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = patients)]
pub struct NewPatient {
    pub user_id: Uuid,
    pub full_name: String,
    pub date_of_birth: Option<NaiveDate>,
    pub gender: Option<String>,
    pub marital_status: Option<String>,
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub ethnicity: Option<String>,
    pub nationality: Option<String>,
    pub naturalness: Option<String>,
    pub occupation: Option<String>,
    pub notes: Option<String>,
    pub is_active: Option<bool>,
}

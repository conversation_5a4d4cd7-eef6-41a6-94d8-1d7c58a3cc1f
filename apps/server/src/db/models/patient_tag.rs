use crate::schema::patient_tags;
use chrono::NaiveDateTime;
use diesel::{Insertable, Queryable, Selectable};
use uuid::Uuid;

#[derive(Debug, Queryable, Selectable)]
pub struct PatientTag {
    pub id: Uuid,
    pub patient_id: Uuid,
    pub tag_id: Uuid, 
    pub created_at: NaiveDateTime,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = patient_tags)]
pub struct NewPatientTag {
    pub patient_id: Uuid,
    pub tag_id: Uuid, 
}

use chrono::NaiveDateTime;
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::documents;

#[derive(Debug, Serialize, Deserialize, Queryable, Selectable, Identifiable)]
#[diesel(table_name = documents)]
pub struct Document {
    pub id: Uuid,
    pub user_id: Uuid, 
    pub patient_id: Option<Uuid>,
    pub model_id: Option<Uuid>, 
    pub title: String,
    pub filename: String,
    pub s3_key: String,
    pub mime_type: Option<String>,
    pub size_bytes: Option<i64>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    pub created_by: Uuid,
    pub updated_by: Uuid,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = documents)]
pub struct NewDocument {
    
    pub user_id: Uuid, 
    pub patient_id: Option<Uuid>, 
    pub model_id: Option<Uuid>, 
    pub title: String,
    pub filename: String,
    pub s3_key: String,
    pub mime_type: Option<String>,
    pub size_bytes: Option<i64>,
    pub created_by: Uuid, 
    pub updated_by: Uuid, 
}

#[derive(Debug, AsChangeset)]
#[diesel(table_name = documents)]
pub struct UpdateDocument {
    pub title: Option<String>,
    pub patient_id: Option<Option<Uuid>>, 
    pub model_id: Option<Option<Uuid>>, 
    pub updated_by: Option<Uuid>,
    pub updated_at: Option<NaiveDateTime>,
}

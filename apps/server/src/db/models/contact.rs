use crate::schema::contacts;
use chrono::{NaiveDate, NaiveDateTime}; // Adicionar NaiveDate
use diesel::{AsChangeset, Identifiable, Insertable, Queryable, Selectable};
use uuid::Uuid;

#[derive(Debug, Queryable, Selectable, Identifiable, AsChangeset, Clone)] // Adicionar C<PERSON>
#[diesel(table_name = contacts)]
pub struct Contact {
    pub id: Uuid,
    pub name: String,
    pub relationship_type: String,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub notes: Option<String>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
    
    pub user_id: Option<Uuid>, 
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub date_of_birth: Option<NaiveDate>, 
    pub gender: Option<String>,
    pub marital_status: Option<String>,
    pub ethnicity: Option<String>,
    pub nationality: Option<String>,
    pub naturalness: Option<String>,
    pub occupation: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = contacts)]
pub struct NewContact {
    pub name: String,
    pub relationship_type: String,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub notes: Option<String>,
    pub user_id: Option<Uuid>,
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub date_of_birth: Option<NaiveDate>,
    pub gender: Option<String>,
    pub marital_status: Option<String>,
    pub ethnicity: Option<String>,
    pub nationality: Option<String>,
    pub naturalness: Option<String>,
    pub occupation: Option<String>,
    pub is_active: Option<bool>, 
}

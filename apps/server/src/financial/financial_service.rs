use crate::config::database::DbPool;
use crate::db::models::appointment::Appointment;
use crate::db::models::transaction::{NewTransaction, Transaction, UpdateTransaction};
use crate::errors::ApiError;
use crate::financial::financial_dto::{
    CreateTransactionRequest, FinancialSummary, TransactionFilters, UpdateTransactionStatusRequest,
};
use crate::financial::financial_repository;
use bigdecimal::BigDecimal;
use chrono::Utc;
use csv::Writer;
use serde::Serialize;
use uuid::Uuid;
use crate::financial::financial_dto::UpdateTransactionRequest;
use diesel;
use tokio;

#[derive(Serialize)]
struct TransactionCsvRecord {
    id: String,
    data: String,
    descricao: String,
    paciente_id: String,
    valor: String,
    tipo: String,
    status: String,
}

pub async fn create_transaction(
    pool: &DbPool,
    user_id: Uuid,
    payload: CreateTransactionRequest,
) -> Result<Transaction, ApiError> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    let new_transaction = NewTransaction {
        user_id,
        patient_id: payload.patient_id,
        appointment_id: payload.appointment_id,
        description: payload.description,
        amount: payload.amount,
        type_: payload.type_,
        status: payload.status,
        transaction_date: payload.transaction_date,
    };
    
    tokio::task::block_in_place(move || {
        financial_repository::create(&mut conn, &new_transaction)
    })
}

pub async fn find_transactions(
    pool: &DbPool,
    user_id: Uuid,
    filters: TransactionFilters,
) -> Result<Vec<Transaction>, ApiError> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    tokio::task::block_in_place(move || {
        financial_repository::find(&mut conn, user_id, &filters)
    })
}

pub async fn get_financial_summary(
    pool: &DbPool,
    user_id: Uuid,
) -> Result<FinancialSummary, ApiError> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    let filters = TransactionFilters {
        patient_id: None,
        start_date: None,
        end_date: None,
    };
    let transactions = tokio::task::block_in_place(move || {
        financial_repository::find(&mut conn, user_id, &filters)
    })?;

    let mut income = BigDecimal::from(0);
    let mut expenses = BigDecimal::from(0);
    let mut pending = BigDecimal::from(0);

    for tx in transactions {
        if tx.type_ == "income" {
            if tx.status == "paid" {
                income += tx.amount;
            } else if tx.status == "pending" {
                pending += tx.amount.clone();
            }
        } else if tx.type_ == "expense" && tx.status == "paid" {
            expenses += tx.amount;
        }
    }

    let balance = income.clone() - expenses.clone();

    Ok(FinancialSummary {
        balance,
        income,
        expenses,
        pending,
    })
}

pub async fn update_transaction(
    pool: &DbPool,
    p_user_id: Uuid,
    p_transaction_id: Uuid,
    payload: UpdateTransactionRequest,
) -> Result<Transaction, ApiError> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;

    let transaction_data = UpdateTransaction {
        patient_id: payload.patient_id,
        appointment_id: payload.appointment_id,
        description: Some(payload.description),
        amount: Some(payload.amount),
        type_: Some(payload.type_),
        status: Some(payload.status),
        transaction_date: Some(payload.transaction_date),
    };

    tokio::task::block_in_place(move || {
        financial_repository::update(&mut conn, p_user_id, p_transaction_id, &transaction_data)
    })
}

pub async fn update_transaction_status(
    pool: &DbPool,
    p_user_id: Uuid,
    p_transaction_id: Uuid,
    payload: UpdateTransactionStatusRequest,
) -> Result<Transaction, ApiError> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    tokio::task::block_in_place(move || {
        financial_repository::update_status(&mut conn, p_user_id, p_transaction_id, &payload.status)
    })
}

pub async fn delete_transaction(pool: &DbPool, p_user_id: Uuid, p_transaction_id: Uuid) -> Result<(), ApiError> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    tokio::task::block_in_place(move || {
        financial_repository::delete(&mut conn, p_user_id, p_transaction_id)
    })?;
    Ok(())
}

pub fn create_transaction_from_appointment(
    conn: &mut diesel::PgConnection,
    user_id: Uuid,
    appointment: &crate::db::models::appointment::Appointment,
) -> Result<Transaction, ApiError> {
    if let Some(existing) = financial_repository::find_by_appointment_id(conn, appointment.id)? {
        return Ok(existing);
    }

    let new_transaction = NewTransaction {
        user_id,
        patient_id: Some(appointment.patient_id),
        appointment_id: Some(appointment.id),
        description: format!("Consulta: {}", appointment.title),
        amount: "150.00".parse().unwrap(), // TODO: Get price from somewhere else
        type_: "income".to_string(),
        status: "pending".to_string(),
        transaction_date: chrono::Utc::now().naive_utc(),
    };

    financial_repository::create(conn, &new_transaction)
}

pub async fn export_transactions_to_csv(
    pool: &DbPool,
    user_id: Uuid,
    filters: TransactionFilters,
) -> Result<Vec<u8>, ApiError> {
    let transactions = find_transactions(pool, user_id, filters).await?;

    let mut wtr = Writer::from_writer(vec![]);

    wtr.write_record(["ID", "Data", "Descrição", "Paciente ID", "Valor", "Tipo", "Status"])
        .map_err(|e| ApiError::InternalServerError(e.to_string()))?;

    for tx in transactions {
        let record = TransactionCsvRecord {
            id: tx.id.to_string(),
            data: tx.transaction_date.format("%Y-%m-%d %H:%M:%S").to_string(),
            descricao: tx.description,
            paciente_id: tx.patient_id.map(|id| id.to_string()).unwrap_or_default(),
            valor: tx.amount.to_string(),
            tipo: tx.type_,
            status: tx.status,
        };
        wtr.serialize(record)
            .map_err(|e| ApiError::InternalServerError(e.to_string()))?;
    }

    wtr.flush()
        .map_err(|e| ApiError::InternalServerError(e.to_string()))?;
    let data = wtr
        .into_inner()
        .map_err(|e| ApiError::InternalServerError(e.to_string()))?;

    Ok(data)
}
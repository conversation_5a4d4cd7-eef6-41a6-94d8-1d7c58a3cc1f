use bigdecimal::BigDecimal;
use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::Validate;

#[derive(Serialize, Deserialize, Debug, Validate)]
pub struct CreateTransactionRequest {
    pub patient_id: Option<Uuid>,
    pub appointment_id: Option<Uuid>,
    #[validate(length(min = 1, max = 255))]
    pub description: String,
    pub amount: BigDecimal,
    // TODO: Add validation for type: 'income' or 'expense'
    #[serde(rename = "type")]
    pub type_: String,
    // TODO: Add validation for status: 'pending', 'paid', 'overdue'
    pub status: String,
    pub transaction_date: NaiveDateTime,
}

#[derive(Serialize, Deserialize, Debug, Validate)]
pub struct UpdateTransactionRequest {
    pub patient_id: Option<Uuid>,
    pub appointment_id: Option<Uuid>,
    #[validate(length(min = 1, max = 255))]
    pub description: String,
    pub amount: BigDecimal,
    #[serde(rename = "type")]
    pub type_: String,
    pub status: String,
    pub transaction_date: NaiveDateTime,
}

#[derive(Serialize, Deserialize, Debug, Validate)]
pub struct UpdateTransactionStatusRequest {
    // TODO: Add validation for status: 'pending', 'paid', 'overdue'
    pub status: String,
}

#[derive(Deserialize, Debug)]
pub struct TransactionFilters {
    pub patient_id: Option<Uuid>,
    pub start_date: Option<NaiveDateTime>,
    pub end_date: Option<NaiveDateTime>,
}

#[derive(Serialize, Deserialize, Debug, Default)]
pub struct FinancialSummary {
    pub balance: BigDecimal,
    pub income: BigDecimal,
    pub expenses: BigDecimal,
    pub pending: BigDecimal,
}

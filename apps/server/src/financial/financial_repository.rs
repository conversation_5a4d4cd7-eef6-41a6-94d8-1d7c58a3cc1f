use crate::config::database::DbPool;
use crate::db::models::transaction::{NewTransaction, Transaction, UpdateTransaction};
use crate::errors::ApiError;
use crate::financial::financial_dto::TransactionFilters;
use crate::schema::transactions;
use crate::schema::transactions::dsl::*;
use diesel::prelude::*;
use diesel::PgConnection;
use uuid::Uuid;

pub fn create(conn: &mut PgConnection, new_transaction: &NewTransaction) -> Result<Transaction, ApiError> {
    diesel::insert_into(transactions::table)
        .values(new_transaction)
        .get_result(conn)
        .map_err(ApiError::from)
}

pub fn find(conn: &mut PgConnection, p_user_id: Uuid, filters: &TransactionFilters) -> Result<Vec<Transaction>, ApiError> {
    let mut query = transactions
        .filter(user_id.eq(p_user_id))
        .order_by(transaction_date.desc())
        .into_boxed();

    if let Some(p_id) = filters.patient_id {
        query = query.filter(patient_id.eq(p_id));
    }

    if let Some(start) = filters.start_date {
        query = query.filter(transaction_date.ge(start));
    }

    if let Some(end) = filters.end_date {
        query = query.filter(transaction_date.le(end));
    }

    query.load::<Transaction>(conn).map_err(ApiError::from)
}

pub fn find_by_id(conn: &mut PgConnection, p_user_id: Uuid, p_transaction_id: Uuid) -> Result<Transaction, ApiError> {
    transactions
        .filter(id.eq(p_transaction_id))
        .filter(user_id.eq(p_user_id))
        .first(conn)
        .map_err(ApiError::from)
}

pub fn update(
    conn: &mut PgConnection,
    p_user_id: Uuid,
    p_transaction_id: Uuid,
    transaction_data: &UpdateTransaction,
) -> Result<Transaction, ApiError> {
    diesel::update(transactions.filter(id.eq(p_transaction_id).and(user_id.eq(p_user_id))))
        .set(transaction_data)
        .get_result(conn)
        .map_err(ApiError::from)
}

pub fn update_status(
    conn: &mut PgConnection,
    p_user_id: Uuid,
    p_transaction_id: Uuid,
    new_status: &str,
) -> Result<Transaction, ApiError> {
    diesel::update(transactions.filter(id.eq(p_transaction_id).and(user_id.eq(p_user_id))))
        .set(status.eq(new_status))
        .get_result(conn)
        .map_err(ApiError::from)
}

pub fn delete(conn: &mut PgConnection, p_user_id: Uuid, p_transaction_id: Uuid) -> Result<usize, ApiError> {
    diesel::delete(transactions.filter(id.eq(p_transaction_id).and(user_id.eq(p_user_id))))
        .execute(conn)
        .map_err(ApiError::from)
}

pub fn find_by_appointment_id(
    conn: &mut PgConnection,
    p_appointment_id: Uuid,
) -> Result<Option<Transaction>, ApiError> {
    transactions
        .filter(appointment_id.eq(p_appointment_id))
        .first::<Transaction>(conn)
        .optional()
        .map_err(ApiError::from)
}
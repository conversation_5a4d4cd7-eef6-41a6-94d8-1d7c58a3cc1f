use crate::app::AppState;
use crate::auth::auth_middleware::AuthUser;
use crate::db::models::transaction::Transaction;
use crate::errors::ApiResult;
use crate::financial::financial_dto::{
    CreateTransactionRequest, FinancialSummary, TransactionFilters, UpdateTransactionRequest,
    UpdateTransactionStatusRequest,
};
use crate::financial::financial_service;
use axum::{
    extract::{Path, Query, State},
    response::Json,
    response::{IntoResponse, Response},
    http::{header, StatusCode},
    Extension,
};
use uuid::Uuid;
use validator::Validate;

pub async fn create_transaction_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Json(payload): Json<CreateTransactionRequest>,
) -> ApiResult<Json<Transaction>> {
    payload.validate()?;
    let transaction =
        financial_service::create_transaction(&state.pool, authenticated_user.0, payload).await?;
    Ok(Json(transaction))
}

pub async fn list_transactions_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Query(filters): Query<TransactionFilters>,
) -> ApiResult<Json<Vec<Transaction>>> {
    let transactions =
        financial_service::find_transactions(&state.pool, authenticated_user.0, filters).await?;
    Ok(Json(transactions))
}

pub async fn get_summary_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
) -> ApiResult<Json<FinancialSummary>> {
    let summary =
        financial_service::get_financial_summary(&state.pool, authenticated_user.0).await?;
    Ok(Json(summary))
}

pub async fn update_transaction_status_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Path(transaction_id): Path<Uuid>,
    Json(payload): Json<UpdateTransactionStatusRequest>,
) -> ApiResult<Json<Transaction>> {
    payload.validate()?;
    let transaction = financial_service::update_transaction_status(
        &state.pool,
        authenticated_user.0,
        transaction_id,
        payload,
    )
    .await?;
    Ok(Json(transaction))
}

pub async fn update_transaction_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Path(transaction_id): Path<Uuid>,
    Json(payload): Json<UpdateTransactionRequest>,
) -> ApiResult<Json<Transaction>> {
    payload.validate()?;
    let transaction = financial_service::update_transaction(
        &state.pool,
        authenticated_user.0,
        transaction_id,
        payload,
    )
    .await?;
    Ok(Json(transaction))
}

pub async fn delete_transaction_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Path(transaction_id): Path<Uuid>,
) -> ApiResult<StatusCode> {
    financial_service::delete_transaction(
        &state.pool,
        authenticated_user.0,
        transaction_id,
    )
    .await?;
    Ok(StatusCode::NO_CONTENT)
}

pub async fn export_transactions_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Query(filters): Query<TransactionFilters>,
) -> ApiResult<Response> {
    let csv_data = financial_service::export_transactions_to_csv(
        &state.pool,
        authenticated_user.0,
        filters,
    )
    .await?;

    let headers = [
        (header::CONTENT_TYPE, "text/csv; charset=utf-8"),
        (
            header::CONTENT_DISPOSITION,
            "attachment; filename=\"transactions.csv\"",
        ),
    ];

    Ok((StatusCode::OK, headers, csv_data).into_response())
}
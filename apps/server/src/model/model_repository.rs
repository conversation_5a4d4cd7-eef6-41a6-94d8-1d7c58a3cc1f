use crate::db::models::model::{Model, NewModel, UpdateModelData};
use crate::schema::models::dsl::*;
use chrono::Utc;
use diesel::prelude::*;
use diesel::result::QueryResult;
use diesel::PgConnection;
use uuid::Uuid;

// Buscar todos os modelos de um usuário
pub fn find_models_by_user(user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<Vec<Model>> {
    models
        .filter(user_id.eq(user_id_val))
        .order_by(created_at.desc())
        .load::<Model>(conn)
}

// Contar todos os modelos de um usuário
pub fn count_models_by_user(user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<i64> {
    use diesel::dsl::count_star;
    models
        .filter(user_id.eq(user_id_val))
        .select(count_star())
        .first::<i64>(conn)
}

// Buscar todos os modelos de um usuário com paginação
pub fn find_models_by_user_paginated(
    user_id_val: Uuid,
    limit: Option<i64>,
    offset: Option<i64>,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Model>> {
    let mut query = models
        .filter(user_id.eq(user_id_val))
        .order_by(created_at.desc())
        .into_boxed();

    if let Some(lim) = limit {
        query = query.limit(lim);
    }
    if let Some(off) = offset {
        query = query.offset(off);
    }

    query.load::<Model>(conn)
}

// Buscar modelo por ID
pub fn find_model_by_id(model_id_val: Uuid, user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<Model> {
    models
        .filter(id.eq(model_id_val))
        .filter(user_id.eq(user_id_val))
        .first::<Model>(conn)
}

// Buscar modelos por categoria
pub fn find_models_by_category(
    user_id_val: Uuid,
    category_val: &str,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Model>> {
    models
        .filter(user_id.eq(user_id_val))
        .filter(category.eq(category_val))
        .order_by(created_at.desc())
        .load::<Model>(conn)
}

// Buscar modelos favoritos
pub fn find_favorite_models(user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<Vec<Model>> {
    models
        .filter(user_id.eq(user_id_val))
        .filter(is_favorite.eq(true))
        .order_by(created_at.desc())
        .load::<Model>(conn)
}

// Buscar modelos personalizados
pub fn find_custom_models(user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<Vec<Model>> {
    models
        .filter(user_id.eq(user_id_val))
        .filter(type_.eq("custom"))
        .order_by(created_at.desc())
        .load::<Model>(conn)
}

// Criar novo modelo
pub fn create_model(new_model: &NewModel, conn: &mut PgConnection) -> QueryResult<Model> {
    diesel::insert_into(models)
        .values(new_model)
        .get_result(conn)
}

// Atualizar modelo existente
pub fn update_model(
    model_id_val: Uuid,
    user_id_val: Uuid,
    update_data: &UpdateModelData,
    conn: &mut PgConnection,
) -> QueryResult<Model> {
    diesel::update(
        models
            .filter(id.eq(model_id_val))
            .filter(user_id.eq(user_id_val)),
    )
    .set(update_data)
    .get_result(conn)
}

// Excluir modelo
pub fn delete_model(model_id_val: Uuid, user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<usize> {
    diesel::delete(
        models
            .filter(id.eq(model_id_val))
            .filter(user_id.eq(user_id_val)),
    )
    .execute(conn)
}

// Marcar/desmarcar como favorito
pub fn toggle_favorite(
    model_id_val: Uuid,
    user_id_val: Uuid,
    is_favorite_val: bool,
    conn: &mut PgConnection,
) -> QueryResult<Model> {
    diesel::update(
        models
            .filter(id.eq(model_id_val))
            .filter(user_id.eq(user_id_val)),
    )
    .set(is_favorite.eq(is_favorite_val))
    .get_result(conn)
}

// Atualizar data de último uso
pub fn update_last_used(
    model_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Model> {
    let now = Utc::now().naive_utc();
    diesel::update(
        models
            .filter(id.eq(model_id_val))
            .filter(user_id.eq(user_id_val)),
    )
    .set(last_used.eq(now))
    .get_result(conn)
}

// Incrementar contador de uso do modelo
pub fn increment_usage_count(model_id_val: Uuid, user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<Model> {
    use crate::schema::models::dsl::*;

    diesel::update(
        models
            .filter(id.eq(model_id_val))
            .filter(user_id.eq(user_id_val)),
    )
    .set(usage_count.eq(diesel::dsl::sql::<diesel::sql_types::Nullable<diesel::sql_types::Integer>>("COALESCE(usage_count, 0) + 1")))
    .get_result(conn)
}

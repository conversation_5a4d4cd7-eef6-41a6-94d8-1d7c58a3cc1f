use crate::db::models::model::{Model, NewModel, UpdateModelData};
use crate::model::model_dto::{ApplyModelRequest, ApplyModelResponse, CreateModelRequest, ModelResponse, UpdateModelRequest};
use crate::model::model_repository;
use diesel::PgConnection;
use uuid::Uuid;

fn model_to_response(model: Model) -> ModelResponse {
    ModelResponse {
        id: model.id,
        title: model.title,
        category: model.category,
        type_: model.type_,
        description: model.description,
        content: model.content,
        is_favorite: model.is_favorite.unwrap_or(false),
        tags: model.tags
            .unwrap_or_default()
            .into_iter()
            .flatten()
            .collect(),
        last_used: model.last_used,
        created_at: model.created_at,
        updated_at: model.updated_at,
    }
}

pub async fn get_all_models(
    user_id: Uuid,
    conn: &mut PgConnection,
) -> Result<Vec<ModelResponse>, String> {
    let models = model_repository::find_models_by_user(user_id, conn)
        .map_err(|e| format!("Erro ao buscar modelos: {}", e))?;
    let responses = models.into_iter().map(model_to_response).collect();
    Ok(responses)
}

pub async fn get_all_models_paginated_with_count(
    user_id: Uuid,
    page: Option<i64>,
    page_size: Option<i64>,
    conn: &mut PgConnection,
) -> Result<crate::model::model_dto::ModelListResponse, String> {
    let page_size = page_size.unwrap_or(20);
    let page = page.unwrap_or(0);
    let limit = Some(page_size);
    let offset = Some(page * page_size);

    let total_count = crate::model::model_repository::count_models_by_user(user_id, conn)
        .map_err(|e| format!("Erro ao contar modelos: {}", e))?;

    let models = crate::model::model_repository::find_models_by_user_paginated(user_id, limit, offset, conn)
        .map_err(|e| format!("Erro ao buscar modelos: {}", e))?;
    let responses = models.into_iter().map(model_to_response).collect();

    Ok(crate::model::model_dto::ModelListResponse {
        models: responses,
        total_count,
    })
}

// Obter todos os modelos com paginação
pub async fn get_all_models_paginated(
    user_id: Uuid,
    page: Option<i64>,
    page_size: Option<i64>,
    conn: &mut PgConnection,
) -> Result<Vec<ModelResponse>, String> {
    let page_size = page_size.unwrap_or(20);
    let page = page.unwrap_or(0);
    let limit = Some(page_size);
    let offset = Some(page * page_size);

    let models = model_repository::find_models_by_user_paginated(user_id, limit, offset, conn)
        .map_err(|e| format!("Erro ao buscar modelos: {}", e))?;
    let responses = models.into_iter().map(model_to_response).collect();
    Ok(responses)
}

// Obter modelo por ID
pub async fn get_model_by_id(
    user_id: Uuid,
    model_id: Uuid,
    conn: &mut PgConnection,
) -> Result<ModelResponse, String> {
    let model = model_repository::find_model_by_id(model_id, user_id, conn)
        .map_err(|e| format!("Erro ao buscar modelo: {}", e))?;
    Ok(model_to_response(model))
}

// Obter modelos por categoria
pub async fn get_models_by_category(
    user_id: Uuid,
    category: &str,
    conn: &mut PgConnection,
) -> Result<Vec<ModelResponse>, String> {
    let models = model_repository::find_models_by_category(user_id, category, conn)
        .map_err(|e| format!("Erro ao buscar modelos por categoria: {}", e))?;
    let responses = models.into_iter().map(model_to_response).collect();
    Ok(responses)
}

// Obter modelos favoritos
pub async fn get_favorite_models(
    user_id: Uuid,
    conn: &mut PgConnection,
) -> Result<Vec<ModelResponse>, String> {
    let models = model_repository::find_favorite_models(user_id, conn)
        .map_err(|e| format!("Erro ao buscar modelos favoritos: {}", e))?;
    let responses = models.into_iter().map(model_to_response).collect();
    Ok(responses)
}

// Obter modelos personalizados
pub async fn get_custom_models(
    user_id: Uuid,
    conn: &mut PgConnection,
) -> Result<Vec<ModelResponse>, String> {
    let models = model_repository::find_custom_models(user_id, conn)
        .map_err(|e| format!("Erro ao buscar modelos personalizados: {}", e))?;
    let responses = models.into_iter().map(model_to_response).collect();
    Ok(responses)
}

// Criar novo modelo
pub async fn create_new_model(
    user_id: Uuid,
    request: CreateModelRequest,
    conn: &mut PgConnection,
) -> Result<ModelResponse, String> {
    // Validar dados
    if request.title.trim().is_empty() {
        return Err("O título do modelo não pode estar vazio".to_string());
    }

    if request.content.trim().is_empty() {
        return Err("O conteúdo do modelo não pode estar vazio".to_string());
    }

    // Preparar tags
    let tags = request.tags.map(|t| t.into_iter().map(Some).collect());

    // Criar novo modelo
    let new_model = NewModel {
        user_id,
        title: request.title,
        category: request.category,
        type_: "custom".to_string(), // Modelos criados pelo usuário são sempre do tipo "custom"
        description: request.description,
        content: request.content,
        is_favorite: Some(false), // Inicialmente não é favorito
        tags,
        last_used: None, // Inicialmente não foi usado

        // NOVOS CAMPOS PARA MODELOS DINÂMICOS
        template_content: None,
        form_schema: None,
        output_types: Some(vec![Some("note".to_string())]), // Padrão: apenas nota
        usage_count: Some(0),
    };

    let model = model_repository::create_model(&new_model, conn)
        .map_err(|e| format!("Erro ao criar modelo: {}", e))?;

    Ok(model_to_response(model))
}

// Atualizar modelo existente
pub async fn update_model(
    user_id: Uuid,
    model_id: Uuid,
    request: UpdateModelRequest,
    conn: &mut PgConnection,
) -> Result<ModelResponse, String> {
    // Verificar se o modelo existe e pertence ao usuário
    let existing_model = model_repository::find_model_by_id(model_id, user_id, conn)
        .map_err(|_| "Modelo não encontrado".to_string())?;

    // Verificar se é um modelo do sistema
    if existing_model.type_ == "system" {
        return Err("Modelos do sistema não podem ser editados".to_string());
    }

    // Validar dados
    if let Some(ref title) = request.title {
        if title.trim().is_empty() {
            return Err("O título do modelo não pode estar vazio".to_string());
        }
    }

    if let Some(ref content) = request.content {
        if content.trim().is_empty() {
            return Err("O conteúdo do modelo não pode estar vazio".to_string());
        }
    }

    // Preparar tags
    let tags = request.tags.map(|t| {
        let tags_option: Vec<Option<String>> = t.into_iter().map(Some).collect();
        Some(tags_option)
    });

    // Preparar dados para atualização
    let update_data = UpdateModelData {
        title: request.title,
        category: request.category,
        description: request.description.map(Some),
        content: request.content,
        is_favorite: None, // Não alterar o status de favorito
        tags,
        last_used: None, // Não alterar a data de último uso

        // NOVOS CAMPOS PARA MODELOS DINÂMICOS (não alterar se não especificado)
        template_content: None,
        form_schema: None,
        output_types: None,
        usage_count: None,
    };

    let model = model_repository::update_model(model_id, user_id, &update_data, conn)
        .map_err(|e| format!("Erro ao atualizar modelo: {}", e))?;

    Ok(model_to_response(model))
}

// Excluir modelo
pub async fn delete_model(
    user_id: Uuid,
    model_id: Uuid,
    conn: &mut PgConnection,
) -> Result<(), String> {
    // Verificar se o modelo existe e pertence ao usuário
    let existing_model = model_repository::find_model_by_id(model_id, user_id, conn)
        .map_err(|_| "Modelo não encontrado".to_string())?;

    // Verificar se é um modelo do sistema
    if existing_model.type_ == "system" {
        return Err("Modelos do sistema não podem ser excluídos".to_string());
    }

    model_repository::delete_model(model_id, user_id, conn)
        .map_err(|e| format!("Erro ao excluir modelo: {}", e))?;

    Ok(())
}

// Marcar/desmarcar como favorito
pub async fn toggle_favorite(
    user_id: Uuid,
    model_id: Uuid,
    is_favorite_val: bool,
    conn: &mut PgConnection,
) -> Result<ModelResponse, String> {
    // Verificar se o modelo existe e pertence ao usuário
    let _ = model_repository::find_model_by_id(model_id, user_id, conn)
        .map_err(|_| "Modelo não encontrado".to_string())?;

    let model = model_repository::toggle_favorite(model_id, user_id, is_favorite_val, conn)
        .map_err(|e| format!("Erro ao atualizar favorito: {}", e))?;

    Ok(model_to_response(model))
}

// Duplicar modelo existente
pub async fn duplicate_model(
    user_id: Uuid,
    model_id: Uuid,
    conn: &mut PgConnection,
) -> Result<ModelResponse, String> {
    // Buscar o modelo original
    let original_model = model_repository::find_model_by_id(model_id, user_id, conn)
        .map_err(|_| "Modelo não encontrado".to_string())?;

    // Criar um novo modelo baseado no original
    let new_model = NewModel {
        user_id,
        title: format!("{} (Cópia)", original_model.title),
        category: original_model.category,
        type_: "custom".to_string(), // Modelos duplicados são sempre do tipo "custom"
        description: original_model.description,
        content: original_model.content,
        is_favorite: Some(false), // Inicialmente não é favorito
        tags: original_model.tags,
        last_used: None, // Inicialmente não foi usado

        // NOVOS CAMPOS PARA MODELOS DINÂMICOS (copiar do original)
        template_content: original_model.template_content,
        form_schema: original_model.form_schema,
        output_types: original_model.output_types,
        usage_count: Some(0), // Resetar contador de uso
    };

    let model = model_repository::create_model(&new_model, conn)
        .map_err(|e| format!("Erro ao duplicar modelo: {}", e))?;

    Ok(model_to_response(model))
}

// Atualizar data de último uso
pub async fn update_last_used(
    user_id: Uuid,
    model_id: Uuid,
    conn: &mut PgConnection,
) -> Result<ModelResponse, String> {
    // Verificar se o modelo existe e pertence ao usuário
    let _ = model_repository::find_model_by_id(model_id, user_id, conn)
        .map_err(|_| "Modelo não encontrado".to_string())?;

    let model = model_repository::update_last_used(model_id, user_id, conn)
        .map_err(|e| format!("Erro ao atualizar data de último uso: {}", e))?;

    Ok(model_to_response(model))
}

// Aplicar modelo dinâmico
pub async fn apply_model(
    user_id: Uuid,
    model_id: Uuid,
    request: ApplyModelRequest,
    conn: &mut PgConnection,
) -> Result<ApplyModelResponse, String> {
    // 1. Buscar modelo
    let model = model_repository::find_model_by_id(model_id, user_id, conn)
        .map_err(|_| "Modelo não encontrado".to_string())?;

    // 2. Verificar se o modelo tem template_content (é um modelo dinâmico)
    let template_content = model.template_content
        .ok_or("Este modelo não possui template dinâmico".to_string())?;

    // 3. Processar template com respostas do formulário
    let processed_content = process_template_with_responses(
        &template_content,
        &request.form_responses,
        request.patient_id,
        request.appointment_id,
        conn
    )?;

    // 4. Incrementar contador de uso
    let _ = model_repository::increment_usage_count(model_id, user_id, conn);

    // 5. Gerar saída baseada no tipo
    match request.output_type.as_str() {
        "note" => create_session_note(user_id, request.patient_id, request.appointment_id, &processed_content, conn).await,
        "pdf" => generate_pdf_document(user_id, request.patient_id, Some(model_id), &processed_content, conn).await,
        "docx" => generate_docx_document(user_id, request.patient_id, Some(model_id), &processed_content, conn).await,
        _ => Err("Tipo de saída inválido".to_string())
    }
}

// Processar template com respostas do formulário
fn process_template_with_responses(
    template_content: &serde_json::Value,
    form_responses: &[crate::model::model_dto::FormResponse],
    _patient_id: Uuid,
    _appointment_id: Option<Uuid>,
    _conn: &mut PgConnection,
) -> Result<String, String> {
    // Converter respostas para mapa
    let response_map: std::collections::HashMap<String, &serde_json::Value> = form_responses
        .iter()
        .map(|r| (r.field_id.clone(), &r.value))
        .collect();

    // Por enquanto, retornar uma versão simplificada
    // Em uma implementação completa, você processaria o JSON do TipTap
    let mut processed = serde_json::to_string_pretty(template_content)
        .map_err(|e| format!("Erro ao processar template: {}", e))?;

    // Substituir placeholders básicos
    for (field_id, value) in response_map {
        let placeholder = format!("[{}]", field_id);
        let value_str = match value {
            serde_json::Value::String(s) => s.clone(),
            serde_json::Value::Number(n) => n.to_string(),
            serde_json::Value::Bool(b) => b.to_string(),
            _ => value.to_string(),
        };
        processed = processed.replace(&placeholder, &value_str);
    }

    Ok(processed)
}

// Criar nota de sessão
async fn create_session_note(
    _user_id: Uuid,
    _patient_id: Uuid,
    _appointment_id: Option<Uuid>,
    _content: &str,
    _conn: &mut PgConnection,
) -> Result<ApplyModelResponse, String> {
    // Por enquanto, uma implementação simplificada
    // Em uma implementação completa, você criaria uma nota de sessão real
    Ok(ApplyModelResponse {
        success: true,
        message: "Nota de sessão criada com sucesso".to_string(),
        output_id: Some(Uuid::new_v4()), // ID fictício
        download_url: None,
    })
}

// Gerar documento PDF
async fn generate_pdf_document(
    _user_id: Uuid,
    _patient_id: Uuid,
    _model_id: Option<Uuid>,
    _content: &str,
    _conn: &mut PgConnection,
) -> Result<ApplyModelResponse, String> {
    // Por enquanto, uma implementação simplificada
    // Em uma implementação completa, você geraria um PDF real
    Ok(ApplyModelResponse {
        success: true,
        message: "Documento PDF gerado com sucesso".to_string(),
        output_id: Some(Uuid::new_v4()), // ID fictício
        download_url: Some("/api/documents/download/pdf/123".to_string()),
    })
}

// Gerar documento DOCX
async fn generate_docx_document(
    _user_id: Uuid,
    _patient_id: Uuid,
    _model_id: Option<Uuid>,
    _content: &str,
    _conn: &mut PgConnection,
) -> Result<ApplyModelResponse, String> {
    // Por enquanto, uma implementação simplificada
    // Em uma implementação completa, você geraria um DOCX real
    Ok(ApplyModelResponse {
        success: true,
        message: "Documento DOCX gerado com sucesso".to_string(),
        output_id: Some(Uuid::new_v4()), // ID fictício
        download_url: Some("/api/documents/download/docx/123".to_string()),
    })
}

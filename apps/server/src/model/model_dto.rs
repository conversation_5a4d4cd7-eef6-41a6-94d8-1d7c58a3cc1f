use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct ModelResponse {
    pub id: Uuid, 
    pub title: String,
    pub category: String,
    #[serde(rename = "type")]
    pub type_: String,
    pub description: Option<String>,
    pub content: String,
    pub is_favorite: bool,
    pub tags: Vec<String>,
    pub last_used: Option<NaiveDateTime>,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Debug, Deserialize)]
pub struct CreateModelRequest {
    pub title: String,
    pub category: String,
    pub description: Option<String>,
    pub content: String,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateModelRequest {
    pub title: Option<String>,
    pub category: Option<String>,
    pub description: Option<String>,
    pub content: Option<String>,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Deserialize)]
pub struct ToggleFavoriteRequest {
    pub is_favorite: bool,
}

#[derive(Debug, Serialize)]
pub struct MessageResponse {
    pub message: String,
}

#[derive(Debug, Serialize)]
pub struct ModelListResponse {
    pub models: Vec<ModelResponse>,
    pub total_count: i64,
}

#[derive(Debug, Deserialize)]
pub struct ModelFilterParams {
    pub category: Option<String>,
    pub favorites_only: Option<bool>,
    pub custom_only: Option<bool>,
    pub page: Option<i64>,
    pub page_size: Option<i64>,
}

// DTOs para aplicação de modelos dinâmicos
#[derive(Debug, Deserialize)]
pub struct ApplyModelRequest {
    pub patient_id: Uuid,
    pub appointment_id: Option<Uuid>,
    pub form_responses: Vec<FormResponse>,
    pub output_type: String, // 'note', 'pdf', 'docx'
}

#[derive(Debug, Deserialize)]
pub struct FormResponse {
    pub field_id: String,
    pub value: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct ApplyModelResponse {
    pub success: bool,
    pub message: String,
    pub output_id: Option<Uuid>, // ID da nota/documento gerado
    pub download_url: Option<String>, // URL para download (PDF/DOCX)
}

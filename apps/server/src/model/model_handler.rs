use crate::auth::auth_middleware::AuthUser;
use crate::config::database::DbPool;
use crate::errors::{ApiError, ApiR<PERSON>ult};
use crate::model::model_dto::{
    ApplyModelRequest, CreateModelRequest, MessageResponse, ModelFilterParams, ToggleFavoriteRequest, UpdateModelRequest,
};
use crate::model::model_service;
use axum::{
    extract::{Extension, Path, Query},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use uuid::Uuid;

// Handler para listar modelos (com filtros opcionais)
pub async fn get_models_list_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Query(params): Query<ModelFilterParams>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Aplicar filtros
    if let Some(true) = params.favorites_only {
        let models = model_service::get_favorite_models(user_id, &mut conn)
            .await
            .map_err(ApiError::InternalServerError)?;
        let total_count = models.len() as i64;
        return Ok(Json(crate::model::model_dto::ModelListResponse { models, total_count }));
    }

    if let Some(true) = params.custom_only {
        let models = model_service::get_custom_models(user_id, &mut conn)
            .await
            .map_err(ApiError::InternalServerError)?;
        let total_count = models.len() as i64;
        return Ok(Json(crate::model::model_dto::ModelListResponse { models, total_count }));
    }

    if let Some(category) = params.category {
        let models = model_service::get_models_by_category(user_id, &category, &mut conn)
            .await
            .map_err(ApiError::InternalServerError)?;
        let total_count = models.len() as i64;
        return Ok(Json(crate::model::model_dto::ModelListResponse { models, total_count }));
    }

    // Sem filtros, retornar todos
    let result = model_service::get_all_models_paginated_with_count(
        user_id,
        params.page,
        params.page_size,
        &mut conn,
    )
    .await
    .map_err(ApiError::InternalServerError)?;
    Ok(Json(result))
}

// Handler para obter detalhes de um modelo
pub async fn get_model_details_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(model_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match model_service::get_model_by_id(user_id, model_id, &mut conn).await {
        Ok(model) => Ok(Json(model)),
        Err(_) => Err(ApiError::ResourceNotFound("Modelo".to_string())),
    }
}

// Handler para criar modelo
pub async fn create_model_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(payload): Json<CreateModelRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match model_service::create_new_model(user_id, payload, &mut conn).await {
        Ok(model) => Ok((StatusCode::CREATED, Json(model))),
        Err(e) => {
            if e.contains("não pode estar vazio") {
                let mut errors = validator::ValidationErrors::new();
                let mut error = validator::ValidationError::new("validation_error");
                error.message = Some(e.into());
                errors.add("content", error);
                Err(ApiError::ValidationError(errors))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

// Handler para atualizar modelo
pub async fn update_model_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(model_id): Path<Uuid>,
    Json(payload): Json<UpdateModelRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match model_service::update_model(user_id, model_id, payload, &mut conn).await {
        Ok(model) => Ok(Json(model)),
        Err(e) => {
            if e == "Modelo não encontrado" {
                Err(ApiError::ResourceNotFound("Modelo".to_string()))
            } else if e.contains("não podem ser editados") {
                Err(ApiError::BadRequest(e))
            } else if e.contains("não pode estar vazio") {
                let mut errors = validator::ValidationErrors::new();
                let mut error = validator::ValidationError::new("validation_error");
                error.message = Some(e.into());
                errors.add("content", error);
                Err(ApiError::ValidationError(errors))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

// Handler para excluir modelo
pub async fn delete_model_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(model_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match model_service::delete_model(user_id, model_id, &mut conn).await {
        Ok(_) => Ok((
            StatusCode::OK,
            Json(MessageResponse {
                message: "Modelo excluído com sucesso".to_string(),
            }),
        )),
        Err(e) => {
            if e == "Modelo não encontrado" {
                Err(ApiError::ResourceNotFound("Modelo".to_string()))
            } else if e.contains("não podem ser excluídos") {
                Err(ApiError::BadRequest(e))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

// Handler para marcar/desmarcar como favorito
pub async fn toggle_favorite_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(model_id): Path<Uuid>,
    Json(payload): Json<ToggleFavoriteRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match model_service::toggle_favorite(user_id, model_id, payload.is_favorite, &mut conn).await {
        Ok(model) => Ok(Json(model)),
        Err(e) => {
            if e == "Modelo não encontrado" {
                Err(ApiError::ResourceNotFound("Modelo".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

// Handler para duplicar modelo
pub async fn duplicate_model_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(model_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match model_service::duplicate_model(user_id, model_id, &mut conn).await {
        Ok(model) => Ok((StatusCode::CREATED, Json(model))),
        Err(e) => {
            if e == "Modelo não encontrado" {
                Err(ApiError::ResourceNotFound("Modelo".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

// Handler para atualizar data de último uso
pub async fn update_last_used_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(model_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    let result = match model_service::update_last_used(user_id, model_id, &mut conn).await {
        Ok(model) => Ok(Json(model)),
        Err(e) => Err(if e == "Modelo não encontrado" {
            ApiError::ResourceNotFound("Modelo".to_string())
        } else {
            ApiError::InternalServerError(e)
        }),
    };
    result
}

use axum_extra::extract::Multipart;
use hyper::{HeaderMap, header, header::HeaderValue};
use serde_json::Value as JsonValue;

// Handler para importar modelo via arquivo JSON (multipart/form-data)
pub async fn import_model_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    mut multipart: Multipart,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Ler o arquivo do multipart
    let mut model_json: Option<String> = None;
    while let Some(field) = multipart.next_field().await.map_err(|_| ApiError::BadRequest("Erro ao ler arquivo".to_string()))? {
        let name = field.name().unwrap_or("");
        if name == "file" {
            let data = field.text().await.map_err(|_| ApiError::BadRequest("Erro ao ler arquivo".to_string()))?;
            model_json = Some(data);
            break;
        }
    }
    let model_json = model_json.ok_or_else(|| ApiError::BadRequest("Arquivo não enviado".to_string()))?;

    // Parsear e validar JSON
    let json_value: JsonValue = serde_json::from_str(&model_json)
        .map_err(|_| ApiError::BadRequest("Arquivo JSON inválido".to_string()))?;

    // Validar campos obrigatórios
    let title = json_value.get("title").and_then(|v| v.as_str()).ok_or_else(|| ApiError::BadRequest("Campo 'title' obrigatório".to_string()))?;
    let content = json_value.get("content").and_then(|v| v.as_str()).ok_or_else(|| ApiError::BadRequest("Campo 'content' obrigatório".to_string()))?;

    // Montar payload para criação
    let description = json_value.get("description").and_then(|v| v.as_str()).unwrap_or("");
    let category = json_value.get("category").and_then(|v| v.as_str()).unwrap_or("document");
    let tags = json_value.get("tags").and_then(|v| v.as_array())
        .map(|arr| arr.iter().filter_map(|t| t.as_str().map(|s| s.to_string())).collect::<Vec<String>>())
        .unwrap_or_default();

    let payload = CreateModelRequest {
        title: title.to_string(),
        category: category.to_string(),
        description: Some(description.to_string()),
        content: content.to_string(),
        tags: Some(tags),
    };

    // Criar modelo no banco
    let model = model_service::create_new_model(user_id, payload, &mut conn)
        .await
        .map_err(ApiError::InternalServerError)?;

    Ok((StatusCode::CREATED, Json(model)))
}

pub async fn export_model_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(model_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    // Buscar modelo pelo id e user_id (ou modelo de sistema)
    let model = match model_service::get_model_by_id(user_id, model_id, &mut conn).await {
        Ok(model) => model,
        Err(_) => return Err(ApiError::ResourceNotFound("Modelo".to_string())),
    };

    // Serializar para JSON
    let json = match serde_json::to_string_pretty(&model) {
        Ok(j) => j,
        Err(_) => return Err(ApiError::InternalServerError("Erro ao serializar modelo".to_string())),
    };

    // Definir nome do arquivo
    let filename = format!("modelo_{}.json", model.title.replace(" ", "_"));

    // Montar headers para download
    let mut headers = HeaderMap::new();
    headers.insert(
        header::CONTENT_TYPE,
        HeaderValue::from_static("application/json"),
    );
    headers.insert(
        header::CONTENT_DISPOSITION,
        HeaderValue::from_str(&format!("attachment; filename=\"{}\"", filename)).unwrap(),
    );

    Ok((headers, json))
}

// Handler para aplicar modelo dinâmico
pub async fn apply_model_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(model_id): Path<Uuid>,
    Json(payload): Json<ApplyModelRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    let result = model_service::apply_model(
        user_id,
        model_id,
        payload,
        &mut conn
    ).await.map_err(ApiError::InternalServerError)?;

    Ok((StatusCode::OK, Json(result)))
}

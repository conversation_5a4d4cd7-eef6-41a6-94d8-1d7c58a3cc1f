use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};
use std::fmt;
use tracing::error;
use validator::ValidationErrors;

// Enum com todos os erros possíveis da aplicação
#[derive(Debug)]
pub enum ApiError {
    // Erros de autenticação
    AuthenticationFailed,
    InvalidCredentials,
    TokenExpired,
    TokenInvalid,
    EmailNotVerified,

    // Erros de autorização
    Unauthorized,
    // InsufficientPermissions,

    // Erros de recurso
    ResourceNotFound(String),
    ResourceAlreadyExists(String),

    // Erros de validação
    ValidationError(ValidationErrors),
    // InvalidInput(String),

    // Erros de banco de dados
    DatabaseError(String),

    // Erros gerais
    InternalServerError(String),
    ServiceUnavailable,
    BadRequest(String),
}

// Modelo de resposta de erro padronizado
#[derive(Serialize, Deserialize)]
pub struct ErrorResponse {
    pub status: String,
    pub code: String,
    pub message: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<serde_json::Value>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub path: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub timestamp: Option<String>,
}

impl fmt::Display for ApiError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ApiError::AuthenticationFailed => write!(f, "Falha na autenticação"),
            ApiError::InvalidCredentials => write!(f, "Credenciais inválidas"),
            ApiError::TokenExpired => write!(f, "Token expirado"),
            ApiError::TokenInvalid => write!(f, "Token inválido"),
            ApiError::EmailNotVerified => write!(f, "E-mail não verificado"),
            ApiError::Unauthorized => write!(f, "Não autorizado"),
            // ApiError::InsufficientPermissions => write!(f, "Permissões insuficientes"),
            ApiError::ResourceNotFound(resource) => write!(f, "{} não encontrado(a)", resource),
            ApiError::ResourceAlreadyExists(resource) => write!(f, "{} já existe", resource),
            ApiError::ValidationError(errors) => {
                let messages: Vec<String> = errors
                    .field_errors().values().flat_map(|errors| {
                        errors
                            .iter()
                            .map(|e| e.message.as_ref().map(|m| m.to_string()).unwrap_or_default())
                            .collect::<Vec<String>>()
                    })
                    .collect();
                write!(f, "Erro de validação: {}", messages.join(", "))
            }
            // ApiError::InvalidInput(msg) => write!(f, "Entrada inválida: {}", msg),
            ApiError::DatabaseError(msg) => write!(f, "Erro de banco de dados: {}", msg),
            ApiError::InternalServerError(msg) => write!(f, "Erro interno do servidor: {}", msg),
            ApiError::ServiceUnavailable => write!(f, "Serviço indisponível"),
            ApiError::BadRequest(msg) => write!(f, "Requisição inválida: {}", msg),
        }
    }
}

impl ApiError {
    // Retorna o código de status HTTP apropriado para cada tipo de erro
    pub fn status_code(&self) -> StatusCode {
        match self {
            ApiError::AuthenticationFailed
            | ApiError::InvalidCredentials
            | ApiError::TokenInvalid
            | ApiError::Unauthorized => StatusCode::UNAUTHORIZED,
            ApiError::TokenExpired => StatusCode::UNAUTHORIZED, // Could also be 403 Forbidden
            ApiError::EmailNotVerified => StatusCode::FORBIDDEN,
            ApiError::ResourceNotFound(_) => StatusCode::NOT_FOUND,
            ApiError::ResourceAlreadyExists(_) => StatusCode::CONFLICT,
            ApiError::ValidationError(_) => StatusCode::UNPROCESSABLE_ENTITY,
            ApiError::BadRequest(_) => StatusCode::BAD_REQUEST,
            ApiError::DatabaseError(_) | ApiError::InternalServerError(_) => {
                StatusCode::INTERNAL_SERVER_ERROR
            }
            ApiError::ServiceUnavailable => StatusCode::SERVICE_UNAVAILABLE,
        }
    }

    // Retorna um código de erro específico para cada tipo de erro
    pub fn error_code(&self) -> String {
        match self {
            ApiError::AuthenticationFailed => "AUTH_FAILED",
            ApiError::InvalidCredentials => "INVALID_CREDENTIALS",
            ApiError::TokenExpired => "TOKEN_EXPIRED",
            ApiError::TokenInvalid => "TOKEN_INVALID",
            ApiError::EmailNotVerified => "EMAIL_NOT_VERIFIED",
            ApiError::Unauthorized => "UNAUTHORIZED",
            // ApiError::InsufficientPermissions => "INSUFFICIENT_PERMISSIONS",
            ApiError::ResourceNotFound(_) => "RESOURCE_NOT_FOUND",
            ApiError::ResourceAlreadyExists(_) => "RESOURCE_ALREADY_EXISTS",
            ApiError::ValidationError(_) => "VALIDATION_ERROR",
            // ApiError::InvalidInput(_) => "INVALID_INPUT",
            ApiError::DatabaseError(_) => "DATABASE_ERROR",
            ApiError::InternalServerError(_) => "INTERNAL_SERVER_ERROR",
            ApiError::ServiceUnavailable => "SERVICE_UNAVAILABLE",
            ApiError::BadRequest(_) => "BAD_REQUEST",
        }
        .to_string()
    }

    // Cria detalhes adicionais para erros específicos
    pub fn details(&self) -> Option<serde_json::Value> {
        match self {
            ApiError::ValidationError(errors) => {
                Some(serde_json::json!({ "errors": errors.field_errors() }))
            }
            ApiError::ResourceNotFound(resource) => {
                Some(serde_json::json!({ "resource": resource }))
            }
            ApiError::ResourceAlreadyExists(resource) => {
                Some(serde_json::json!({ "resource": resource }))
            }
            ApiError::DatabaseError(message) | ApiError::InternalServerError(message) => {
                if cfg!(debug_assertions) {
                    Some(serde_json::json!({ "message": message }))
                } else {
                    None
                }
            }
            _ => None,
        }
    }
}

// Implementação para converter ApiError em uma Response para o Axum
impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        // Registrar erro no log, especialmente para erros internos
        match &self {
            ApiError::InternalServerError(_) | ApiError::DatabaseError(_) => {
                error!("Erro interno: {}", self);
            }
            _ => {
                // Para erros não críticos, podemos usar um nível de log diferente
                tracing::info!("Erro da API: {}", self);
            }
        }

        // Criar a resposta de erro padronizada
        let status = self.status_code();
        let error_response = ErrorResponse {
            status: status.to_string(),
            code: self.error_code(),
            message: self.to_string(),
            details: self.details(),
            path: None, // Isso seria definido em um middleware
            timestamp: Some(chrono::Utc::now().to_rfc3339()),
        };

        // Retornar a resposta formatada
        (status, Json(error_response)).into_response()
    }
}

// Métodos de conversão de erros genéricos para ApiError
impl From<diesel::result::Error> for ApiError {
    fn from(error: diesel::result::Error) -> Self {
        match error {
            diesel::result::Error::NotFound => ApiError::ResourceNotFound("Recurso".to_string()),
            diesel::result::Error::DatabaseError(kind, info) => {
                if let diesel::result::DatabaseErrorKind::UniqueViolation = kind {
                    ApiError::ResourceAlreadyExists("Recurso".to_string())
                } else {
                    ApiError::DatabaseError(format!("{:?}: {}", kind, info.message()))
                }
            }
            _ => ApiError::DatabaseError(error.to_string()),
        }
    }
}

impl From<ValidationErrors> for ApiError {
    fn from(errors: ValidationErrors) -> Self {
        ApiError::ValidationError(errors)
    }
}

impl From<diesel::r2d2::Error> for ApiError {
    fn from(error: diesel::r2d2::Error) -> Self {
        ApiError::InternalServerError(format!("Erro no pool de conexões: {}", error))
    }
}

impl From<bcrypt::BcryptError> for ApiError {
    fn from(error: bcrypt::BcryptError) -> Self {
        ApiError::InternalServerError(error.to_string())
    }
}

impl From<String> for ApiError {
    fn from(error: String) -> Self {
        ApiError::InternalServerError(error)
    }
}

// Implementação para erros padrão
impl std::error::Error for ApiError {}

// Helper para construir um Result com ApiError
pub type ApiResult<T> = Result<T, ApiError>;

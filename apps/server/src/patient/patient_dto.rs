
use crate::contact::contact_dto::{ContactResponse, CreateContactRequest as ContactInput}; // Usar DTO de contato
use crate::db::models::patient::Patient;
use crate::utils::cpf_validator::is_valid_cpf;
use chrono::{Datelike, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::{Validate, ValidationError}; 

fn validate_cpf(cpf: &str) -> Result<(), ValidationError> {
    if cpf.trim().is_empty() {
        return Ok(()); 
    }

    if !is_valid_cpf(cpf) {
        return Err(ValidationError::new("cpf_invalido"));
    }

    Ok(())
}

#[derive(Debug, Serialize)]
pub struct PatientResponse {
    pub id: Uuid, 
    pub full_name: String,
    pub date_of_birth: Option<NaiveDate>,
    pub age: Option<i32>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub notes: Option<String>,
    pub cpf: Option<String>,
    pub guardian: Option<ContactResponse>, 
    pub contacts: Option<Vec<ContactResponse>>, 
}

impl From<Patient> for PatientResponse {
    fn from(patient: Patient) -> Self {
        let age = patient.date_of_birth.map(|dob| {
            let today = Utc::now().date_naive();
            let years = today.year() - dob.year();

            if today.ordinal() < dob.ordinal() {
                years - 1
            } else {
                years
            }
        });

        Self {
            id: patient.id,
            full_name: patient.full_name,
            date_of_birth: patient.date_of_birth,
            age,
            phone: patient.phone,
            email: patient.email,
            address: patient.address,
            city: patient.city,
            state: patient.state,
            notes: patient.notes,
            cpf: patient.cpf,
            guardian: None, 
            contacts: None, 
        }
    }
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreatePatientRequest {
    #[validate(length(min = 3, message = "Nome completo deve ter pelo menos 3 caracteres"))]
    pub full_name: String,
    pub date_of_birth: Option<NaiveDate>,
    #[validate(custom(function = "validate_cpf"))]
    pub cpf: Option<String>,
    pub phone: Option<String>,
    #[validate(email(message = "Email em formato inválido"))]
    pub email: Option<String>,
    pub address: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub notes: Option<String>,
    pub contacts: Option<Vec<ContactLinkRequest>>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct ContactLinkRequest {
    pub contact_id: Option<Uuid>, 
    #[validate(nested)]
    pub contact_data: Option<ContactInput>, 
    #[validate(length(min = 1))]
    pub role: String, 
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdatePatientRequest {
    #[validate(length(min = 3, message = "Nome completo deve ter pelo menos 3 caracteres"))]
    pub full_name: Option<String>,
    pub date_of_birth: Option<NaiveDate>,
    #[validate(custom(function = "validate_cpf"))]
    pub cpf: Option<String>,
    pub phone: Option<String>,
    #[validate(email(message = "Email em formato inválido"))]
    pub email: Option<String>,
    pub address: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub notes: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct SimplePatient {
    pub id: Uuid, 
    pub full_name: String,
}

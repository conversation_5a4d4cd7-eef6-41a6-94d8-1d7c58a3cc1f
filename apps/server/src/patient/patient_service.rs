use crate::{
    config::database::Db<PERSON><PERSON>,
    contact::contact_service,
    db::models::patient::{NewPatient, Patient},
    patient::{
        patient_dto::{CreatePatientRequest, PatientResponse, UpdatePatientRequest},
        patient_repository,
    },
};
use diesel::PgConnection;
use uuid::Uuid;

async fn patient_to_response(
    conn: &mut PgConnection,
    patient: Patient,
) -> Result<PatientResponse, String> {
    let mut response: PatientResponse = patient.into();
    let contacts =
        contact_service::get_patient_contacts(response.id, conn).await?; // User ID is not needed here
    response.guardian = contacts.iter().find(|c| c.role == Some("guardian".to_string())).cloned();
    response.contacts = Some(contacts);
    Ok(response)
}

pub async fn get_patients_list(
    user_id: Uuid,
    pool: DbPool,
) -> Result<Vec<PatientResponse>, String> {
    let mut conn = pool.get().map_err(|e| e.to_string())?;
    let patients = patient_repository::find_patients_by_user(user_id, &mut conn)
        .map_err(|e| e.to_string())?;
    let mut responses = Vec::new();
    for p in patients {
        responses.push(patient_to_response(&mut conn, p).await?);
    }
    Ok(responses)
}

pub async fn get_patient_details(
    user_id: Uuid,
    patient_id: Uuid,
    pool: DbPool,
) -> Result<PatientResponse, String> {
    let mut conn = pool.get().map_err(|e| e.to_string())?;
    let patient =
        patient_repository::find_patient_by_id(patient_id, user_id, &mut conn)
            .map_err(|e| e.to_string())?;
    patient_to_response(&mut conn, patient).await
}

pub async fn create_new_patient(
    user_id: Uuid,
    request: CreatePatientRequest,
    pool: DbPool,
) -> Result<PatientResponse, String> {
    let mut conn = pool.get().map_err(|e| e.to_string())?;
    let new_patient = NewPatient {
        user_id,
        full_name: request.full_name,
        date_of_birth: request.date_of_birth,
        cpf: request.cpf,
        phone: request.phone,
        email: request.email,
        address: request.address,
        city: request.city,
        state: request.state,
        notes: request.notes,
        gender: None,
        marital_status: None,
        rg: None,
        ethnicity: None,
        nationality: None,
        naturalness: None,
        occupation: None,
        is_active: Some(true),
    };
    let created_patient = patient_repository::create_patient(&new_patient, &mut conn)
        .map_err(|e| e.to_string())?;
    // Handle contacts linking here if needed, similar to the old implementation
    patient_to_response(&mut conn, created_patient).await
}

pub async fn update_existing_patient(
    user_id: Uuid,
    patient_id: Uuid,
    request: UpdatePatientRequest,
    pool: DbPool,
) -> Result<PatientResponse, String> {
    let mut conn = pool.get().map_err(|e| e.to_string())?;
    let mut patient =
        patient_repository::find_patient_by_id(patient_id, user_id, &mut conn)
            .map_err(|e| e.to_string())?;

    if let Some(name) = request.full_name {
        patient.full_name = name;
    }
    if let Some(dob) = request.date_of_birth {
        patient.date_of_birth = Some(dob);
    }
    // ... update other fields ...
    let updated_patient =
        patient_repository::update_patient(patient_id, user_id, &patient, &mut conn)
            .map_err(|e| e.to_string())?;
    patient_to_response(&mut conn, updated_patient).await
}

pub async fn delete_patient(
    user_id: Uuid,
    patient_id: Uuid,
    pool: DbPool,
) -> Result<(), String> {
    let mut conn = pool.get().map_err(|e| e.to_string())?;
    patient_repository::deactivate_patient(patient_id, user_id, &mut conn)
        .map_err(|e| e.to_string())?;
    Ok(())
}

pub async fn search_patients_by_term(
    user_id: Uuid,
    search_term: &str,
    pool: DbPool,
) -> Result<Vec<PatientResponse>, String> {
    let mut conn = pool.get().map_err(|e| e.to_string())?;
    let patients =
        patient_repository::search_patients(user_id, search_term, &mut conn)
            .map_err(|e| e.to_string())?;
    let mut responses = Vec::new();
    for p in patients {
        responses.push(patient_to_response(&mut conn, p).await?);
    }
    Ok(responses)
}

use crate::{
    app::AppState,
    auth::auth_middleware::AuthUser,
    errors::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    patient::{
        patient_dto::{CreatePatientRequest, PatientResponse, UpdatePatientRequest},
        patient_service,
    },
};
use axum::{
    extract::{Path, Query, State},
    response::Json,
    Extension,
};
use serde::Deserialize;
use uuid::Uuid;
use validator::Validate;

#[derive(Deserialize)]
pub struct SearchQuery {
    q: String,
}

pub async fn create_patient_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Json(payload): <PERSON><PERSON><CreatePatientRequest>,
) -> ApiResult<Json<PatientResponse>> {
    payload.validate()?;
    let patient =
        patient_service::create_new_patient(authenticated_user.0, payload, state.pool).await?;
    Ok(Json(patient))
}

pub async fn list_patients_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
) -> ApiResult<Json<Vec<PatientResponse>>> {
    let patients = patient_service::get_patients_list(authenticated_user.0, state.pool).await?;
    Ok(Json(patients))
}

pub async fn get_patient_details_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Path(patient_id): Path<Uuid>,
) -> ApiResult<Json<PatientResponse>> {
    let patient =
        patient_service::get_patient_details(authenticated_user.0, patient_id, state.pool).await?;
    Ok(Json(patient))
}

pub async fn update_patient_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Path(patient_id): Path<Uuid>,
    Json(payload): Json<UpdatePatientRequest>,
) -> ApiResult<Json<PatientResponse>> {
    payload.validate()?;
    let patient = patient_service::update_existing_patient(
        authenticated_user.0,
        patient_id,
        payload,
        state.pool,
    )
    .await?;
    Ok(Json(patient))
}

pub async fn delete_patient_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Path(patient_id): Path<Uuid>,
) -> ApiResult<Json<()>> {
    patient_service::delete_patient(authenticated_user.0, patient_id, state.pool).await?;
    Ok(Json(()))
}

pub async fn search_patients_handler(
    State(state): State<AppState>,
    Extension(authenticated_user): Extension<AuthUser>,
    Query(query): Query<SearchQuery>,
) -> ApiResult<Json<Vec<PatientResponse>>> {
    let patients =
        patient_service::search_patients_by_term(authenticated_user.0, &query.q, state.pool)
            .await?;
    Ok(Json(patients))
}

use crate::db::models::patient::{NewPatient, Patient};
use crate::schema::patients; 
use chrono::{Datelike, Utc}; 
use diesel::prelude::*; 
use diesel::result::QueryResult;
use diesel::sql_types::{Integer, Nullable, Text, Timestamp}; 
use uuid::Uuid; 
use diesel::define_sql_function;

define_sql_function! {
    #[sql_name = "date_part"]
    fn date_part_nullable(part: Text, date: Nullable<Timestamp>) -> Integer
}

define_sql_function! {
    #[sql_name = "date_part"]
    fn date_part_non_nullable(part: Text, date: Timestamp) -> Integer
}

define_sql_function! {
    #[sql_name = "make_date"]
    fn make_date(year: Integer, month: Integer, day: Integer) -> Timestamp
}

pub fn find_patients_by_user(
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Patient>> {
    patients::table
        .filter(patients::user_id.eq(user_id_val))
        .filter(patients::is_active.eq(true))
        .order_by(patients::full_name.asc())
        .select(Patient::as_select()) 
        .load(conn)
}

pub fn count_patients_by_user(user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<i64> {
    patients::table
        .filter(patients::user_id.eq(user_id_val))
        .filter(patients::is_active.eq(true))
        .count()
        .get_result(conn)
}

pub fn find_patient_by_id(
    patient_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Patient> {
    patients::table
        .filter(patients::id.eq(patient_id_val))
        .filter(patients::user_id.eq(user_id_val))
        .filter(patients::is_active.eq(true))
        .select(Patient::as_select()) 
        .first(conn)
}

pub fn create_patient(new_patient: &NewPatient, conn: &mut PgConnection) -> QueryResult<Patient> {
    diesel::insert_into(patients::table)
        .values(new_patient)
        .returning(Patient::as_returning()) 
        .get_result(conn)
}

// Atualizar para usar AsChangeset e remover guardian_id
pub fn update_patient(
    patient_id_val: Uuid,
    user_id_val: Uuid,
    patient_data: &Patient, 
    conn: &mut PgConnection,
) -> QueryResult<Patient> {
    diesel::update(
        patients::table
            .filter(patients::id.eq(patient_id_val))
            .filter(patients::user_id.eq(user_id_val)),
    )
    .set(patient_data) 
    .returning(Patient::as_returning()) 
    .get_result(conn)
}

pub fn deactivate_patient(
    patient_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Patient> {
    diesel::update(
        patients::table
            .filter(patients::id.eq(patient_id_val))
            .filter(patients::user_id.eq(user_id_val)),
    )
    .set(patients::is_active.eq(false))
    .returning(Patient::as_returning()) 
        .get_result(conn)
}

pub fn count_new_patients_this_month_by_user(
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<i64> {
    let now = Utc::now().naive_utc();
    let current_year = now.year();
    let current_month = now.month() as i32;

    patients::table
        .filter(patients::user_id.eq(user_id_val))
        .filter(patients::is_active.eq(true))
        .filter(date_part_non_nullable("year", patients::created_at).eq(current_year))
        .filter(date_part_non_nullable("month", patients::created_at).eq(current_month))
        .count()
        .get_result(conn)
}

pub fn find_recent_patients_by_user(
    user_id_val: Uuid,
    limit: i64,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Patient>> {
    patients::table
        .filter(patients::user_id.eq(user_id_val))
        .filter(patients::is_active.eq(true))
        .order_by(patients::created_at.desc())
        .limit(limit)
        .select(Patient::as_select())
        .load(conn)
}

pub fn search_patients(
    user_id_val: Uuid,
    search_term: &str,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Patient>> {
    let search = format!("%{}%", search_term);

    patients::table
        .filter(patients::user_id.eq(user_id_val))
        .filter(patients::is_active.eq(true))
        .filter(
            patients::full_name
                .ilike(&search)
                .or(patients::email.ilike(&search))
                .or(patients::cpf.ilike(&search)),
        )
        .order_by(patients::full_name.asc())
        .select(Patient::as_select()) 
        .load(conn)
}

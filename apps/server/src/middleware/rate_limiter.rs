use axum::{
    extract::{Request, State},
    http::StatusCode,
    middleware::Next,
    response::IntoResponse,
};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::Mutex;
use std::time::{Duration, Instant};

#[derive(Clone)]
pub struct RateLimiter {
    store: Arc<Mutex<HashMap<String, (Instant, u32)>>>,
    max_requests: u32,
    window_size: u64,
}

impl RateLimiter {
    pub fn new(max_requests: u32, window_size: u64) -> Self {
        Self {
            store: Arc::new(Mutex::new(HashMap::new())),
            max_requests,
            window_size,
        }
    }

    fn clean_old_entries(&self) {
        let now = Instant::now();
        let window = Duration::from_secs(self.window_size);

        let mut store = self.store.lock().unwrap();
        store.retain(|_, v| now.duration_since(v.0) < window);
    }

    fn get_rate_limit_key(&self, req: &Request) -> String {
        if let Some(user_agent) = req
            .headers()
            .get("User-Agent")
            .and_then(|v| v.to_str().ok())
        {
            return format!("ua:{}", user_agent);
        }
        "unknown".to_string()
    }
}

// Middleware para rate limiting - note a assinatura correta
pub async fn rate_limit_middleware(
    State(limiter): State<RateLimiter>,
    req: Request,
    next: Next,
) -> impl IntoResponse {
    let rate_limit_key = limiter.get_rate_limit_key(&req);
    let now = Instant::now();
    let window = Duration::from_secs(limiter.window_size);

    // Limpeza periódica
    if rand::random::<f32>() < 0.01 {
        limiter.clean_old_entries();
    }

    let exceeded = {
        let mut store = limiter.store.lock().unwrap();
        let mut exceeded = false;

        if let Some(entry) = store.get_mut(&rate_limit_key) {
            if now.duration_since(entry.0) > window {
                // Reset se o tempo da janela passou
                *entry = (now, 1);
            } else if entry.1 >= limiter.max_requests {
                // Limite excedido
                exceeded = true;
            } else {
                // Incrementar contagem
                entry.1 += 1;
            }
        } else {
            // Primeira requisição deste cliente
            store.insert(rate_limit_key, (now, 1));
        }

        exceeded
    };

    if exceeded {
        return (
            StatusCode::TOO_MANY_REQUESTS,
            "Muitas requisições, tente novamente mais tarde",
        )
            .into_response();
    }

    next.run(req).await
}

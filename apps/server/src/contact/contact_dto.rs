use chrono::NaiveDate; // Adicionar NaiveDate
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Serialize, Clone)]  
pub struct ContactResponse {
    pub id: Uuid, 
    pub name: String,
    pub relationship_type: String,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub notes: Option<String>,
    pub user_id: Option<Uuid>, 
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub date_of_birth: Option<NaiveDate>,
    pub gender: Option<String>,
    pub marital_status: Option<String>,
    pub ethnicity: Option<String>,
    pub nationality: Option<String>,
    pub naturalness: Option<String>,
    pub occupation: Option<String>,
    pub is_active: Option<bool>,
    pub patient_id: Option<Uuid>, 
    pub role: Option<String>, 
    pub linked_patient_count: i64, 
}

use validator::Validate; 

#[derive(Debug, Deserialize, Validate)] 
pub struct CreateContactRequest {
    #[validate(length(min = 3, message = "Nome deve ter pelo menos 3 caracteres"))] 
    pub name: String,
    #[validate(length(min = 1, message = "Tipo de relação é obrigatório"))] // Adicionar validação
    pub relationship_type: String,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub notes: Option<String>,
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub date_of_birth: Option<NaiveDate>,
    pub gender: Option<String>,
    pub marital_status: Option<String>,
    pub ethnicity: Option<String>,
    pub nationality: Option<String>,
    pub naturalness: Option<String>,
    pub occupation: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Deserialize, Validate)] 
pub struct UpdateContactRequest {
     #[validate(length(min = 3, message = "Nome deve ter pelo menos 3 caracteres"))] 
    pub name: Option<String>,
    #[validate(length(min = 1, message = "Tipo de relação é obrigatório"))] 
    pub relationship_type: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<String>,
    pub notes: Option<String>,
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub date_of_birth: Option<NaiveDate>,
    pub gender: Option<String>,
    pub marital_status: Option<String>,
    pub ethnicity: Option<String>,
    pub nationality: Option<String>,
    pub naturalness: Option<String>,
    pub occupation: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct LinkContactRequest {
    pub contact_id: Uuid, 
    pub role: String, 
}

#[derive(Debug, Serialize)]
pub struct ContactLinksSummaryResponse {
    pub guardian_link_count: i64, 
    pub other_link_count: i64,    
}

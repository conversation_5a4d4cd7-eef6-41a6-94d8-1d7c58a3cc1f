use crate::contact::contact_dto::{ContactResponse, CreateContactRequest, UpdateContactRequest, ContactLinksSummaryResponse}; // Adicionar ContactLinksSummaryResponse
use crate::contact::contact_repository;
use crate::db::models::contact::{Contact, NewContact};
use crate::db::models::patient_contact::PatientContact;
use crate::patient::patient_repository;
use crate::patient::patient_dto::SimplePatient;
use diesel::result::Error as DieselError;
use diesel::PgConnection;
use uuid::Uuid;

// Lista contatos pertencentes a um usuário
pub async fn get_all_contacts(
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> Result<Vec<ContactResponse>, String> {
    let contacts_with_count_tuples = contact_repository::find_all_contacts_by_user_with_link_count(user_id_val, conn)
        .map_err(|e| format!("Erro ao buscar contatos com contagem: {}", e))?;

    let responses = contacts_with_count_tuples
        .into_iter()
        .map(|(contact, count)| contact_to_response(contact, None, None, Some(count)))
        .collect::<Result<Vec<_>, _>>()?;

    Ok(responses)
}

// Busca detalhes de um contato pertencente a um usuário
pub async fn get_contact_details(
    user_id_val: Uuid,
    contact_id: Uuid,
    conn: &mut PgConnection,
) -> Result<ContactResponse, String> {
    let contact = contact_repository::find_contact_by_id_and_user(contact_id, user_id_val, conn)
        .map_err(|e| match e {
            DieselError::NotFound => "Contato não encontrado ou não pertence ao usuário.".to_string(),
            _ => format!("Erro ao buscar contato: {}", e),
        })?;

    let patient_links: Vec<PatientContact> =
        contact_repository::find_contact_patients(contact_id, conn)
            .map_err(|e| format!("Erro ao buscar vínculos do contato: {}", e))?;

    let (patient_id, role) = if let Some(link) = patient_links.first() {
        (Some(link.patient_id), Some(link.role.clone()))
    } else {
        (None, None)
    };

    let linked_count = patient_links.iter().filter(|l| l.role != "guardian").count() as i64;

    contact_to_response(contact, patient_id, role, Some(linked_count))
}

// Cria um novo contato associado a um usuário
pub async fn create_new_contact(
    user_id_val: Uuid,
    request: CreateContactRequest,
    conn: &mut PgConnection,
) -> Result<ContactResponse, String> {
    let new_contact = NewContact {
        user_id: Some(user_id_val),
        name: request.name,
        relationship_type: request.relationship_type,
        phone: request.phone,
        email: request.email,
        address: request.address,
        notes: request.notes,
        cpf: request.cpf,
        rg: request.rg,
        city: request.city,
        state: request.state,
        date_of_birth: request.date_of_birth,
        gender: request.gender,
        marital_status: request.marital_status,
        ethnicity: request.ethnicity,
        nationality: request.nationality,
        naturalness: request.naturalness,
        occupation: request.occupation,
        is_active: request.is_active.or(Some(true)),
    };

    let contact = contact_repository::create_contact(&new_contact, conn)
        .map_err(|e| format!("Erro ao criar contato: {}", e))?;

    contact_to_response(contact, None, None, Some(0))
}

// Atualiza um contato existente pertencente a um usuário
pub async fn update_existing_contact(
    user_id_val: Uuid,
    contact_id: Uuid,
    request: UpdateContactRequest,
    conn: &mut PgConnection,
) -> Result<ContactResponse, String> {
    let mut contact = contact_repository::find_contact_by_id_and_user(contact_id, user_id_val, conn)
        .map_err(|e| match e {
            DieselError::NotFound => "Contato não encontrado ou não pertence ao usuário.".to_string(),
            _ => format!("Erro ao buscar contato para atualização: {}", e),
        })?;

    // Atualizar campos...
    if let Some(name_val) = request.name { contact.name = name_val; }
    if let Some(relationship_type_val) = request.relationship_type { contact.relationship_type = relationship_type_val; }
    contact.phone = request.phone.or(contact.phone);
    contact.email = request.email.or(contact.email);
    contact.address = request.address.or(contact.address);
    contact.notes = request.notes.or(contact.notes);
    contact.cpf = request.cpf.or(contact.cpf);
    contact.rg = request.rg.or(contact.rg);
    contact.city = request.city.or(contact.city);
    contact.state = request.state.or(contact.state);
    contact.date_of_birth = request.date_of_birth.or(contact.date_of_birth);
    contact.gender = request.gender.or(contact.gender);
    contact.marital_status = request.marital_status.or(contact.marital_status);
    contact.ethnicity = request.ethnicity.or(contact.ethnicity);
    contact.nationality = request.nationality.or(contact.nationality);
    contact.naturalness = request.naturalness.or(contact.naturalness);
    contact.occupation = request.occupation.or(contact.occupation);
    contact.is_active = request.is_active.or(contact.is_active);


    let updated_contact = contact_repository::update_contact(contact_id, &contact, conn)
        .map_err(|e| format!("Erro ao atualizar contato: {}", e))?;

    let patient_links: Vec<PatientContact> =
        contact_repository::find_contact_patients(contact_id, conn)
            .map_err(|e| format!("Erro ao buscar vínculos do contato: {}", e))?;

    let (patient_id, role) = if let Some(link) = patient_links.first() {
        (Some(link.patient_id), Some(link.role.clone()))
    } else {
        (None, None)
    };

     let linked_count = patient_links.iter().filter(|l| l.role != "guardian").count() as i64;

    contact_to_response(updated_contact, patient_id, role, Some(linked_count))
}

// Deleta um contato pertencente a um usuário
pub async fn delete_contact(
    user_id_val: Uuid,
    contact_id: Uuid,
    conn: &mut PgConnection,
) -> Result<(), String> {
    let _contact = contact_repository::find_contact_by_id_and_user(contact_id, user_id_val, conn)
        .map_err(|e| match e {
            DieselError::NotFound => "Contato não encontrado ou não pertence ao usuário.".to_string(),
            _ => format!("Erro ao buscar contato para exclusão: {}", e),
        })?;

    contact_repository::delete_contact(contact_id, conn)
        .map_err(|e| format!("Erro ao excluir contato: {}", e))?;

    Ok(())
}

// Busca os contatos vinculados a um paciente
pub async fn get_patient_contacts(
    patient_id: Uuid,
    conn: &mut PgConnection,
) -> Result<Vec<ContactResponse>, String> {
    let patient_contacts: Vec<(Contact, PatientContact)> =
        contact_repository::find_patient_contacts(patient_id, conn)
            .map_err(|e| format!("Erro ao buscar contatos do paciente: {}", e))?;

    let responses = patient_contacts
        .into_iter()
        .map(|(contact, link)| {
            contact_to_response(contact, Some(patient_id), Some(link.role), None)
        })
        .collect::<Result<Vec<_>, _>>()?;

    Ok(responses)
}

// Vincula um contato a um paciente
pub async fn link_contact_to_patient(
    user_id_val: Uuid,
    patient_id: Uuid,
    contact_id: Uuid,
    role: String,
    conn: &mut PgConnection,
) -> Result<ContactResponse, String> {
    let _patient = patient_repository::find_patient_by_id(patient_id, user_id_val, conn)
        .map_err(|e| match e {
            DieselError::NotFound => "Paciente não encontrado ou não pertence ao usuário.".to_string(),
            _ => format!("Erro ao buscar paciente: {}", e),
        })?;

    let contact = contact_repository::find_contact_by_id_and_user(contact_id, user_id_val, conn)
        .map_err(|e| match e {
            DieselError::NotFound => "Contato não encontrado ou não pertence ao usuário.".to_string(),
            _ => format!("Erro ao buscar contato: {}", e),
        })?;

    if role == "guardian" {
        let patient_contacts: Vec<(Contact, PatientContact)> =
            contact_repository::find_patient_contacts(patient_id, conn)
                .map_err(|e| format!("Erro ao buscar contatos do paciente: {}", e))?;

        for (existing_contact, link) in patient_contacts {
            if link.role == "guardian" && existing_contact.id != contact_id {
                contact_repository::unlink_contact_from_patient(patient_id, existing_contact.id, conn)
                    .map_err(|e| format!("Erro ao desvincular responsável anterior: {}", e))?;
                contact_repository::link_contact_to_patient(patient_id, existing_contact.id, "other".to_string(), conn)
                    .map_err(|e| format!("Erro ao reconfigurar vínculo do responsável anterior: {}", e))?;
                break;
            }
        }
    }

    contact_repository::link_contact_to_patient(patient_id, contact_id, role.clone(), conn)
        .map_err(|e| format!("Erro ao vincular contato ao paciente: {}", e))?;

     let linked_count = contact_repository::find_contact_patients(contact_id, conn)
        .map(|links| links.into_iter().filter(|l| l.role != "guardian").count() as i64)
        .unwrap_or(0);

    contact_to_response(contact, Some(patient_id), Some(role), Some(linked_count))
}

// Desvincula um contato de um paciente
pub async fn unlink_contact_from_patient(
    user_id_val: Uuid,
    patient_id: Uuid,
    contact_id: Uuid,
    conn: &mut PgConnection,
) -> Result<(), String> {
    let patient = patient_repository::find_patient_by_id(patient_id, user_id_val, conn)
        .map_err(|e| match e {
            DieselError::NotFound => "Paciente não encontrado ou não pertence ao usuário.".to_string(),
            _ => format!("Erro ao buscar paciente: {}", e),
        })?;

    let patient_contacts: Vec<(Contact, PatientContact)> =
        contact_repository::find_patient_contacts(patient_id, conn)
            .map_err(|e| format!("Erro ao buscar contatos do paciente: {}", e))?;

    let mut is_guardian = false;
    let mut contact_link_exists = false;

    for (contact, link) in &patient_contacts {
        if contact.id == contact_id {
            contact_link_exists = true;
            if link.role == "guardian" {
                is_guardian = true;
                break;
            }
        }
    }

    if !contact_link_exists {
        return Err("Contato não está vinculado a este paciente".to_string());
    }

    if is_guardian {
        if let Some(birth_date) = patient.date_of_birth {
            let today = chrono::Local::now().naive_local().date();
            let age = today.years_since(birth_date);
            if let Some(age) = age {
                if age < 18 {
                    return Err("Não é possível remover o responsável principal de um paciente menor de idade.".to_string());
                }
            }
        }
    }

    contact_repository::unlink_contact_from_patient(patient_id, contact_id, conn)
        .map_err(|e| format!("Erro ao desvincular contato: {}", e))?;

    Ok(())
}

// Nova função para buscar pacientes vinculados a um contato
pub async fn get_linked_patients_for_contact(
    user_id_val: Uuid,
    contact_id: Uuid,
    conn: &mut PgConnection,
) -> Result<Vec<SimplePatient>, String> {
    // 1. Verificar se o contato pertence ao usuário
    let _contact = contact_repository::find_contact_by_id_and_user(contact_id, user_id_val, conn)
        .map_err(|e| match e {
            DieselError::NotFound => "Contato não encontrado ou não pertence ao usuário.".to_string(),
            _ => format!("Erro ao buscar contato: {}", e),
        })?;

    // 2. Chamar a função do repositório para buscar os pacientes vinculados (retorna tupla)
    let patient_tuples = contact_repository::find_linked_patients_for_contact(contact_id, conn)
        .map_err(|e| format!("Erro ao buscar pacientes vinculados: {}", e))?;

    // 3. Mapear a tupla para a struct SimplePatient (DTO)
    let simple_patients = patient_tuples
        .into_iter()
        .map(|(id, full_name)| SimplePatient { id, full_name })
        .collect();

    Ok(simple_patients)
}

// Nova função para buscar o resumo de vínculos de um contato
pub async fn get_contact_links_summary(
    user_id_val: Uuid,
    contact_id: Uuid,
    conn: &mut PgConnection,
) -> Result<ContactLinksSummaryResponse, String> {
     // 1. Verificar se o contato pertence ao usuário
    let _contact = contact_repository::find_contact_by_id_and_user(contact_id, user_id_val, conn)
        .map_err(|e| match e {
            DieselError::NotFound => "Contato não encontrado ou não pertence ao usuário.".to_string(),
            _ => format!("Erro ao buscar contato: {}", e),
        })?;

    // 2. Buscar todos os vínculos para este contato
    let all_links = contact_repository::find_contact_patients(contact_id, conn)
         .map_err(|e| format!("Erro ao buscar vínculos do contato: {}", e))?;

    // 3. Calcular as contagens
    let guardian_link_count = all_links.iter().filter(|link| link.role == "guardian").count() as i64;
    let other_link_count = all_links.iter().filter(|link| link.role != "guardian").count() as i64; // Ou role == "other"

    Ok(ContactLinksSummaryResponse {
        guardian_link_count,
        other_link_count,
    })
}


// Função auxiliar para converter Contact em ContactResponse
pub fn contact_to_response(
    contact: Contact,
    patient_id: Option<Uuid>,
    role: Option<String>,
    linked_patient_count: Option<i64>,
) -> Result<ContactResponse, String> {
    Ok(ContactResponse {
        id: contact.id,
        name: contact.name,
        relationship_type: contact.relationship_type,
        phone: contact.phone,
        email: contact.email,
        address: contact.address,
        notes: contact.notes,
        user_id: contact.user_id,
        cpf: contact.cpf,
        rg: contact.rg,
        city: contact.city,
        state: contact.state,
        date_of_birth: contact.date_of_birth,
        gender: contact.gender,
        marital_status: contact.marital_status,
        ethnicity: contact.ethnicity,
        nationality: contact.nationality,
        naturalness: contact.naturalness,
        occupation: contact.occupation,
        is_active: contact.is_active,
        patient_id,
        role,
        linked_patient_count: linked_patient_count.unwrap_or(0),
    })
}

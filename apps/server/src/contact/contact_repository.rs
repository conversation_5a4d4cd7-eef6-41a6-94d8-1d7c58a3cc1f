use crate::db::models::contact::{Contact, NewContact};
use crate::db::models::patient_contact::{NewPatientContact, PatientContact};
use crate::schema::{contacts, patient_contacts, patients};
use diesel::prelude::*;
use diesel::result::QueryResult;
use diesel::sql_types::BigInt;
use uuid::Uuid;

// Busca todos os contatos pertencentes a um usuário, incluindo contagem de vínculos secundários
pub fn find_all_contacts_by_user_with_link_count(
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Vec<(Contact, i64)>> {
    use diesel::dsl::sql;

    contacts::table
        .left_join(patient_contacts::table.on(
             contacts::id.eq(patient_contacts::contact_id)
        ))
        .filter(contacts::user_id.eq(user_id_val))
        .group_by(contacts::all_columns)
        .order_by(contacts::name.asc())
        .select((
            Contact::as_select(),
            sql::<BigInt>("COALESCE(COUNT(patient_contacts.patient_id) FILTER (WHERE patient_contacts.role <> 'guardian'), 0)")
        ))
        .load::<(Contact, i64)>(conn)
}


// Busca todos os contatos pertencentes a um usuário (Função original mantida)
pub fn find_all_contacts_by_user(
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Contact>> {
    contacts::table
        .filter(contacts::user_id.eq(user_id_val))
        .order_by(contacts::name.asc())
        .select(Contact::as_select())
        .load(conn)
}

// Busca um contato específico pertencente a um usuário
pub fn find_contact_by_id_and_user(
    contact_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Contact> {
    contacts::table
        .filter(contacts::id.eq(contact_id_val))
        .filter(contacts::user_id.eq(user_id_val))
        .select(Contact::as_select())
        .first(conn)
}

// Cria um novo contato
pub fn create_contact(new_contact: &NewContact, conn: &mut PgConnection) -> QueryResult<Contact> {
    diesel::insert_into(contacts::table)
        .values(new_contact)
        .returning(Contact::as_returning())
        .get_result(conn)
}

// Atualiza um contato existente
pub fn update_contact(
    contact_id_val: Uuid,
    contact_data: &Contact,
    conn: &mut PgConnection,
) -> QueryResult<Contact> {
    diesel::update(contacts::table.find(contact_id_val))
        .set(contact_data)
        .returning(Contact::as_returning())
        .get_result(conn)
}

// Deleta um contato
pub fn delete_contact(contact_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<usize> {
    diesel::delete(contacts::table.find(contact_id_val)).execute(conn)
}

// --- Funções para gerenciar relações entre pacientes e contatos ---

// Vincula um contato a um paciente com um determinado papel
pub fn link_contact_to_patient(
    patient_id_val: Uuid,
    contact_id_val: Uuid,
    role_val: String,
    conn: &mut PgConnection,
) -> QueryResult<PatientContact> {
    let new_link = NewPatientContact {
        patient_id: patient_id_val,
        contact_id: contact_id_val,
        role: role_val.clone(),
    };

    diesel::insert_into(patient_contacts::table)
        .values(&new_link)
        .on_conflict((patient_contacts::patient_id, patient_contacts::contact_id))
        .do_update()
        .set(patient_contacts::role.eq(role_val))
        .returning(PatientContact::as_returning())
        .get_result(conn)
}

// Desvincula um contato de um paciente
pub fn unlink_contact_from_patient(
    patient_id_val: Uuid,
    contact_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    diesel::delete(
        patient_contacts::table
            .filter(patient_contacts::patient_id.eq(patient_id_val))
            .filter(patient_contacts::contact_id.eq(contact_id_val)),
    )
    .execute(conn)
}

// Busca os contatos vinculados a um paciente
pub fn find_patient_contacts(
    patient_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Vec<(Contact, PatientContact)>> {
    contacts::table
        .inner_join(
            patient_contacts::table.on(contacts::id
                .eq(patient_contacts::contact_id)
                .and(patient_contacts::patient_id.eq(patient_id_val))),
        )
        .order_by(patient_contacts::role.asc())
        .then_order_by(contacts::name.asc())
        .select((Contact::as_select(), PatientContact::as_select()))
        .load(conn)
}

// Busca os vínculos de um contato específico com pacientes
pub fn find_contact_patients(
    contact_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Vec<PatientContact>> {
    patient_contacts::table
        .filter(patient_contacts::contact_id.eq(contact_id_val))
        .select(PatientContact::as_select())
        .load(conn)
}

// Struct SimplePatient movida para patient_dto.rs

// Busca IDs e nomes de pacientes vinculados a um contato (exceto como 'guardian')
pub fn find_linked_patients_for_contact(
    contact_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Vec<(Uuid, String)>> { // Retornar tupla (id, full_name)
    patients::table
        .inner_join(patient_contacts::table.on(
            patients::id.eq(patient_contacts::patient_id)
            .and(patient_contacts::contact_id.eq(contact_id_val))
            .and(patient_contacts::role.ne("guardian"))
        ))
        .select((patients::id, patients::full_name)) // Selecionar colunas explícitas
        .order_by(patients::full_name.asc())
        .load::<(Uuid, String)>(conn) // Carregar na tupla
}
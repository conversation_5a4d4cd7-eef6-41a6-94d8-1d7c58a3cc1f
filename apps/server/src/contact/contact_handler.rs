use crate::auth::auth_middleware::AuthUser;
use crate::config::database::DbPool;
use crate::contact::contact_dto::{CreateContactRequest, LinkContactRequest, UpdateContactRequest};
use crate::contact::contact_service;
use crate::errors::{ApiError, ApiResult};
use axum::{
    extract::{Extension, Path},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use uuid::Uuid;

pub async fn get_contacts_list_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::get_all_contacts(user_id, &mut conn).await {
        Ok(contacts) => Ok(J<PERSON>(contacts)),
        Err(e) => Err(ApiError::InternalServerError(e)),
    }
}

pub async fn get_contact_details_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(contact_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::get_contact_details(user_id, contact_id, &mut conn).await {
        Ok(contact) => Ok(Json(contact)),
        Err(e) => {
            if e.contains("não encontrado") {
                Err(ApiError::ResourceNotFound("Contato".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

// Novo handler para buscar o resumo de vínculos de um contato
pub async fn get_contact_links_summary_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(contact_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::get_contact_links_summary(user_id, contact_id, &mut conn).await {
        Ok(summary) => Ok(Json(summary)),
        Err(e) => {
             if e.contains("não encontrado") {
                Err(ApiError::ResourceNotFound("Contato".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn create_contact_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(payload): Json<CreateContactRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::create_new_contact(user_id, payload, &mut conn).await {
        Ok(contact) => Ok((StatusCode::CREATED, Json(contact))),
        Err(e) => Err(ApiError::InternalServerError(e)),
    }
}

pub async fn update_contact_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(contact_id): Path<Uuid>,
    Json(payload): Json<UpdateContactRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::update_existing_contact(user_id, contact_id, payload, &mut conn).await {
        Ok(contact) => Ok(Json(contact)),
        Err(e) => {
            if e.contains("não encontrado") {
                Err(ApiError::ResourceNotFound("Contato".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn delete_contact_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(contact_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::delete_contact(user_id, contact_id, &mut conn).await {
        Ok(_) => Ok(StatusCode::NO_CONTENT),
        Err(e) => {
            if e.contains("não encontrado") {
                Err(ApiError::ResourceNotFound("Contato".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn get_patient_contacts_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(patient_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let _user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::get_patient_contacts(patient_id, &mut conn).await {
        Ok(contacts) => Ok(Json(contacts)),
        Err(e) => {
            if e.contains("não encontrado") {
                Err(ApiError::ResourceNotFound("Paciente".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn link_contact_to_patient_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(patient_id): Path<Uuid>,
    Json(payload): Json<LinkContactRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::link_contact_to_patient(
        user_id,
        patient_id,
        payload.contact_id,
        payload.role,
        &mut conn,
    )
    .await
    {
        Ok(contact) => Ok(Json(contact)),
        Err(e) => {
            if e.contains("Paciente não encontrado") {
                Err(ApiError::ResourceNotFound("Paciente".to_string()))
            } else if e.contains("Contato não encontrado") {
                Err(ApiError::ResourceNotFound("Contato".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn unlink_contact_from_patient_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path((patient_id, contact_id)): Path<(Uuid, Uuid)>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::unlink_contact_from_patient(user_id, patient_id, contact_id, &mut conn).await {
        Ok(_) => Ok(StatusCode::NO_CONTENT),
        Err(e) => {
            if e.contains("não está vinculado") {
                Err(ApiError::ResourceNotFound(
                    "Vínculo entre paciente e contato".to_string(),
                ))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

// Novo handler para buscar pacientes vinculados a um contato
pub async fn get_linked_patients_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Path(contact_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match contact_service::get_linked_patients_for_contact(user_id, contact_id, &mut conn).await {
        Ok(patients) => Ok(Json(patients)),
        Err(e) => {
             if e.contains("não encontrado") {
                Err(ApiError::ResourceNotFound("Contato".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

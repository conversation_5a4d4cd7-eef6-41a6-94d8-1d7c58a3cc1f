use crate::auth::auth_middleware::AuthUser;
use crate::config::database::DbPool;
use crate::errors::{ApiError, ApiResult};
use crate::user::user_dto::UpdateUserProfileRequest;
use crate::user::user_service;
use axum::{extract::Extension, response::IntoResponse, Json};

pub async fn get_user_profile_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match user_service::get_user_profile(user_id, &mut conn).await {
        Ok(user_response) => Ok(Json(user_response)),
        Err(e) => {
            if e.contains("não encontrado") {
                Err(ApiError::ResourceNotFound("Usuário".to_string()))
            } else {
                Err(ApiError::InternalServerError(e))
            }
        }
    }
}

pub async fn update_user_profile_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Json(payload): Json<UpdateUserProfileRequest>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match user_service::update_user_profile(user_id, payload, &mut conn).await {
        Ok(user_response) => Ok(Json(user_response)),
        Err(e) => Err(ApiError::InternalServerError(e)),
    }
}

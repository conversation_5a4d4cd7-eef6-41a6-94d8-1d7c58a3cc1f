use crate::user::user_dto::{UpdateUserProfileRequest, UpdateUserProfileResponse};
use crate::user::user_repository;
use diesel::PgConnection;
use uuid::Uuid;

pub async fn get_user_profile(
    user_id: Uuid,
    conn: &mut PgConnection,
) -> Result<UpdateUserProfileResponse, String> {
    user_repository::find_user_by_id(user_id, conn)
        .map(UpdateUserProfileResponse::from)
        .map_err(|_| "Usuário não encontrado".to_string())
}

pub async fn update_user_profile(
    user_id: Uuid,
    payload: UpdateUserProfileRequest,
    conn: &mut PgConnection,
) -> Result<UpdateUserProfileResponse, String> {
    // Validações adicionais de negócio

    // Lógica de atualização
    user_repository::update_user_profile(user_id, payload, conn)
        .map(UpdateUserProfileResponse::from)
        .map_err(|e| format!("Erro ao atualizar perfil: {}", e))
}

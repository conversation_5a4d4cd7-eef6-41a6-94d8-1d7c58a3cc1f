use crate::db::models::user::User;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Deserialize)]
pub struct UpdateUserProfileRequest {
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub phone: String,
    pub profile_picture: String,
}

#[derive(Debug, Serialize)]
pub struct UpdateUserProfileResponse {
    pub id: Uuid, 
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub phone: Option<String>,
    pub profile_picture: String,
    pub is_verified: bool,
}

impl From<User> for UpdateUserProfileResponse {
    fn from(user: User) -> Self {
        Self {
            id: user.id, 
            email: user.email,
            first_name: user.first_name,
            last_name: user.last_name,
            phone: user.phone,
            profile_picture: "".to_string(),
            is_verified: user.is_verified.unwrap_or(false),
        }
    }
}

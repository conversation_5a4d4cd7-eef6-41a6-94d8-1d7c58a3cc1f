use crate::db::models::user::{NewUser, User};
use crate::schema::users::dsl::users;
use crate::schema::users::{
    email, first_name, is_verified, last_name, password_hash, // dashboard_layout removido
    password_reset_expires, password_reset_token, verification_token,
    verification_token_expires,
};
use crate::user::user_dto::UpdateUserProfileRequest;
use chrono::NaiveDateTime;
use diesel::QueryDsl;
use diesel::{ExpressionMethods, RunQueryDsl};
use diesel::{PgConnection, QueryResult};
 // Para trabalhar com JSONB
use uuid::Uuid;

pub fn find_user_by_email(user_email: &str, conn: &mut PgConnection) -> QueryResult<User> {
    users.filter(email.eq(user_email)).first(conn)
}

pub fn find_user_by_id(user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<User> {
    users.find(user_id_val).first(conn)
}

pub fn find_user_by_reset_token(reset_token: &str, conn: &mut PgConnection) -> QueryResult<User> {
    users
        .filter(password_reset_token.eq(reset_token))
        .first(conn)
}

pub fn find_user_by_verification_token(
    verification_token_1: &str,
    conn: &mut PgConnection,
) -> QueryResult<User> {
    users
        .filter(verification_token.eq(verification_token_1))
        .first(conn)
}

pub fn save_user(new_user: &NewUser, conn: &mut PgConnection) -> QueryResult<User> {
    diesel::insert_into(users).values(new_user).get_result(conn)
}

pub fn update_password_reset_token(
    user_email: &str,
    reset_token: &str,
    _expires_at: NaiveDateTime,
    conn: &mut PgConnection,
) -> QueryResult<User> {
    diesel::update(users.filter(email.eq(user_email)))
        .set((
            password_reset_token.eq(reset_token),
            password_reset_expires.eq(_expires_at),
        ))
        .get_result(conn)
}

pub fn update_verification_token(
    user_id: Uuid,
    verification_token_val: &str,
    _expires_at: NaiveDateTime,
    conn: &mut PgConnection,
) -> QueryResult<User> {
    diesel::update(users.find(user_id))
        .set((
            verification_token.eq(verification_token_val),
            verification_token_expires.eq(_expires_at),
        ))
        .get_result(conn)
}

pub fn update_user_verification_status(
    user_id: Uuid,
    is_verified_1: bool,
    conn: &mut PgConnection,
) -> QueryResult<User> {
    diesel::update(users.find(user_id))
        .set(is_verified.eq(is_verified_1))
        .get_result(conn)
}

pub fn update_password(
    user_id: Uuid,
    new_password_hash: &str,
    conn: &mut PgConnection,
) -> QueryResult<User> {
    diesel::update(users.find(user_id))
        .set((
            password_hash.eq(new_password_hash),
            password_reset_token.eq::<Option<String>>(None),
            password_reset_expires.eq::<Option<NaiveDateTime>>(None),
        ))
        .get_result(conn)
}

pub fn update_user_profile(
    user_id: Uuid,
    payload: UpdateUserProfileRequest, // Você precisa definir este DTO
    conn: &mut PgConnection,
) -> QueryResult<User> {
    diesel::update(users.find(user_id))
        .set((
            first_name.eq(payload.first_name),
            last_name.eq(payload.last_name),
        ))
        .get_result(conn)
}

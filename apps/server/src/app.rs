use crate::auth::auth_repository;
use crate::config::database::{establish_connection_pool, DbPool};
use crate::email::email_service::EmailService;
use crate::routes::create_routes;
use hyper::server::conn::http1;
use hyper::service::service_fn;
use hyper_util::rt::TokioIo;
use std::net::SocketAddr;
use tokio::net::TcpListener;
use tower::ServiceExt;
use axum::{Extension, Router};

#[derive(Clone)]
pub struct AppState {
    pub pool: DbPool,
}

// Função pública que cria o Router. Será usada em `main.rs` e nos testes.
pub fn app() -> Router {
    dotenv::dotenv().ok();
    
    let pool = establish_connection_pool();
    let email_service = EmailService::new();

    let app_state = AppState {
        pool: pool.clone(),
    };

    // Realizar limpeza inicial de tokens expirados ao iniciar o servidor
    let pool_init_cleanup = pool.clone();
    tokio::spawn(async move {
        if let Ok(mut conn) = pool_init_cleanup.get() {
            match auth_repository::clean_expired_refresh_tokens(&mut conn) {
                Ok(count) => tracing::info!(
                    "Inicialização: {} tokens de atualização expirados foram removidos",
                    count
                ),
                Err(e) => tracing::error!(
                    "Erro ao limpar tokens expirados durante inicialização: {}",
                    e
                ),
            }
        } else {
            tracing::error!("Falha ao obter conexão de banco de dados para limpeza inicial");
        }
    });

    // Configurar limpeza periódica refresh tokens expirados 24h
    let pool_periodic = pool.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(86400)); // 24 horas

        loop {
            interval.tick().await;

            if let Ok(mut conn) = pool_periodic.get() {
                match auth_repository::clean_expired_refresh_tokens(&mut conn) {
                    Ok(count) => {
                        if count > 0 {
                            tracing::info!("Limpeza periódica: {} refresh tokens de atualização expirados foram removidos", count);
                        }
                    }
                    Err(e) => {
                        tracing::error!(
                            "Erro ao executar limpeza periódica de refrsh tokens: {}",
                            e
                        )
                    }
                }
            } else {
                tracing::error!("Falha ao obter conexão de banco de dados para limpeza periódica");
            }
        }
    });
    // limpeza de access tokens revogados expirados 24h
    let pool_revoked_tokens = pool.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(86400));

        loop {
            interval.tick().await;

            if let Ok(mut conn) = pool_revoked_tokens.get() {
                match auth_repository::clean_expired_revoked_tokens(&mut conn) {
                    Ok(count) => {
                        if count > 0 {
                            tracing::info!("Limpeza periódica: {} access tokens revogados expirados foram removidos", count);
                        }
                    }
                    Err(e) => {
                        tracing::error!(
                            "Erro ao executar limpeza periódica de access tokens revogados: {}",
                            e
                        )
                    }
                }
            }
        }
    });

    create_routes(app_state)
        .layer(Extension(email_service))
        .layer(Extension(pool))
}

pub async fn start_server() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    tracing_subscriber::fmt::init();

    // Tarefas de limpeza periódica
    let _pool = establish_connection_pool();
    // ... (código de limpeza de tokens movido para cá, se preferir separar do `app`)
    // Ou pode deixar em `app()` se não houver problema em rodar durante os testes.
    // Por simplicidade, vamos deixar em `app()` por agora.

    let app = app(); // Usamos a nova função

    let addr = SocketAddr::from(([127, 0, 0, 1], 3000));
    let listener = TcpListener::bind(addr).await?;
    tracing::info!("Servidor iniciado em http://{}", addr);

    loop {
        let (stream, _) = listener.accept().await?;
        let io = TokioIo::new(stream);
        let app = app.clone();

        tokio::spawn(async move {
            if let Err(err) = http1::Builder::new()
                .serve_connection(io, service_fn(move |req| app.clone().oneshot(req)))
                .await
            {
                tracing::error!("Erro ao atender conexão: {:?}", err);
            }
        });
    }
}

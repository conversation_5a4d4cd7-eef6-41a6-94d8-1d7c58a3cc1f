use aws_config::BehaviorVersion;
use aws_config::meta::region::RegionProviderChain;
use aws_sdk_s3::{
    config::{Credentials, Region},
    error::SdkError,
    operation::{
        delete_object::{DeleteObjectError, DeleteObjectOutput},
        get_object::{GetObjectError, GetObjectOutput},
        put_object::{PutObjectError, PutObjectOutput},
    },
    presigning::PresigningConfig,
    primitives::ByteStream,
    Client as S3Client,
};
use bytes::Bytes;
use std::env;
use std::time::Duration;
use tracing::{info, error, debug};

pub struct S3Storage {
    client: S3Client,
    bucket: String,
}

impl S3Storage {
    pub async fn new() -> Result<Self, String> {
        // Carregar a configuração da AWS a partir do ambiente
        let config = aws_config::defaults(BehaviorVersion::latest()).load().await;

        // Extrair a região do ambiente ou usar uma padrão
        let region = config.region().cloned().unwrap_or_else(|| Region::new("us-east-1"));

        // Carregar variáveis de ambiente
        let endpoint_url = env::var("MINIO_ENDPOINT_URL")
            .unwrap_or_else(|_| "http://localhost:9000".to_string());
        let access_key = env::var("MINIO_ACCESS_KEY")
            .unwrap_or_else(|_| "minioadmin".to_string());
        let secret_key = env::var("MINIO_SECRET_KEY")
            .unwrap_or_else(|_| "minioadmin".to_string());
        // Ler o nome do bucket da variável de ambiente
        let bucket = env::var("MINIO_BUCKET_NAME")
            .map_err(|_| "Variável de ambiente MINIO_BUCKET_NAME não definida".to_string())?;

        info!("Iniciando S3Storage com endpoint: {}, bucket: {}, região: {}", endpoint_url, bucket, region.to_string());

        // Configurar credenciais
        let credentials = Credentials::new(
            access_key,
            secret_key,
            None, // token de sessão (não necessário para MinIO)
            None, // data de expiração
            "minio-credentials",
        );

        // Configurar região
        let region_provider = RegionProviderChain::first_try(region);

        // Configurar cliente S3 base
        let config = aws_config::defaults(BehaviorVersion::latest())
            .region(region_provider)
            .credentials_provider(credentials)
            .endpoint_url(endpoint_url)
            .load()
            .await;

        // Criar config específica do S3 forçando path style
        let s3_config = aws_sdk_s3::config::Builder::from(&config)
            .force_path_style(true) // Forçar uso de path style para compatibilidade com MinIO
            .build();

        // Usar a config modificada para criar o cliente
        let client = S3Client::from_conf(s3_config);

        // Criar uma instância do S3Storage
        let s3_storage = Self { client, bucket: bucket.clone() };

        // Verificar se o bucket existe e criar se necessário
        if !s3_storage.bucket_exists().await {
            info!("Bucket '{}' não existe. Tentando criar...", bucket);
            match s3_storage.create_bucket().await {
                Ok(_) => info!("Bucket '{}' criado com sucesso", bucket),
                Err(e) => error!("Erro ao criar bucket '{}': {}", bucket, e)
            }
        } else {
            info!("Bucket '{}' já existe", bucket);
        }

        Ok(s3_storage)
    }

    // Upload de objeto para o bucket
    pub async fn upload_object(
        &self,
        key: &str,
        data: Bytes,
        content_type: &str,
    ) -> Result<PutObjectOutput, SdkError<PutObjectError>> {
        // Remover barras iniciais do key, se houver
        let clean_key = key.strip_prefix('/').unwrap_or(key);

        info!("Enviando objeto para o S3: key='{}', bucket='{}', content_type='{}'", clean_key, self.bucket, content_type);
        let body = ByteStream::from(data);

        // Verificar se o bucket existe
        let bucket_exists = self.bucket_exists().await;
        info!("Bucket '{}' existe? {}", self.bucket, bucket_exists);

        let result = self.client
            .put_object()
            .bucket(&self.bucket)
            .key(clean_key)
            .body(body)
            .content_type(content_type)
            .send()
            .await;

        match &result {
            Ok(_) => info!("Upload realizado com sucesso: {}", clean_key),
            Err(e) => error!("Erro ao fazer upload do objeto '{}': {:?}", clean_key, e),
        }
        result
    }

    // Download de objeto do bucket
    pub async fn get_object(&self, key: &str) -> Result<GetObjectOutput, SdkError<GetObjectError>> {
        info!("Baixando objeto do S3: key='{}'", key);
        let result = self.client
            .get_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await;
        match &result {
            Ok(_) => info!("Download realizado com sucesso: {}", key),
            Err(e) => error!("Erro ao baixar objeto '{}': {:?}", key, e),
        }
        result
    }

    // Gerar URL pré-assinada para download
    pub async fn get_presigned_url(&self, key: &str, expires_in: Duration) -> Result<String, String> {
        info!("Gerando URL pré-assinada para key='{}', expira em {:?}", key, expires_in);
        let presigning_config = PresigningConfig::expires_in(expires_in)
            .map_err(|e| format!("Erro ao configurar expiração da URL: {}", e))?;

        let presigned_req = self
            .client
            .get_object()
            .bucket(&self.bucket)
            .key(key)
            .presigned(presigning_config)
            .await
            .map_err(|e| format!("Erro ao gerar URL pré-assinada: {}", e))?;

        info!("URL pré-assinada gerada com sucesso para '{}': {}", key, presigned_req.uri());
        Ok(presigned_req.uri().to_string())
    }

    // Excluir objeto do bucket
    pub async fn delete_object(
        &self,
        key: &str,
    ) -> Result<DeleteObjectOutput, SdkError<DeleteObjectError>> {
        info!("Excluindo objeto do S3: key='{}'", key);
        let result = self.client
            .delete_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await;
        match &result {
            Ok(_) => info!("Objeto excluído com sucesso: {}", key),
            Err(e) => error!("Erro ao excluir objeto '{}': {:?}", key, e),
        }
        result
    }

    // Verificar se um objeto existe
    pub async fn object_exists(&self, key: &str) -> bool {
        debug!("Verificando existência do objeto no S3: key='{}'", key);
        match self.client
            .head_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await
        {
            Ok(_) => {
                debug!("Objeto existe: {}", key);
                true
            },
            Err(_) => {
                debug!("Objeto não encontrado: {}", key);
                false
            },
        }
    }

    // Verificar se o bucket existe
    pub async fn bucket_exists(&self) -> bool {
        info!("Verificando se o bucket '{}' existe", self.bucket);
        match self.client
            .head_bucket()
            .bucket(&self.bucket)
            .send()
            .await
        {
            Ok(_) => {
                info!("Bucket '{}' existe", self.bucket);
                true
            },
            Err(e) => {
                error!("Bucket '{}' não existe ou erro ao verificar: {:?}", self.bucket, e);
                false
            },
        }
    }

    // Criar um novo bucket
    pub async fn create_bucket(&self) -> Result<(), String> {
        info!("Criando bucket '{}'", self.bucket);

        // Obter a região atual
        let region = env::var("AWS_REGION").unwrap_or_else(|_| "us-east-1".to_string());

        let create_bucket_request = self.client
            .create_bucket()
            .bucket(&self.bucket);

        // Adicionar a configuração de região (exceto para us-east-1 que é o padrão)
        let create_bucket_request = if region != "us-east-1" {
            create_bucket_request.create_bucket_configuration(
                aws_sdk_s3::types::CreateBucketConfiguration::builder()
                    .location_constraint(aws_sdk_s3::types::BucketLocationConstraint::from(region.as_str()))
                    .build()
            )
        } else {
            create_bucket_request
        };

        match create_bucket_request.send().await {
            Ok(_) => {
                info!("Bucket '{}' criado com sucesso", self.bucket);
                Ok(())
            },
            Err(e) => {
                let error_msg = format!("Erro ao criar bucket '{}': {:?}", self.bucket, e);
                error!("{}", error_msg);
                Err(error_msg)
            },
        }
    }
}

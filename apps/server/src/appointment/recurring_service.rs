use crate::appointment::appointment_dto::CreateAppointmentRequest;
use crate::db::models::appointment::NewAppointment;
use chrono::{Datelike, Duration, NaiveDate, NaiveDateTime, Utc};
use std::fmt::Display;
use uuid::Uuid;

#[derive(Debu<PERSON>, <PERSON>lone, PartialEq)]
pub enum RecurrencePattern {
    Weekly,
    Biweekly,
    Monthly,
    Custom(i32), 
}

impl From<&str> for RecurrencePattern {
    fn from(value: &str) -> Self {
        match value.to_lowercase().as_str() {
            "weekly" => RecurrencePattern::Weekly,
            "biweekly" => RecurrencePattern::Biweekly,
            "monthly" => RecurrencePattern::Monthly,
            custom if custom.starts_with("custom_") => {
                let parts: Vec<&str> = custom.split('_').collect();
                if parts.len() == 2 {
                    if let Ok(days) = parts[1].parse::<i32>() {
                        if days > 0 {
                            return RecurrencePattern::Custom(days);
                        }
                    }
                }
                
                RecurrencePattern::Weekly
            }
            _ => RecurrencePattern::Weekly,
        }
    }
}

impl Display for RecurrencePattern {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let str = match self {
            RecurrencePattern::Weekly => "weekly".to_string(),
            RecurrencePattern::Biweekly => "biweekly".to_string(),
            RecurrencePattern::Monthly => "monthly".to_string(),
            RecurrencePattern::Custom(days) => format!("custom_{}", days),
        };
        write!(f, "{}", str)
    }
}


const MAX_RECURRENCE_OCCURRENCES: usize = 52;

pub fn generate_recurring_appointments(
    user_id: Uuid,
    request: &CreateAppointmentRequest,
) -> Result<Vec<NewAppointment>, String> {
    
    if !request.is_recurrent.unwrap_or(false) {
        return Err("A consulta não é recorrente".to_string());
    }

    
    let pattern_str = request
        .recurrence_pattern
        .as_ref()
        .ok_or_else(|| "Padrão de recorrência não especificado".to_string())?;

    let pattern = RecurrencePattern::from(pattern_str.as_str());

    
    let start_time = request.start_time.naive_utc();
    let end_time = request.end_time.naive_utc();

    if start_time >= end_time {
        return Err("Horário de início deve ser anterior ao horário de término".to_string());
    }

    
    let series_id = Uuid::new_v4().to_string();

    
    let mut occurrences = MAX_RECURRENCE_OCCURRENCES; // Limite máximo padrão

    if let Some(count) = request.occurrences {
        if count == 0 {
            return Err("O número de ocorrências deve ser positivo".to_string());
        }
        occurrences = count as usize;
    } else if let Some(recurrence_end_date) = request.recurrence_end_date {
        let end_date = recurrence_end_date.naive_utc();
        if end_date <= start_time {
            return Err("A data final deve ser posterior à data inicial".to_string());
        }

        
        let mut current_date = start_time;
        let mut count = 0;

        while current_date <= end_date && count < MAX_RECURRENCE_OCCURRENCES {
            count += 1;

            current_date = match pattern {
                RecurrencePattern::Weekly => current_date + Duration::weeks(1),
                RecurrencePattern::Biweekly => current_date + Duration::weeks(2),
                RecurrencePattern::Monthly => add_months(current_date, 1),
                RecurrencePattern::Custom(days) => current_date + Duration::days(days as i64),
            };
        }

        occurrences = count;
    }

    
    if occurrences > MAX_RECURRENCE_OCCURRENCES {
        occurrences = MAX_RECURRENCE_OCCURRENCES;
    }

    
    let mut appointments: Vec<NewAppointment> = Vec::with_capacity(occurrences);

    for i in 0..occurrences {
        let current_start_time;
        let current_end_time;

        if i == 0 {
            
            current_start_time = start_time;
            current_end_time = end_time;
        } else {
            // Calcular os horários das consultas subsequentes
            let previous_start = appointments[i - 1].start_time;
            let previous_end = appointments[i - 1].end_time;
            let duration = previous_end - previous_start;

            current_start_time = match pattern {
                RecurrencePattern::Weekly => previous_start + Duration::weeks(1),
                RecurrencePattern::Biweekly => previous_start + Duration::weeks(2),
                RecurrencePattern::Monthly => add_months(previous_start, 1),
                RecurrencePattern::Custom(days) => previous_start + Duration::days(days as i64),
            };

            current_end_time = current_start_time + duration;
        }

        // Marcar a primeira consulta como principal
        let is_main_appointment = i == 0;

        let appointment = NewAppointment {
            user_id,
            patient_id: request.patient_id,
            title: request.title.clone(),
            notes: request.notes.clone(),
            start_time: current_start_time,
            end_time: current_end_time,
            status: request
                .status
                .clone()
                .unwrap_or_else(|| "scheduled".to_string()),
            type_: request
                .type_
                .clone()
                .unwrap_or_else(|| "consultation".to_string()),
            color: request.color.clone(),
            location: request.location.clone(),
            series_id: Some(series_id.clone()),
            // Todas as consultas têm o mesmo valor de is_recurring
            is_recurring: Some(true),
            // Apenas a primeira consulta mantém os metadados de recorrência
            recurrence_pattern: if is_main_appointment {
                request.recurrence_pattern.clone()
            } else {
                None
            },
            recurrence_end_date: if is_main_appointment {
                request.recurrence_end_date.map(|dt| dt.naive_utc())
            } else {
                None
            },
            recurrence_count: if is_main_appointment {
                request.occurrences.map(|o| o as i32)
            } else {
                None
            },
        };

        appointments.push(appointment);
    }

    Ok(appointments)
}

// Função aprimorada para adicionar meses a uma data
fn add_months(date: NaiveDateTime, months: u32) -> NaiveDateTime {
    let mut year = date.year();
    let mut month = date.month() + months;

    // Ajustar o ano se o mês ultrapassar dezembro
    while month > 12 {
        year += 1;
        month -= 12;
    }

    // Obter o dia atual
    let day = date.day();

    // Calcular o último dia do mês de destino
    let last_day_of_month = get_last_day_of_month(year, month);

    // Ajustar o dia se necessário
    let new_day = std::cmp::min(day, last_day_of_month);

    // Tentar criar a nova data
    if let Some(new_date) = NaiveDate::from_ymd_opt(year, month, new_day) {
        // Preservar o horário original
        NaiveDateTime::new(new_date, date.time())
    } else {
        // Fallback para o último dia do mês anterior se a data for inválida
        let (prev_year, prev_month) = if month == 1 {
            (year - 1, 12)
        } else {
            (year, month - 1)
        };

        let last_day = get_last_day_of_month(prev_year, prev_month);

        if let Some(fallback_date) = NaiveDate::from_ymd_opt(prev_year, prev_month, last_day) {
            NaiveDateTime::new(fallback_date, date.time())
        } else {
            // Último recurso - usar a data original
            date
        }
    }
}

// Função auxiliar para obter o último dia do mês
fn get_last_day_of_month(year: i32, month: u32) -> u32 {
    match month {
        1 | 3 | 5 | 7 | 8 | 10 | 12 => 31,
        4 | 6 | 9 | 11 => 30,
        2 => {
            // Verificar se é ano bissexto
            if (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0) {
                29
            } else {
                28
            }
        }
        _ => {
            // Valor impossível - retornar um valor seguro
            30
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::{NaiveTime, Timelike};

    #[test]
    fn test_add_months_normal_case() {
        let date = NaiveDateTime::new(
            NaiveDate::from_ymd_opt(2025, 1, 15).unwrap(),
            NaiveTime::from_hms_opt(10, 30, 0).unwrap(),
        );

        let result = add_months(date, 1);

        assert_eq!(result.year(), 2025);
        assert_eq!(result.month(), 2);
        assert_eq!(result.day(), 15);
        assert_eq!(result.hour(), 10);
        assert_eq!(result.minute(), 30);
    }

    #[test]
    fn test_add_months_year_boundary() {
        let date = NaiveDateTime::new(
            NaiveDate::from_ymd_opt(2025, 12, 15).unwrap(),
            NaiveTime::from_hms_opt(10, 30, 0).unwrap(),
        );

        let result = add_months(date, 1);

        assert_eq!(result.year(), 2026);
        assert_eq!(result.month(), 1);
        assert_eq!(result.day(), 15);
    }

    #[test]
    fn test_add_months_day_adjustment() {
        // 31 de janeiro + 1 mês = 28/29 de fevereiro
        let date = NaiveDateTime::new(
            NaiveDate::from_ymd_opt(2025, 1, 31).unwrap(),
            NaiveTime::from_hms_opt(10, 30, 0).unwrap(),
        );

        let result = add_months(date, 1);

        assert_eq!(result.year(), 2025);
        assert_eq!(result.month(), 2);
        // 2025 não é bissexto, então fevereiro tem 28 dias
        assert_eq!(result.day(), 28);
    }

    #[test]
    fn test_recurrence_pattern_from_string() {
        assert_eq!(RecurrencePattern::from("weekly"), RecurrencePattern::Weekly);
        assert_eq!(RecurrencePattern::from("WEEKLY"), RecurrencePattern::Weekly);
        assert_eq!(
            RecurrencePattern::from("biweekly"),
            RecurrencePattern::Biweekly
        );
        assert_eq!(
            RecurrencePattern::from("monthly"),
            RecurrencePattern::Monthly
        );
        assert_eq!(
            RecurrencePattern::from("custom_14"),
            RecurrencePattern::Custom(14)
        );

        // Casos inválidos devem retornar o padrão Weekly
        assert_eq!(
            RecurrencePattern::from("invalid"),
            RecurrencePattern::Weekly
        );
        assert_eq!(
            RecurrencePattern::from("custom_-1"),
            RecurrencePattern::Weekly
        );
        assert_eq!(
            RecurrencePattern::from("custom_abc"),
            RecurrencePattern::Weekly
        );
    }
}

use chrono::{DateTime, NaiveDate, NaiveDateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::Validate;

#[derive(Debug, Serialize)]
pub struct AppointmentResponse {
    pub id: Uuid,
    pub user_id: Uuid,
    pub patient_id: Uuid,
    pub patient_name: Option<String>,
    pub title: String,
    pub notes: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub duration_minutes: i64,
    pub status: String,
    #[serde(rename = "type")]
    pub type_: String,
    pub color: Option<String>,
    pub location: Option<String>,
    pub series_id: Option<String>,
    pub is_recurring: bool,
    pub recurrence_pattern: Option<String>,
    pub recurrence_end_date: Option<DateTime<Utc>>,
    pub recurrence_count: Option<i32>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateAppointmentRequest {
    #[serde(skip)]
    pub user_id: Option<Uuid>,
    pub patient_id: Uuid,
    #[validate(length(min = 1))]
    pub title: String,
    pub notes: Option<String>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub status: Option<String>,
    #[serde(rename = "type")]
    pub type_: Option<String>,
    pub color: Option<String>,
    pub location: Option<String>,
    pub is_recurrent: Option<bool>,
    pub recurrence_pattern: Option<String>,
    pub recurrence_end_date: Option<DateTime<Utc>>,
    pub occurrences: Option<u32>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateAppointmentRequest {
    #[serde(skip)]
    pub user_id: Option<Uuid>,
    pub patient_id: Option<Uuid>,
    #[validate(length(min = 1))]
    pub title: Option<String>,
    // pub notes: Option<String>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    // pub status: Option<String>,
    // #[serde(rename = "type")]
    // pub type_: Option<String>,
    // pub color: Option<String>,
    // pub location: Option<String>,
    // pub update_mode: Option<String>, // 'single', 'future', 'all'
}

#[derive(Debug, Deserialize, Validate)]
pub struct DeleteAppointmentQuery {
    pub delete_mode: Option<String>, // 'single', 'future', 'all'
    pub date: Option<NaiveDate>,     // Para 'future' ou 'single' em uma data específica
}

#[derive(Debug, Deserialize)]
pub struct GetAppointmentsQuery {
    pub start_date: Option<String>,
    pub end_date: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct GetAvailableSlotsQuery {
    pub date: NaiveDate,
    #[validate(range(min = 1, max = 90))]
    pub days: i64,
    #[validate(range(min = 1, max = 100))]
    pub limit: Option<i64>,
}

#[derive(Debug, Serialize)]
pub struct AvailableSlot {
    pub start: NaiveDateTime,
    pub end: NaiveDateTime,
}

#[derive(Debug, Serialize)]
pub struct AppointmentSummary {
    pub today_count: i64,
    pub upcoming_count: i64,
    pub upcoming_list: Vec<AppointmentResponse>,
}

#[derive(Debug, Serialize)]
pub struct RecurringSeriesResponse {
    pub series_id: String,
    pub pattern: String,
    pub total_count: i32,
    pub appointments: Vec<AppointmentResponse>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateRecurringAppointmentRequest {
    #[serde(skip)]
    pub user_id: Option<Uuid>,
    pub patient_id: Option<Uuid>,
    #[validate(length(min = 1))]
    pub title: Option<String>,
    pub notes: Option<String>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub status: Option<String>,
    #[serde(rename = "type")]
    pub type_: Option<String>,
    pub color: Option<String>,
    pub location: Option<String>,
    #[validate(length(min = 1))]
    pub update_mode: String, // 'single', 'future', 'all'
}

use crate::{
    appointment::{
        appointment_dto::{
            AppointmentResponse, AvailableSlot, CreateAppointmentRequest, UpdateAppointmentRequest, UpdateRecurringAppointmentRequest,
        },
        appointment_repository, recurring_service,
    },
    config::database::DbPool,
    db::models::appointment::NewAppointment,
    errors::{ApiError, ApiResult},
    financial::financial_service,
    patient::patient_repository,
};
use chrono::{Duration, NaiveDate, NaiveDateTime, Utc};
use diesel::{Connection, PgConnection};
use std::error::Error;
use uuid::Uuid;
use validator::ValidationErrors;

async fn to_appointment_response(
    conn: &mut PgConnection,
    appointment: crate::db::models::appointment::Appointment,
) -> AppointmentResponse {
    let patient_name =
        patient_repository::find_patient_by_id(appointment.patient_id, appointment.user_id, conn)
            .ok()
            .map(|p| p.full_name);

    let mut response: AppointmentResponse = appointment.into();
    response.patient_name = patient_name;
    response
}

pub async fn get_appointments(
    pool: &DbPool,
    user_id: Uuid,
    start_date: Option<NaiveDate>,
    end_date: Option<NaiveDate>,
) -> ApiResult<Vec<AppointmentResponse>> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    let start_time = start_date.map(|d| d.and_hms_opt(0, 0, 0).unwrap());
    let end_time = end_date.map(|d| d.and_hms_opt(23, 59, 59).unwrap());

    let appointments = appointment_repository::find_appointments_filtered(
        user_id, None, None, start_time, end_time, &mut conn,
    )?;

    let mut responses = Vec::new();
    for app in appointments {
        responses.push(to_appointment_response(&mut conn, app).await);
    }
    Ok(responses)
}

pub async fn get_appointment_by_id(
    pool: &DbPool,
    user_id: Uuid,
    appointment_id: Uuid,
) -> ApiResult<AppointmentResponse> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    let appointment =
        appointment_repository::find_appointment_by_id(appointment_id, user_id, &mut conn)?;
    Ok(to_appointment_response(&mut conn, appointment).await)
}

pub async fn create_appointment(
    pool: &DbPool,
    request: CreateAppointmentRequest,
) -> ApiResult<serde_json::Value> {
    let start_time = request.start_time.naive_utc();
    let end_time = request.end_time.naive_utc();

    if start_time >= end_time {
        let mut errors = ValidationErrors::new();
        let mut error = validator::ValidationError::new("invalid_time_range");
        error.message = Some("Horário de início deve ser anterior ao horário de término".into());
        errors.add("time", error);
        return Err(ApiError::ValidationError(errors));
    }

    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    let user_id = request.user_id.unwrap(); // O handler já deve ter inserido

    // Verifica se o paciente existe e pertence ao usuário
    if let Err(_) = patient_repository::find_patient_by_id(request.patient_id, user_id, &mut conn) {
        return Err(ApiError::ResourceNotFound(
            "Paciente não encontrado ou não pertence ao usuário.".to_string(),
        ));
    }

    let appointments = if request.is_recurrent.unwrap_or(false) {
        recurring_service::generate_recurring_appointments(user_id, &request)?
    } else {
        vec![NewAppointment {
            user_id,
            patient_id: request.patient_id,
            title: request.title,
            notes: request.notes,
            start_time,
            end_time,
            status: request.status.unwrap_or_else(|| "scheduled".to_string()),
            type_: request.type_.unwrap_or_else(|| "consultation".to_string()),
            color: request.color,
            location: request.location,
            series_id: None,
            is_recurring: Some(false),
            recurrence_pattern: None,
            recurrence_end_date: None,
            recurrence_count: None,
        }]
    };

    let saved = conn
        .transaction::<_, Box<dyn Error>, _>(|transaction_conn| {
            appointment_repository::create_recurring_appointments(&appointments, transaction_conn)
                .map_err(|e| e.into())
        })
        .map_err(|e| ApiError::DatabaseError(e.to_string()))?;

    if let Some(first_appointment) = saved.into_iter().next() {
        let response = to_appointment_response(&mut conn, first_appointment).await;
        let json_response = serde_json::to_value(response)
            .map_err(|e| ApiError::InternalServerError(e.to_string()))?;
        Ok(json_response)
    } else {
        Err(ApiError::InternalServerError(
            "Falha ao criar agendamento e recuperar seus dados.".to_string(),
        ))
    }
}

pub async fn update_appointment(
    pool: &DbPool,
    appointment_id: Uuid,
    request: UpdateAppointmentRequest,
) -> ApiResult<serde_json::Value> {
    if let (Some(start), Some(end)) = (request.start_time, request.end_time) {
        if start >= end {
            let mut errors = ValidationErrors::new();
            let mut error = validator::ValidationError::new("invalid_time_range");
            error.message = Some("Horário de início deve ser anterior ao horário de término".into());
            errors.add("time", error);
            return Err(ApiError::ValidationError(errors));
        }
    }

    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    let user_id = request.user_id.unwrap();

    let result = conn
        .transaction::<_, Box<dyn Error>, _>(|transaction_conn| {
            // ... lógica de atualização para 'single', 'future', 'all' ...
            // Esta parte precisa de uma implementação mais detalhada
            // Por enquanto, vamos implementar a atualização 'single'
            let mut appointment =
                appointment_repository::find_appointment_by_id(appointment_id, user_id, transaction_conn)?;

            if let Some(patient_id) = request.patient_id {
                appointment.patient_id = patient_id;
            }
            if let Some(title) = request.title {
                appointment.title = title;
            }
            //... etc para outros campos
            let updated = appointment_repository::update_appointment(
                appointment_id,
                user_id,
                &appointment,
                transaction_conn,
            )?;

             if updated.status == "completed" {
                financial_service::create_transaction_from_appointment(
                    transaction_conn,
                    user_id,
                    &updated,
                )?;
            }

            Ok(serde_json::json!({ "updated": updated }))
        })
        .map_err(|e| ApiError::DatabaseError(e.to_string()))?;

    Ok(result)
}

pub async fn update_recurring_appointment(
    pool: &DbPool,
    appointment_id: Uuid,
    request: UpdateRecurringAppointmentRequest,
) -> ApiResult<serde_json::Value> {
    // Validar se horário de início é anterior ao de término (se ambos forem fornecidos)
    if let (Some(start), Some(end)) = (request.start_time, request.end_time.clone()) {
        if start >= end {
            let mut errors = ValidationErrors::new();
            let mut error = validator::ValidationError::new("invalid_time_range");
            error.message = Some("Horário de início deve ser anterior ao horário de término".into());
            errors.add("time", error);
            return Err(ApiError::ValidationError(errors));
        }
    }

    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    let user_id = request.user_id.unwrap();

    // Primeiro obter o agendamento para verificar se ele pertence a uma série
    let current_appointment = appointment_repository::find_appointment_by_id(
        appointment_id, 
        user_id, 
        &mut conn
    )?;

    // Se não for parte de uma série recorrente, retornar erro
    if current_appointment.series_id.is_none() || !current_appointment.is_recurring.unwrap_or(false) {
        return Err(ApiError::BadRequest(
            "Este agendamento não faz parte de uma série recorrente.".to_string(),
        ));
    }

    let series_id = current_appointment.series_id.as_ref().unwrap().clone();
    let update_mode = request.update_mode.as_str();

    let result = conn.transaction::<_, Box<dyn Error>, _>(|transaction_conn| {
        match update_mode {
            "single" => {
                // Lógica atual: atualiza apenas a instância específica
                let mut appointment = current_appointment.clone();
                
                // Aplicar as atualizações do request
                if let Some(patient_id) = request.patient_id {
                    appointment.patient_id = patient_id;
                }
                if let Some(title) = request.title {
                    appointment.title = title;
                }
                if let Some(notes) = request.notes {
                    appointment.notes = Some(notes);
                }
                if let Some(start_time) = request.start_time {
                    appointment.start_time = start_time.naive_utc();
                }
                if let Some(end_time) = request.end_time {
                    appointment.end_time = end_time.naive_utc();
                }
                if let Some(status) = request.status {
                    appointment.status = status;
                }
                if let Some(type_) = request.type_ {
                    appointment.type_ = type_;
                }
                if let Some(color) = request.color {
                    appointment.color = Some(color);
                }
                if let Some(location) = request.location {
                    appointment.location = Some(location);
                }

                let updated = appointment_repository::update_appointment(
                    appointment_id,
                    user_id,
                    &appointment,
                    transaction_conn,
                )?;

                if updated.status == "completed" {
                    financial_service::create_transaction_from_appointment(
                        transaction_conn,
                        user_id,
                        &updated,
                    )?;
                }

                Ok(serde_json::json!({ "updated": "single", "appointment": updated }))
            }
            "future" => {
                // Atualiza a instância atual e todas as futuras da mesma série
                let current_time = current_appointment.start_time;
                
                // Buscar todos os agendamentos futuros da mesma série
                let future_appointments = appointment_repository::find_future_appointments_by_series(
                    user_id,
                    &series_id,
                    current_time,
                    transaction_conn,
                )?;
                
                let mut updated_count = 0;
                
                // Atualizar cada agendamento futuro
                for mut appointment in future_appointments {
                    // Aplicar as mesmas atualizações a cada agendamento
                    if let Some(patient_id) = request.patient_id {
                        appointment.patient_id = patient_id;
                    }
                    if let Some(ref title) = request.title {
                        appointment.title = title.clone();
                    }
                    if let Some(ref notes) = request.notes {
                        appointment.notes = Some(notes.clone());
                    }
                    
                    // Para start_time e end_time, preservar a diferença de dias entre os agendamentos
                    // mas aplicar o novo horário
                    if let Some(start_time) = request.start_time {
                        let start_naive = start_time.naive_utc();
                        
                        // Calcular a diferença em dias entre o agendamento atual e o original
                        let original_start = current_appointment.start_time;
                        let days_diff = (appointment.start_time.date() - original_start.date()).num_days();
                        
                        // Criar uma nova data mantendo a diferença de dias mas com o novo horário
                        let new_date = start_naive.date().succ_opt().unwrap().pred_opt().unwrap() + chrono::Duration::days(days_diff);
                        let new_time = start_naive.time();
                        appointment.start_time = NaiveDateTime::new(new_date, new_time);
                        
                        // Calcular a duração do agendamento
                        if let Some(end_time) = request.end_time {
                            let duration = end_time.naive_utc() - start_naive;
                            appointment.end_time = appointment.start_time + duration;
                        }
                    } else if let Some(end_time) = request.end_time {
                        // Se apenas end_time foi fornecido, ajustar apenas o fim mantendo a mesma duração
                        let old_duration = current_appointment.end_time - current_appointment.start_time;
                        let new_duration = end_time.naive_utc() - current_appointment.start_time;
                        let duration_diff = new_duration - old_duration;
                        
                        appointment.end_time = appointment.end_time + duration_diff;
                    }
                    
                    if let Some(ref status) = request.status {
                        appointment.status = status.clone();
                    }
                    if let Some(ref type_) = request.type_ {
                        appointment.type_ = type_.clone();
                    }
                    if let Some(ref color) = request.color {
                        appointment.color = Some(color.clone());
                    }
                    if let Some(ref location) = request.location {
                        appointment.location = Some(location.clone());
                    }
                    
                    // Salvar as alterações
                    appointment_repository::update_appointment(
                        appointment.id,
                        user_id,
                        &appointment,
                        transaction_conn,
                    )?;
                    
                    updated_count += 1;
                    
                    if appointment.status == "completed" {
                        financial_service::create_transaction_from_appointment(
                            transaction_conn,
                            user_id,
                            &appointment,
                        )?;
                    }
                }
                
                Ok(serde_json::json!({ "updated": "future", "count": updated_count }))
            }
            "all" => {
                // Atualiza todos os agendamentos da série
                let all_appointments = appointment_repository::find_appointments_by_series(
                    user_id,
                    &series_id,
                    transaction_conn,
                )?;
                
                let mut updated_count = 0;
                
                // Atualizar cada agendamento da série
                for mut appointment in all_appointments {
                    // Aplicar as mesmas atualizações a cada agendamento
                    if let Some(patient_id) = request.patient_id {
                        appointment.patient_id = patient_id;
                    }
                    if let Some(ref title) = request.title {
                        appointment.title = title.clone();
                    }
                    if let Some(ref notes) = request.notes {
                        appointment.notes = Some(notes.clone());
                    }
                    
                    // Para start_time e end_time, preservar a diferença de dias entre os agendamentos
                    // mas aplicar o novo horário
                    if let Some(start_time) = request.start_time {
                        let start_naive = start_time.naive_utc();
                        
                        // Calcular a diferença em dias entre o agendamento atual e o original
                        let original_start = current_appointment.start_time;
                        let days_diff = (appointment.start_time.date() - original_start.date()).num_days();
                        
                        // Criar uma nova data mantendo a diferença de dias mas com o novo horário
                        let new_date = start_naive.date().succ_opt().unwrap().pred_opt().unwrap() + chrono::Duration::days(days_diff);
                        let new_time = start_naive.time();
                        appointment.start_time = NaiveDateTime::new(new_date, new_time);
                        
                        // Calcular a duração do agendamento
                        if let Some(end_time) = request.end_time {
                            let duration = end_time.naive_utc() - start_naive;
                            appointment.end_time = appointment.start_time + duration;
                        }
                    } else if let Some(end_time) = request.end_time {
                        // Se apenas end_time foi fornecido, ajustar apenas o fim mantendo a mesma duração
                        let old_duration = current_appointment.end_time - current_appointment.start_time;
                        let new_duration = end_time.naive_utc() - current_appointment.start_time;
                        let duration_diff = new_duration - old_duration;
                        
                        appointment.end_time = appointment.end_time + duration_diff;
                    }
                    
                    if let Some(ref status) = request.status {
                        appointment.status = status.clone();
                    }
                    if let Some(ref type_) = request.type_ {
                        appointment.type_ = type_.clone();
                    }
                    if let Some(ref color) = request.color {
                        appointment.color = Some(color.clone());
                    }
                    if let Some(ref location) = request.location {
                        appointment.location = Some(location.clone());
                    }
                    
                    // Salvar as alterações
                    appointment_repository::update_appointment(
                        appointment.id,
                        user_id,
                        &appointment,
                        transaction_conn,
                    )?;
                    
                    updated_count += 1;
                    
                    if appointment.status == "completed" {
                        financial_service::create_transaction_from_appointment(
                            transaction_conn,
                            user_id,
                            &appointment,
                        )?;
                    }
                }
                
                Ok(serde_json::json!({ "updated": "all", "count": updated_count }))
            }
            _ => Err("Modo de atualização inválido".into()),
        }
    })
    .map_err(|e| ApiError::DatabaseError(e.to_string()))?;

    Ok(result)
}

pub async fn get_appointments_by_patient(
    pool: &DbPool,
    user_id: Uuid,
    patient_id: Uuid,
) -> ApiResult<Vec<AppointmentResponse>> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    let appointments =
        appointment_repository::find_appointments_by_patient(user_id, patient_id, &mut conn)?;

    let mut responses = Vec::new();
    for app in appointments {
        responses.push(to_appointment_response(&mut conn, app).await);
    }
    Ok(responses)
}

pub async fn delete_appointment(
    pool: &DbPool,
    user_id: Uuid,
    appointment_id: Uuid,
    _delete_mode: Option<String>,
    _date: Option<NaiveDate>,
) -> ApiResult<()> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;

    conn.transaction::<_, Box<dyn Error>, _>(|transaction_conn| {
        // ... lógica para deletar 'single', 'future', 'all' ...
        // Por enquanto, vamos implementar a deleção 'single'
        let deleted_count =
            appointment_repository::delete_appointment(appointment_id, user_id, transaction_conn)?;
        if deleted_count == 0 {
            return Err("Agendamento não encontrado".into());
        }
        Ok(())
    })
    .map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    Ok(())
}

pub async fn get_available_slots(
    pool: &DbPool,
    user_id: Uuid,
    date: NaiveDate,
    days: i64,
    limit: Option<i64>,
) -> ApiResult<Vec<AvailableSlot>> {
    let mut conn = pool.get().map_err(|e| ApiError::DatabaseError(e.to_string()))?;
    let start_time = date.and_hms_opt(0, 0, 0).unwrap();
    let end_time = date.and_hms_opt(23, 59, 59).unwrap() + Duration::days(days - 1);

    let appointments = appointment_repository::find_appointments_in_range(user_id, start_time, end_time, &mut conn)?;

    // Lógica para encontrar slots vagos (simplificada)
    let mut slots = Vec::new();
    let mut current_time = start_time;
    let end_of_day = date.and_hms_opt(22, 0, 0).unwrap();

    while current_time < end_of_day {
        let potential_slot_end = current_time + Duration::minutes(60);
        let has_conflict = appointments.iter().any(|a| {
            (a.start_time < potential_slot_end) && (a.end_time > current_time)
        });

        if !has_conflict {
            slots.push(AvailableSlot { start: current_time, end: potential_slot_end });
        }

        current_time = potential_slot_end;
        if let Some(l) = limit {
            if slots.len() >= l as usize {
                break;
            }
        }
    }
    
    Ok(slots)
}

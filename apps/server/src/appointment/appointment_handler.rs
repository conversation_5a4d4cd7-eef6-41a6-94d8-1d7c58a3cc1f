use crate::{
    app::AppState,
    appointment::{appointment_dto::*, appointment_service},
    auth::auth_middleware::AuthUser,
    errors::{ApiError, ApiResult},
};
use axum::{
    extract::{Extension, Path, Query, State},
    http::StatusCode,
    Json,
};
use chrono::{NaiveDate, NaiveDateTime};
use serde_json::Value;
use uuid::Uuid;
use validator::{Validate, ValidationErrors};

fn parse_date(date_str: &Option<String>) -> Result<Option<NaiveDate>, ApiError> {
    match date_str {
        Some(s) if !s.is_empty() => {
            NaiveDate::parse_from_str(s, "%Y-%m-%d")
                .or_else(|_| NaiveDateTime::parse_from_str(s, "%Y-%m-%dT%H:%M:%S%.fZ").map(|dt| dt.date()))
                .map(Some)
                .map_err(|_| {
                    let mut errors = ValidationErrors::new();
                    let mut error = validator::ValidationError::new("invalid_date_format");
                    error.message = Some("Formato de data inválido. Use YYYY-MM-DD.".into());
                    errors.add("date", error);
                    ApiError::ValidationError(errors)
                })
        }
        _ => Ok(None),
    }
}

pub async fn get_appointments_handler(
    State(state): State<AppState>,
    Extension(user): Extension<AuthUser>,
    Query(params): Query<GetAppointmentsQuery>,
) -> ApiResult<Json<Vec<AppointmentResponse>>> {
    let start_date = parse_date(&params.start_date)?;
    let end_date = parse_date(&params.end_date)?;
    let appointments =
        appointment_service::get_appointments(&state.pool, user.0, start_date, end_date).await?;
    Ok(Json(appointments))
}

pub async fn get_appointment_by_id_handler(
    State(state): State<AppState>,
    Extension(user): Extension<AuthUser>,
    Path(appointment_id): Path<Uuid>,
) -> ApiResult<Json<AppointmentResponse>> {
    let appointment =
        appointment_service::get_appointment_by_id(&state.pool, user.0, appointment_id).await?;
    Ok(Json(appointment))
}

pub async fn create_appointment_handler(
    State(state): State<AppState>,
    Extension(user): Extension<AuthUser>,
    Json(mut request): Json<CreateAppointmentRequest>,
) -> ApiResult<Json<Value>> {
    request.validate()?;
    request.user_id = Some(user.0);
    let response = appointment_service::create_appointment(&state.pool, request).await?;
    Ok(Json(response))
}

pub async fn update_appointment_handler(
    State(state): State<AppState>,
    Extension(user): Extension<AuthUser>,
    Path(appointment_id): Path<Uuid>,
    Json(mut request): Json<UpdateAppointmentRequest>,
) -> ApiResult<Json<Value>> {
    request.validate()?;
    request.user_id = Some(user.0);
    let response =
        appointment_service::update_appointment(&state.pool, appointment_id, request).await?;
    Ok(Json(response))
}

pub async fn delete_appointment_handler(
    State(state): State<AppState>,
    Extension(user): Extension<AuthUser>,
    Path(appointment_id): Path<Uuid>,
    Query(params): Query<DeleteAppointmentQuery>,
) -> ApiResult<StatusCode> {
    params.validate()?;
    appointment_service::delete_appointment(
        &state.pool,
        user.0,
        appointment_id,
        params.delete_mode,
        params.date,
    )
    .await?;
    Ok(StatusCode::NO_CONTENT)
}

pub async fn update_recurring_appointment_handler(
    State(state): State<AppState>,
    Extension(user): Extension<AuthUser>,
    Path(appointment_id): Path<Uuid>,
    Json(mut request): Json<UpdateRecurringAppointmentRequest>,
) -> ApiResult<Json<Value>> {
    request.validate()?;
    request.user_id = Some(user.0);
    
    // Verificando se o update_mode é válido
    match request.update_mode.as_str() {
        "single" | "future" | "all" => {},
        _ => {
            let mut errors = ValidationErrors::new();
            let mut error = validator::ValidationError::new("invalid_update_mode");
            error.message = Some("O modo de atualização deve ser 'single', 'future' ou 'all'.".into());
            errors.add("update_mode", error);
            return Err(ApiError::ValidationError(errors));
        }
    }
    
    let response = appointment_service::update_recurring_appointment(
        &state.pool, 
        appointment_id, 
        request
    ).await?;
    
    Ok(Json(response))
}

pub async fn get_available_slots_handler(
    State(state): State<AppState>,
    Extension(user): Extension<AuthUser>,
    Query(params): Query<GetAvailableSlotsQuery>,
) -> ApiResult<Json<Vec<AvailableSlot>>> {
    params.validate()?;
    let slots = appointment_service::get_available_slots(
        &state.pool,
        user.0,
        params.date,
        params.days,
        params.limit,
    )
    .await?;
    Ok(Json(slots))
}

pub async fn get_appointments_by_patient_handler(
    State(state): State<AppState>,
    Extension(user): Extension<AuthUser>,
    Path(patient_id): Path<Uuid>,
) -> ApiResult<Json<Vec<AppointmentResponse>>> {
    let appointments =
        appointment_service::get_appointments_by_patient(&state.pool, user.0, patient_id).await?;
    Ok(Json(appointments))
}
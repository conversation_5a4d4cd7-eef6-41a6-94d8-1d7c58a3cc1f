use crate::db::models::appointment::{Appointment, NewAppointment};
use crate::schema::appointments::dsl::*;
use crate::schema::appointments::{
    end_time as appointment_end_time, patient_id as appointment_patient_id,
    start_time as appointment_start_time, status as appointment_status,
};
use chrono::NaiveDateTime;
use diesel::result::QueryResult;
use diesel::{BoolExpressionMethods, ExpressionMethods, PgConnection, QueryDsl, RunQueryDsl};
use uuid::Uuid;

pub fn find_appointments_filtered(
    user_id_val: Uuid,
    patient_id_val: Option<Uuid>,
    status_val: Option<&str>,
    start_date: Option<NaiveDateTime>,
    end_date: Option<NaiveDateTime>,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Appointment>> {
    let mut query = appointments.filter(user_id.eq(user_id_val)).into_boxed();

    if let Some(pid) = patient_id_val {
        query = query.filter(appointment_patient_id.eq(pid));
    }

    if let Some(s) = status_val {
        query = query.filter(appointment_status.eq(s));
    }

    if let Some(start) = start_date {
        query = query.filter(appointment_end_time.ge(start));
    }

    if let Some(end) = end_date {
        query = query.filter(appointment_start_time.le(end));
    }

    query
        .order_by(appointment_start_time.asc())
        .load::<Appointment>(conn)
}

pub fn find_appointments_by_patient(
    user_id_val: Uuid,
    patient_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Appointment>> {
    appointments
        .filter(user_id.eq(user_id_val))
        .filter(appointment_patient_id.eq(patient_id_val))
        .order_by(appointment_start_time.asc())
        .load::<Appointment>(conn)
}

pub fn find_appointment_by_id(
    appointment_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<Appointment> {
    appointments
        .filter(id.eq(appointment_id_val))
        .filter(user_id.eq(user_id_val))
        .first::<Appointment>(conn)
}

pub fn find_appointments_in_range(
    user_id_val: Uuid,
    start: NaiveDateTime,
    end: NaiveDateTime,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Appointment>> {
    appointments
        .filter(user_id.eq(user_id_val))
        .filter(appointment_start_time.ge(start))
        .filter(appointment_start_time.le(end))
        .order_by(appointment_start_time.asc())
        .load::<Appointment>(conn)
}

pub fn count_today_appointments(user_id_val: Uuid, conn: &mut PgConnection) -> QueryResult<i64> {
    let now = chrono::Utc::now().naive_utc();
    let day_start = chrono::NaiveDateTime::new(
        now.date(),
        chrono::NaiveTime::from_hms_opt(0, 0, 0).unwrap(),
    );
    let day_end = chrono::NaiveDateTime::new(
        now.date(),
        chrono::NaiveTime::from_hms_opt(23, 59, 59).unwrap(),
    );

    appointments
        .filter(user_id.eq(user_id_val))
        .filter(appointment_start_time.ge(day_start))
        .filter(appointment_start_time.le(day_end))
        .count()
        .get_result(conn)
}

pub fn count_today_appointments_by_status(
    user_id_val: Uuid,
    status_val: &str,
    conn: &mut PgConnection,
) -> QueryResult<i64> {
    let now = chrono::Utc::now().naive_utc();
    let day_start = chrono::NaiveDateTime::new(
        now.date(),
        chrono::NaiveTime::from_hms_opt(0, 0, 0).unwrap(),
    );
    let day_end = chrono::NaiveDateTime::new(
        now.date(),
        chrono::NaiveTime::from_hms_opt(23, 59, 59).unwrap(),
    );

    appointments
        .filter(user_id.eq(user_id_val))
        .filter(appointment_start_time.ge(day_start))
        .filter(appointment_start_time.le(day_end))
        .filter(appointment_status.eq(status_val))
        .count()
        .get_result(conn)
}

pub fn count_upcoming_appointments(
    user_id_val: Uuid,
    days: i64,
    conn: &mut PgConnection,
) -> QueryResult<i64> {
    let now = chrono::Utc::now().naive_utc();
    let future_date = now + chrono::Duration::days(days);

    appointments
        .filter(user_id.eq(user_id_val))
        .filter(appointment_start_time.ge(now))
        .filter(appointment_start_time.le(future_date))
        .count()
        .get_result(conn)
}

pub fn create_recurring_appointments(
    new_appointments: &[NewAppointment],
    conn: &mut PgConnection,
) -> QueryResult<Vec<Appointment>> {
    diesel::insert_into(appointments)
        .values(new_appointments)
        .get_results(conn)
}

pub fn update_appointment(
    appointment_id_val: Uuid,
    user_id_val: Uuid,
    appointment_data: &Appointment,
    conn: &mut PgConnection,
) -> QueryResult<Appointment> {
    diesel::update(
        appointments
            .filter(id.eq(appointment_id_val))
            .filter(user_id.eq(user_id_val)),
    )
    .set(appointment_data)
    .get_result(conn)
}

pub fn delete_appointment(
    appointment_id_val: Uuid,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    diesel::delete(
        appointments
            .filter(id.eq(appointment_id_val))
            .filter(user_id.eq(user_id_val)),
    )
    .execute(conn)
}

pub fn find_appointments_by_series(
    user_id_val: Uuid,
    series_id_val: &str,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Appointment>> {
    appointments
        .filter(user_id.eq(user_id_val))
        .filter(series_id.eq(series_id_val))
        .order_by(appointment_start_time.asc())
        .load::<Appointment>(conn)
}

pub fn find_future_appointments_by_series(
    user_id_val: Uuid,
    series_id_val: &str,
    from_time: NaiveDateTime,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Appointment>> {
    appointments
        .filter(user_id.eq(user_id_val))
        .filter(series_id.eq(series_id_val))
        .filter(appointment_start_time.ge(from_time))
        .order_by(appointment_start_time.asc())
        .load::<Appointment>(conn)
}

pub fn find_completed_appointments_without_notes(
    user_id_val: Uuid,
    from_date: chrono::NaiveDate,
    conn: &mut PgConnection,
) -> QueryResult<Vec<Appointment>> {
    use crate::schema::session_notes;
    use diesel::dsl::not;
    use diesel::ExpressionMethods;

    // Converter a data para datetime com hora 00:00:00
    let from_datetime = chrono::NaiveDateTime::new(
        from_date,
        chrono::NaiveTime::from_hms_opt(0, 0, 0).unwrap(),
    );

    let now = chrono::Utc::now().naive_utc();

    // Buscar IDs de agendamentos que têm notas de sessão
    let appointment_ids_with_notes: Vec<Uuid> = session_notes::table
        .filter(session_notes::appointment_id.is_not_null())
        .select(session_notes::appointment_id)
        .load::<Option<Uuid>>(conn)?
        .into_iter()
        .flatten()
        .collect();

    // Query principal: agendamentos com status "completed" nos últimos dias,
    // que não estejam na lista de agendamentos com notas
    let mut query = appointments
        .filter(user_id.eq(user_id_val))
        .filter(appointment_status.eq("completed")) // Status "Realizado"
        .filter(appointment_start_time.ge(from_datetime)) // A partir da data inicial
        .filter(appointment_start_time.le(now)) // Até a data atual
        .into_boxed();

    if !appointment_ids_with_notes.is_empty() {
        query = query.filter(not(id.eq_any(appointment_ids_with_notes)));
    }

    query
        .order_by(appointment_start_time.desc()) // Ordenar do mais recente para o mais antigo
        .load::<Appointment>(conn)
}

pub fn delete_appointments_by_series(
    series_id_val: &str,
    user_id_val: Uuid,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    diesel::delete(
        appointments
            .filter(user_id.eq(user_id_val))
            .filter(series_id.eq(series_id_val)),
    )
    .execute(conn)
}

pub fn delete_future_appointments_by_series(
    series_id_val: &str,
    user_id_val: Uuid,
    from_time: chrono::NaiveDateTime,
    conn: &mut PgConnection,
) -> QueryResult<usize> {
    diesel::delete(
        appointments
            .filter(user_id.eq(user_id_val))
            .filter(series_id.eq(series_id_val))
            .filter(appointment_start_time.ge(from_time)),
    )
    .execute(conn)
}

use crate::email::email_template::EmailTemplate;
use resend_rs::{types::CreateEmailBaseOptions, Resend, Result};
use std::env;

#[derive(Clone)]
pub struct EmailService {
    client: Option<Resend>,
}

impl Default for EmailService {
    fn default() -> Self {
        Self::new()
    }
}

impl EmailService {
    pub fn new() -> Self {
        match env::var("RESEND_API_KEY") {
            Ok(api_key) if !api_key.is_empty() => {
                let client = Resend::new(&api_key);
                Self { client: Some(client) }
            }
            _ => {
                println!("[AVISO] RESEND_API_KEY não configurada. O serviço de e-mail estará desativado.");
                Self { client: None }
            }
        }
    }

    pub async fn send_verification_email(&self, to: String, verification_link: &str) -> Result<()> {
        let client = match &self.client {
            Some(client) => client,
            None => {
                println!("[AVISO] Tentativa de enviar e-mail de verificação, mas o serviço de e-mail está desativado.");
                return Ok(()); // Retorna Ok para não quebrar o fluxo em ambiente de teste
            }
        };

        let template = EmailTemplate::verification_email(verification_link);

        let email = CreateEmailBaseOptions::new(
            "Evolua Care <<EMAIL>>",
            [to],
            template.subject,
        )
        .with_html(&template.body);

        client.emails.send(email).await?;
        Ok(())
    }

    pub async fn send_reset_password_email(&self, to: String, reset_link: &str) -> Result<()> {
        let client = match &self.client {
            Some(client) => client,
            None => {
                println!("[AVISO] Tentativa de enviar e-mail de redefinição de senha, mas o serviço de e-mail está desativado.");
                return Ok(()); // Retorna Ok para não quebrar o fluxo
            }
        };

        let template = EmailTemplate::reset_password(reset_link);

        let email = CreateEmailBaseOptions::new(
            "Evolua Care <<EMAIL>>",
            [to],
            template.subject,
        )
        .with_html(&template.body);

        client.emails.send(email).await?;
        Ok(())
    }
}

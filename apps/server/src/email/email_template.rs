pub struct EmailTemplate {
    pub subject: String,
    pub body: String,
}

impl EmailTemplate {
    pub fn verification_email(verification_link: &str) -> Self {
        Self {
            subject: String::from("Verifique seu email"),
            body: format!(
                r#"
                <h1>Verificação de Email</h1>
                <p>Obrigado por se cadastrar! Por favor, clique no link abaixo para verificar seu email:</p>
                <a href="{}">{}</a>
                <p>Se você não solicitou esta verificação, por favor ignore este email.</p>
                "#,
                verification_link, verification_link
            ),
        }
    }

    pub fn reset_password(reset_link: &str) -> Self {
        Self {
            subject: String::from("Redefinição de Senha"),
            body: format!(
                r#"
                <h1>Redefinição de Senha</h1>
                <p>Você solicitou a redefinição de sua senha. Clique no link abaixo para continuar:</p>
                <a href="{}">{}</a>
                <p>Se você não solicitou esta redefinição, por favor ignore este email.</p>
                "#,
                reset_link, reset_link
            ),
        }
    }
}

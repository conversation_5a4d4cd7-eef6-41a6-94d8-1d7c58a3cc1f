use crate::{
    auth::auth_middleware::Auth<PERSON><PERSON>,
    config::database::Db<PERSON><PERSON>,
    errors::ApiResult,
    search::search_service,
};
use axum::{
    extract::{Extension, Query},
    response::IntoResponse,
    Json,
};
use serde::Deserialize;

#[derive(Debug, Deserialize)]
pub struct SearchParams {
    pub q: String,
}

pub async fn global_search_handler(
    Extension(pool): Extension<DbPool>,
    Extension(auth_user): Extension<AuthUser>,
    Query(params): Query<SearchParams>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let results = search_service::perform_global_search(&pool, user_id, &params.q).await?;
    Ok(Json(results))
} 
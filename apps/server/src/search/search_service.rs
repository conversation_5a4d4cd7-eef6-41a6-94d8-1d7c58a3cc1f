use crate::{
    config::database::Db<PERSON>ool,
    errors::ApiResult,
    contact::contact_repository,
    document::document_repository,
    patient::patient_repository,
    search::search_dto::GlobalSearchResults,
};
use futures::try_join;
use uuid::Uuid;

pub async fn perform_global_search(
    pool: &DbPool,
    user_id: Uuid,
    query: &str,
) -> ApiResult<GlobalSearchResults> {
    let mut conn = pool.get()?;

    let conn_clone1 = pool.get()?;
    let conn_clone2 = pool.get()?;
    let conn_clone3 = pool.get()?;

    let patients_future = tokio::task::spawn_blocking(move || {
        patient_repository::search_patients_by_name(&mut conn_clone1, user_id, query, Some(5))
    });
    
    let documents_future = tokio::task::spawn_blocking(move || {
        document_repository::search_documents_by_title(&mut conn_clone2, user_id, query, Some(5))
    });
    
    let contacts_future = tokio::task::spawn_blocking(move || {
        contact_repository::search_contacts_by_name(&mut conn_clone3, user_id, query, Some(5))
    });

    let (patients, documents, contacts) = tokio::try_join!(
        patients_future,
        documents_future,
        contacts_future
    )?;

    Ok(GlobalSearchResults {
        patients: patients?,
        documents: documents?,
        contacts: contacts?,
    })
}

// Placeholder for search service
pub fn hello() {
    println!("Hello from search service");
} 
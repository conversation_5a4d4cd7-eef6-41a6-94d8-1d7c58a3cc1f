mod app;
mod appointment;
mod assessment;
mod auth;
mod config;
mod contact;
mod dashboard;
mod db;
mod document; 
mod email;
mod errors;
mod financial;
mod middleware;
mod model; 
mod notes; 
mod patient;
mod routes;
mod s3_client; 
mod schema;
mod session_note; 
mod tag;
mod user;
mod utils;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    app::start_server().await
}

// Importar o novo repositório e modelos
use crate::dashboard::dashboard_model::{Dashboard, NewDashboard, UpdateDashboard};
use crate::dashboard::dashboard_repository;
use crate::{
    appointment::appointment_service, config::database::DbPool, notes::note_repository,
    patient::patient_repository,
};
use chrono::{Utc, Duration};
use diesel::{PgConnection, result::Error as DieselError}; // Importar DieselError
use serde::Serialize;
use serde_json::Value as JsonValue;
use uuid::Uuid;

// --- Funções para Resumo do Dashboard (mantidas como estão) ---

#[derive(Serialize)]
pub struct DashboardSummary {
    pub patients: PatientMetrics,
    pub appointments: AppointmentsSummary,
    pub notes: NotesSummary,
    pub pending_session_notes: Vec<AppointmentItem>,
}

#[derive(Serialize)]
pub struct PatientMetrics {
    pub total: i64,
    pub new_this_month: i64,
    pub recent_list: Vec<PatientItem>,
}

#[derive(Serialize)]
pub struct AppointmentsSummary {
    pub today_count: i64,
    pub upcoming_count: i64,
    pub today_progress: f64,
    pub upcoming_list: Vec<AppointmentItem>,
}

#[derive(Serialize)]
pub struct AppointmentItem {
    pub id: Uuid,
    pub patient_id: Uuid,
    pub patient_name: String,
    pub title: String,
    pub start_time: String,
    pub end_time: String,
}

#[derive(Serialize)]
pub struct PatientItem {
    pub id: Uuid,
    pub full_name: String,
    pub email: Option<String>,
    pub phone: Option<String>,
}

#[derive(Serialize)]
pub struct NoteItem {
    pub id: Uuid,
    pub content: String,
    pub updated_at: String,
}

#[derive(Serialize)]
pub struct NotesSummary {
    pub notes_list: Vec<NoteItem>,
}

pub async fn get_dashboard_summary(
    user_id: Uuid,
    pool: &DbPool,
    conn: &mut PgConnection,
) -> Result<DashboardSummary, String> {
    // Obter métricas de pacientes
    let patient_metrics = get_patient_metrics(user_id, conn)?;

    // Obter resumo de agendamentos
    let appointments_summary = get_appointments_summary(user_id, conn, pool).await?;

    // Obter resumo de notas
    let notes_summary = get_notes_summary(user_id, conn)?;
    
    // Obter agendamentos recentes marcados como "Realizados" mas sem notas de sessão
    let pending_session_notes = get_pending_session_notes(user_id, conn).await?;

    Ok(DashboardSummary {
        patients: patient_metrics,
        appointments: appointments_summary,
        notes: notes_summary,
        pending_session_notes,
    })
}

fn get_patient_metrics(user_id: Uuid, conn: &mut PgConnection) -> Result<PatientMetrics, String> {
    // Total de pacientes
    let total = patient_repository::count_patients_by_user(user_id, conn)
        .map_err(|e| format!("Erro ao contar total de pacientes: {}", e))?;

    // Novos pacientes deste mês
    let new_this_month = patient_repository::count_new_patients_this_month_by_user(user_id, conn)
        .map_err(|e| format!("Erro ao contar novos pacientes do mês: {}", e))?;

    // Pacientes recentes
    let recent_patients = patient_repository::find_recent_patients_by_user(user_id, 5, conn)
        .map_err(|e| format!("Erro ao buscar pacientes recentes: {}", e))?;

    let recent_list = recent_patients
        .into_iter()
        .map(|p| PatientItem {
            id: p.id,
            full_name: p.full_name,
            email: p.email,
            phone: p.phone,
        })
        .collect();

    Ok(PatientMetrics {
        total,
        new_this_month,
        recent_list,
    })
}

fn get_notes_summary(user_id: Uuid, conn: &mut PgConnection) -> Result<NotesSummary, String> {
    let recent_notes = note_repository::find_recent_notes_by_user(user_id, 5, conn)
        .map_err(|e| format!("Erro ao buscar notas recentes: {}", e))?;

    let notes_list = recent_notes
        .into_iter()
        .map(|n| NoteItem {
            id: n.id,
            content: n.content,
            updated_at: n.updated_at.to_string(),
        })
        .collect();

    Ok(NotesSummary { notes_list })
}

async fn get_appointments_summary(
    user_id: Uuid,
    conn: &mut PgConnection,
    pool: &DbPool,
) -> Result<AppointmentsSummary, String> {
    // Obter contagem de agendamentos do dia
    let today_count =
        crate::appointment::appointment_repository::count_today_appointments(user_id, conn)
            .map_err(|e| format!("Erro ao contar agendamentos do dia: {}", e))?;

    // Obter contagem de agendamentos concluídos do dia
    let today_completed_count = crate::appointment::appointment_repository::count_today_appointments_by_status(
        user_id, "completed", conn,
    )
    .map_err(|e| format!("Erro ao contar agendamentos concluídos do dia: {}", e))?;

    // Calcular progresso
    let today_progress = if today_count > 0 {
        (today_completed_count as f64 / today_count as f64) * 100.0
    } else {
        0.0
    };

    // Calcular intervalo para próximos 7 dias
    let now = Utc::now().date_naive();
    let future_date = now + Duration::days(7);

    // Contagem de próximos agendamentos
    let upcoming_count =
        crate::appointment::appointment_repository::count_upcoming_appointments(user_id, 7, conn)
            .map_err(|e| format!("Erro ao contar próximos agendamentos: {}", e))?;

    // Obter lista dos próximos 5 agendamentos
    let upcoming_appointments =
        appointment_service::get_appointments(pool, user_id, Some(now), Some(future_date))
            .await
            .map_err(|e| e.to_string())?;

    // Converter para o formato simplificado
    let upcoming_list = upcoming_appointments
        .into_iter()
        .map(|apt| AppointmentItem {
            id: apt.id,
            patient_id: apt.patient_id,
            patient_name: apt.patient_name.unwrap_or_else(|| "Paciente".to_string()),
            title: apt.title,
            start_time: apt.start_time.format("%Y-%m-%d %H:%M").to_string(),
            end_time: apt.end_time.format("%Y-%m-%d %H:%M").to_string(),
        })
        .collect();

    Ok(AppointmentsSummary {
        today_count,
        upcoming_count,
        today_progress,
        upcoming_list,
    })
}

async fn get_pending_session_notes(
    user_id: Uuid,
    conn: &mut PgConnection,
) -> Result<Vec<AppointmentItem>, String> {
    // Buscar consultas dos últimos 14 dias que estão marcadas como "Realizados" 
    // mas não possuem uma nota de sessão associada
    use chrono::{Utc, Duration};
    let now = Utc::now().date_naive();
    let days_ago = now - Duration::days(14); // Considerar os últimos 14 dias

    // Buscar os agendamentos concluídos recentemente
    let completed_appointments = crate::appointment::appointment_repository::find_completed_appointments_without_notes(
        user_id,
        days_ago,
        conn,
    )
    .map_err(|e| format!("Erro ao buscar agendamentos pendentes de notas: {}", e))?;

    // Converter para o formato de saída, buscando o nome do paciente para cada agendamento
    let mut pending_items: Vec<AppointmentItem> = Vec::new();
    
    for apt in completed_appointments {
        // Buscar o nome do paciente
        let patient_name = patient_repository::find_patient_by_id(apt.patient_id, user_id, conn)
            .map(|p| p.full_name)
            .unwrap_or_else(|_| "Paciente".to_string());
            
        pending_items.push(AppointmentItem {
            id: apt.id,
            patient_id: apt.patient_id,
            patient_name,
            title: apt.title,
            start_time: apt.start_time.format("%Y-%m-%d %H:%M").to_string(),
            end_time: apt.end_time.format("%Y-%m-%d %H:%M").to_string(),
        });
    }

    Ok(pending_items)
}

// --- Funções para Gerenciamento de Múltiplos Dashboards ---

// Lista todos os dashboards de um usuário
pub fn list_user_dashboards(uid: Uuid, conn: &mut PgConnection) -> Result<Vec<Dashboard>, String> {
    dashboard_repository::find_dashboards_by_user_id(uid, conn)
        .map_err(|e| format!("Erro ao listar dashboards: {}", e))
}

// Busca um dashboard específico pelo ID, verificando se pertence ao usuário
pub fn get_dashboard_by_id(did: Uuid, uid: Uuid, conn: &mut PgConnection) -> Result<Dashboard, String> {
    match dashboard_repository::find_dashboard_by_id(did, conn) {
        Ok(dashboard) => {
            if dashboard.user_id == uid {
                Ok(dashboard)
            } else {
                Err("Dashboard não pertence ao usuário".to_string())
            }
        }
        Err(DieselError::NotFound) => Err("Dashboard não encontrado".to_string()),
        Err(e) => Err(format!("Erro ao buscar dashboard: {}", e)),
    }
}

// Busca o dashboard padrão do usuário (ou cria um se não existir)
pub fn get_default_dashboard(uid: Uuid, conn: &mut PgConnection) -> Result<Dashboard, String> {
    dashboard_repository::ensure_default_dashboard(uid, conn)
        .map_err(|e| format!("Erro ao buscar/criar dashboard padrão: {}", e))
}


// Cria um novo dashboard
pub fn create_new_dashboard(
    uid: Uuid,
    name: String,
    layout: Option<JsonValue>, // Layout pode ser opcional na criação, usando um padrão
    make_default: Option<bool>,
    conn: &mut PgConnection,
) -> Result<Dashboard, String> {
    // Define um layout padrão se nenhum for fornecido
    let final_layout = layout.unwrap_or_else(|| serde_json::json!([])); // Layout vazio como padrão inicial

    let new_dashboard = NewDashboard {
        user_id: uid,
        name,
        layout: final_layout,
        is_default: make_default, // Passa None se não especificado
    };

    dashboard_repository::create_dashboard(&new_dashboard, conn)
        .map_err(|e| format!("Erro ao criar dashboard: {}", e))
}

// Atualiza um dashboard (layout, nome, is_default)
pub fn update_existing_dashboard(
    did: Uuid,
    uid: Uuid, // Para verificar propriedade
    update_payload: UpdateDashboard,
    conn: &mut PgConnection,
) -> Result<Dashboard, String> {
    // Verifica se o dashboard pertence ao usuário antes de atualizar
    match get_dashboard_by_id(did, uid, conn) {
        Ok(_) => {
            // Se pertence, atualiza
            dashboard_repository::update_dashboard(did, &update_payload, conn)
                .map_err(|e| format!("Erro ao atualizar dashboard: {}", e))
        }
        Err(e) => Err(e), // Retorna o erro de get_dashboard_by_id (não encontrado ou não pertence)
    }
}

// Deleta um dashboard
pub fn delete_dashboard_by_id(did: Uuid, uid: Uuid, conn: &mut PgConnection) -> Result<(), String> {
     // Verifica se o dashboard pertence ao usuário antes de deletar
    match get_dashboard_by_id(did, uid, conn) {
        Ok(dashboard) => {
            // Não permitir deletar o último dashboard ou o padrão? (Regra de negócio a definir)
            if dashboard.is_default {
                return Err("Não é possível deletar o dashboard padrão.".to_string());
            }
            // Verificar se é o último?
            // let user_dashboards = list_user_dashboards(uid, conn)?;
            // if user_dashboards.len() <= 1 {
            //     return Err("Não é possível deletar o último dashboard.".to_string());
            // }

            // Se pertence e não é padrão (ou se a regra permitir), deleta
            dashboard_repository::delete_dashboard(did, conn)
                .map(|_| ()) // Converte QueryResult<usize> para QueryResult<()>
                .map_err(|e| format!("Erro ao deletar dashboard: {}", e))
        }
        Err(e) => Err(e), // Retorna o erro de get_dashboard_by_id (não encontrado ou não pertence)
    }
}

// Define um dashboard como padrão
pub fn set_dashboard_as_default(uid: Uuid, did: Uuid, conn: &mut PgConnection) -> Result<Dashboard, String> {
     // Verifica se o dashboard pertence ao usuário antes de definir como padrão
    match get_dashboard_by_id(did, uid, conn) {
        Ok(_) => {
             dashboard_repository::set_default_dashboard(uid, did, conn)
                .map_err(|e| format!("Erro ao definir dashboard como padrão: {}", e))
        }
         Err(e) => Err(e), // Retorna o erro de get_dashboard_by_id (não encontrado ou não pertence)
    }
}

use crate::schema::dashboards::dsl::*;
use crate::dashboard::dashboard_model::{Dashboard, NewDashboard, UpdateDashboard};
use diesel::prelude::*;
use uuid::Uuid;
use diesel::PgConnection;
use diesel::QueryResult;                        

pub fn find_dashboards_by_user_id(uid: Uuid, conn: &mut PgConnection) -> QueryResult<Vec<Dashboard>> {
    dashboards.filter(user_id.eq(uid)).load::<Dashboard>(conn)
}

pub fn find_default_dashboard_by_user_id(uid: Uuid, conn: &mut PgConnection) -> QueryResult<Dashboard> {
    dashboards
        .filter(user_id.eq(uid))
        .filter(is_default.eq(true))
        .first::<Dashboard>(conn)
}

pub fn find_dashboard_by_id(did: Uuid, conn: &mut PgConnection) -> QueryResult<Dashboard> {
    dashboards.find(did).first::<Dashboard>(conn)
}

pub fn create_dashboard(new_dashboard: &NewDashboard, conn: &mut PgConnection) -> QueryResult<Dashboard> {
    if new_dashboard.is_default == Some(true) {
        clear_default_dashboard_for_user(new_dashboard.user_id, conn)?;
    }

    diesel::insert_into(dashboards)
        .values(new_dashboard)
        .get_result::<Dashboard>(conn)
}

pub fn update_dashboard(did: Uuid, dashboard_update: &UpdateDashboard, conn: &mut PgConnection) -> QueryResult<Dashboard> {
    if dashboard_update.is_default == Some(true) {
        let dashboard_to_update = find_dashboard_by_id(did, conn)?;
        clear_default_dashboard_for_user(dashboard_to_update.user_id, conn)?;
    }

    diesel::update(dashboards.find(did))
        .set(dashboard_update)
        .get_result::<Dashboard>(conn)
}

pub fn delete_dashboard(did: Uuid, conn: &mut PgConnection) -> QueryResult<usize> {
    diesel::delete(dashboards.find(did)).execute(conn)
}

pub fn set_default_dashboard(uid: Uuid, did: Uuid, conn: &mut PgConnection) -> QueryResult<Dashboard> {
    conn.transaction::<_, diesel::result::Error, _>(|transaction_conn| {
        clear_default_dashboard_for_user(uid, transaction_conn)?;

        diesel::update(dashboards.find(did).filter(user_id.eq(uid)))
            .set(is_default.eq(true))
            .get_result::<Dashboard>(transaction_conn)
    })
}

fn clear_default_dashboard_for_user(uid: Uuid, conn: &mut PgConnection) -> QueryResult<usize> {
    diesel::update(dashboards.filter(user_id.eq(uid)).filter(is_default.eq(true)))
        .set(is_default.eq(false))
        .execute(conn)
}

pub fn ensure_default_dashboard(uid: Uuid, conn: &mut PgConnection) -> QueryResult<Dashboard> {
    match find_default_dashboard_by_user_id(uid, conn) {
        Ok(dashboard) => Ok(dashboard),
        Err(diesel::NotFound) => {
            match find_dashboards_by_user_id(uid, conn)?.first() {
                Some(existing_dashboard) => {
                    set_default_dashboard(uid, existing_dashboard.id, conn)
                },
                None => {
                    let default_layout = serde_json::json!([
                      {"i": "patientStats-1", "x": 0, "y": 0, "w": 1, "h": 1, "widgetId": "patientStats", "instanceId": "patientStats-1", "visible": true},
                      {"i": "appointmentsToday-1", "x": 1, "y": 0, "w": 1, "h": 1, "widgetId": "appointmentsToday", "instanceId": "appointmentsToday-1", "visible": true},
                      {"i": "financialSummary-1", "x": 2, "y": 0, "w": 1, "h": 1, "widgetId": "financialSummary", "instanceId": "financialSummary-1", "visible": true},
                      {"i": "upcomingAppointments-1", "x": 0, "y": 1, "w": 2, "h": 2, "widgetId": "upcomingAppointments", "instanceId": "upcomingAppointments-1", "visible": true},
                      {"i": "calendar-1", "x": 2, "y": 1, "w": 1, "h": 2, "widgetId": "calendar", "instanceId": "calendar-1", "visible": true},
                      {"i": "quickActions-1", "x": 0, "y": 3, "w": 1, "h": 1, "widgetId": "quickActions", "instanceId": "quickActions-1", "visible": true},
                      {"i": "notes-1", "x": 1, "y": 3, "w": 2, "h": 1, "widgetId": "notes", "instanceId": "notes-1", "visible": true},
                      {"i": "recentPatients-1", "x": 0, "y": 4, "w": 2, "h": 1, "widgetId": "recentPatients", "instanceId": "recentPatients-1", "visible": true}
                    ]);
                    let new_dashboard = NewDashboard {
                        user_id: uid,
                        name: "Dashboard Padrão".to_string(),
                        layout: default_layout,
                        is_default: Some(true),
                    };
                    create_dashboard(&new_dashboard, conn)
                }
            }
        },
        Err(e) => Err(e),
    }
}

use crate::app::AppState;
use crate::auth::auth_middleware::AuthUser;
use crate::dashboard::dashboard_model::UpdateDashboard;
use crate::dashboard::dashboard_service;
use crate::errors::{ApiError, ApiR<PERSON>ult};
use axum::{
    extract::{Extension, Path, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde::Deserialize;
use serde_json::Value as JsonValue;
use uuid::Uuid;

pub async fn get_dashboard_summary_handler(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = state.pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match dashboard_service::get_dashboard_summary(user_id, &state.pool, &mut conn).await {
        Ok(dashboard_data) => Ok(Json(dashboard_data)),
        Err(e) => Err(ApiError::InternalServerError(e)),
    }
}

pub async fn list_dashboards_handler(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = state.pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match dashboard_service::list_user_dashboards(user_id, &mut conn) {
        Ok(dashboards) => Ok(Json(dashboards)),
        Err(e) => Err(ApiError::InternalServerError(format!(
            "Erro ao listar dashboards: {}",
            e
        ))),
    }
}

pub async fn get_default_dashboard_handler(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = state.pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match dashboard_service::get_default_dashboard(user_id, &mut conn) {
        Ok(dashboard) => Ok(Json(dashboard)),
        Err(e) => Err(ApiError::InternalServerError(format!(
            "Erro ao buscar dashboard padrão: {}",
            e
        ))),
    }
}

pub async fn get_dashboard_handler(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(dashboard_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = state.pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match dashboard_service::get_dashboard_by_id(dashboard_id, user_id, &mut conn) {
        Ok(dashboard) => Ok(Json(dashboard)),
        Err(msg) => {
            if msg == "Dashboard não encontrado" {
                Err(ApiError::ResourceNotFound(msg))
            } else if msg == "Dashboard não pertence ao usuário" {
                Err(ApiError::Unauthorized)
            } else {
                Err(ApiError::InternalServerError(format!(
                    "Erro ao buscar dashboard: {}",
                    msg
                )))
            }
        }
    }
}

#[derive(Deserialize)]
pub struct CreateDashboardPayload {
    name: String,
    layout: Option<JsonValue>,
    is_default: Option<bool>,
}

pub async fn create_dashboard_handler(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Json(payload): Json<CreateDashboardPayload>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = state.pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match dashboard_service::create_new_dashboard(
        user_id,
        payload.name,
        payload.layout,
        payload.is_default,
        &mut conn,
    ) {
        Ok(new_dashboard) => Ok((StatusCode::CREATED, Json(new_dashboard))),
        Err(e) => Err(ApiError::InternalServerError(format!(
            "Erro ao criar dashboard: {}",
            e
        ))),
    }
}

pub async fn update_dashboard_handler(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(dashboard_id): Path<Uuid>,
    Json(payload): Json<UpdateDashboard>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = state.pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match dashboard_service::update_existing_dashboard(dashboard_id, user_id, payload, &mut conn) {
        Ok(updated_dashboard) => Ok(Json(updated_dashboard)),
        Err(msg) => {
            if msg == "Dashboard não encontrado" {
                Err(ApiError::ResourceNotFound(msg))
            } else if msg == "Dashboard não pertence ao usuário" {
                Err(ApiError::Unauthorized)
            } else {
                Err(ApiError::InternalServerError(format!(
                    "Erro ao atualizar dashboard: {}",
                    msg
                )))
            }
        }
    }
}

pub async fn delete_dashboard_handler(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(dashboard_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = state.pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match dashboard_service::delete_dashboard_by_id(dashboard_id, user_id, &mut conn) {
        Ok(_) => Ok(StatusCode::NO_CONTENT),
        Err(msg) => {
            if msg == "Dashboard não encontrado" {
                Err(ApiError::ResourceNotFound(msg))
            } else if msg == "Dashboard não pertence ao usuário" {
                Err(ApiError::Unauthorized)
            } else if msg == "Não é possível deletar o dashboard padrão." {
                Err(ApiError::BadRequest(msg))
            } else {
                Err(ApiError::InternalServerError(format!(
                    "Erro ao deletar dashboard: {}",
                    msg
                )))
            }
        }
    }
}

pub async fn set_default_dashboard_handler(
    State(state): State<AppState>,
    Extension(auth_user): Extension<AuthUser>,
    Path(dashboard_id): Path<Uuid>,
) -> ApiResult<impl IntoResponse> {
    let user_id = auth_user.0;
    let mut conn = state.pool.get().map_err(|_| ApiError::ServiceUnavailable)?;

    match dashboard_service::set_dashboard_as_default(user_id, dashboard_id, &mut conn) {
        Ok(updated_dashboard) => Ok(Json(updated_dashboard)),
        Err(msg) => {
            if msg == "Dashboard não encontrado" {
                Err(ApiError::ResourceNotFound(msg))
            } else if msg == "Dashboard não pertence ao usuário" {
                Err(ApiError::Unauthorized)
            } else {
                Err(ApiError::InternalServerError(format!(
                    "Erro ao definir dashboard como padrão: {}",
                    msg
                )))
            }
        }
    }
}
use crate::schema::dashboards;
use crate::db::models::user::User; // Assuming User model exists here
use diesel::prelude::*;
use serde::{Serialize, Deserialize};
use chrono::NaiveDateTime;
use serde_json::Value as JsonValue; // Use serde_json::Value for JSONB
use uuid::Uuid;

#[derive(Queryable, Selectable, Identifiable, Associations, Serialize, Deserialize, Debug, Clone)]
#[diesel(belongs_to(User))]
#[diesel(table_name = dashboards)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct Dashboard {
    pub id: Uuid, 
    pub user_id: Uuid, 
    pub name: String,
    pub layout: JsonValue, 
    pub is_default: bool,
    pub created_at: NaiveDateTime,
    pub updated_at: NaiveDateTime,
}

#[derive(Insertable, AsChangeset, Deserialize, Debug)]
#[diesel(table_name = dashboards)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct NewDashboard {
    pub user_id: Uuid, 
    pub name: String,
    pub layout: JsonValue,
    pub is_default: Option<bool>, 
}


#[derive(AsChangeset, Deserialize, Debug)]
#[diesel(table_name = dashboards)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct UpdateDashboard {
    pub name: Option<String>,
    pub layout: Option<JsonValue>,
    pub is_default: Option<bool>,
}

// Em: apps/server/tests/auth_test.rs

use axum::body::Body;
use axum::http::{header, Request, StatusCode};
use axum::Router;
use diesel::{Connection, PgConnection};
use diesel::r2d2::{ConnectionManager, Pool};
use evolua_server::auth::auth_dto::RegisterResponse;
use evolua_server::app;
use http_body_util::BodyExt;
use once_cell::sync::Lazy;
use serde_json::json;
use std::env;
use tower::ServiceExt;

// Pool de conexão de teste global, criado apenas uma vez.
static TEST_POOL: Lazy<Pool<ConnectionManager<PgConnection>>> = Lazy::new(|| {
    dotenv::dotenv().ok();
    let database_url = env::var("TEST_DATABASE_URL").expect("TEST_DATABASE_URL must be set");
    let manager = ConnectionManager::<PgConnection>::new(database_url);
    Pool::builder()
        .build(manager)
        .expect("Failed to create test pool.")
});

/// Retorna uma instância do Router da aplicação para ser usada nos testes.
fn setup_test_app() -> Router {
    app() // A mesma função que usamos em main.rs
}

#[tokio::test]
async fn test_register_success() {
    // Obter conexão e iniciar transação de teste
    let mut conn = TEST_POOL.get().expect("Failed to get DB connection from pool");
    conn.begin_test_transaction().expect("Failed to begin test transaction");

    let app = setup_test_app();

    // 1. Preparar a Requisição
    let request_body = json!({
        "first_name": "Test",
        "last_name": "User",
        "email": "<EMAIL>",
        "occupation": "Tester",
        "password": "Password123!"
    });

    let request = Request::builder()
        .uri("/auth/register")
        .method("POST")
        .header(header::CONTENT_TYPE, "application/json")
        .body(Body::from(serde_json::to_vec(&request_body).unwrap()))
        .unwrap();

    // 2. Executar a Requisição
    let response = app.oneshot(request).await.unwrap();

    // 3. Capturar o corpo da resposta para debug
    let status = response.status();
    let body_bytes = response.into_body().collect().await.unwrap().to_bytes();
    let body_text = String::from_utf8_lossy(&body_bytes);

    // Se não for 200, imprimir o erro para debug
    if status != StatusCode::OK {
        println!("Status: {}", status);
        println!("Response body: {}", body_text);
        panic!("Expected status 200, got {}", status);
    }

    // 4. Validar o Corpo da Resposta
    let body: RegisterResponse = serde_json::from_slice(&body_bytes).unwrap();

    assert!(!body.access_token.is_empty());
    // O refresh_token não é mais enviado no corpo, então não precisamos testá-lo aqui.
} 
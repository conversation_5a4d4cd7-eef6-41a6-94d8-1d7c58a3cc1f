-- Add dynamic fields to models table for TipTap-based dynamic models

-- Add template_content field to store TipTap JSON content
ALTER TABLE models ADD COLUMN template_content JSONB DEFAULT NULL;

-- Add form_schema field to store dynamic form configuration
ALTER TABLE models ADD COLUMN form_schema JSONB DEFAULT NULL;

-- Add output_types field to store available output formats
ALTER TABLE models ADD COLUMN output_types TEXT[] DEFAULT ARRAY['note'];

-- Add usage_count field to track model usage
ALTER TABLE models ADD COLUMN usage_count INTEGER DEFAULT 0;

-- Add indexes for better performance
CREATE INDEX idx_models_template_content ON models USING GIN (template_content);
CREATE INDEX idx_models_form_schema ON models USING GIN (form_schema);
CREATE INDEX idx_models_output_types ON models USING GIN (output_types);
CREATE INDEX idx_models_usage_count ON models (usage_count);

-- Add comments for documentation
COMMENT ON COLUMN models.template_content IS 'TipTap JSON content for the model template';
COMMENT ON COLUMN models.form_schema IS 'Dynamic form configuration including sections, fields, and validation rules';
COMMENT ON COLUMN models.output_types IS 'Array of supported output types: note, pdf, docx';
COMMENT ON COLUMN models.usage_count IS 'Number of times this model has been applied';

[package]
name = "server"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.44.2", features = ["full"] }
dotenv = "0.15.0"
serde = { version = "1.0.219", features = ["derive"] }
jsonwebtoken = "9.3.1"
chrono = { version = "0.4.40", features = ["serde"] }
bcrypt = "0.16.0"
diesel = { version = "2.2.0", features = ["postgres", "r2d2", "chrono", "serde_json", "uuid", "numeric"] }
axum = "0.8.3"
axum-extra = { version = "0.10.0", features = ["multipart"] }
hyper = { version = "1.6.0", features = ["full"] }
tracing-subscriber = "0.3.19"
tracing = "0.1.41"
hyper-util = "0.1.11"
tower = "0.5.2"
tower-http = { version = "0.6.2", features = ["cors", "trace"] }
rand = { version = "0.9.0" }
rand_distr = "0.5.0"
resend-rs = "0.11.2"
validator = { version = "0.20.0", features = ["derive"] }
serde_json = "1.0.140"
time = "0.3.37"
tower-cookies = { version = "0.11.0", features = ["signed", "private"] }
regex = "1.11.1"
once_cell = "1.20.2"
bytes = "1.9.0"
http-body-util = "0.1.2"
uuid = { version = "1.16.0", features = ["v4", "serde"] }
futures = "0.3.31"
# Dependências para MinIO/S3
aws-config = "1.1.7"
aws-sdk-s3 = { version = "1.24.0", features = ["rt-tokio"] }
aws-smithy-types = "1.1.7"
aws-smithy-http = "0.60.0"
mime = "0.3.17"
mime_guess = "2.0.4"
sanitize-filename = "0.5.0"
bigdecimal = { version = "0.4.8", features = ["serde"] }
clap = { version = "4.5.4", features = ["derive"] }
lettre = "0.11.7"
csv = "1.3.1"

[dev-dependencies]
hyper = "1.6.0"
hyper-util = "0.1.11"
tower = { version = "0.5.2", features = ["util"] }
serde_json = "1.0.140"
anyhow = "1.0.86"
http-body-util = "0.1.2"

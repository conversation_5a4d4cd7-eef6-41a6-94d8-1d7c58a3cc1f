{"output": {"filePath": "repomix-output.txt", "style": "plain", "parsableStyle": false, "fileSummary": true, "directoryStructure": true, "removeComments": false, "removeEmptyLines": false, "compress": false, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": true, "sortByChangesMaxCommits": 100}}, "include": [], "ignore": {"useGitignore": true, "useDefaultPatterns": false, "customPatterns": []}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}
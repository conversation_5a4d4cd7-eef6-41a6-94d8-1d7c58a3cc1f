#!/bin/bash

# Criar diretório de sons se não existir
mkdir -p client/public/resources/sounds

# URLs para sons gratuitos
BELL_URL="https://assets.mixkit.co/active_storage/sfx/2869/2869-preview.mp3"
ALARM_URL="https://assets.mixkit.co/active_storage/sfx/2867/2867-preview.mp3"
CHIME_URL="https://assets.mixkit.co/active_storage/sfx/2870/2870-preview.mp3"

# Baixar os sons
echo "Baixando sons para o widget de timer..."
curl -L "$BELL_URL" -o client/public/resources/sounds/timer-end.mp3
curl -L "$ALARM_URL" -o client/public/resources/sounds/alarm.mp3
curl -L "$CHIME_URL" -o client/public/resources/sounds/chime.mp3

echo "Download concluído! Os sons foram salvos em client/public/resources/sounds/"

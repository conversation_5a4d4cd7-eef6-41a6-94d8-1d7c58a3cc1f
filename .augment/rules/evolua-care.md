---
type: "always_apply"
---

## 1. Visão Geral do Projeto

**Propósito:** Evolua Care é um Sistema de Gerenciamento de Prática Clínica para terapeutas autônomos e pequenas equipes. O objetivo é automatizar tarefas administrativas e centralizar informações clínicas, financeiras e de agendamento.

---

## 2. Arquitetura Frontend: Feature-Sliced Design (FSD)

**Regra Crítica:** O frontend segue estritamente os princípios do FSD. Todas as contribuições devem respeitar esta estrutura.

**Estrutura de Diretórios:**
- **`apps/client/src/app`**: Lógica de inicialização da aplicação (roteador, providers, estilos globais). **Não deve conter lógica de negócio.**
- **`apps/client/src/pages`**: Componentes de página, que compõem telas a partir de features e widgets. **Regra: Páginas não podem ser importadas por outras páginas, features ou widgets.**
- **`apps/client/src/features`**: Funcionalidades de negócio (ex: `auth`, `calendar`, `patient-management`). Cada feature é um "mini-aplicativo" autocontido.
  - **Estrutura de uma Feature (`<feature-name>`):**
    - `api/`: Funções de chamada à API e chaves de query (`<feature>.api.ts`, `constants.ts`).
    - `components/`: Componentes React específicos da feature.
    - `hooks/`: Hooks customizados (`use<Feature>Query.ts`, `use<Feature>Mutations.ts`).
    - `types/`: Schemas Zod e tipos TypeScript (`<feature>.schema.ts`).
- **`apps/client/src/shared`**: Código reutilizável e agnóstico de domínio.
  - `api/`: Configuração central do cliente de API (Axios).
  - `components/`: Componentes de UI reutilizáveis e complexos (ex: `ConfirmDeleteDialog`).
  - `hooks/`: Hooks genéricos (ex: `useMediaQuery`, `useToast`).
  - `lib/`: Utilitários e bibliotecas (ex: `utils.ts`, `date.ts`).
  - `ui/`: Componentes base do Shadcn/ui (botões, cards, etc.).

> **Regra de Ouro do FSD (Slice Rule):** As camadas só podem importar de camadas estritamente abaixo delas.
> - `app` pode importar de `pages`, `features`, `widgets`, `shared`.
> - `pages` podem importar de `features`, `widgets`, `shared`.
> - `features` podem importar de `shared`.
> - `shared` **não pode** importar de nenhuma outra camada.

---

## 3. Padrões de Código Frontend

**3.1. Estado e Dados (TanStack Query + Zustand)**
- **Server State:** **SEMPRE** use TanStack Query (`useQuery`, `useMutation`) para interagir com a API do backend.
  - **Chaves de Query:** Centralize as chaves em `features/<feature>/api/constants.ts`. Siga o padrão de aninhamento (ex: `['patients', 'list']`, `['patients', 'detail', patientId]`).
  - **Mutações:** Use `onSuccess` para invalidar queries relevantes (`queryClient.invalidateQueries`) e para exibir `toast` de sucesso/erro. A UI deve reagir à invalidação, não a manipulações manuais de estado.
- **Client State:** Use Zustand (`create`) para estado global da UI que não é persistido no servidor (ex: estado de um diálogo, tema da UI, modo de visualização).
  - Crie stores específicos por domínio (ex: `useCalendarStore`, `useWidgetsStore`).

**3.2. Tipagem e Validação (TypeScript + Zod)**
- **Fonte da Verdade:** Todos os tipos de dados que vêm da API ou são usados em formulários **DEVEM** ser definidos com um schema Zod em `features/<feature>/types/<entity>.schema.ts`.
- **IDs:** Todos os IDs de entidades (usuário, paciente, agendamento, etc.) são `string` no formato UUID. **NUNCA** use `parseInt` ou `Number()` para converter IDs.
- **Formulários:** Use `react-hook-form` com `zodResolver` para uma integração robusta entre validação e estado do formulário.

**3.3. Componentes e Estilização**
- **Componentes:** Siga os padrões do Shadcn/ui. Componentes devem ser o mais "burros" possível, recebendo dados e callbacks via props.
- **Estilização:** Use Tailwind CSS e o utilitário `cn()` para compor classes. Evite CSS-in-JS ou estilos inline.

**3.4. Roteamento (TanStack Router)**
- As rotas são definidas pela estrutura de arquivos em `src/app/routes`.
- Use o hook `useParams` para obter IDs de rotas (eles sempre serão `string`).
- Use o hook `useNavigate` para navegação programática.
- Use o componente `<Link>` para navegação declarativa.

---

## 4. Arquitetura Backend (Rust/Axum)

O backend segue uma arquitetura modular por domínio.

**Estrutura de um Módulo (`<domain-name>`):**
- **`mod.rs`**: Declara os submódulos.
- **`*_handler.rs`**: Lida com a requisição HTTP (extrai dados, chama o serviço) e formata a resposta. **Não deve conter lógica de negócio.**
- **`*_service.rs`**: Contém a lógica de negócio principal. Orquestra chamadas ao repositório e a outros serviços.
- **`*_repository.rs`**: Lida com a comunicação com o banco de dados (queries Diesel). **Não deve conter lógica de negócio.**
- **`*_dto.rs`**: Define as "Data Transfer Objects" - as estruturas de dados que são expostas pela API. Garante o desacoplamento dos modelos do banco de dados.

> **Regra de Fluxo de Controle Backend:** O fluxo de uma requisição **DEVE** ser: `Handler -> Service -> Repository`. Um handler nunca deve chamar um repositório diretamente.

**Regras Adicionais do Backend:**
1.  **Tratamento de Erros:** Use o `ApiError` customizado em `errors/mod.rs` para todos os erros retornados pelos handlers. O serviço pode retornar `Result<T, String>`, que o handler converte para um `ApiResult`.
2.  **Transações:** Operações que envolvem múltiplas escritas no banco de dados **DEVEM** ser encapsuladas em uma transação Diesel (`conn.transaction(...)`) no nível de serviço ou repositório.
3.  **Validação:** A validação de entrada (payloads JSON) deve ser feita no **handler**, usando a crate `validator`, antes de passar os dados para o serviço.

---

## 5. Regras Gerais do Projeto

1.  **Commits:** Use commits semânticos (ex: `feat:`, `fix:`, `refactor:`, `docs:`).
2.  **Dados Mockados:** É proibido fazer merge de código com dados mockados em arquivos de funcionalidades. Dados mockados são permitidos apenas no arquivo `features/dashboard/lib/dashboard.mock.ts` para simulação da API durante o desenvolvimento.
3.  **Comentários:** nao comentar nunca .O código deve ser a principal documentação.
4.  **Consistência:** Mantenha a consistência com o código existente. Se encontrar um padrão, siga-o. Se precisar mudá-lo, refatore todas as ocorrências.

Sempre siga estas diretrizes ao gerar ou modificar código para o projeto Evolua Care.